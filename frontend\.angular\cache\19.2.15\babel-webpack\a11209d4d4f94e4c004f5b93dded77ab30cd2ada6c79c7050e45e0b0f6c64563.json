{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/list\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nconst _c0 = a0 => [\"/doc\", a0];\nfunction DocumentationNavComponent_mat_list_item_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-list-item\", 9)(1, \"mat-icon\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r1.route));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.description);\n  }\n}\nexport class DocumentationNavComponent {\n  constructor() {\n    this.navItems = [{\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    }, {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    }, {\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    }, {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    }, {\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    }, {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    }, {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }];\n  }\n  static {\n    this.ɵfac = function DocumentationNavComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DocumentationNavComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DocumentationNavComponent,\n      selectors: [[\"app-documentation-nav\"]],\n      decls: 20,\n      vars: 1,\n      consts: [[1, \"nav-container\"], [1, \"nav-header\"], [1, \"nav-subtitle\"], [1, \"nav-list\"], [\"routerLinkActive\", \"active-nav-item\", \"class\", \"nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-footer\"], [1, \"version-info\"], [1, \"quick-links\"], [\"href\", \"https://github.com/blockchain-spt\", \"target\", \"_blank\", \"mat-button\", \"\"], [\"routerLinkActive\", \"active-nav-item\", 1, \"nav-item\", 3, \"routerLink\"], [\"matListItemIcon\", \"\"], [\"matListItemTitle\", \"\"], [\"matListItemLine\", \"\", 1, \"nav-description\"]],\n      template: function DocumentationNavComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵtext(3, \"Documentation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 2);\n          i0.ɵɵtext(5, \"Comprehensive guide to SPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-nav-list\", 3);\n          i0.ɵɵtemplate(7, DocumentationNavComponent_mat_list_item_7_Template, 7, 6, \"mat-list-item\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"mat-divider\");\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\");\n          i0.ɵɵtext(14, \"SPT v1.0.0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"a\", 8)(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" GitHub \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.navItems);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, MatListModule, i3.MatNavList, i3.MatListItem, i3.MatListItemIcon, i3.MatDivider, i3.MatListItemLine, i3.MatListItemTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatAnchor, MatDividerModule],\n      styles: [\".nav-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 16px 0;\\n}\\n\\n.nav-header[_ngcontent-%COMP%] {\\n  padding: 0 16px 16px;\\n  border-bottom: 1px solid #e0e0e0;\\n  margin-bottom: 16px;\\n}\\n\\n.nav-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n\\n.nav-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n.nav-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  margin: 4px 8px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(25, 118, 210, 0.08);\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%] {\\n  background-color: rgba(25, 118, 210, 0.12);\\n  color: #1976d2;\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n\\n.nav-description[_ngcontent-%COMP%] {\\n  font-size: 0.8em;\\n  color: #666;\\n  margin-top: 2px;\\n}\\n\\n.nav-footer[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-top: 1px solid #e0e0e0;\\n  margin-top: 16px;\\n}\\n\\n.version-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n.version-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.quick-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.quick-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  font-size: 0.9em;\\n}\\n\\n.quick-links[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  margin-right: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatListModule", "MatIconModule", "MatButtonModule", "MatDividerModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "item_r1", "route", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "label", "description", "DocumentationNavComponent", "constructor", "navItems", "selectors", "decls", "vars", "consts", "template", "DocumentationNavComponent_Template", "rf", "ctx", "ɵɵtemplate", "DocumentationNavComponent_mat_list_item_7_Template", "ɵɵelement", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "RouterLink", "RouterLinkActive", "i3", "MatNavList", "MatListItem", "MatListItemIcon", "<PERSON><PERSON><PERSON><PERSON>", "MatListItemLine", "MatListItemTitle", "i4", "MatIcon", "i5", "<PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\documentation-nav\\documentation-nav.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\n\ninterface NavItem {\n  label: string;\n  route: string;\n  icon: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-documentation-nav',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatDividerModule\n  ],\n  template: `\n    <div class=\"nav-container\">\n      <div class=\"nav-header\">\n        <h3>Documentation</h3>\n        <p class=\"nav-subtitle\">Comprehensive guide to SPT</p>\n      </div>\n\n      <mat-nav-list class=\"nav-list\">\n        <mat-list-item \n          *ngFor=\"let item of navItems\" \n          [routerLink]=\"['/doc', item.route]\"\n          routerLinkActive=\"active-nav-item\"\n          class=\"nav-item\">\n          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>\n          <div matListItemTitle>{{ item.label }}</div>\n          <div matListItemLine class=\"nav-description\">{{ item.description }}</div>\n        </mat-list-item>\n      </mat-nav-list>\n\n      <mat-divider></mat-divider>\n\n      <div class=\"nav-footer\">\n        <div class=\"version-info\">\n          <mat-icon>info</mat-icon>\n          <span>SPT v1.0.0</span>\n        </div>\n        <div class=\"quick-links\">\n          <a href=\"https://github.com/blockchain-spt\" target=\"_blank\" mat-button>\n            <mat-icon>code</mat-icon>\n            GitHub\n          </a>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .nav-container {\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      padding: 16px 0;\n    }\n\n    .nav-header {\n      padding: 0 16px 16px;\n      border-bottom: 1px solid #e0e0e0;\n      margin-bottom: 16px;\n    }\n\n    .nav-header h3 {\n      margin: 0 0 4px 0;\n      color: #1976d2;\n      font-weight: 500;\n    }\n\n    .nav-subtitle {\n      margin: 0;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    .nav-list {\n      flex: 1;\n      padding: 0;\n    }\n\n    .nav-item {\n      margin: 4px 8px;\n      border-radius: 8px;\n      transition: all 0.2s ease;\n    }\n\n    .nav-item:hover {\n      background-color: rgba(25, 118, 210, 0.08);\n    }\n\n    .nav-item.active-nav-item {\n      background-color: rgba(25, 118, 210, 0.12);\n      color: #1976d2;\n    }\n\n    .nav-item.active-nav-item mat-icon {\n      color: #1976d2;\n    }\n\n    .nav-description {\n      font-size: 0.8em;\n      color: #666;\n      margin-top: 2px;\n    }\n\n    .nav-footer {\n      padding: 16px;\n      border-top: 1px solid #e0e0e0;\n      margin-top: 16px;\n    }\n\n    .version-info {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-bottom: 12px;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    .version-info mat-icon {\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .quick-links {\n      display: flex;\n      gap: 8px;\n    }\n\n    .quick-links a {\n      font-size: 0.9em;\n    }\n\n    .quick-links mat-icon {\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n      margin-right: 4px;\n    }\n  `]\n})\nexport class DocumentationNavComponent {\n  navItems: NavItem[] = [\n    {\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    },\n    {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    },\n    {\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    },\n    {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    },\n    {\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    },\n    {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    },\n    {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;IAiClDC,EALF,CAAAC,cAAA,uBAImB,mBACS;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACrEF,EADqE,CAAAG,YAAA,EAAM,EAC3D;;;;IANdH,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,KAAA,EAAmC;IAGTR,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IACnBX,EAAA,CAAAS,SAAA,GAAgB;IAAhBT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;IACOZ,EAAA,CAAAS,SAAA,GAAsB;IAAtBT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAM,WAAA,CAAsB;;;AAkH7E,OAAM,MAAOC,yBAAyB;EA5ItCC,YAAA;IA6IE,KAAAC,QAAQ,GAAc,CACpB;MACEJ,KAAK,EAAE,UAAU;MACjBJ,KAAK,EAAE,UAAU;MACjBG,IAAI,EAAE,MAAM;MACZE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBJ,KAAK,EAAE,iBAAiB;MACxBG,IAAI,EAAE,YAAY;MAClBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,eAAe;MACtBJ,KAAK,EAAE,eAAe;MACtBG,IAAI,EAAE,KAAK;MACXE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,oBAAoB;MAC3BJ,KAAK,EAAE,oBAAoB;MAC3BG,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,WAAW;MAClBJ,KAAK,EAAE,WAAW;MAClBG,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,mBAAmB;MAC1BJ,KAAK,EAAE,kBAAkB;MACzBG,IAAI,EAAE,WAAW;MACjBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,cAAc;MACrBJ,KAAK,EAAE,cAAc;MACrBG,IAAI,EAAE,cAAc;MACpBE,WAAW,EAAE;KACd,CACF;;;;uCA5CUC,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9H9BvB,EAFJ,CAAAC,cAAA,aAA2B,aACD,SAClB;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,WAAwB;UAAAD,EAAA,CAAAE,MAAA,iCAA0B;UACpDF,EADoD,CAAAG,YAAA,EAAI,EAClD;UAENH,EAAA,CAAAC,cAAA,sBAA+B;UAC7BD,EAAA,CAAAyB,UAAA,IAAAC,kDAAA,2BAImB;UAKrB1B,EAAA,CAAAG,YAAA,EAAe;UAEfH,EAAA,CAAA2B,SAAA,kBAA2B;UAIvB3B,EAFJ,CAAAC,cAAA,aAAwB,cACI,gBACd;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAClBF,EADkB,CAAAG,YAAA,EAAO,EACnB;UAGFH,EAFJ,CAAAC,cAAA,cAAyB,YACgD,gBAC3D;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,gBACF;UAGNF,EAHM,CAAAG,YAAA,EAAI,EACA,EACF,EACF;;;UAxBiBH,EAAA,CAAAS,SAAA,GAAW;UAAXT,EAAA,CAAAI,UAAA,YAAAoB,GAAA,CAAAR,QAAA,CAAW;;;qBAhBlCtB,YAAY,EAAAkC,EAAA,CAAAC,OAAA,EACZlC,YAAY,EAAAmC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA,EACZpC,aAAa,EAAAqC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,UAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,gBAAA,EACb1C,aAAa,EAAA2C,EAAA,CAAAC,OAAA,EACb3C,eAAe,EAAA4C,EAAA,CAAAC,SAAA,EACf5C,gBAAgB;MAAA6C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}