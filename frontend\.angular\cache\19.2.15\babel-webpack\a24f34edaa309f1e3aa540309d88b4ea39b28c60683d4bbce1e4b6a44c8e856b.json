{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { SEVERITY_COLORS } from '../../models/security.models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/table\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/router\";\nconst _c0 = a0 => [\"/scan\", a0];\nfunction DashboardComponent_th_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Scan ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(scan_r1.id);\n  }\n}\nfunction DashboardComponent_th_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getProjectName(scan_r2.project_path));\n  }\n}\nfunction DashboardComponent_th_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Chains\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_59_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chain_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(chain_r4);\n  }\n}\nfunction DashboardComponent_td_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32)(1, \"mat-chip-listbox\");\n    i0.ɵɵtemplate(2, DashboardComponent_td_59_mat_chip_2_Template, 2, 1, \"mat-chip\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", scan_r5.chains);\n  }\n}\nfunction DashboardComponent_th_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + scan_r6.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(scan_r6.status);\n  }\n}\nfunction DashboardComponent_th_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate((scan_r7.issues == null ? null : scan_r7.issues.length) || 0);\n  }\n}\nfunction DashboardComponent_th_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(scan_r8.created_at));\n  }\n}\nfunction DashboardComponent_th_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 32)(1, \"button\", 34)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, scan_r9.id));\n  }\n}\nfunction DashboardComponent_tr_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 35);\n  }\n}\nfunction DashboardComponent_tr_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 36);\n  }\n}\nfunction DashboardComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38)(2, \"mat-chip\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 39)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 40)(12, \"mat-chip\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const issue_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getSeverityColor(issue_r10.severity));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", issue_r10.severity, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r10.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", issue_r10.file, \":\", issue_r10.line, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.chain);\n  }\n}\nexport class DashboardComponent {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.stats = {\n      totalScans: 0,\n      criticalIssues: 0,\n      highIssues: 0,\n      mediumIssues: 0\n    };\n    this.recentScans = [];\n    this.topIssues = [];\n    this.displayedColumns = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: response => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: error => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n  loadMockData() {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts.critical || 0,\n      highIssues: mockScan.severity_counts.high || 0,\n      mediumIssues: mockScan.severity_counts.medium || 0\n    };\n  }\n  calculateStats(scans) {\n    this.stats.totalScans = scans.length;\n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues = [];\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.critical || 0;\n      highTotal += scan.severity_counts?.high || 0;\n      mediumTotal += scan.severity_counts?.medium || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n    // Get top 5 most severe issues\n    this.topIssues = allIssues.sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity)).slice(0, 5);\n  }\n  getSeverityWeight(severity) {\n    const weights = {\n      critical: 4,\n      high: 3,\n      medium: 2,\n      low: 1,\n      info: 0\n    };\n    return weights[severity] || 0;\n  }\n  getSeverityColor(severity) {\n    return SEVERITY_COLORS[severity] || '#666';\n  }\n  getProjectName(path) {\n    return path.split('/').pop() || path;\n  }\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleDateString();\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 103,\n      vars: 8,\n      consts: [[1, \"dashboard-container\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-number\"], [1, \"stat-card\", \"critical\"], [1, \"stat-card\", \"high\"], [1, \"stat-card\", \"medium\"], [1, \"recent-scans-card\"], [\"mat-button\", \"\", \"routerLink\", \"/scan\", \"color\", \"primary\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"scans-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"project\"], [\"matColumnDef\", \"chains\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [1, \"top-issues-card\"], [1, \"issues-list\"], [\"class\", \"issue-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"quick-actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/checklist\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/reports\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/settings\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"mat-icon-button\", \"\", 3, \"routerLink\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"issue-item\"], [1, \"issue-severity\"], [1, \"issue-details\"], [1, \"issue-chain\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"\\uD83D\\uDEE1\\uFE0F Security Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"mat-card\", 2)(5, \"mat-card-header\")(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-card-title\");\n          i0.ɵɵtext(9, \"Total Scans\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"div\", 3);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"mat-card\", 4)(14, \"mat-card-header\")(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"error\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"mat-card-title\");\n          i0.ɵɵtext(18, \"Critical Issues\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"mat-card-content\")(20, \"div\", 3);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"mat-card\", 5)(23, \"mat-card-header\")(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-card-title\");\n          i0.ɵɵtext(27, \"High Issues\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"mat-card-content\")(29, \"div\", 3);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"mat-card\", 6)(32, \"mat-card-header\")(33, \"mat-icon\");\n          i0.ɵɵtext(34, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"mat-card-title\");\n          i0.ɵɵtext(36, \"Medium Issues\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-card-content\")(38, \"div\", 3);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"mat-card\", 7)(41, \"mat-card-header\")(42, \"mat-card-title\");\n          i0.ɵɵtext(43, \"Recent Scans\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"button\", 8)(45, \"mat-icon\");\n          i0.ɵɵtext(46, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \" New Scan \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"mat-card-content\")(49, \"div\", 9)(50, \"table\", 10);\n          i0.ɵɵelementContainerStart(51, 11);\n          i0.ɵɵtemplate(52, DashboardComponent_th_52_Template, 2, 0, \"th\", 12)(53, DashboardComponent_td_53_Template, 2, 1, \"td\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(54, 14);\n          i0.ɵɵtemplate(55, DashboardComponent_th_55_Template, 2, 0, \"th\", 12)(56, DashboardComponent_td_56_Template, 2, 1, \"td\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(57, 15);\n          i0.ɵɵtemplate(58, DashboardComponent_th_58_Template, 2, 0, \"th\", 12)(59, DashboardComponent_td_59_Template, 3, 1, \"td\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(60, 16);\n          i0.ɵɵtemplate(61, DashboardComponent_th_61_Template, 2, 0, \"th\", 12)(62, DashboardComponent_td_62_Template, 3, 3, \"td\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(63, 17);\n          i0.ɵɵtemplate(64, DashboardComponent_th_64_Template, 2, 0, \"th\", 12)(65, DashboardComponent_td_65_Template, 2, 1, \"td\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(66, 18);\n          i0.ɵɵtemplate(67, DashboardComponent_th_67_Template, 2, 0, \"th\", 12)(68, DashboardComponent_td_68_Template, 2, 1, \"td\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(69, 19);\n          i0.ɵɵtemplate(70, DashboardComponent_th_70_Template, 2, 0, \"th\", 12)(71, DashboardComponent_td_71_Template, 4, 3, \"td\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(72, DashboardComponent_tr_72_Template, 1, 0, \"tr\", 20)(73, DashboardComponent_tr_73_Template, 1, 0, \"tr\", 21);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"mat-card\", 22)(75, \"mat-card-header\")(76, \"mat-card-title\");\n          i0.ɵɵtext(77, \"Top Security Issues\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"mat-card-content\")(79, \"div\", 23);\n          i0.ɵɵtemplate(80, DashboardComponent_div_80_Template, 14, 8, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"mat-card\", 25)(82, \"mat-card-header\")(83, \"mat-card-title\");\n          i0.ɵɵtext(84, \"Quick Actions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"mat-card-content\")(86, \"div\", 26)(87, \"button\", 27)(88, \"mat-icon\");\n          i0.ɵɵtext(89, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(90, \" Start New Scan \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"button\", 28)(92, \"mat-icon\");\n          i0.ɵɵtext(93, \"checklist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(94, \" Security Checklist \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"button\", 29)(96, \"mat-icon\");\n          i0.ɵɵtext(97, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(98, \" Generate Report \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"button\", 30)(100, \"mat-icon\");\n          i0.ɵɵtext(101, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(102, \" Settings \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.totalScans);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.stats.criticalIssues);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.stats.highIssues);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.stats.mediumIssues);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"dataSource\", ctx.recentScans);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topIssues);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, i5.MatIconButton, MatTableModule, i6.MatTable, i6.MatHeaderCellDef, i6.MatHeaderRowDef, i6.MatColumnDef, i6.MatCellDef, i6.MatRowDef, i6.MatHeaderCell, i6.MatCell, i6.MatHeaderRow, i6.MatRow, MatChipsModule, i7.MatChip, i7.MatChipListbox, MatProgressBarModule, RouterModule, i8.RouterLink],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  color: #333;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  margin-bottom: 10px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  margin-right: 10px;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: bold;\\n  color: #333;\\n}\\n\\n.stat-card.critical[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.stat-card.high[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n.stat-card.medium[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  color: #ffeb3b;\\n}\\n\\n.recent-scans-card[_ngcontent-%COMP%], \\n.top-issues-card[_ngcontent-%COMP%], \\n.quick-actions-card[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.recent-scans-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.scans-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n  color: white;\\n}\\n\\n.status-running[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n  color: white;\\n}\\n\\n.status-failed[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  color: white;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n}\\n\\n.issues-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.issue-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.issue-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 12px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 60px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatTableModule", "MatChipsModule", "MatProgressBarModule", "RouterModule", "SEVERITY_COLORS", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "scan_r1", "id", "ctx_r2", "getProjectName", "scan_r2", "project_path", "chain_r4", "ɵɵtemplate", "DashboardComponent_td_59_mat_chip_2_Template", "ɵɵproperty", "scan_r5", "chains", "ɵɵclassMap", "scan_r6", "status", "scan_r7", "issues", "length", "formatDate", "scan_r8", "created_at", "ɵɵpureFunction1", "_c0", "scan_r9", "ɵɵelement", "ɵɵstyleProp", "getSeverityColor", "issue_r10", "severity", "ɵɵtextInterpolate1", "title", "description", "ɵɵtextInterpolate2", "file", "line", "chain", "DashboardComponent", "constructor", "apiService", "stats", "totalScans", "criticalIssues", "highIssues", "mediumIssues", "recentScans", "topIssues", "displayedColumns", "ngOnInit", "loadDashboardData", "getScanHistory", "subscribe", "next", "response", "scans", "slice", "calculateStats", "error", "console", "loadMockData", "mockScan", "generateMockScanResult", "severity_counts", "critical", "high", "medium", "criticalTotal", "highTotal", "mediumTotal", "allIssues", "for<PERSON>ach", "scan", "concat", "sort", "a", "b", "getSeverityWeight", "weights", "low", "info", "path", "split", "pop", "dateString", "Date", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "ApiService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "DashboardComponent_th_52_Template", "DashboardComponent_td_53_Template", "DashboardComponent_th_55_Template", "DashboardComponent_td_56_Template", "DashboardComponent_th_58_Template", "DashboardComponent_td_59_Template", "DashboardComponent_th_61_Template", "DashboardComponent_td_62_Template", "DashboardComponent_th_64_Template", "DashboardComponent_td_65_Template", "DashboardComponent_th_67_Template", "DashboardComponent_td_68_Template", "DashboardComponent_th_70_Template", "DashboardComponent_td_71_Template", "DashboardComponent_tr_72_Template", "DashboardComponent_tr_73_Template", "DashboardComponent_div_80_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i4", "MatIcon", "i5", "MatButton", "MatIconButton", "i6", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i7", "MatChip", "MatChipListbox", "i8", "RouterLink", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { ApiService } from '../../services/api.service';\nimport { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatTableModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    RouterModule\n  ],\n  template: `\n    <div class=\"dashboard-container\">\n      <h1>🛡️ Security Dashboard</h1>\n      \n      <!-- Stats Cards -->\n      <div class=\"stats-grid\">\n        <mat-card class=\"stat-card\">\n          <mat-card-header>\n            <mat-icon>security</mat-icon>\n            <mat-card-title>Total Scans</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"stat-number\">{{ stats.totalScans }}</div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"stat-card critical\">\n          <mat-card-header>\n            <mat-icon>error</mat-icon>\n            <mat-card-title>Critical Issues</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"stat-number\">{{ stats.criticalIssues }}</div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"stat-card high\">\n          <mat-card-header>\n            <mat-icon>warning</mat-icon>\n            <mat-card-title>High Issues</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"stat-number\">{{ stats.highIssues }}</div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"stat-card medium\">\n          <mat-card-header>\n            <mat-icon>info</mat-icon>\n            <mat-card-title>Medium Issues</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"stat-number\">{{ stats.mediumIssues }}</div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Recent Scans -->\n      <mat-card class=\"recent-scans-card\">\n        <mat-card-header>\n          <mat-card-title>Recent Scans</mat-card-title>\n          <button mat-button routerLink=\"/scan\" color=\"primary\">\n            <mat-icon>add</mat-icon>\n            New Scan\n          </button>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"table-container\">\n            <table mat-table [dataSource]=\"recentScans\" class=\"scans-table\">\n              <ng-container matColumnDef=\"id\">\n                <th mat-header-cell *matHeaderCellDef>Scan ID</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ scan.id }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"project\">\n                <th mat-header-cell *matHeaderCellDef>Project</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ getProjectName(scan.project_path) }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"chains\">\n                <th mat-header-cell *matHeaderCellDef>Chains</th>\n                <td mat-cell *matCellDef=\"let scan\">\n                  <mat-chip-listbox>\n                    <mat-chip *ngFor=\"let chain of scan.chains\">{{ chain }}</mat-chip>\n                  </mat-chip-listbox>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"status\">\n                <th mat-header-cell *matHeaderCellDef>Status</th>\n                <td mat-cell *matCellDef=\"let scan\">\n                  <mat-chip [class]=\"'status-' + scan.status\">{{ scan.status }}</mat-chip>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"issues\">\n                <th mat-header-cell *matHeaderCellDef>Issues</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ scan.issues?.length || 0 }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"date\">\n                <th mat-header-cell *matHeaderCellDef>Date</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ formatDate(scan.created_at) }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let scan\">\n                  <button mat-icon-button [routerLink]=\"['/scan', scan.id]\">\n                    <mat-icon>visibility</mat-icon>\n                  </button>\n                </td>\n              </ng-container>\n\n              <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n            </table>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Top Issues -->\n      <mat-card class=\"top-issues-card\">\n        <mat-card-header>\n          <mat-card-title>Top Security Issues</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"issues-list\">\n            <div *ngFor=\"let issue of topIssues\" class=\"issue-item\">\n              <div class=\"issue-severity\">\n                <mat-chip [style.background-color]=\"getSeverityColor(issue.severity)\">\n                  {{ issue.severity }}\n                </mat-chip>\n              </div>\n              <div class=\"issue-details\">\n                <h4>{{ issue.title }}</h4>\n                <p>{{ issue.description }}</p>\n                <small>{{ issue.file }}:{{ issue.line }}</small>\n              </div>\n              <div class=\"issue-chain\">\n                <mat-chip>{{ issue.chain }}</mat-chip>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Quick Actions -->\n      <mat-card class=\"quick-actions-card\">\n        <mat-card-header>\n          <mat-card-title>Quick Actions</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"actions-grid\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/scan\">\n              <mat-icon>security</mat-icon>\n              Start New Scan\n            </button>\n            <button mat-raised-button color=\"accent\" routerLink=\"/checklist\">\n              <mat-icon>checklist</mat-icon>\n              Security Checklist\n            </button>\n            <button mat-raised-button routerLink=\"/reports\">\n              <mat-icon>assessment</mat-icon>\n              Generate Report\n            </button>\n            <button mat-raised-button routerLink=\"/settings\">\n              <mat-icon>settings</mat-icon>\n              Settings\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .dashboard-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    h1 {\n      margin-bottom: 30px;\n      color: #333;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n\n    .stat-card {\n      text-align: center;\n    }\n\n    .stat-card mat-card-header {\n      justify-content: center;\n      margin-bottom: 10px;\n    }\n\n    .stat-card mat-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n      margin-right: 10px;\n    }\n\n    .stat-number {\n      font-size: 48px;\n      font-weight: bold;\n      color: #333;\n    }\n\n    .stat-card.critical .stat-number {\n      color: #f44336;\n    }\n\n    .stat-card.high .stat-number {\n      color: #ff9800;\n    }\n\n    .stat-card.medium .stat-number {\n      color: #ffeb3b;\n    }\n\n    .recent-scans-card,\n    .top-issues-card,\n    .quick-actions-card {\n      margin-bottom: 30px;\n    }\n\n    .recent-scans-card mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .scans-table {\n      width: 100%;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-running {\n      background-color: #2196f3;\n      color: white;\n    }\n\n    .status-failed {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .status-pending {\n      background-color: #ff9800;\n      color: white;\n    }\n\n    .issues-list {\n      display: flex;\n      flex-direction: column;\n      gap: 15px;\n    }\n\n    .issue-item {\n      display: flex;\n      align-items: center;\n      gap: 15px;\n      padding: 15px;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .issue-details {\n      flex: 1;\n    }\n\n    .issue-details h4 {\n      margin: 0 0 5px 0;\n      color: #333;\n    }\n\n    .issue-details p {\n      margin: 0 0 5px 0;\n      color: #666;\n      font-size: 14px;\n    }\n\n    .issue-details small {\n      color: #999;\n      font-size: 12px;\n    }\n\n    .actions-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n    }\n\n    .actions-grid button {\n      height: 60px;\n      display: flex;\n      flex-direction: column;\n      gap: 5px;\n    }\n\n    .actions-grid mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n  `]\n})\nexport class DashboardComponent implements OnInit {\n  stats = {\n    totalScans: 0,\n    criticalIssues: 0,\n    highIssues: 0,\n    mediumIssues: 0\n  };\n\n  recentScans: ScanResult[] = [];\n  topIssues: SecurityIssue[] = [];\n  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n\n  constructor(private apiService: ApiService) {}\n\n  ngOnInit(): void {\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: (response) => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: (error) => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n\n  loadMockData(): void {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts.critical || 0,\n      highIssues: mockScan.severity_counts.high || 0,\n      mediumIssues: mockScan.severity_counts.medium || 0\n    };\n  }\n\n  calculateStats(scans: ScanResult[]): void {\n    this.stats.totalScans = scans.length;\n    \n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues: SecurityIssue[] = [];\n\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.critical || 0;\n      highTotal += scan.severity_counts?.high || 0;\n      mediumTotal += scan.severity_counts?.medium || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n\n    // Get top 5 most severe issues\n    this.topIssues = allIssues\n      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))\n      .slice(0, 5);\n  }\n\n  getSeverityWeight(severity: string): number {\n    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };\n    return weights[severity as keyof typeof weights] || 0;\n  }\n\n  getSeverityColor(severity: string): string {\n    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';\n  }\n\n  getProjectName(path: string): string {\n    return path.split('/').pop() || path;\n  }\n\n  formatDate(dateString: string): string {\n    return new Date(dateString).toLocaleDateString();\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAAoCC,eAAe,QAAQ,8BAA8B;;;;;;;;;;;;;IA2EzEC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,EAAA,CAAa;;;;;IAIjDP,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAA5CH,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAC,cAAA,CAAAC,OAAA,CAAAC,YAAA,EAAuC;;;;;IAI3EX,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAAtBH,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAO,QAAA,CAAW;;;;;IADzDZ,EADF,CAAAC,cAAA,aAAoC,uBAChB;IAChBD,EAAA,CAAAa,UAAA,IAAAC,4CAAA,uBAA4C;IAEhDd,EADE,CAAAG,YAAA,EAAmB,EAChB;;;;IAF2BH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAe,UAAA,YAAAC,OAAA,CAAAC,MAAA,CAAc;;;;;IAM9CjB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAoC,eACU;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC/DF,EAD+D,CAAAG,YAAA,EAAW,EACrE;;;;IADOH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAkB,UAAA,aAAAC,OAAA,CAAAC,MAAA,CAAiC;IAACpB,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAc,OAAA,CAAAC,MAAA,CAAiB;;;;;IAK/DpB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACjDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAnCH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,EAAAgB,OAAA,CAAAC,MAAA,kBAAAD,OAAA,CAAAC,MAAA,CAAAC,MAAA,OAA8B;;;;;IAIlEvB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC/CH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAgB,UAAA,CAAAC,OAAA,CAAAC,UAAA,EAAiC;;;;;IAIrE1B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG9CH,EAFJ,CAAAC,cAAA,aAAoC,iBACwB,eAC9C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAExBF,EAFwB,CAAAG,YAAA,EAAW,EACxB,EACN;;;;IAHqBH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAA2B,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAtB,EAAA,EAAiC;;;;;IAM7DP,EAAA,CAAA8B,SAAA,aAA4D;;;;;IAC5D9B,EAAA,CAAA8B,SAAA,aAAkE;;;;;IAehE9B,EAFJ,CAAAC,cAAA,cAAwD,cAC1B,eAC4C;IACpED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9BH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAC1CF,EAD0C,CAAAG,YAAA,EAAQ,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACb;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAE/BF,EAF+B,CAAAG,YAAA,EAAW,EAClC,EACF;;;;;IAZQH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAA+B,WAAA,qBAAAvB,MAAA,CAAAwB,gBAAA,CAAAC,SAAA,CAAAC,QAAA,EAA2D;IACnElC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAmC,kBAAA,MAAAF,SAAA,CAAAC,QAAA,MACF;IAGIlC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA4B,SAAA,CAAAG,KAAA,CAAiB;IAClBpC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAA4B,SAAA,CAAAI,WAAA,CAAuB;IACnBrC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAsC,kBAAA,KAAAL,SAAA,CAAAM,IAAA,OAAAN,SAAA,CAAAO,IAAA,KAAiC;IAG9BxC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA4B,SAAA,CAAAQ,KAAA,CAAiB;;;AAuL3C,OAAM,MAAOC,kBAAkB;EAY7BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAX9B,KAAAC,KAAK,GAAG;MACNC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE;KACf;IAED,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,SAAS,GAAoB,EAAE;IAC/B,KAAAC,gBAAgB,GAAa,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;EAElD;EAE7CC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACV,UAAU,CAACW,cAAc,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACR,WAAW,GAAGQ,QAAQ,CAACC,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAACC,cAAc,CAACH,QAAQ,CAACC,KAAK,CAAC;MACrC,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACA,IAAI,CAACE,YAAY,EAAE;MACrB;KACD,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACrB,UAAU,CAACsB,sBAAsB,EAAE;IACzD,IAAI,CAAChB,WAAW,GAAG,CAACe,QAAQ,CAAC;IAC7B,IAAI,CAACd,SAAS,GAAGc,QAAQ,CAAC3C,MAAM;IAChC,IAAI,CAACuB,KAAK,GAAG;MACXC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAEkB,QAAQ,CAACE,eAAe,CAACC,QAAQ,IAAI,CAAC;MACtDpB,UAAU,EAAEiB,QAAQ,CAACE,eAAe,CAACE,IAAI,IAAI,CAAC;MAC9CpB,YAAY,EAAEgB,QAAQ,CAACE,eAAe,CAACG,MAAM,IAAI;KAClD;EACH;EAEAT,cAAcA,CAACF,KAAmB;IAChC,IAAI,CAACd,KAAK,CAACC,UAAU,GAAGa,KAAK,CAACpC,MAAM;IAEpC,IAAIgD,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,SAAS,GAAoB,EAAE;IAEnCf,KAAK,CAACgB,OAAO,CAACC,IAAI,IAAG;MACnBL,aAAa,IAAIK,IAAI,CAACT,eAAe,EAAEC,QAAQ,IAAI,CAAC;MACpDI,SAAS,IAAII,IAAI,CAACT,eAAe,EAAEE,IAAI,IAAI,CAAC;MAC5CI,WAAW,IAAIG,IAAI,CAACT,eAAe,EAAEG,MAAM,IAAI,CAAC;MAChDI,SAAS,GAAGA,SAAS,CAACG,MAAM,CAACD,IAAI,CAACtD,MAAM,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC;IAEF,IAAI,CAACuB,KAAK,CAACE,cAAc,GAAGwB,aAAa;IACzC,IAAI,CAAC1B,KAAK,CAACG,UAAU,GAAGwB,SAAS;IACjC,IAAI,CAAC3B,KAAK,CAACI,YAAY,GAAGwB,WAAW;IAErC;IACA,IAAI,CAACtB,SAAS,GAAGuB,SAAS,CACvBI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAAC9C,QAAQ,CAAC,GAAG,IAAI,CAAC+C,iBAAiB,CAACF,CAAC,CAAC7C,QAAQ,CAAC,CAAC,CACvF0B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEAqB,iBAAiBA,CAAC/C,QAAgB;IAChC,MAAMgD,OAAO,GAAG;MAAEd,QAAQ,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEa,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAC,CAAE;IACpE,OAAOF,OAAO,CAAChD,QAAgC,CAAC,IAAI,CAAC;EACvD;EAEAF,gBAAgBA,CAACE,QAAgB;IAC/B,OAAOnC,eAAe,CAACmC,QAAwC,CAAC,IAAI,MAAM;EAC5E;EAEAzB,cAAcA,CAAC4E,IAAY;IACzB,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIF,IAAI;EACtC;EAEA7D,UAAUA,CAACgE,UAAkB;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,EAAE;EAClD;;;uCAtFWhD,kBAAkB,EAAA1C,EAAA,CAAA2F,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAlBnD,kBAAkB;MAAAoD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvTzBpG,EADF,CAAAC,cAAA,aAAiC,SAC3B;UAAAD,EAAA,CAAAE,MAAA,4CAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAMzBH,EAHN,CAAAC,cAAA,aAAwB,kBACM,sBACT,eACL;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,qBAAgB;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAC7BF,EAD6B,CAAAG,YAAA,EAAiB,EAC5B;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,cACS;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACpC,EACV;UAIPH,EAFJ,CAAAC,cAAA,mBAAqC,uBAClB,gBACL;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACjCF,EADiC,CAAAG,YAAA,EAAiB,EAChC;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,cACS;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAEvDF,EAFuD,CAAAG,YAAA,EAAM,EACxC,EACV;UAIPH,EAFJ,CAAAC,cAAA,mBAAiC,uBACd,gBACL;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAC7BF,EAD6B,CAAAG,YAAA,EAAiB,EAC5B;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,cACS;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACpC,EACV;UAIPH,EAFJ,CAAAC,cAAA,mBAAmC,uBAChB,gBACL;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAC/BF,EAD+B,CAAAG,YAAA,EAAiB,EAC9B;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,cACS;UAAAD,EAAA,CAAAE,MAAA,IAAwB;UAGvDF,EAHuD,CAAAG,YAAA,EAAM,EACtC,EACV,EACP;UAKFH,EAFJ,CAAAC,cAAA,mBAAoC,uBACjB,sBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAE3CH,EADF,CAAAC,cAAA,iBAAsD,gBAC1C;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,kBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACO;UAGdH,EAFJ,CAAAC,cAAA,wBAAkB,cACa,iBACqC;UAC9DD,EAAA,CAAAsG,uBAAA,QAAgC;UAE9BtG,EADA,CAAAa,UAAA,KAAA0F,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtCxG,EAAA,CAAAsG,uBAAA,QAAqC;UAEnCtG,EADA,CAAAa,UAAA,KAAA4F,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtC1G,EAAA,CAAAsG,uBAAA,QAAoC;UAElCtG,EADA,CAAAa,UAAA,KAAA8F,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAOtC5G,EAAA,CAAAsG,uBAAA,QAAoC;UAElCtG,EADA,CAAAa,UAAA,KAAAgG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAKtC9G,EAAA,CAAAsG,uBAAA,QAAoC;UAElCtG,EADA,CAAAa,UAAA,KAAAkG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtChH,EAAA,CAAAsG,uBAAA,QAAkC;UAEhCtG,EADA,CAAAa,UAAA,KAAAoG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtClH,EAAA,CAAAsG,uBAAA,QAAqC;UAEnCtG,EADA,CAAAa,UAAA,KAAAsG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAQtCpH,EADA,CAAAa,UAAA,KAAAwG,iCAAA,iBAAuD,KAAAC,iCAAA,iBACM;UAIrEtH,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,oBAAkC,uBACf,sBACC;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACS;UACvBD,EAAA,CAAAa,UAAA,KAAA0G,kCAAA,mBAAwD;UAiB9DvH,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,oBAAqC,uBAClB,sBACC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAC/BF,EAD+B,CAAAG,YAAA,EAAiB,EAC9B;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACU,kBACqC,gBACjD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,kBAAiE,gBACrD;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,4BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,kBAAgD,gBACpC;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,yBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,kBAAiD,iBACrC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,mBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;UAvJ2BH,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAC,UAAA,CAAsB;UAUtB9C,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAE,cAAA,CAA0B;UAU1B/C,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAG,UAAA,CAAsB;UAUtBhD,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAI,YAAA,CAAwB;UAgBhCjD,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAe,UAAA,eAAAsF,GAAA,CAAAnD,WAAA,CAA0B;UA8CrBlD,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAe,UAAA,oBAAAsF,GAAA,CAAAjD,gBAAA,CAAiC;UACpBpD,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAe,UAAA,qBAAAsF,GAAA,CAAAjD,gBAAA,CAA0B;UAatCpD,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAe,UAAA,YAAAsF,GAAA,CAAAlD,SAAA,CAAY;;;qBA/H3C5D,YAAY,EAAAiI,EAAA,CAAAC,OAAA,EACZjI,aAAa,EAAAkI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbrI,aAAa,EAAAsI,EAAA,CAAAC,OAAA,EACbtI,eAAe,EAAAuI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfxI,cAAc,EAAAyI,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACdlJ,cAAc,EAAAmJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EACdpJ,oBAAoB,EACpBC,YAAY,EAAAoJ,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}