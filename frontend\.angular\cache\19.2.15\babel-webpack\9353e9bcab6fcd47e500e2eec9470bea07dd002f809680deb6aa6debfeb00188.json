{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { DocumentationNavComponent } from '../documentation-nav/documentation-nav.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/sidenav\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nexport let DocumentationLayoutComponent = /*#__PURE__*/(() => {\n  class DocumentationLayoutComponent {\n    static {\n      this.ɵfac = function DocumentationLayoutComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DocumentationLayoutComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DocumentationLayoutComponent,\n        selectors: [[\"app-documentation-layout\"]],\n        decls: 37,\n        vars: 0,\n        consts: [[1, \"spt-documentation-container\"], [1, \"spt-doc-header\"], [1, \"spt-header-content\"], [1, \"spt-brand\"], [1, \"spt-brand-icon\"], [1, \"spt-brand-text\"], [1, \"spt-brand-title\"], [1, \"spt-brand-subtitle\"], [1, \"spt-header-nav\"], [1, \"spt-nav-actions\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", 1, \"spt-nav-btn\"], [\"mat-button\", \"\", \"routerLink\", \"/scan\", 1, \"spt-nav-btn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard\", 1, \"spt-back-btn\"], [1, \"spt-doc-main\"], [1, \"spt-sidenav-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"spt-sidebar\"], [1, \"spt-sidebar-content\"], [1, \"spt-content\"], [1, \"spt-content-wrapper\"]],\n        template: function DocumentationLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\");\n            i0.ɵɵtext(6, \"shield\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"h1\", 6);\n            i0.ɵɵtext(9, \"SPT Documentation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"p\", 7);\n            i0.ɵɵtext(11, \"Security Protocol Tool\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"nav\", 8)(13, \"div\", 9)(14, \"button\", 10)(15, \"mat-icon\");\n            i0.ɵɵtext(16, \"dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"span\");\n            i0.ɵɵtext(18, \"Dashboard\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"button\", 11)(20, \"mat-icon\");\n            i0.ɵɵtext(21, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"span\");\n            i0.ɵɵtext(23, \"Scan\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"button\", 12)(25, \"mat-icon\");\n            i0.ɵɵtext(26, \"arrow_back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"span\");\n            i0.ɵɵtext(28, \"Back to App\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(29, \"div\", 13)(30, \"mat-sidenav-container\", 14)(31, \"mat-sidenav\", 15)(32, \"div\", 16);\n            i0.ɵɵelement(33, \"app-documentation-nav\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"mat-sidenav-content\", 17)(35, \"main\", 18);\n            i0.ɵɵelement(36, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n          }\n        },\n        dependencies: [CommonModule, RouterModule, i1.RouterOutlet, i1.RouterLink, MatToolbarModule, MatSidenavModule, i2.MatSidenav, i2.MatSidenavContainer, i2.MatSidenavContent, MatIconModule, i3.MatIcon, MatButtonModule, i4.MatButton, MatTooltipModule, DocumentationNavComponent],\n        styles: [\".spt-documentation-container[_ngcontent-%COMP%]{height:100vh;display:flex;flex-direction:column;background:var(--spt-gray-50);font-family:Inter,sans-serif}.spt-doc-header[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid var(--spt-gray-200);box-shadow:var(--spt-shadow-sm);position:sticky;top:0;z-index:var(--spt-z-sticky)}.spt-header-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:0 var(--spt-space-8);min-height:72px;max-width:1400px;margin:0 auto}.spt-brand[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-4)}.spt-brand-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-primary-600) 0%,var(--spt-secondary-600) 100%);border-radius:var(--spt-radius-xl);padding:var(--spt-space-3);box-shadow:var(--spt-shadow-md);display:flex;align-items:center;justify-content:center}.spt-brand-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff;font-size:28px;width:28px;height:28px}.spt-brand-text[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-1)}.spt-brand-title[_ngcontent-%COMP%]{font-size:var(--spt-text-2xl);font-weight:var(--spt-font-bold);color:var(--spt-gray-900);margin:0;letter-spacing:-.025em}.spt-brand-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-gray-600);margin:0;font-weight:var(--spt-font-medium)}.spt-header-nav[_ngcontent-%COMP%]{display:flex;align-items:center}.spt-nav-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2)}.spt-nav-btn[_ngcontent-%COMP%]{color:var(--spt-gray-600)!important;font-weight:var(--spt-font-medium)!important;border-radius:var(--spt-radius-lg)!important;padding:var(--spt-space-2) var(--spt-space-3)!important;transition:all .2s ease!important}.spt-nav-btn[_ngcontent-%COMP%]:hover{background:var(--spt-gray-100)!important;color:var(--spt-gray-900)!important}.spt-nav-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:var(--spt-space-2);font-size:18px;width:18px;height:18px}.spt-back-btn[_ngcontent-%COMP%]{background:var(--spt-primary-600)!important;color:#fff!important;font-weight:var(--spt-font-semibold)!important;border-radius:var(--spt-radius-lg)!important;padding:var(--spt-space-3) var(--spt-space-5)!important;box-shadow:var(--spt-shadow-sm)!important;transition:all .2s ease!important}.spt-back-btn[_ngcontent-%COMP%]:hover{background:var(--spt-primary-700)!important;box-shadow:var(--spt-shadow-md)!important;transform:translateY(-1px)}.spt-back-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:var(--spt-space-2);font-size:18px;width:18px;height:18px}.spt-doc-main[_ngcontent-%COMP%]{flex:1;display:flex;overflow:hidden}.spt-sidenav-container[_ngcontent-%COMP%]{flex:1;background:transparent}.spt-sidebar[_ngcontent-%COMP%]{width:320px;background:#fff;border-right:1px solid var(--spt-gray-200);box-shadow:none}.spt-sidebar-content[_ngcontent-%COMP%]{height:100%;overflow-y:auto;padding:var(--spt-space-6) 0}.spt-content[_ngcontent-%COMP%]{background:transparent;overflow-y:auto}.spt-content-wrapper[_ngcontent-%COMP%]{padding:var(--spt-space-8);max-width:1200px;margin:0 auto;min-height:calc(100vh - 72px)}mat-icon[_ngcontent-%COMP%]{display:inline-flex!important;align-items:center!important;justify-content:center!important;vertical-align:middle!important;line-height:1!important}@media (max-width: 1200px){.spt-header-content[_ngcontent-%COMP%]{padding:0 var(--spt-space-6)}.spt-content-wrapper[_ngcontent-%COMP%]{padding:var(--spt-space-6)}}@media (max-width: 1024px){.spt-sidebar[_ngcontent-%COMP%]{width:280px}.spt-header-content[_ngcontent-%COMP%]{padding:0 var(--spt-space-4)}.spt-content-wrapper[_ngcontent-%COMP%]{padding:var(--spt-space-5)}.spt-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}.spt-nav-btn[_ngcontent-%COMP%]{min-width:44px;padding:var(--spt-space-2)!important}}@media (max-width: 768px){.spt-header-content[_ngcontent-%COMP%]{min-height:64px;padding:0 var(--spt-space-4)}.spt-brand-title[_ngcontent-%COMP%]{font-size:var(--spt-text-xl)}.spt-brand-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-xs)}.spt-sidebar[_ngcontent-%COMP%]{width:260px}.spt-content-wrapper[_ngcontent-%COMP%]{padding:var(--spt-space-4)}.spt-nav-actions[_ngcontent-%COMP%]{gap:var(--spt-space-1)}.spt-back-btn[_ngcontent-%COMP%]{padding:var(--spt-space-2) var(--spt-space-3)!important;font-size:var(--spt-text-sm)!important}}@media (max-width: 640px){.spt-brand[_ngcontent-%COMP%]{gap:var(--spt-space-3)}.spt-brand-icon[_ngcontent-%COMP%]{padding:var(--spt-space-2)}.spt-brand-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.spt-nav-btn[_ngcontent-%COMP%], .spt-back-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}.spt-back-btn[_ngcontent-%COMP%]{min-width:44px;padding:var(--spt-space-2)!important;border-radius:50%!important}.spt-back-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin:0}.spt-sidebar[_ngcontent-%COMP%]{width:240px}}@media (max-width: 480px){.spt-header-content[_ngcontent-%COMP%]{padding:0 var(--spt-space-3)}.spt-content-wrapper[_ngcontent-%COMP%]{padding:var(--spt-space-3)}.spt-brand-text[_ngcontent-%COMP%]{display:none}.spt-sidebar[_ngcontent-%COMP%]{width:220px}}\"]\n      });\n    }\n  }\n  return DocumentationLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}