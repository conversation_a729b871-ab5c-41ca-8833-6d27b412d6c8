{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/list\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nconst _c0 = a0 => [\"/doc\", a0];\nfunction DocumentationNavComponent_mat_list_item_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-list-item\", 16)(1, \"div\", 17)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r1.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.description);\n  }\n}\nexport class DocumentationNavComponent {\n  constructor() {\n    this.navItems = [{\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    }, {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    }, {\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    }, {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    }, {\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    }, {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    }, {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }];\n  }\n  static {\n    this.ɵfac = function DocumentationNavComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DocumentationNavComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DocumentationNavComponent,\n      selectors: [[\"app-documentation-nav\"]],\n      decls: 29,\n      vars: 1,\n      consts: [[1, \"nav-container\"], [1, \"nav-header\"], [1, \"nav-header-icon\"], [1, \"nav-header-text\"], [1, \"nav-subtitle\"], [1, \"nav-list\"], [\"routerLinkActive\", \"active-nav-item\", \"class\", \"nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-divider\"], [1, \"nav-footer\"], [1, \"version-info\"], [1, \"version-icon\"], [1, \"version-text\"], [1, \"version-number\"], [1, \"version-status\"], [1, \"quick-links\"], [\"href\", \"https://github.com/blockchain-spt\", \"target\", \"_blank\", \"mat-button\", \"\", 1, \"github-link\"], [\"routerLinkActive\", \"active-nav-item\", 1, \"nav-item\", 3, \"routerLink\"], [\"matListItemIcon\", \"\", 1, \"nav-item-icon\"], [\"matListItemTitle\", \"\", 1, \"nav-item-title\"], [\"matListItemLine\", \"\", 1, \"nav-description\"]],\n      template: function DocumentationNavComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"menu_book\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"h3\");\n          i0.ɵɵtext(7, \"Documentation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 4);\n          i0.ɵɵtext(9, \"Comprehensive guide to SPT\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"mat-nav-list\", 5);\n          i0.ɵɵtemplate(11, DocumentationNavComponent_mat_list_item_11_Template, 8, 6, \"mat-list-item\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"div\", 7);\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"div\", 10)(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"verified\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 11)(19, \"span\", 12);\n          i0.ɵɵtext(20, \"SPT v1.0.0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\", 13);\n          i0.ɵɵtext(22, \"Stable Release\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"a\", 15)(25, \"mat-icon\");\n          i0.ɵɵtext(26, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\");\n          i0.ɵɵtext(28, \"GitHub\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.navItems);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, MatListModule, i3.MatNavList, i3.MatListItem, i3.MatListItemIcon, i3.MatListItemLine, i3.MatListItemTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatAnchor, MatDividerModule],\n      styles: [\".nav-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 32px 0;\\n}\\n\\n.nav-header[_ngcontent-%COMP%] {\\n  padding: 0 32px 32px;\\n  margin-bottom: 24px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  position: relative;\\n}\\n\\n.nav-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 32px;\\n  right: 32px;\\n  height: 1px;\\n  background: #e2e8f0;\\n}\\n\\n.nav-header-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  padding: 10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n\\n.nav-header-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.nav-header-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #1a202c;\\n  font-weight: 700;\\n  font-size: 1.25em;\\n  letter-spacing: -0.5px;\\n}\\n\\n.nav-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 0.875em;\\n  font-weight: 500;\\n}\\n\\n.nav-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0 24px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  overflow: hidden;\\n  background: #ffffff;\\n  border: 1px solid #f1f5f9;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n  border-color: #e2e8f0;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%] {\\n  background: #f0f9ff;\\n  border-color: #0ea5e9;\\n  border-left: 4px solid #0ea5e9;\\n  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.15);\\n}\\n\\n.nav-item-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n  border-radius: 10px;\\n  padding: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 16px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-item-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\\n}\\n\\n.nav-item-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.nav-item-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.95em;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-item-title[_ngcontent-%COMP%] {\\n  color: #4c63d2;\\n}\\n\\n.nav-description[_ngcontent-%COMP%] {\\n  font-size: 0.8em;\\n  color: #8892b0;\\n  margin-top: 2px;\\n  line-height: 1.3;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-description[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.nav-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent 0%, #e8eaff 20%, #d1d9ff 50%, #e8eaff 80%, transparent 100%);\\n  margin: 20px 24px;\\n}\\n\\n.nav-footer[_ngcontent-%COMP%] {\\n  padding: 20px 24px 0;\\n}\\n\\n.version-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding: 12px;\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);\\n  border-radius: 12px;\\n  border: 1px solid #e8eaff;\\n}\\n\\n.version-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n  border-radius: 8px;\\n  padding: 6px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.version-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.version-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n\\n.version-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4c63d2;\\n  font-size: 0.9em;\\n}\\n\\n.version-status[_ngcontent-%COMP%] {\\n  font-size: 0.75em;\\n  color: #8892b0;\\n  font-weight: 500;\\n}\\n\\n.quick-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.github-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #24292e 0%, #1a1e22 100%);\\n  color: white;\\n  border-radius: 10px;\\n  padding: 8px 16px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  border: none;\\n  width: 100%;\\n  justify-content: flex-start;\\n}\\n\\n.github-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #2f363d 0%, #24292e 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(36, 41, 46, 0.3);\\n}\\n\\n.github-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  margin-right: 8px;\\n}\\n\\n.github-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.9em;\\n}\\n\\n@media (max-width: 768px) {\\n  .nav-container[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .nav-header[_ngcontent-%COMP%] {\\n    padding: 0 16px 16px;\\n    margin-bottom: 16px;\\n    gap: 12px;\\n  }\\n  .nav-header[_ngcontent-%COMP%]::after {\\n    left: 16px;\\n    right: 16px;\\n  }\\n  .nav-header-icon[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .nav-header-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    width: 20px;\\n    height: 20px;\\n  }\\n  .nav-list[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n  }\\n  .nav-divider[_ngcontent-%COMP%] {\\n    margin: 16px 16px;\\n  }\\n  .nav-footer[_ngcontent-%COMP%] {\\n    padding: 16px 16px 0;\\n  }\\n  .version-info[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9kb2N1bWVudGF0aW9uL2NvbXBvbmVudHMvZG9jdW1lbnRhdGlvbi1uYXYvZG9jdW1lbnRhdGlvbi1uYXYuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSTtFQUNFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0Esa0JBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLCtDQUFBO0FBQU47O0FBR0k7RUFDRSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esc0JBQUE7QUFBTjs7QUFHSTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsT0FBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7QUFBTjs7QUFLSTtFQUNFLG1CQUFBO0VBQ0EscUJBQUE7RUFDQSx5Q0FBQTtFQUNBLDJCQUFBO0FBRk47O0FBS0k7RUFDRSxtQkFBQTtFQUNBLHFCQUFBO0VBQ0EsOEJBQUE7RUFDQSw4Q0FBQTtBQUZOOztBQUtJO0VBQ0UsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBRk47O0FBS0k7RUFDRSw2REFBQTtFQUNBLDhDQUFBO0FBRk47O0FBS0k7RUFDRSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBRk47O0FBS0k7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtBQUZOOztBQUtJO0VBQ0UsY0FBQTtBQUZOOztBQUtJO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBRk47O0FBS0k7RUFDRSxjQUFBO0FBRk47O0FBS0k7RUFDRSxXQUFBO0VBQ0EsMkdBQUE7RUFDQSxpQkFBQTtBQUZOOztBQUtJO0VBQ0Usb0JBQUE7QUFGTjs7QUFLSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSw2REFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7QUFGTjs7QUFLSTtFQUNFLDZEQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFGTjs7QUFLSTtFQUNFLFlBQUE7RUFDQSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFGTjs7QUFLSTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFFBQUE7QUFGTjs7QUFLSTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBRk47O0FBS0k7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQUZOOztBQUtJO0VBQ0UsYUFBQTtFQUNBLFFBQUE7QUFGTjs7QUFLSTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7RUFDQSwyQkFBQTtBQUZOOztBQUtJO0VBQ0UsNkRBQUE7RUFDQSwyQkFBQTtFQUNBLDRDQUFBO0FBRk47O0FBS0k7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtBQUZOOztBQUtJO0VBQ0UsZ0JBQUE7QUFGTjs7QUFLSTtFQUNFO0lBQ0UsZUFBQTtFQUZOO0VBS0k7SUFDRSxvQkFBQTtJQUNBLG1CQUFBO0lBQ0EsU0FBQTtFQUhOO0VBTUk7SUFDRSxVQUFBO0lBQ0EsV0FBQTtFQUpOO0VBT0k7SUFDRSxZQUFBO0VBTE47RUFRSTtJQUNFLGVBQUE7SUFDQSxXQUFBO0lBQ0EsWUFBQTtFQU5OO0VBU0k7SUFDRSxjQUFBO0VBUE47RUFVSTtJQUNFLGlCQUFBO0VBUk47RUFXSTtJQUNFLG9CQUFBO0VBVE47RUFZSTtJQUNFLGFBQUE7RUFWTjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm5hdi1jb250YWluZXIge1xuICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBwYWRkaW5nOiAzMnB4IDA7XG4gICAgfVxuXG4gICAgLm5hdi1oZWFkZXIge1xuICAgICAgcGFkZGluZzogMCAzMnB4IDMycHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDE2cHg7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgfVxuXG4gICAgLm5hdi1oZWFkZXI6OmFmdGVyIHtcbiAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgYm90dG9tOiAwO1xuICAgICAgbGVmdDogMzJweDtcbiAgICAgIHJpZ2h0OiAzMnB4O1xuICAgICAgaGVpZ2h0OiAxcHg7XG4gICAgICBiYWNrZ3JvdW5kOiAjZTJlOGYwO1xuICAgIH1cblxuICAgIC5uYXYtaGVhZGVyLWljb24ge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICBwYWRkaW5nOiAxMHB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpO1xuICAgIH1cblxuICAgIC5uYXYtaGVhZGVyLWljb24gbWF0LWljb24ge1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgZm9udC1zaXplOiAyNHB4O1xuICAgICAgd2lkdGg6IDI0cHg7XG4gICAgICBoZWlnaHQ6IDI0cHg7XG4gICAgfVxuXG4gICAgLm5hdi1oZWFkZXItdGV4dCBoMyB7XG4gICAgICBtYXJnaW46IDAgMCA0cHggMDtcbiAgICAgIGNvbG9yOiAjMWEyMDJjO1xuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgIGZvbnQtc2l6ZTogMS4yNWVtO1xuICAgICAgbGV0dGVyLXNwYWNpbmc6IC0wLjVweDtcbiAgICB9XG5cbiAgICAubmF2LXN1YnRpdGxlIHtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGNvbG9yOiAjNjQ3NDhiO1xuICAgICAgZm9udC1zaXplOiAwLjg3NWVtO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICB9XG5cbiAgICAubmF2LWxpc3Qge1xuICAgICAgZmxleDogMTtcbiAgICAgIHBhZGRpbmc6IDAgMjRweDtcbiAgICB9XG5cbiAgICAubmF2LWl0ZW0ge1xuICAgICAgbWFyZ2luOiA4cHggMDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZjFmNWY5O1xuICAgIH1cblxuXG5cbiAgICAubmF2LWl0ZW06aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZmFmYztcbiAgICAgIGJvcmRlci1jb2xvcjogI2UyZThmMDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgIH1cblxuICAgIC5uYXYtaXRlbS5hY3RpdmUtbmF2LWl0ZW0ge1xuICAgICAgYmFja2dyb3VuZDogI2YwZjlmZjtcbiAgICAgIGJvcmRlci1jb2xvcjogIzBlYTVlOTtcbiAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzBlYTVlOTtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDE0LCAxNjUsIDIzMywgMC4xNSk7XG4gICAgfVxuXG4gICAgLm5hdi1pdGVtLWljb24ge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiAwJSwgI2Y1NTc2YyAxMDAlKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gICAgICBwYWRkaW5nOiA4cHg7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgbWFyZ2luLXJpZ2h0OiAxNnB4O1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgei1pbmRleDogMTtcbiAgICB9XG5cbiAgICAubmF2LWl0ZW0uYWN0aXZlLW5hdi1pdGVtIC5uYXYtaXRlbS1pY29uIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XG4gICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpO1xuICAgIH1cblxuICAgIC5uYXYtaXRlbS1pY29uIG1hdC1pY29uIHtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICAgIHdpZHRoOiAyMHB4O1xuICAgICAgaGVpZ2h0OiAyMHB4O1xuICAgIH1cblxuICAgIC5uYXYtaXRlbS10aXRsZSB7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6ICMyZDM3NDg7XG4gICAgICBmb250LXNpemU6IDAuOTVlbTtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgIHotaW5kZXg6IDE7XG4gICAgfVxuXG4gICAgLm5hdi1pdGVtLmFjdGl2ZS1uYXYtaXRlbSAubmF2LWl0ZW0tdGl0bGUge1xuICAgICAgY29sb3I6ICM0YzYzZDI7XG4gICAgfVxuXG4gICAgLm5hdi1kZXNjcmlwdGlvbiB7XG4gICAgICBmb250LXNpemU6IDAuOGVtO1xuICAgICAgY29sb3I6ICM4ODkyYjA7XG4gICAgICBtYXJnaW4tdG9wOiAycHg7XG4gICAgICBsaW5lLWhlaWdodDogMS4zO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgei1pbmRleDogMTtcbiAgICB9XG5cbiAgICAubmF2LWl0ZW0uYWN0aXZlLW5hdi1pdGVtIC5uYXYtZGVzY3JpcHRpb24ge1xuICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgfVxuXG4gICAgLm5hdi1kaXZpZGVyIHtcbiAgICAgIGhlaWdodDogMXB4O1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCAwJSwgI2U4ZWFmZiAyMCUsICNkMWQ5ZmYgNTAlLCAjZThlYWZmIDgwJSwgdHJhbnNwYXJlbnQgMTAwJSk7XG4gICAgICBtYXJnaW46IDIwcHggMjRweDtcbiAgICB9XG5cbiAgICAubmF2LWZvb3RlciB7XG4gICAgICBwYWRkaW5nOiAyMHB4IDI0cHggMDtcbiAgICB9XG5cbiAgICAudmVyc2lvbi1pbmZvIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxMnB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICAgIHBhZGRpbmc6IDEycHg7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZmIDAlLCAjZThlYWZmIDEwMCUpO1xuICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlOGVhZmY7XG4gICAgfVxuXG4gICAgLnZlcnNpb24taWNvbiB7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNGZhY2ZlIDAlLCAjMDBmMmZlIDEwMCUpO1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgcGFkZGluZzogNnB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICB9XG5cbiAgICAudmVyc2lvbi1pY29uIG1hdC1pY29uIHtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgIHdpZHRoOiAxNnB4O1xuICAgICAgaGVpZ2h0OiAxNnB4O1xuICAgIH1cblxuICAgIC52ZXJzaW9uLXRleHQge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBnYXA6IDJweDtcbiAgICB9XG5cbiAgICAudmVyc2lvbi1udW1iZXIge1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGNvbG9yOiAjNGM2M2QyO1xuICAgICAgZm9udC1zaXplOiAwLjllbTtcbiAgICB9XG5cbiAgICAudmVyc2lvbi1zdGF0dXMge1xuICAgICAgZm9udC1zaXplOiAwLjc1ZW07XG4gICAgICBjb2xvcjogIzg4OTJiMDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuXG4gICAgLnF1aWNrLWxpbmtzIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDhweDtcbiAgICB9XG5cbiAgICAuZ2l0aHViLWxpbmsge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzI0MjkyZSAwJSwgIzFhMWUyMiAxMDAlKTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gICAgICBwYWRkaW5nOiA4cHggMTZweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG4gICAgfVxuXG4gICAgLmdpdGh1Yi1saW5rOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyZjM2M2QgMCUsICMyNDI5MmUgMTAwJSk7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMzYsIDQxLCA0NiwgMC4zKTtcbiAgICB9XG5cbiAgICAuZ2l0aHViLWxpbmsgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgd2lkdGg6IDE2cHg7XG4gICAgICBoZWlnaHQ6IDE2cHg7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDhweDtcbiAgICB9XG5cbiAgICAuZ2l0aHViLWxpbmsgc3BhbiB7XG4gICAgICBmb250LXNpemU6IDAuOWVtO1xuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgLm5hdi1jb250YWluZXIge1xuICAgICAgICBwYWRkaW5nOiAxNnB4IDA7XG4gICAgICB9XG5cbiAgICAgIC5uYXYtaGVhZGVyIHtcbiAgICAgICAgcGFkZGluZzogMCAxNnB4IDE2cHg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gICAgICAgIGdhcDogMTJweDtcbiAgICAgIH1cblxuICAgICAgLm5hdi1oZWFkZXI6OmFmdGVyIHtcbiAgICAgICAgbGVmdDogMTZweDtcbiAgICAgICAgcmlnaHQ6IDE2cHg7XG4gICAgICB9XG5cbiAgICAgIC5uYXYtaGVhZGVyLWljb24ge1xuICAgICAgICBwYWRkaW5nOiA4cHg7XG4gICAgICB9XG5cbiAgICAgIC5uYXYtaGVhZGVyLWljb24gbWF0LWljb24ge1xuICAgICAgICBmb250LXNpemU6IDIwcHg7XG4gICAgICAgIHdpZHRoOiAyMHB4O1xuICAgICAgICBoZWlnaHQ6IDIwcHg7XG4gICAgICB9XG5cbiAgICAgIC5uYXYtbGlzdCB7XG4gICAgICAgIHBhZGRpbmc6IDAgOHB4O1xuICAgICAgfVxuXG4gICAgICAubmF2LWRpdmlkZXIge1xuICAgICAgICBtYXJnaW46IDE2cHggMTZweDtcbiAgICAgIH1cblxuICAgICAgLm5hdi1mb290ZXIge1xuICAgICAgICBwYWRkaW5nOiAxNnB4IDE2cHggMDtcbiAgICAgIH1cblxuICAgICAgLnZlcnNpb24taW5mbyB7XG4gICAgICAgIHBhZGRpbmc6IDEwcHg7XG4gICAgICB9XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatListModule", "MatIconModule", "MatButtonModule", "MatDividerModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "item_r1", "route", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "label", "description", "DocumentationNavComponent", "constructor", "navItems", "selectors", "decls", "vars", "consts", "template", "DocumentationNavComponent_Template", "rf", "ctx", "ɵɵtemplate", "DocumentationNavComponent_mat_list_item_11_Template", "ɵɵelement", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "RouterLink", "RouterLinkActive", "i3", "MatNavList", "MatListItem", "MatListItemIcon", "MatListItemLine", "MatListItemTitle", "i4", "MatIcon", "i5", "<PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\documentation-nav\\documentation-nav.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\n\ninterface NavItem {\n  label: string;\n  route: string;\n  icon: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-documentation-nav',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatDividerModule\n  ],\n  template: `\n    <div class=\"nav-container\">\n      <div class=\"nav-header\">\n        <div class=\"nav-header-icon\">\n          <mat-icon>menu_book</mat-icon>\n        </div>\n        <div class=\"nav-header-text\">\n          <h3>Documentation</h3>\n          <p class=\"nav-subtitle\">Comprehensive guide to SPT</p>\n        </div>\n      </div>\n\n      <mat-nav-list class=\"nav-list\">\n        <mat-list-item\n          *ngFor=\"let item of navItems\"\n          [routerLink]=\"['/doc', item.route]\"\n          routerLinkActive=\"active-nav-item\"\n          class=\"nav-item\">\n          <div class=\"nav-item-icon\" matListItemIcon>\n            <mat-icon>{{ item.icon }}</mat-icon>\n          </div>\n          <div matListItemTitle class=\"nav-item-title\">{{ item.label }}</div>\n          <div matListItemLine class=\"nav-description\">{{ item.description }}</div>\n        </mat-list-item>\n      </mat-nav-list>\n\n      <div class=\"nav-divider\"></div>\n\n      <div class=\"nav-footer\">\n        <div class=\"version-info\">\n          <div class=\"version-icon\">\n            <mat-icon>verified</mat-icon>\n          </div>\n          <div class=\"version-text\">\n            <span class=\"version-number\">SPT v1.0.0</span>\n            <span class=\"version-status\">Stable Release</span>\n          </div>\n        </div>\n        <div class=\"quick-links\">\n          <a href=\"https://github.com/blockchain-spt\" target=\"_blank\" mat-button class=\"github-link\">\n            <mat-icon>code</mat-icon>\n            <span>GitHub</span>\n          </a>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .nav-container {\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      padding: 32px 0;\n    }\n\n    .nav-header {\n      padding: 0 32px 32px;\n      margin-bottom: 24px;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      position: relative;\n    }\n\n    .nav-header::after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 32px;\n      right: 32px;\n      height: 1px;\n      background: #e2e8f0;\n    }\n\n    .nav-header-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 12px;\n      padding: 10px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .nav-header-icon mat-icon {\n      color: white;\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .nav-header-text h3 {\n      margin: 0 0 4px 0;\n      color: #1a202c;\n      font-weight: 700;\n      font-size: 1.25em;\n      letter-spacing: -0.5px;\n    }\n\n    .nav-subtitle {\n      margin: 0;\n      color: #64748b;\n      font-size: 0.875em;\n      font-weight: 500;\n    }\n\n    .nav-list {\n      flex: 1;\n      padding: 0 24px;\n    }\n\n    .nav-item {\n      margin: 8px 0;\n      border-radius: 12px;\n      transition: all 0.2s ease;\n      position: relative;\n      overflow: hidden;\n      background: #ffffff;\n      border: 1px solid #f1f5f9;\n    }\n\n\n\n    .nav-item:hover {\n      background: #f8fafc;\n      border-color: #e2e8f0;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n      transform: translateY(-1px);\n    }\n\n    .nav-item.active-nav-item {\n      background: #f0f9ff;\n      border-color: #0ea5e9;\n      border-left: 4px solid #0ea5e9;\n      box-shadow: 0 2px 8px rgba(14, 165, 233, 0.15);\n    }\n\n    .nav-item-icon {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n      border-radius: 10px;\n      padding: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 16px;\n      position: relative;\n      z-index: 1;\n    }\n\n    .nav-item.active-nav-item .nav-item-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n    }\n\n    .nav-item-icon mat-icon {\n      color: white;\n      font-size: 20px;\n      width: 20px;\n      height: 20px;\n    }\n\n    .nav-item-title {\n      font-weight: 600;\n      color: #2d3748;\n      font-size: 0.95em;\n      position: relative;\n      z-index: 1;\n    }\n\n    .nav-item.active-nav-item .nav-item-title {\n      color: #4c63d2;\n    }\n\n    .nav-description {\n      font-size: 0.8em;\n      color: #8892b0;\n      margin-top: 2px;\n      line-height: 1.3;\n      position: relative;\n      z-index: 1;\n    }\n\n    .nav-item.active-nav-item .nav-description {\n      color: #667eea;\n    }\n\n    .nav-divider {\n      height: 1px;\n      background: linear-gradient(90deg, transparent 0%, #e8eaff 20%, #d1d9ff 50%, #e8eaff 80%, transparent 100%);\n      margin: 20px 24px;\n    }\n\n    .nav-footer {\n      padding: 20px 24px 0;\n    }\n\n    .version-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      margin-bottom: 16px;\n      padding: 12px;\n      background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);\n      border-radius: 12px;\n      border: 1px solid #e8eaff;\n    }\n\n    .version-icon {\n      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n      border-radius: 8px;\n      padding: 6px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .version-icon mat-icon {\n      color: white;\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    .version-text {\n      display: flex;\n      flex-direction: column;\n      gap: 2px;\n    }\n\n    .version-number {\n      font-weight: 600;\n      color: #4c63d2;\n      font-size: 0.9em;\n    }\n\n    .version-status {\n      font-size: 0.75em;\n      color: #8892b0;\n      font-weight: 500;\n    }\n\n    .quick-links {\n      display: flex;\n      gap: 8px;\n    }\n\n    .github-link {\n      background: linear-gradient(135deg, #24292e 0%, #1a1e22 100%);\n      color: white;\n      border-radius: 10px;\n      padding: 8px 16px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n      border: none;\n      width: 100%;\n      justify-content: flex-start;\n    }\n\n    .github-link:hover {\n      background: linear-gradient(135deg, #2f363d 0%, #24292e 100%);\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(36, 41, 46, 0.3);\n    }\n\n    .github-link mat-icon {\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n      margin-right: 8px;\n    }\n\n    .github-link span {\n      font-size: 0.9em;\n    }\n\n    @media (max-width: 768px) {\n      .nav-container {\n        padding: 16px 0;\n      }\n\n      .nav-header {\n        padding: 0 16px 16px;\n        margin-bottom: 16px;\n        gap: 12px;\n      }\n\n      .nav-header::after {\n        left: 16px;\n        right: 16px;\n      }\n\n      .nav-header-icon {\n        padding: 8px;\n      }\n\n      .nav-header-icon mat-icon {\n        font-size: 20px;\n        width: 20px;\n        height: 20px;\n      }\n\n      .nav-list {\n        padding: 0 8px;\n      }\n\n      .nav-divider {\n        margin: 16px 16px;\n      }\n\n      .nav-footer {\n        padding: 16px 16px 0;\n      }\n\n      .version-info {\n        padding: 10px;\n      }\n    }\n  `]\n})\nexport class DocumentationNavComponent {\n  navItems: NavItem[] = [\n    {\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    },\n    {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    },\n    {\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    },\n    {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    },\n    {\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    },\n    {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    },\n    {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;IAuChDC,EANJ,CAAAC,cAAA,wBAImB,cAC0B,eAC/B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IACNH,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnEH,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACrEF,EADqE,CAAAG,YAAA,EAAM,EAC3D;;;;IARdH,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,KAAA,EAAmC;IAIvBR,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAEkBX,EAAA,CAAAS,SAAA,GAAgB;IAAhBT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;IAChBZ,EAAA,CAAAS,SAAA,GAAsB;IAAtBT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAM,WAAA,CAAsB;;;AAyS7E,OAAM,MAAOC,yBAAyB;EA1UtCC,YAAA;IA2UE,KAAAC,QAAQ,GAAc,CACpB;MACEJ,KAAK,EAAE,UAAU;MACjBJ,KAAK,EAAE,UAAU;MACjBG,IAAI,EAAE,MAAM;MACZE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBJ,KAAK,EAAE,iBAAiB;MACxBG,IAAI,EAAE,YAAY;MAClBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,eAAe;MACtBJ,KAAK,EAAE,eAAe;MACtBG,IAAI,EAAE,KAAK;MACXE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,oBAAoB;MAC3BJ,KAAK,EAAE,oBAAoB;MAC3BG,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,WAAW;MAClBJ,KAAK,EAAE,WAAW;MAClBG,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,mBAAmB;MAC1BJ,KAAK,EAAE,kBAAkB;MACzBG,IAAI,EAAE,WAAW;MACjBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,cAAc;MACrBJ,KAAK,EAAE,cAAc;MACrBG,IAAI,EAAE,cAAc;MACpBE,WAAW,EAAE;KACd,CACF;;;;uCA5CUC,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3T5BvB,EAHN,CAAAC,cAAA,aAA2B,aACD,aACO,eACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EAC1B;UAEJH,EADF,CAAAC,cAAA,aAA6B,SACvB;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,WAAwB;UAAAD,EAAA,CAAAE,MAAA,iCAA0B;UAEtDF,EAFsD,CAAAG,YAAA,EAAI,EAClD,EACF;UAENH,EAAA,CAAAC,cAAA,uBAA+B;UAC7BD,EAAA,CAAAyB,UAAA,KAAAC,mDAAA,2BAImB;UAOrB1B,EAAA,CAAAG,YAAA,EAAe;UAEfH,EAAA,CAAA2B,SAAA,cAA+B;UAKzB3B,EAHN,CAAAC,cAAA,cAAwB,cACI,eACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;UAEJH,EADF,CAAAC,cAAA,eAA0B,gBACK;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAyB,aACoE,gBAC/E;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAIpBF,EAJoB,CAAAG,YAAA,EAAO,EACjB,EACA,EACF,EACF;;;UA/BiBH,EAAA,CAAAS,SAAA,IAAW;UAAXT,EAAA,CAAAI,UAAA,YAAAoB,GAAA,CAAAR,QAAA,CAAW;;;qBArBlCtB,YAAY,EAAAkC,EAAA,CAAAC,OAAA,EACZlC,YAAY,EAAAmC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA,EACZpC,aAAa,EAAAqC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,gBAAA,EACbzC,aAAa,EAAA0C,EAAA,CAAAC,OAAA,EACb1C,eAAe,EAAA2C,EAAA,CAAAC,SAAA,EACf3C,gBAAgB;MAAA4C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}