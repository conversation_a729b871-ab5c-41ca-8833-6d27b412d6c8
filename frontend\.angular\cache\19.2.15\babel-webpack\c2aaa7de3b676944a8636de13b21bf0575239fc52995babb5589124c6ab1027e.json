{"ast": null, "code": "/*!\n * Chart.js v4.5.0\n * https://www.chartjs.org\n * (c) 2025 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n    * An empty function that can be used, for example, for optional callback.\n    */\nfunction noop() {\n  /* noop */}\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nconst uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isNullOrUndef(value) {\n  return value === null || value === undefined;\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value) {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nfunction clone(source) {\n  if (isArray(source)) {\n    return source.map(clone);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current;\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n/**\n * @private\n */\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n/**\n * @private\n */\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n/**\n * @private\n */\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * @param e - The event\n * @private\n */\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n/**\n * Verifies that attempting to coerce n to string or number won't throw a TypeError.\n */\nfunction isNonPrimitive(n) {\n  return typeof n === 'symbol' || typeof n === 'object' && n !== null && !(Symbol.toPrimitive in n || 'toString' in n || 'valueOf' in n);\n}\nfunction isNumber(n) {\n  return !isNonPrimitive(n) && !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => {\n  const ti = table[index][key];\n  return ti < value || ti === value && table[index + 1][key] === value;\n} : index => table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\n/**\n * @param items\n */\nfunction _arrayUnique(items) {\n  const set = new Set(items);\n  if (set.size === items.length) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nfunction throttled(fn, thisArg) {\n  let argsToUse = [];\n  let ticking = false;\n  return function (...args) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */\nfunction debounce(fn, delay) {\n  let timeout;\n  return function (...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {\n      iScale,\n      vScale,\n      _parsed\n    } = meta;\n    const spanGaps = meta.dataset ? meta.dataset.options ? meta.dataset.options.spanGaps : null : null;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n    if (minDefined) {\n      start = Math.min(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, axis, min).lo,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo);\n      if (spanGaps) {\n        const distanceToDefinedLo = _parsed.slice(0, start + 1).reverse().findIndex(point => !isNullOrUndef(point[vScale.axis]));\n        start -= Math.max(0, distanceToDefinedLo);\n      }\n      start = _limitValue(start, 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      let end = Math.max(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1);\n      if (spanGaps) {\n        const distanceToDefinedHi = _parsed.slice(end - 1).findIndex(point => !isNullOrUndef(point[vScale.axis]));\n        end += Math.max(0, distanceToDefinedHi);\n      }\n      count = _limitValue(end, start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {\n    start,\n    count\n  };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\nconst numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\nfunction applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined\n  });\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: name => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n  });\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    }\n  });\n  defaults.describe('animations', {\n    _fallback: 'animation'\n  });\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0\n        }\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0\n        }\n      }\n    }\n  });\n}\nfunction applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst formatters = {\n  values(value) {\n    return isArray(value) ? value : '' + value;\n  },\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue;\n    if (ticks.length > 1) {\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n      delta = calculateDelta(tickValue, ticks);\n    }\n    const logDelta = log10(Math.abs(delta));\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n    const options = {\n      notation,\n      minimumFractionDigits: numDecimal,\n      maximumFractionDigits: numDecimal\n    };\n    Object.assign(options, this.options.ticks.format);\n    return formatNumber(tickValue, locale, options);\n  },\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n};\nfunction calculateDelta(tickValue, ticks) {\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\nvar Ticks = {\n  formatters\n};\nfunction applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n    bounds: 'ticks',\n    clip: true,\n    grace: 0,\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false\n    },\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n    title: {\n      display: false,\n      text: '',\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2\n    }\n  });\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: name => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: name => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n  });\n  defaults.describe('scales', {\n    _fallback: 'scale'\n  });\n  defaults.describe('scale.ticks', {\n    _scriptable: name => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: name => name !== 'backdropPadding'\n  });\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n  apply(appliers) {\n    appliers.forEach(apply => apply(this));\n  }\n}\nvar defaults = /* #__PURE__ */new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */\nfunction clearCanvas(canvas, ctx) {\n  if (!ctx && !canvas) {\n    return;\n  }\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      // NOTE: the rounded rect implementation changed to use `arc` instead of\n      // `quadraticCurveTo` since it generates better results when rect is\n      // almost a circle. 0.516 (instead of 0.5) produces results with visually\n      // closer proportion to the previous impl and it is inscribed in the\n      // circle with `radius`. For more details, see the following PRs:\n      // https://github.com/chartjs/Chart.js/issues/5597\n      // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\n/**\n * @private\n */\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n    * Now that IE11 support has been dropped, we can use more\n    * of the TextMetrics object. The actual bounding boxes\n    * are unflagged in Chrome, Firefox, Edge, and Safari so they\n    * can be safely used.\n    * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n    */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction drawBackdrop(ctx, opts) {\n  const oldColor = ctx.fillStyle;\n  ctx.fillStyle = opts.color;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += Number(font.lineHeight);\n  }\n  ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n    * Converts the given line height `value` in pixels for a specific font `size`.\n    * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n    * @param size - The font size (in pixels) used to resolve relative `value`.\n    * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n    * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n    * @since 2.7.0\n    */\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nfunction _createResolver(scopes, prefixes = [''], rootScopes, fallback, getTarget = () => scopes[0]) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  });\n}\n/**\n * @private\n */\nfunction _descriptors(proxy, defaults = {\n  scriptable: true,\n  indexable: true\n}) {\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop]; // resolve from proxy\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  // Props to Rob Spencer at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n  // This function must also respect \"skipped\" points\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n/**\n * @private\n */\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\n/**\n * @private\n */\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\nfunction getRelativePosition(event, chart) {\n  if ('native' in event) {\n    return event;\n  }\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n  return {\n    width,\n    height\n  };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n  const canvas = chart.canvas;\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n/**\n * @private\n */\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\n/**\n * @private\n */\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({\n  start,\n  end,\n  count,\n  loop,\n  style\n}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function (key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\nfunction getSizeForArea(scale, chartArea, field) {\n  return scale.options.clip ? scale[field] : chartArea[field];\n}\nfunction getDatasetArea(meta, chartArea) {\n  const {\n    xScale,\n    yScale\n  } = meta;\n  if (xScale && yScale) {\n    return {\n      left: getSizeForArea(xScale, chartArea, 'left'),\n      right: getSizeForArea(xScale, chartArea, 'right'),\n      top: getSizeForArea(yScale, chartArea, 'top'),\n      bottom: getSizeForArea(yScale, chartArea, 'bottom')\n    };\n  }\n  return chartArea;\n}\nfunction getDatasetClipArea(chart, meta) {\n  const clip = meta._clip;\n  if (clip.disabled) {\n    return false;\n  }\n  const area = getDatasetArea(meta, chart.chartArea);\n  return {\n    left: clip.left === false ? 0 : area.left - (clip.left === true ? 0 : clip.left),\n    right: clip.right === false ? chart.width : area.right + (clip.right === true ? 0 : clip.right),\n    top: clip.top === false ? 0 : area.top - (clip.top === true ? 0 : clip.top),\n    bottom: clip.bottom === false ? chart.height : area.bottom + (clip.bottom === true ? 0 : clip.bottom)\n  };\n}\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, getStyle as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, distanceBetweenPoints as aF, noop as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, Ticks as aM, log10 as aN, _longestText as aO, _filterBetween as aP, _lookup as aQ, isPatternOrGradient as aR, getHoverColor as aS, clone as aT, _merger as aU, _mergerIf as aV, _deprecated as aW, _splitKey as aX, toFontString as aY, splineCurve as aZ, splineCurveMonotone as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, getDatasetClipArea as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _normalizeAngle as al, _readValueToProps as am, _updateBezierControlPoints as an, _computeSegments as ao, _boundSegments as ap, _steppedInterpolation as aq, _bezierInterpolation as ar, _pointInLine as as, _steppedLineTo as at, _bezierCurveTo as au, drawPoint as av, addRoundedRectPath as aw, toTRBL as ax, toTRBLCorners as ay, _boundSegment as az, isArray as b, fontString as b0, toLineHeight as b1, PITAU as b2, INFINITY as b3, RAD_PER_DEG as b4, QUARTER_PI as b5, TWO_THIRDS_PI as b6, _angleDiff as b7, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };", "map": {"version": 3, "names": ["Color", "noop", "uid", "id", "isNullOrUndef", "value", "undefined", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "console", "warn", "keyResolvers", "v", "x", "o", "y", "_splitKey", "parts", "split", "tmp", "part", "push", "_getKeyResolver", "obj", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNonPrimitive", "n", "Symbol", "toPrimitive", "isNumber", "isNaN", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "argsToUse", "ticking", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "vScale", "_parsed", "spanGaps", "dataset", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "distanceToDefinedLo", "findIndex", "point", "distanceToDefinedHi", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "duration", "easing", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "numeric", "tickValue", "ticks", "chart", "notation", "delta", "maxTick", "calculateDelta", "log<PERSON><PERSON><PERSON>", "numDecimal", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "Ticks", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "clip", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "descriptors", "getScope$1", "node", "root", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "appliers", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "area", "margin", "clipArea", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "parseInt", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "getValue", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer", "getSizeForArea", "chartArea", "field", "getDatasetArea", "getDatasetClipArea", "_clip", "disabled", "$", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "_", "a$", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "aA", "aB", "aC", "aD", "aE", "aF", "aG", "aH", "aI", "aJ", "aK", "aL", "aM", "aN", "aO", "aP", "aQ", "aR", "aS", "aT", "aU", "aV", "aW", "aX", "aY", "aZ", "a_", "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am", "an", "ao", "ap", "aq", "ar", "as", "at", "au", "av", "aw", "ax", "ay", "az", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "f", "g", "q", "u", "z"], "sources": ["D:/TGI/Blockchain.SPT/frontend/node_modules/chart.js/dist/chunks/helpers.dataset.js"], "sourcesContent": ["/*!\n * Chart.js v4.5.0\n * https://www.chartjs.org\n * (c) 2025 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n * An empty function that can be used, for example, for optional callback.\n */ function noop() {\n/* noop */ }\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */ const uid = (()=>{\n    let id = 0;\n    return ()=>id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */ function isNullOrUndef(value) {\n    return value === null || value === undefined;\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */ function isArray(value) {\n    if (Array.isArray && Array.isArray(value)) {\n        return true;\n    }\n    const type = Object.prototype.toString.call(value);\n    if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n        return true;\n    }\n    return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */ function isObject(value) {\n    return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */ function isNumberFinite(value) {\n    return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */ function finiteOrDefault(value, defaultValue) {\n    return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */ function valueOrDefault(value, defaultValue) {\n    return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension)=>typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension)=>typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */ function callback(fn, args, thisArg) {\n    if (fn && typeof fn.call === 'function') {\n        return fn.apply(thisArg, args);\n    }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n    let i, len, keys;\n    if (isArray(loopable)) {\n        len = loopable.length;\n        if (reverse) {\n            for(i = len - 1; i >= 0; i--){\n                fn.call(thisArg, loopable[i], i);\n            }\n        } else {\n            for(i = 0; i < len; i++){\n                fn.call(thisArg, loopable[i], i);\n            }\n        }\n    } else if (isObject(loopable)) {\n        keys = Object.keys(loopable);\n        len = keys.length;\n        for(i = 0; i < len; i++){\n            fn.call(thisArg, loopable[keys[i]], keys[i]);\n        }\n    }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */ function _elementsEqual(a0, a1) {\n    let i, ilen, v0, v1;\n    if (!a0 || !a1 || a0.length !== a1.length) {\n        return false;\n    }\n    for(i = 0, ilen = a0.length; i < ilen; ++i){\n        v0 = a0[i];\n        v1 = a1[i];\n        if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */ function clone(source) {\n    if (isArray(source)) {\n        return source.map(clone);\n    }\n    if (isObject(source)) {\n        const target = Object.create(null);\n        const keys = Object.keys(source);\n        const klen = keys.length;\n        let k = 0;\n        for(; k < klen; ++k){\n            target[keys[k]] = clone(source[keys[k]]);\n        }\n        return target;\n    }\n    return source;\n}\nfunction isValidKey(key) {\n    return [\n        '__proto__',\n        'prototype',\n        'constructor'\n    ].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */ function _merger(key, target, source, options) {\n    if (!isValidKey(key)) {\n        return;\n    }\n    const tval = target[key];\n    const sval = source[key];\n    if (isObject(tval) && isObject(sval)) {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        merge(tval, sval, options);\n    } else {\n        target[key] = clone(sval);\n    }\n}\nfunction merge(target, source, options) {\n    const sources = isArray(source) ? source : [\n        source\n    ];\n    const ilen = sources.length;\n    if (!isObject(target)) {\n        return target;\n    }\n    options = options || {};\n    const merger = options.merger || _merger;\n    let current;\n    for(let i = 0; i < ilen; ++i){\n        current = sources[i];\n        if (!isObject(current)) {\n            continue;\n        }\n        const keys = Object.keys(current);\n        for(let k = 0, klen = keys.length; k < klen; ++k){\n            merger(keys[k], target, current, options);\n        }\n    }\n    return target;\n}\nfunction mergeIf(target, source) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return merge(target, source, {\n        merger: _mergerIf\n    });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */ function _mergerIf(key, target, source) {\n    if (!isValidKey(key)) {\n        return;\n    }\n    const tval = target[key];\n    const sval = source[key];\n    if (isObject(tval) && isObject(sval)) {\n        mergeIf(tval, sval);\n    } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n        target[key] = clone(sval);\n    }\n}\n/**\n * @private\n */ function _deprecated(scope, value, previous, current) {\n    if (value !== undefined) {\n        console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n    }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n    // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n    '': (v)=>v,\n    // default resolvers\n    x: (o)=>o.x,\n    y: (o)=>o.y\n};\n/**\n * @private\n */ function _splitKey(key) {\n    const parts = key.split('.');\n    const keys = [];\n    let tmp = '';\n    for (const part of parts){\n        tmp += part;\n        if (tmp.endsWith('\\\\')) {\n            tmp = tmp.slice(0, -1) + '.';\n        } else {\n            keys.push(tmp);\n            tmp = '';\n        }\n    }\n    return keys;\n}\nfunction _getKeyResolver(key) {\n    const keys = _splitKey(key);\n    return (obj)=>{\n        for (const k of keys){\n            if (k === '') {\n                break;\n            }\n            obj = obj && obj[k];\n        }\n        return obj;\n    };\n}\nfunction resolveObjectKey(obj, key) {\n    const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n    return resolver(obj);\n}\n/**\n * @private\n */ function _capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = (value)=>typeof value !== 'undefined';\nconst isFunction = (value)=>typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b)=>{\n    if (a.size !== b.size) {\n        return false;\n    }\n    for (const item of a){\n        if (!b.has(item)) {\n            return false;\n        }\n    }\n    return true;\n};\n/**\n * @param e - The event\n * @private\n */ function _isClickEvent(e) {\n    return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */ const PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n    return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */ function niceNum(range) {\n    const roundedRange = Math.round(range);\n    range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n    const niceRange = Math.pow(10, Math.floor(log10(range)));\n    const fraction = range / niceRange;\n    const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n    return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */ function _factorize(value) {\n    const result = [];\n    const sqrt = Math.sqrt(value);\n    let i;\n    for(i = 1; i < sqrt; i++){\n        if (value % i === 0) {\n            result.push(i);\n            result.push(value / i);\n        }\n    }\n    if (sqrt === (sqrt | 0)) {\n        result.push(sqrt);\n    }\n    result.sort((a, b)=>a - b).pop();\n    return result;\n}\n/**\n * Verifies that attempting to coerce n to string or number won't throw a TypeError.\n */ function isNonPrimitive(n) {\n    return typeof n === 'symbol' || typeof n === 'object' && n !== null && !(Symbol.toPrimitive in n || 'toString' in n || 'valueOf' in n);\n}\nfunction isNumber(n) {\n    return !isNonPrimitive(n) && !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n    const rounded = Math.round(x);\n    return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */ function _setMinAndMaxByKey(array, target, property) {\n    let i, ilen, value;\n    for(i = 0, ilen = array.length; i < ilen; i++){\n        value = array[i][property];\n        if (!isNaN(value)) {\n            target.min = Math.min(target.min, value);\n            target.max = Math.max(target.max, value);\n        }\n    }\n}\nfunction toRadians(degrees) {\n    return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n    return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */ function _decimalPlaces(x) {\n    if (!isNumberFinite(x)) {\n        return;\n    }\n    let e = 1;\n    let p = 0;\n    while(Math.round(x * e) / e !== x){\n        e *= 10;\n        p++;\n    }\n    return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n    const distanceFromXCenter = anglePoint.x - centrePoint.x;\n    const distanceFromYCenter = anglePoint.y - centrePoint.y;\n    const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n    let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n    if (angle < -0.5 * PI) {\n        angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n    }\n    return {\n        angle,\n        distance: radialDistanceFromCenter\n    };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n    return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */ function _angleDiff(a, b) {\n    return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */ function _normalizeAngle(a) {\n    return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */ function _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n    const a = _normalizeAngle(angle);\n    const s = _normalizeAngle(start);\n    const e = _normalizeAngle(end);\n    const angleToStart = _normalizeAngle(s - a);\n    const angleToEnd = _normalizeAngle(e - a);\n    const startToAngle = _normalizeAngle(a - s);\n    const endToAngle = _normalizeAngle(a - e);\n    return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */ function _limitValue(value, min, max) {\n    return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */ function _int16Range(value) {\n    return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */ function _isBetween(value, start, end, epsilon = 1e-6) {\n    return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n\nfunction _lookup(table, value, cmp) {\n    cmp = cmp || ((index)=>table[index] < value);\n    let hi = table.length - 1;\n    let lo = 0;\n    let mid;\n    while(hi - lo > 1){\n        mid = lo + hi >> 1;\n        if (cmp(mid)) {\n            lo = mid;\n        } else {\n            hi = mid;\n        }\n    }\n    return {\n        lo,\n        hi\n    };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */ const _lookupByKey = (table, key, value, last)=>_lookup(table, value, last ? (index)=>{\n        const ti = table[index][key];\n        return ti < value || ti === value && table[index + 1][key] === value;\n    } : (index)=>table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */ const _rlookupByKey = (table, key, value)=>_lookup(table, value, (index)=>table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */ function _filterBetween(values, min, max) {\n    let start = 0;\n    let end = values.length;\n    while(start < end && values[start] < min){\n        start++;\n    }\n    while(end > start && values[end - 1] > max){\n        end--;\n    }\n    return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = [\n    'push',\n    'pop',\n    'shift',\n    'splice',\n    'unshift'\n];\nfunction listenArrayEvents(array, listener) {\n    if (array._chartjs) {\n        array._chartjs.listeners.push(listener);\n        return;\n    }\n    Object.defineProperty(array, '_chartjs', {\n        configurable: true,\n        enumerable: false,\n        value: {\n            listeners: [\n                listener\n            ]\n        }\n    });\n    arrayEvents.forEach((key)=>{\n        const method = '_onData' + _capitalize(key);\n        const base = array[key];\n        Object.defineProperty(array, key, {\n            configurable: true,\n            enumerable: false,\n            value (...args) {\n                const res = base.apply(this, args);\n                array._chartjs.listeners.forEach((object)=>{\n                    if (typeof object[method] === 'function') {\n                        object[method](...args);\n                    }\n                });\n                return res;\n            }\n        });\n    });\n}\nfunction unlistenArrayEvents(array, listener) {\n    const stub = array._chartjs;\n    if (!stub) {\n        return;\n    }\n    const listeners = stub.listeners;\n    const index = listeners.indexOf(listener);\n    if (index !== -1) {\n        listeners.splice(index, 1);\n    }\n    if (listeners.length > 0) {\n        return;\n    }\n    arrayEvents.forEach((key)=>{\n        delete array[key];\n    });\n    delete array._chartjs;\n}\n/**\n * @param items\n */ function _arrayUnique(items) {\n    const set = new Set(items);\n    if (set.size === items.length) {\n        return items;\n    }\n    return Array.from(set);\n}\n\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n    return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/ const requestAnimFrame = function() {\n    if (typeof window === 'undefined') {\n        return function(callback) {\n            return callback();\n        };\n    }\n    return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */ function throttled(fn, thisArg) {\n    let argsToUse = [];\n    let ticking = false;\n    return function(...args) {\n        // Save the args for use later\n        argsToUse = args;\n        if (!ticking) {\n            ticking = true;\n            requestAnimFrame.call(window, ()=>{\n                ticking = false;\n                fn.apply(thisArg, argsToUse);\n            });\n        }\n    };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */ function debounce(fn, delay) {\n    let timeout;\n    return function(...args) {\n        if (delay) {\n            clearTimeout(timeout);\n            timeout = setTimeout(fn, delay, args);\n        } else {\n            fn.apply(this, args);\n        }\n        return delay;\n    };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */ const _toLeftRightCenter = (align)=>align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */ const _alignStartEnd = (align, start, end)=>align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */ const _textX = (align, left, right, rtl)=>{\n    const check = rtl ? 'left' : 'right';\n    return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */ function _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n    const pointCount = points.length;\n    let start = 0;\n    let count = pointCount;\n    if (meta._sorted) {\n        const { iScale , vScale , _parsed  } = meta;\n        const spanGaps = meta.dataset ? meta.dataset.options ? meta.dataset.options.spanGaps : null : null;\n        const axis = iScale.axis;\n        const { min , max , minDefined , maxDefined  } = iScale.getUserBounds();\n        if (minDefined) {\n            start = Math.min(// @ts-expect-error Need to type _parsed\n            _lookupByKey(_parsed, axis, min).lo, // @ts-expect-error Need to fix types on _lookupByKey\n            animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo);\n            if (spanGaps) {\n                const distanceToDefinedLo = _parsed.slice(0, start + 1).reverse().findIndex((point)=>!isNullOrUndef(point[vScale.axis]));\n                start -= Math.max(0, distanceToDefinedLo);\n            }\n            start = _limitValue(start, 0, pointCount - 1);\n        }\n        if (maxDefined) {\n            let end = Math.max(// @ts-expect-error Need to type _parsed\n            _lookupByKey(_parsed, iScale.axis, max, true).hi + 1, // @ts-expect-error Need to fix types on _lookupByKey\n            animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1);\n            if (spanGaps) {\n                const distanceToDefinedHi = _parsed.slice(end - 1).findIndex((point)=>!isNullOrUndef(point[vScale.axis]));\n                end += Math.max(0, distanceToDefinedHi);\n            }\n            count = _limitValue(end, start, pointCount) - start;\n        } else {\n            count = pointCount - start;\n        }\n    }\n    return {\n        start,\n        count\n    };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */ function _scaleRangesChanged(meta) {\n    const { xScale , yScale , _scaleRanges  } = meta;\n    const newRanges = {\n        xmin: xScale.min,\n        xmax: xScale.max,\n        ymin: yScale.min,\n        ymax: yScale.max\n    };\n    if (!_scaleRanges) {\n        meta._scaleRanges = newRanges;\n        return true;\n    }\n    const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n    Object.assign(_scaleRanges, newRanges);\n    return changed;\n}\n\nconst atEdge = (t)=>t === 0 || t === 1;\nconst elasticIn = (t, s, p)=>-(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p)=>Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */ const effects = {\n    linear: (t)=>t,\n    easeInQuad: (t)=>t * t,\n    easeOutQuad: (t)=>-t * (t - 2),\n    easeInOutQuad: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n    easeInCubic: (t)=>t * t * t,\n    easeOutCubic: (t)=>(t -= 1) * t * t + 1,\n    easeInOutCubic: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n    easeInQuart: (t)=>t * t * t * t,\n    easeOutQuart: (t)=>-((t -= 1) * t * t * t - 1),\n    easeInOutQuart: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n    easeInQuint: (t)=>t * t * t * t * t,\n    easeOutQuint: (t)=>(t -= 1) * t * t * t * t + 1,\n    easeInOutQuint: (t)=>(t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n    easeInSine: (t)=>-Math.cos(t * HALF_PI) + 1,\n    easeOutSine: (t)=>Math.sin(t * HALF_PI),\n    easeInOutSine: (t)=>-0.5 * (Math.cos(PI * t) - 1),\n    easeInExpo: (t)=>t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n    easeOutExpo: (t)=>t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n    easeInOutExpo: (t)=>atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n    easeInCirc: (t)=>t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n    easeOutCirc: (t)=>Math.sqrt(1 - (t -= 1) * t),\n    easeInOutCirc: (t)=>(t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n    easeInElastic: (t)=>atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n    easeOutElastic: (t)=>atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n    easeInOutElastic (t) {\n        const s = 0.1125;\n        const p = 0.45;\n        return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n    },\n    easeInBack (t) {\n        const s = 1.70158;\n        return t * t * ((s + 1) * t - s);\n    },\n    easeOutBack (t) {\n        const s = 1.70158;\n        return (t -= 1) * t * ((s + 1) * t + s) + 1;\n    },\n    easeInOutBack (t) {\n        let s = 1.70158;\n        if ((t /= 0.5) < 1) {\n            return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n        }\n        return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n    },\n    easeInBounce: (t)=>1 - effects.easeOutBounce(1 - t),\n    easeOutBounce (t) {\n        const m = 7.5625;\n        const d = 2.75;\n        if (t < 1 / d) {\n            return m * t * t;\n        }\n        if (t < 2 / d) {\n            return m * (t -= 1.5 / d) * t + 0.75;\n        }\n        if (t < 2.5 / d) {\n            return m * (t -= 2.25 / d) * t + 0.9375;\n        }\n        return m * (t -= 2.625 / d) * t + 0.984375;\n    },\n    easeInOutBounce: (t)=>t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\n\nfunction isPatternOrGradient(value) {\n    if (value && typeof value === 'object') {\n        const type = value.toString();\n        return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n    }\n    return false;\n}\nfunction color(value) {\n    return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n    return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n\nconst numbers = [\n    'x',\n    'y',\n    'borderWidth',\n    'radius',\n    'tension'\n];\nconst colors = [\n    'color',\n    'borderColor',\n    'backgroundColor'\n];\nfunction applyAnimationsDefaults(defaults) {\n    defaults.set('animation', {\n        delay: undefined,\n        duration: 1000,\n        easing: 'easeOutQuart',\n        fn: undefined,\n        from: undefined,\n        loop: undefined,\n        to: undefined,\n        type: undefined\n    });\n    defaults.describe('animation', {\n        _fallback: false,\n        _indexable: false,\n        _scriptable: (name)=>name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n    });\n    defaults.set('animations', {\n        colors: {\n            type: 'color',\n            properties: colors\n        },\n        numbers: {\n            type: 'number',\n            properties: numbers\n        }\n    });\n    defaults.describe('animations', {\n        _fallback: 'animation'\n    });\n    defaults.set('transitions', {\n        active: {\n            animation: {\n                duration: 400\n            }\n        },\n        resize: {\n            animation: {\n                duration: 0\n            }\n        },\n        show: {\n            animations: {\n                colors: {\n                    from: 'transparent'\n                },\n                visible: {\n                    type: 'boolean',\n                    duration: 0\n                }\n            }\n        },\n        hide: {\n            animations: {\n                colors: {\n                    to: 'transparent'\n                },\n                visible: {\n                    type: 'boolean',\n                    easing: 'linear',\n                    fn: (v)=>v | 0\n                }\n            }\n        }\n    });\n}\n\nfunction applyLayoutsDefaults(defaults) {\n    defaults.set('layout', {\n        autoPadding: true,\n        padding: {\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0\n        }\n    });\n}\n\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n    options = options || {};\n    const cacheKey = locale + JSON.stringify(options);\n    let formatter = intlCache.get(cacheKey);\n    if (!formatter) {\n        formatter = new Intl.NumberFormat(locale, options);\n        intlCache.set(cacheKey, formatter);\n    }\n    return formatter;\n}\nfunction formatNumber(num, locale, options) {\n    return getNumberFormat(locale, options).format(num);\n}\n\nconst formatters = {\n values (value) {\n        return isArray(value) ?  value : '' + value;\n    },\n numeric (tickValue, index, ticks) {\n        if (tickValue === 0) {\n            return '0';\n        }\n        const locale = this.chart.options.locale;\n        let notation;\n        let delta = tickValue;\n        if (ticks.length > 1) {\n            const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n            if (maxTick < 1e-4 || maxTick > 1e+15) {\n                notation = 'scientific';\n            }\n            delta = calculateDelta(tickValue, ticks);\n        }\n        const logDelta = log10(Math.abs(delta));\n        const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n        const options = {\n            notation,\n            minimumFractionDigits: numDecimal,\n            maximumFractionDigits: numDecimal\n        };\n        Object.assign(options, this.options.ticks.format);\n        return formatNumber(tickValue, locale, options);\n    },\n logarithmic (tickValue, index, ticks) {\n        if (tickValue === 0) {\n            return '0';\n        }\n        const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n        if ([\n            1,\n            2,\n            3,\n            5,\n            10,\n            15\n        ].includes(remain) || index > 0.8 * ticks.length) {\n            return formatters.numeric.call(this, tickValue, index, ticks);\n        }\n        return '';\n    }\n};\nfunction calculateDelta(tickValue, ticks) {\n    let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n    if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n        delta = tickValue - Math.floor(tickValue);\n    }\n    return delta;\n}\n var Ticks = {\n    formatters\n};\n\nfunction applyScaleDefaults(defaults) {\n    defaults.set('scale', {\n        display: true,\n        offset: false,\n        reverse: false,\n        beginAtZero: false,\n bounds: 'ticks',\n        clip: true,\n grace: 0,\n        grid: {\n            display: true,\n            lineWidth: 1,\n            drawOnChartArea: true,\n            drawTicks: true,\n            tickLength: 8,\n            tickWidth: (_ctx, options)=>options.lineWidth,\n            tickColor: (_ctx, options)=>options.color,\n            offset: false\n        },\n        border: {\n            display: true,\n            dash: [],\n            dashOffset: 0.0,\n            width: 1\n        },\n        title: {\n            display: false,\n            text: '',\n            padding: {\n                top: 4,\n                bottom: 4\n            }\n        },\n        ticks: {\n            minRotation: 0,\n            maxRotation: 50,\n            mirror: false,\n            textStrokeWidth: 0,\n            textStrokeColor: '',\n            padding: 3,\n            display: true,\n            autoSkip: true,\n            autoSkipPadding: 3,\n            labelOffset: 0,\n            callback: Ticks.formatters.values,\n            minor: {},\n            major: {},\n            align: 'center',\n            crossAlign: 'near',\n            showLabelBackdrop: false,\n            backdropColor: 'rgba(255, 255, 255, 0.75)',\n            backdropPadding: 2\n        }\n    });\n    defaults.route('scale.ticks', 'color', '', 'color');\n    defaults.route('scale.grid', 'color', '', 'borderColor');\n    defaults.route('scale.border', 'color', '', 'borderColor');\n    defaults.route('scale.title', 'color', '', 'color');\n    defaults.describe('scale', {\n        _fallback: false,\n        _scriptable: (name)=>!name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n        _indexable: (name)=>name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n    });\n    defaults.describe('scales', {\n        _fallback: 'scale'\n    });\n    defaults.describe('scale.ticks', {\n        _scriptable: (name)=>name !== 'backdropPadding' && name !== 'callback',\n        _indexable: (name)=>name !== 'backdropPadding'\n    });\n}\n\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\n function getScope$1(node, key) {\n    if (!key) {\n        return node;\n    }\n    const keys = key.split('.');\n    for(let i = 0, n = keys.length; i < n; ++i){\n        const k = keys[i];\n        node = node[k] || (node[k] = Object.create(null));\n    }\n    return node;\n}\nfunction set(root, scope, values) {\n    if (typeof scope === 'string') {\n        return merge(getScope$1(root, scope), values);\n    }\n    return merge(getScope$1(root, ''), scope);\n}\n class Defaults {\n    constructor(_descriptors, _appliers){\n        this.animation = undefined;\n        this.backgroundColor = 'rgba(0,0,0,0.1)';\n        this.borderColor = 'rgba(0,0,0,0.1)';\n        this.color = '#666';\n        this.datasets = {};\n        this.devicePixelRatio = (context)=>context.chart.platform.getDevicePixelRatio();\n        this.elements = {};\n        this.events = [\n            'mousemove',\n            'mouseout',\n            'click',\n            'touchstart',\n            'touchmove'\n        ];\n        this.font = {\n            family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n            size: 12,\n            style: 'normal',\n            lineHeight: 1.2,\n            weight: null\n        };\n        this.hover = {};\n        this.hoverBackgroundColor = (ctx, options)=>getHoverColor(options.backgroundColor);\n        this.hoverBorderColor = (ctx, options)=>getHoverColor(options.borderColor);\n        this.hoverColor = (ctx, options)=>getHoverColor(options.color);\n        this.indexAxis = 'x';\n        this.interaction = {\n            mode: 'nearest',\n            intersect: true,\n            includeInvisible: false\n        };\n        this.maintainAspectRatio = true;\n        this.onHover = null;\n        this.onClick = null;\n        this.parsing = true;\n        this.plugins = {};\n        this.responsive = true;\n        this.scale = undefined;\n        this.scales = {};\n        this.showLine = true;\n        this.drawActiveElementsOnTop = true;\n        this.describe(_descriptors);\n        this.apply(_appliers);\n    }\n set(scope, values) {\n        return set(this, scope, values);\n    }\n get(scope) {\n        return getScope$1(this, scope);\n    }\n describe(scope, values) {\n        return set(descriptors, scope, values);\n    }\n    override(scope, values) {\n        return set(overrides, scope, values);\n    }\n route(scope, name, targetScope, targetName) {\n        const scopeObject = getScope$1(this, scope);\n        const targetScopeObject = getScope$1(this, targetScope);\n        const privateName = '_' + name;\n        Object.defineProperties(scopeObject, {\n            [privateName]: {\n                value: scopeObject[name],\n                writable: true\n            },\n            [name]: {\n                enumerable: true,\n                get () {\n                    const local = this[privateName];\n                    const target = targetScopeObject[targetName];\n                    if (isObject(local)) {\n                        return Object.assign({}, target, local);\n                    }\n                    return valueOrDefault(local, target);\n                },\n                set (value) {\n                    this[privateName] = value;\n                }\n            }\n        });\n    }\n    apply(appliers) {\n        appliers.forEach((apply)=>apply(this));\n    }\n}\nvar defaults = /* #__PURE__ */ new Defaults({\n    _scriptable: (name)=>!name.startsWith('on'),\n    _indexable: (name)=>name !== 'events',\n    hover: {\n        _fallback: 'interaction'\n    },\n    interaction: {\n        _scriptable: false,\n        _indexable: false\n    }\n}, [\n    applyAnimationsDefaults,\n    applyLayoutsDefaults,\n    applyScaleDefaults\n]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */ function toFontString(font) {\n    if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n        return null;\n    }\n    return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */ function _measureText(ctx, data, gc, longest, string) {\n    let textWidth = data[string];\n    if (!textWidth) {\n        textWidth = data[string] = ctx.measureText(string).width;\n        gc.push(string);\n    }\n    if (textWidth > longest) {\n        longest = textWidth;\n    }\n    return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n    cache = cache || {};\n    let data = cache.data = cache.data || {};\n    let gc = cache.garbageCollect = cache.garbageCollect || [];\n    if (cache.font !== font) {\n        data = cache.data = {};\n        gc = cache.garbageCollect = [];\n        cache.font = font;\n    }\n    ctx.save();\n    ctx.font = font;\n    let longest = 0;\n    const ilen = arrayOfThings.length;\n    let i, j, jlen, thing, nestedThing;\n    for(i = 0; i < ilen; i++){\n        thing = arrayOfThings[i];\n        // Undefined strings and arrays should not be measured\n        if (thing !== undefined && thing !== null && !isArray(thing)) {\n            longest = _measureText(ctx, data, gc, longest, thing);\n        } else if (isArray(thing)) {\n            // if it is an array lets measure each element\n            // to do maybe simplify this function a bit so we can do this more recursively?\n            for(j = 0, jlen = thing.length; j < jlen; j++){\n                nestedThing = thing[j];\n                // Undefined strings and arrays should not be measured\n                if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n                    longest = _measureText(ctx, data, gc, longest, nestedThing);\n                }\n            }\n        }\n    }\n    ctx.restore();\n    const gcLen = gc.length / 2;\n    if (gcLen > arrayOfThings.length) {\n        for(i = 0; i < gcLen; i++){\n            delete data[gc[i]];\n        }\n        gc.splice(0, gcLen);\n    }\n    return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */ function _alignPixel(chart, pixel, width) {\n    const devicePixelRatio = chart.currentDevicePixelRatio;\n    const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n    return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */ function clearCanvas(canvas, ctx) {\n    if (!ctx && !canvas) {\n        return;\n    }\n    ctx = ctx || canvas.getContext('2d');\n    ctx.save();\n    // canvas.width and canvas.height do not consider the canvas transform,\n    // while clearRect does\n    ctx.resetTransform();\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n    ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n    let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n    const style = options.pointStyle;\n    const rotation = options.rotation;\n    const radius = options.radius;\n    let rad = (rotation || 0) * RAD_PER_DEG;\n    if (style && typeof style === 'object') {\n        type = style.toString();\n        if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n            ctx.save();\n            ctx.translate(x, y);\n            ctx.rotate(rad);\n            ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n            ctx.restore();\n            return;\n        }\n    }\n    if (isNaN(radius) || radius <= 0) {\n        return;\n    }\n    ctx.beginPath();\n    switch(style){\n        // Default includes circle\n        default:\n            if (w) {\n                ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n            } else {\n                ctx.arc(x, y, radius, 0, TAU);\n            }\n            ctx.closePath();\n            break;\n        case 'triangle':\n            width = w ? w / 2 : radius;\n            ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n            rad += TWO_THIRDS_PI;\n            ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n            rad += TWO_THIRDS_PI;\n            ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n            ctx.closePath();\n            break;\n        case 'rectRounded':\n            // NOTE: the rounded rect implementation changed to use `arc` instead of\n            // `quadraticCurveTo` since it generates better results when rect is\n            // almost a circle. 0.516 (instead of 0.5) produces results with visually\n            // closer proportion to the previous impl and it is inscribed in the\n            // circle with `radius`. For more details, see the following PRs:\n            // https://github.com/chartjs/Chart.js/issues/5597\n            // https://github.com/chartjs/Chart.js/issues/5858\n            cornerRadius = radius * 0.516;\n            size = radius - cornerRadius;\n            xOffset = Math.cos(rad + QUARTER_PI) * size;\n            xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n            yOffset = Math.sin(rad + QUARTER_PI) * size;\n            yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n            ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n            ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n            ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n            ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n            ctx.closePath();\n            break;\n        case 'rect':\n            if (!rotation) {\n                size = Math.SQRT1_2 * radius;\n                width = w ? w / 2 : size;\n                ctx.rect(x - width, y - size, 2 * width, 2 * size);\n                break;\n            }\n            rad += QUARTER_PI;\n        /* falls through */ case 'rectRot':\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            ctx.closePath();\n            break;\n        case 'crossRot':\n            rad += QUARTER_PI;\n        /* falls through */ case 'cross':\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.moveTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            break;\n        case 'star':\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.moveTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            rad += QUARTER_PI;\n            xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n            xOffset = Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n            ctx.moveTo(x - xOffsetW, y - yOffset);\n            ctx.lineTo(x + xOffsetW, y + yOffset);\n            ctx.moveTo(x + yOffsetW, y - xOffset);\n            ctx.lineTo(x - yOffsetW, y + xOffset);\n            break;\n        case 'line':\n            xOffset = w ? w / 2 : Math.cos(rad) * radius;\n            yOffset = Math.sin(rad) * radius;\n            ctx.moveTo(x - xOffset, y - yOffset);\n            ctx.lineTo(x + xOffset, y + yOffset);\n            break;\n        case 'dash':\n            ctx.moveTo(x, y);\n            ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n            break;\n        case false:\n            ctx.closePath();\n            break;\n    }\n    ctx.fill();\n    if (options.borderWidth > 0) {\n        ctx.stroke();\n    }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */ function _isPointInArea(point, area, margin) {\n    margin = margin || 0.5; // margin - default is to match rounded decimals\n    return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n    ctx.save();\n    ctx.beginPath();\n    ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n    ctx.clip();\n}\nfunction unclipArea(ctx) {\n    ctx.restore();\n}\n/**\n * @private\n */ function _steppedLineTo(ctx, previous, target, flip, mode) {\n    if (!previous) {\n        return ctx.lineTo(target.x, target.y);\n    }\n    if (mode === 'middle') {\n        const midpoint = (previous.x + target.x) / 2.0;\n        ctx.lineTo(midpoint, previous.y);\n        ctx.lineTo(midpoint, target.y);\n    } else if (mode === 'after' !== !!flip) {\n        ctx.lineTo(previous.x, target.y);\n    } else {\n        ctx.lineTo(target.x, previous.y);\n    }\n    ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */ function _bezierCurveTo(ctx, previous, target, flip) {\n    if (!previous) {\n        return ctx.lineTo(target.x, target.y);\n    }\n    ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n    if (opts.translation) {\n        ctx.translate(opts.translation[0], opts.translation[1]);\n    }\n    if (!isNullOrUndef(opts.rotation)) {\n        ctx.rotate(opts.rotation);\n    }\n    if (opts.color) {\n        ctx.fillStyle = opts.color;\n    }\n    if (opts.textAlign) {\n        ctx.textAlign = opts.textAlign;\n    }\n    if (opts.textBaseline) {\n        ctx.textBaseline = opts.textBaseline;\n    }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n    if (opts.strikethrough || opts.underline) {\n        /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */ const metrics = ctx.measureText(line);\n        const left = x - metrics.actualBoundingBoxLeft;\n        const right = x + metrics.actualBoundingBoxRight;\n        const top = y - metrics.actualBoundingBoxAscent;\n        const bottom = y + metrics.actualBoundingBoxDescent;\n        const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n        ctx.strokeStyle = ctx.fillStyle;\n        ctx.beginPath();\n        ctx.lineWidth = opts.decorationWidth || 2;\n        ctx.moveTo(left, yDecoration);\n        ctx.lineTo(right, yDecoration);\n        ctx.stroke();\n    }\n}\nfunction drawBackdrop(ctx, opts) {\n    const oldColor = ctx.fillStyle;\n    ctx.fillStyle = opts.color;\n    ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n    ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */ function renderText(ctx, text, x, y, font, opts = {}) {\n    const lines = isArray(text) ? text : [\n        text\n    ];\n    const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n    let i, line;\n    ctx.save();\n    ctx.font = font.string;\n    setRenderOpts(ctx, opts);\n    for(i = 0; i < lines.length; ++i){\n        line = lines[i];\n        if (opts.backdrop) {\n            drawBackdrop(ctx, opts.backdrop);\n        }\n        if (stroke) {\n            if (opts.strokeColor) {\n                ctx.strokeStyle = opts.strokeColor;\n            }\n            if (!isNullOrUndef(opts.strokeWidth)) {\n                ctx.lineWidth = opts.strokeWidth;\n            }\n            ctx.strokeText(line, x, y, opts.maxWidth);\n        }\n        ctx.fillText(line, x, y, opts.maxWidth);\n        decorateText(ctx, x, y, line, opts);\n        y += Number(font.lineHeight);\n    }\n    ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */ function addRoundedRectPath(ctx, rect) {\n    const { x , y , w , h , radius  } = rect;\n    // top left arc\n    ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n    // line from top left to bottom left\n    ctx.lineTo(x, y + h - radius.bottomLeft);\n    // bottom left arc\n    ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n    // line from bottom left to bottom right\n    ctx.lineTo(x + w - radius.bottomRight, y + h);\n    // bottom right arc\n    ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n    // line from bottom right to top right\n    ctx.lineTo(x + w, y + radius.topRight);\n    // top right arc\n    ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n    // line from top right to top left\n    ctx.lineTo(x + radius.topLeft, y);\n}\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */ function toLineHeight(value, size) {\n    const matches = ('' + value).match(LINE_HEIGHT);\n    if (!matches || matches[1] === 'normal') {\n        return size * 1.2;\n    }\n    value = +matches[2];\n    switch(matches[3]){\n        case 'px':\n            return value;\n        case '%':\n            value /= 100;\n            break;\n    }\n    return size * value;\n}\nconst numberOrZero = (v)=>+v || 0;\nfunction _readValueToProps(value, props) {\n    const ret = {};\n    const objProps = isObject(props);\n    const keys = objProps ? Object.keys(props) : props;\n    const read = isObject(value) ? objProps ? (prop)=>valueOrDefault(value[prop], value[props[prop]]) : (prop)=>value[prop] : ()=>value;\n    for (const prop of keys){\n        ret[prop] = numberOrZero(read(prop));\n    }\n    return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */ function toTRBL(value) {\n    return _readValueToProps(value, {\n        top: 'y',\n        right: 'x',\n        bottom: 'y',\n        left: 'x'\n    });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */ function toTRBLCorners(value) {\n    return _readValueToProps(value, [\n        'topLeft',\n        'topRight',\n        'bottomLeft',\n        'bottomRight'\n    ]);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */ function toPadding(value) {\n    const obj = toTRBL(value);\n    obj.width = obj.left + obj.right;\n    obj.height = obj.top + obj.bottom;\n    return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */ function toFont(options, fallback) {\n    options = options || {};\n    fallback = fallback || defaults.font;\n    let size = valueOrDefault(options.size, fallback.size);\n    if (typeof size === 'string') {\n        size = parseInt(size, 10);\n    }\n    let style = valueOrDefault(options.style, fallback.style);\n    if (style && !('' + style).match(FONT_STYLE)) {\n        console.warn('Invalid font style specified: \"' + style + '\"');\n        style = undefined;\n    }\n    const font = {\n        family: valueOrDefault(options.family, fallback.family),\n        lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n        size,\n        style,\n        weight: valueOrDefault(options.weight, fallback.weight),\n        string: ''\n    };\n    font.string = toFontString(font);\n    return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */ function resolve(inputs, context, index, info) {\n    let cacheable = true;\n    let i, ilen, value;\n    for(i = 0, ilen = inputs.length; i < ilen; ++i){\n        value = inputs[i];\n        if (value === undefined) {\n            continue;\n        }\n        if (context !== undefined && typeof value === 'function') {\n            value = value(context);\n            cacheable = false;\n        }\n        if (index !== undefined && isArray(value)) {\n            value = value[index % value.length];\n            cacheable = false;\n        }\n        if (value !== undefined) {\n            if (info && !cacheable) {\n                info.cacheable = false;\n            }\n            return value;\n        }\n    }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */ function _addGrace(minmax, grace, beginAtZero) {\n    const { min , max  } = minmax;\n    const change = toDimension(grace, (max - min) / 2);\n    const keepZero = (value, add)=>beginAtZero && value === 0 ? 0 : value + add;\n    return {\n        min: keepZero(min, -Math.abs(change)),\n        max: keepZero(max, change)\n    };\n}\nfunction createContext(parentContext, context) {\n    return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */ function _createResolver(scopes, prefixes = [\n    ''\n], rootScopes, fallback, getTarget = ()=>scopes[0]) {\n    const finalRootScopes = rootScopes || scopes;\n    if (typeof fallback === 'undefined') {\n        fallback = _resolve('_fallback', scopes);\n    }\n    const cache = {\n        [Symbol.toStringTag]: 'Object',\n        _cacheable: true,\n        _scopes: scopes,\n        _rootScopes: finalRootScopes,\n        _fallback: fallback,\n        _getTarget: getTarget,\n        override: (scope)=>_createResolver([\n                scope,\n                ...scopes\n            ], prefixes, finalRootScopes, fallback)\n    };\n    return new Proxy(cache, {\n        /**\n     * A trap for the delete operator.\n     */ deleteProperty (target, prop) {\n            delete target[prop]; // remove from cache\n            delete target._keys; // remove cached keys\n            delete scopes[0][prop]; // remove from top level scope\n            return true;\n        },\n        /**\n     * A trap for getting property values.\n     */ get (target, prop) {\n            return _cached(target, prop, ()=>_resolveWithPrefixes(prop, prefixes, scopes, target));\n        },\n        /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */ getOwnPropertyDescriptor (target, prop) {\n            return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n        },\n        /**\n     * A trap for Object.getPrototypeOf.\n     */ getPrototypeOf () {\n            return Reflect.getPrototypeOf(scopes[0]);\n        },\n        /**\n     * A trap for the in operator.\n     */ has (target, prop) {\n            return getKeysFromAllScopes(target).includes(prop);\n        },\n        /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */ ownKeys (target) {\n            return getKeysFromAllScopes(target);\n        },\n        /**\n     * A trap for setting property values.\n     */ set (target, prop, value) {\n            const storage = target._storage || (target._storage = getTarget());\n            target[prop] = storage[prop] = value; // set to top level scope + cache\n            delete target._keys; // remove cached keys\n            return true;\n        }\n    });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */ function _attachContext(proxy, context, subProxy, descriptorDefaults) {\n    const cache = {\n        _cacheable: false,\n        _proxy: proxy,\n        _context: context,\n        _subProxy: subProxy,\n        _stack: new Set(),\n        _descriptors: _descriptors(proxy, descriptorDefaults),\n        setContext: (ctx)=>_attachContext(proxy, ctx, subProxy, descriptorDefaults),\n        override: (scope)=>_attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n    };\n    return new Proxy(cache, {\n        /**\n     * A trap for the delete operator.\n     */ deleteProperty (target, prop) {\n            delete target[prop]; // remove from cache\n            delete proxy[prop]; // remove from proxy\n            return true;\n        },\n        /**\n     * A trap for getting property values.\n     */ get (target, prop, receiver) {\n            return _cached(target, prop, ()=>_resolveWithContext(target, prop, receiver));\n        },\n        /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */ getOwnPropertyDescriptor (target, prop) {\n            return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n                enumerable: true,\n                configurable: true\n            } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n        },\n        /**\n     * A trap for Object.getPrototypeOf.\n     */ getPrototypeOf () {\n            return Reflect.getPrototypeOf(proxy);\n        },\n        /**\n     * A trap for the in operator.\n     */ has (target, prop) {\n            return Reflect.has(proxy, prop);\n        },\n        /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */ ownKeys () {\n            return Reflect.ownKeys(proxy);\n        },\n        /**\n     * A trap for setting property values.\n     */ set (target, prop, value) {\n            proxy[prop] = value; // set to proxy\n            delete target[prop]; // remove from cache\n            return true;\n        }\n    });\n}\n/**\n * @private\n */ function _descriptors(proxy, defaults = {\n    scriptable: true,\n    indexable: true\n}) {\n    const { _scriptable =defaults.scriptable , _indexable =defaults.indexable , _allKeys =defaults.allKeys  } = proxy;\n    return {\n        allKeys: _allKeys,\n        scriptable: _scriptable,\n        indexable: _indexable,\n        isScriptable: isFunction(_scriptable) ? _scriptable : ()=>_scriptable,\n        isIndexable: isFunction(_indexable) ? _indexable : ()=>_indexable\n    };\n}\nconst readKey = (prefix, name)=>prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value)=>isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n    if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n        return target[prop];\n    }\n    const value = resolve();\n    // cache the resolved value\n    target[prop] = value;\n    return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n    const { _proxy , _context , _subProxy , _descriptors: descriptors  } = target;\n    let value = _proxy[prop]; // resolve from proxy\n    // resolve with context\n    if (isFunction(value) && descriptors.isScriptable(prop)) {\n        value = _resolveScriptable(prop, value, target, receiver);\n    }\n    if (isArray(value) && value.length) {\n        value = _resolveArray(prop, value, target, descriptors.isIndexable);\n    }\n    if (needsSubResolver(prop, value)) {\n        // if the resolved value is an object, create a sub resolver for it\n        value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n    }\n    return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n    const { _proxy , _context , _subProxy , _stack  } = target;\n    if (_stack.has(prop)) {\n        throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n    }\n    _stack.add(prop);\n    let value = getValue(_context, _subProxy || receiver);\n    _stack.delete(prop);\n    if (needsSubResolver(prop, value)) {\n        // When scriptable option returns an object, create a resolver on that.\n        value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n    }\n    return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n    const { _proxy , _context , _subProxy , _descriptors: descriptors  } = target;\n    if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n        return value[_context.index % value.length];\n    } else if (isObject(value[0])) {\n        // Array of objects, return array or resolvers\n        const arr = value;\n        const scopes = _proxy._scopes.filter((s)=>s !== arr);\n        value = [];\n        for (const item of arr){\n            const resolver = createSubResolver(scopes, _proxy, prop, item);\n            value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n        }\n    }\n    return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n    return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent)=>key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n    for (const parent of parentScopes){\n        const scope = getScope(key, parent);\n        if (scope) {\n            set.add(scope);\n            const fallback = resolveFallback(scope._fallback, key, value);\n            if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n                // When we reach the descriptor that defines a new _fallback, return that.\n                // The fallback will resume to that new scope.\n                return fallback;\n            }\n        } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n            // Fallback to `false` results to `false`, when falling back to different key.\n            // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n            return null;\n        }\n    }\n    return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n    const rootScopes = resolver._rootScopes;\n    const fallback = resolveFallback(resolver._fallback, prop, value);\n    const allScopes = [\n        ...parentScopes,\n        ...rootScopes\n    ];\n    const set = new Set();\n    set.add(value);\n    let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n    if (key === null) {\n        return false;\n    }\n    if (typeof fallback !== 'undefined' && fallback !== prop) {\n        key = addScopesFromKey(set, allScopes, fallback, key, value);\n        if (key === null) {\n            return false;\n        }\n    }\n    return _createResolver(Array.from(set), [\n        ''\n    ], rootScopes, fallback, ()=>subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n    while(key){\n        key = addScopes(set, allScopes, key, fallback, item);\n    }\n    return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n    const parent = resolver._getTarget();\n    if (!(prop in parent)) {\n        parent[prop] = {};\n    }\n    const target = parent[prop];\n    if (isArray(target) && isObject(value)) {\n        // For array of objects, the object is used to store updated values\n        return value;\n    }\n    return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n    let value;\n    for (const prefix of prefixes){\n        value = _resolve(readKey(prefix, prop), scopes);\n        if (typeof value !== 'undefined') {\n            return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n        }\n    }\n}\nfunction _resolve(key, scopes) {\n    for (const scope of scopes){\n        if (!scope) {\n            continue;\n        }\n        const value = scope[key];\n        if (typeof value !== 'undefined') {\n            return value;\n        }\n    }\n}\nfunction getKeysFromAllScopes(target) {\n    let keys = target._keys;\n    if (!keys) {\n        keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n    }\n    return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n    const set = new Set();\n    for (const scope of scopes){\n        for (const key of Object.keys(scope).filter((k)=>!k.startsWith('_'))){\n            set.add(key);\n        }\n    }\n    return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n    const { iScale  } = meta;\n    const { key ='r'  } = this._parsing;\n    const parsed = new Array(count);\n    let i, ilen, index, item;\n    for(i = 0, ilen = count; i < ilen; ++i){\n        index = i + start;\n        item = data[index];\n        parsed[i] = {\n            r: iScale.parse(resolveObjectKey(item, key), index)\n        };\n    }\n    return parsed;\n}\n\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i)=>i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis)=>indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n    // Props to Rob Spencer at scaled innovation for his post on splining between points\n    // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n    // This function must also respect \"skipped\" points\n    const previous = firstPoint.skip ? middlePoint : firstPoint;\n    const current = middlePoint;\n    const next = afterPoint.skip ? middlePoint : afterPoint;\n    const d01 = distanceBetweenPoints(current, previous);\n    const d12 = distanceBetweenPoints(next, current);\n    let s01 = d01 / (d01 + d12);\n    let s12 = d12 / (d01 + d12);\n    // If all points are the same, s01 & s02 will be inf\n    s01 = isNaN(s01) ? 0 : s01;\n    s12 = isNaN(s12) ? 0 : s12;\n    const fa = t * s01; // scaling factor for triangle Ta\n    const fb = t * s12;\n    return {\n        previous: {\n            x: current.x - fa * (next.x - previous.x),\n            y: current.y - fa * (next.y - previous.y)\n        },\n        next: {\n            x: current.x + fb * (next.x - previous.x),\n            y: current.y + fb * (next.y - previous.y)\n        }\n    };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */ function monotoneAdjust(points, deltaK, mK) {\n    const pointsLen = points.length;\n    let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n    let pointAfter = getPoint(points, 0);\n    for(let i = 0; i < pointsLen - 1; ++i){\n        pointCurrent = pointAfter;\n        pointAfter = getPoint(points, i + 1);\n        if (!pointCurrent || !pointAfter) {\n            continue;\n        }\n        if (almostEquals(deltaK[i], 0, EPSILON)) {\n            mK[i] = mK[i + 1] = 0;\n            continue;\n        }\n        alphaK = mK[i] / deltaK[i];\n        betaK = mK[i + 1] / deltaK[i];\n        squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n        if (squaredMagnitude <= 9) {\n            continue;\n        }\n        tauK = 3 / Math.sqrt(squaredMagnitude);\n        mK[i] = alphaK * tauK * deltaK[i];\n        mK[i + 1] = betaK * tauK * deltaK[i];\n    }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n    const valueAxis = getValueAxis(indexAxis);\n    const pointsLen = points.length;\n    let delta, pointBefore, pointCurrent;\n    let pointAfter = getPoint(points, 0);\n    for(let i = 0; i < pointsLen; ++i){\n        pointBefore = pointCurrent;\n        pointCurrent = pointAfter;\n        pointAfter = getPoint(points, i + 1);\n        if (!pointCurrent) {\n            continue;\n        }\n        const iPixel = pointCurrent[indexAxis];\n        const vPixel = pointCurrent[valueAxis];\n        if (pointBefore) {\n            delta = (iPixel - pointBefore[indexAxis]) / 3;\n            pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n            pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n        }\n        if (pointAfter) {\n            delta = (pointAfter[indexAxis] - iPixel) / 3;\n            pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n            pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n        }\n    }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */ function splineCurveMonotone(points, indexAxis = 'x') {\n    const valueAxis = getValueAxis(indexAxis);\n    const pointsLen = points.length;\n    const deltaK = Array(pointsLen).fill(0);\n    const mK = Array(pointsLen);\n    // Calculate slopes (deltaK) and initialize tangents (mK)\n    let i, pointBefore, pointCurrent;\n    let pointAfter = getPoint(points, 0);\n    for(i = 0; i < pointsLen; ++i){\n        pointBefore = pointCurrent;\n        pointCurrent = pointAfter;\n        pointAfter = getPoint(points, i + 1);\n        if (!pointCurrent) {\n            continue;\n        }\n        if (pointAfter) {\n            const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n            // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n            deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n        }\n        mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n    }\n    monotoneAdjust(points, deltaK, mK);\n    monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n    return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n    let i, ilen, point, inArea, inAreaPrev;\n    let inAreaNext = _isPointInArea(points[0], area);\n    for(i = 0, ilen = points.length; i < ilen; ++i){\n        inAreaPrev = inArea;\n        inArea = inAreaNext;\n        inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n        if (!inArea) {\n            continue;\n        }\n        point = points[i];\n        if (inAreaPrev) {\n            point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n            point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n        }\n        if (inAreaNext) {\n            point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n            point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n        }\n    }\n}\n/**\n * @private\n */ function _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n    let i, ilen, point, controlPoints;\n    // Only consider points that are drawn in case the spanGaps option is used\n    if (options.spanGaps) {\n        points = points.filter((pt)=>!pt.skip);\n    }\n    if (options.cubicInterpolationMode === 'monotone') {\n        splineCurveMonotone(points, indexAxis);\n    } else {\n        let prev = loop ? points[points.length - 1] : points[0];\n        for(i = 0, ilen = points.length; i < ilen; ++i){\n            point = points[i];\n            controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n            point.cp1x = controlPoints.previous.x;\n            point.cp1y = controlPoints.previous.y;\n            point.cp2x = controlPoints.next.x;\n            point.cp2y = controlPoints.next.y;\n            prev = point;\n        }\n    }\n    if (options.capBezierPoints) {\n        capBezierPoints(points, area);\n    }\n}\n\n/**\n * @private\n */ function _isDomSupported() {\n    return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */ function _getParentNode(domNode) {\n    let parent = domNode.parentNode;\n    if (parent && parent.toString() === '[object ShadowRoot]') {\n        parent = parent.host;\n    }\n    return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */ function parseMaxStyle(styleValue, node, parentProperty) {\n    let valueInPixels;\n    if (typeof styleValue === 'string') {\n        valueInPixels = parseInt(styleValue, 10);\n        if (styleValue.indexOf('%') !== -1) {\n            // percentage * size in dimension\n            valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n        }\n    } else {\n        valueInPixels = styleValue;\n    }\n    return valueInPixels;\n}\nconst getComputedStyle = (element)=>element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n    return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = [\n    'top',\n    'right',\n    'bottom',\n    'left'\n];\nfunction getPositionedStyle(styles, style, suffix) {\n    const result = {};\n    suffix = suffix ? '-' + suffix : '';\n    for(let i = 0; i < 4; i++){\n        const pos = positions[i];\n        result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n    }\n    result.width = result.left + result.right;\n    result.height = result.top + result.bottom;\n    return result;\n}\nconst useOffsetPos = (x, y, target)=>(x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */ function getCanvasPosition(e, canvas) {\n    const touches = e.touches;\n    const source = touches && touches.length ? touches[0] : e;\n    const { offsetX , offsetY  } = source;\n    let box = false;\n    let x, y;\n    if (useOffsetPos(offsetX, offsetY, e.target)) {\n        x = offsetX;\n        y = offsetY;\n    } else {\n        const rect = canvas.getBoundingClientRect();\n        x = source.clientX - rect.left;\n        y = source.clientY - rect.top;\n        box = true;\n    }\n    return {\n        x,\n        y,\n        box\n    };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */ function getRelativePosition(event, chart) {\n    if ('native' in event) {\n        return event;\n    }\n    const { canvas , currentDevicePixelRatio  } = chart;\n    const style = getComputedStyle(canvas);\n    const borderBox = style.boxSizing === 'border-box';\n    const paddings = getPositionedStyle(style, 'padding');\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const { x , y , box  } = getCanvasPosition(event, canvas);\n    const xOffset = paddings.left + (box && borders.left);\n    const yOffset = paddings.top + (box && borders.top);\n    let { width , height  } = chart;\n    if (borderBox) {\n        width -= paddings.width + borders.width;\n        height -= paddings.height + borders.height;\n    }\n    return {\n        x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n        y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n    };\n}\nfunction getContainerSize(canvas, width, height) {\n    let maxWidth, maxHeight;\n    if (width === undefined || height === undefined) {\n        const container = canvas && _getParentNode(canvas);\n        if (!container) {\n            width = canvas.clientWidth;\n            height = canvas.clientHeight;\n        } else {\n            const rect = container.getBoundingClientRect(); // this is the border box of the container\n            const containerStyle = getComputedStyle(container);\n            const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n            const containerPadding = getPositionedStyle(containerStyle, 'padding');\n            width = rect.width - containerPadding.width - containerBorder.width;\n            height = rect.height - containerPadding.height - containerBorder.height;\n            maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n            maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n        }\n    }\n    return {\n        width,\n        height,\n        maxWidth: maxWidth || INFINITY,\n        maxHeight: maxHeight || INFINITY\n    };\n}\nconst round1 = (v)=>Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n    const style = getComputedStyle(canvas);\n    const margins = getPositionedStyle(style, 'margin');\n    const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n    const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n    const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n    let { width , height  } = containerSize;\n    if (style.boxSizing === 'content-box') {\n        const borders = getPositionedStyle(style, 'border', 'width');\n        const paddings = getPositionedStyle(style, 'padding');\n        width -= paddings.width + borders.width;\n        height -= paddings.height + borders.height;\n    }\n    width = Math.max(0, width - margins.width);\n    height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n    width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n    height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n    if (width && !height) {\n        // https://github.com/chartjs/Chart.js/issues/4659\n        // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n        height = round1(width / 2);\n    }\n    const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n    if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n        height = containerSize.height;\n        width = round1(Math.floor(height * aspectRatio));\n    }\n    return {\n        width,\n        height\n    };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */ function retinaScale(chart, forceRatio, forceStyle) {\n    const pixelRatio = forceRatio || 1;\n    const deviceHeight = Math.floor(chart.height * pixelRatio);\n    const deviceWidth = Math.floor(chart.width * pixelRatio);\n    chart.height = Math.floor(chart.height);\n    chart.width = Math.floor(chart.width);\n    const canvas = chart.canvas;\n    // If no style has been set on the canvas, the render size is used as display size,\n    // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n    // See https://github.com/chartjs/Chart.js/issues/3575\n    if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n        canvas.style.height = `${chart.height}px`;\n        canvas.style.width = `${chart.width}px`;\n    }\n    if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n        chart.currentDevicePixelRatio = pixelRatio;\n        canvas.height = deviceHeight;\n        canvas.width = deviceWidth;\n        chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n        return true;\n    }\n    return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */ const supportsEventListenerOptions = function() {\n    let passiveSupported = false;\n    try {\n        const options = {\n            get passive () {\n                passiveSupported = true;\n                return false;\n            }\n        };\n        if (_isDomSupported()) {\n            window.addEventListener('test', null, options);\n            window.removeEventListener('test', null, options);\n        }\n    } catch (e) {\n    // continue regardless of error\n    }\n    return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */ function readUsedSize(element, property) {\n    const value = getStyle(element, property);\n    const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n    return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */ function _pointInLine(p1, p2, t, mode) {\n    return {\n        x: p1.x + t * (p2.x - p1.x),\n        y: p1.y + t * (p2.y - p1.y)\n    };\n}\n/**\n * @private\n */ function _steppedInterpolation(p1, p2, t, mode) {\n    return {\n        x: p1.x + t * (p2.x - p1.x),\n        y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n    };\n}\n/**\n * @private\n */ function _bezierInterpolation(p1, p2, t, mode) {\n    const cp1 = {\n        x: p1.cp2x,\n        y: p1.cp2y\n    };\n    const cp2 = {\n        x: p2.cp1x,\n        y: p2.cp1y\n    };\n    const a = _pointInLine(p1, cp1, t);\n    const b = _pointInLine(cp1, cp2, t);\n    const c = _pointInLine(cp2, p2, t);\n    const d = _pointInLine(a, b, t);\n    const e = _pointInLine(b, c, t);\n    return _pointInLine(d, e, t);\n}\n\nconst getRightToLeftAdapter = function(rectX, width) {\n    return {\n        x (x) {\n            return rectX + rectX + width - x;\n        },\n        setWidth (w) {\n            width = w;\n        },\n        textAlign (align) {\n            if (align === 'center') {\n                return align;\n            }\n            return align === 'right' ? 'left' : 'right';\n        },\n        xPlus (x, value) {\n            return x - value;\n        },\n        leftForLtr (x, itemWidth) {\n            return x - itemWidth;\n        }\n    };\n};\nconst getLeftToRightAdapter = function() {\n    return {\n        x (x) {\n            return x;\n        },\n        setWidth (w) {},\n        textAlign (align) {\n            return align;\n        },\n        xPlus (x, value) {\n            return x + value;\n        },\n        leftForLtr (x, _itemWidth) {\n            return x;\n        }\n    };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n    return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n    let style, original;\n    if (direction === 'ltr' || direction === 'rtl') {\n        style = ctx.canvas.style;\n        original = [\n            style.getPropertyValue('direction'),\n            style.getPropertyPriority('direction')\n        ];\n        style.setProperty('direction', direction, 'important');\n        ctx.prevTextDirection = original;\n    }\n}\nfunction restoreTextDirection(ctx, original) {\n    if (original !== undefined) {\n        delete ctx.prevTextDirection;\n        ctx.canvas.style.setProperty('direction', original[0], original[1]);\n    }\n}\n\nfunction propertyFn(property) {\n    if (property === 'angle') {\n        return {\n            between: _angleBetween,\n            compare: _angleDiff,\n            normalize: _normalizeAngle\n        };\n    }\n    return {\n        between: _isBetween,\n        compare: (a, b)=>a - b,\n        normalize: (x)=>x\n    };\n}\nfunction normalizeSegment({ start , end , count , loop , style  }) {\n    return {\n        start: start % count,\n        end: end % count,\n        loop: loop && (end - start + 1) % count === 0,\n        style\n    };\n}\nfunction getSegment(segment, points, bounds) {\n    const { property , start: startBound , end: endBound  } = bounds;\n    const { between , normalize  } = propertyFn(property);\n    const count = points.length;\n    let { start , end , loop  } = segment;\n    let i, ilen;\n    if (loop) {\n        start += count;\n        end += count;\n        for(i = 0, ilen = count; i < ilen; ++i){\n            if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n                break;\n            }\n            start--;\n            end--;\n        }\n        start %= count;\n        end %= count;\n    }\n    if (end < start) {\n        end += count;\n    }\n    return {\n        start,\n        end,\n        loop,\n        style: segment.style\n    };\n}\n function _boundSegment(segment, points, bounds) {\n    if (!bounds) {\n        return [\n            segment\n        ];\n    }\n    const { property , start: startBound , end: endBound  } = bounds;\n    const count = points.length;\n    const { compare , between , normalize  } = propertyFn(property);\n    const { start , end , loop , style  } = getSegment(segment, points, bounds);\n    const result = [];\n    let inside = false;\n    let subStart = null;\n    let value, point, prevValue;\n    const startIsBefore = ()=>between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n    const endIsBefore = ()=>compare(endBound, value) === 0 || between(endBound, prevValue, value);\n    const shouldStart = ()=>inside || startIsBefore();\n    const shouldStop = ()=>!inside || endIsBefore();\n    for(let i = start, prev = start; i <= end; ++i){\n        point = points[i % count];\n        if (point.skip) {\n            continue;\n        }\n        value = normalize(point[property]);\n        if (value === prevValue) {\n            continue;\n        }\n        inside = between(value, startBound, endBound);\n        if (subStart === null && shouldStart()) {\n            subStart = compare(value, startBound) === 0 ? i : prev;\n        }\n        if (subStart !== null && shouldStop()) {\n            result.push(normalizeSegment({\n                start: subStart,\n                end: i,\n                loop,\n                count,\n                style\n            }));\n            subStart = null;\n        }\n        prev = i;\n        prevValue = value;\n    }\n    if (subStart !== null) {\n        result.push(normalizeSegment({\n            start: subStart,\n            end,\n            loop,\n            count,\n            style\n        }));\n    }\n    return result;\n}\n function _boundSegments(line, bounds) {\n    const result = [];\n    const segments = line.segments;\n    for(let i = 0; i < segments.length; i++){\n        const sub = _boundSegment(segments[i], line.points, bounds);\n        if (sub.length) {\n            result.push(...sub);\n        }\n    }\n    return result;\n}\n function findStartAndEnd(points, count, loop, spanGaps) {\n    let start = 0;\n    let end = count - 1;\n    if (loop && !spanGaps) {\n        while(start < count && !points[start].skip){\n            start++;\n        }\n    }\n    while(start < count && points[start].skip){\n        start++;\n    }\n    start %= count;\n    if (loop) {\n        end += start;\n    }\n    while(end > start && points[end % count].skip){\n        end--;\n    }\n    end %= count;\n    return {\n        start,\n        end\n    };\n}\n function solidSegments(points, start, max, loop) {\n    const count = points.length;\n    const result = [];\n    let last = start;\n    let prev = points[start];\n    let end;\n    for(end = start + 1; end <= max; ++end){\n        const cur = points[end % count];\n        if (cur.skip || cur.stop) {\n            if (!prev.skip) {\n                loop = false;\n                result.push({\n                    start: start % count,\n                    end: (end - 1) % count,\n                    loop\n                });\n                start = last = cur.stop ? end : null;\n            }\n        } else {\n            last = end;\n            if (prev.skip) {\n                start = end;\n            }\n        }\n        prev = cur;\n    }\n    if (last !== null) {\n        result.push({\n            start: start % count,\n            end: last % count,\n            loop\n        });\n    }\n    return result;\n}\n function _computeSegments(line, segmentOptions) {\n    const points = line.points;\n    const spanGaps = line.options.spanGaps;\n    const count = points.length;\n    if (!count) {\n        return [];\n    }\n    const loop = !!line._loop;\n    const { start , end  } = findStartAndEnd(points, count, loop, spanGaps);\n    if (spanGaps === true) {\n        return splitByStyles(line, [\n            {\n                start,\n                end,\n                loop\n            }\n        ], points, segmentOptions);\n    }\n    const max = end < start ? end + count : end;\n    const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n    return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n function splitByStyles(line, segments, points, segmentOptions) {\n    if (!segmentOptions || !segmentOptions.setContext || !points) {\n        return segments;\n    }\n    return doSplitByStyles(line, segments, points, segmentOptions);\n}\n function doSplitByStyles(line, segments, points, segmentOptions) {\n    const chartContext = line._chart.getContext();\n    const baseStyle = readStyle(line.options);\n    const { _datasetIndex: datasetIndex , options: { spanGaps  }  } = line;\n    const count = points.length;\n    const result = [];\n    let prevStyle = baseStyle;\n    let start = segments[0].start;\n    let i = start;\n    function addStyle(s, e, l, st) {\n        const dir = spanGaps ? -1 : 1;\n        if (s === e) {\n            return;\n        }\n        s += count;\n        while(points[s % count].skip){\n            s -= dir;\n        }\n        while(points[e % count].skip){\n            e += dir;\n        }\n        if (s % count !== e % count) {\n            result.push({\n                start: s % count,\n                end: e % count,\n                loop: l,\n                style: st\n            });\n            prevStyle = st;\n            start = e % count;\n        }\n    }\n    for (const segment of segments){\n        start = spanGaps ? start : segment.start;\n        let prev = points[start % count];\n        let style;\n        for(i = start + 1; i <= segment.end; i++){\n            const pt = points[i % count];\n            style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n                type: 'segment',\n                p0: prev,\n                p1: pt,\n                p0DataIndex: (i - 1) % count,\n                p1DataIndex: i % count,\n                datasetIndex\n            })));\n            if (styleChanged(style, prevStyle)) {\n                addStyle(start, i - 1, segment.loop, prevStyle);\n            }\n            prev = pt;\n            prevStyle = style;\n        }\n        if (start < i - 1) {\n            addStyle(start, i - 1, segment.loop, prevStyle);\n        }\n    }\n    return result;\n}\nfunction readStyle(options) {\n    return {\n        backgroundColor: options.backgroundColor,\n        borderCapStyle: options.borderCapStyle,\n        borderDash: options.borderDash,\n        borderDashOffset: options.borderDashOffset,\n        borderJoinStyle: options.borderJoinStyle,\n        borderWidth: options.borderWidth,\n        borderColor: options.borderColor\n    };\n}\nfunction styleChanged(style, prevStyle) {\n    if (!prevStyle) {\n        return false;\n    }\n    const cache = [];\n    const replacer = function(key, value) {\n        if (!isPatternOrGradient(value)) {\n            return value;\n        }\n        if (!cache.includes(value)) {\n            cache.push(value);\n        }\n        return cache.indexOf(value);\n    };\n    return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n\nfunction getSizeForArea(scale, chartArea, field) {\n    return scale.options.clip ? scale[field] : chartArea[field];\n}\nfunction getDatasetArea(meta, chartArea) {\n    const { xScale , yScale  } = meta;\n    if (xScale && yScale) {\n        return {\n            left: getSizeForArea(xScale, chartArea, 'left'),\n            right: getSizeForArea(xScale, chartArea, 'right'),\n            top: getSizeForArea(yScale, chartArea, 'top'),\n            bottom: getSizeForArea(yScale, chartArea, 'bottom')\n        };\n    }\n    return chartArea;\n}\nfunction getDatasetClipArea(chart, meta) {\n    const clip = meta._clip;\n    if (clip.disabled) {\n        return false;\n    }\n    const area = getDatasetArea(meta, chart.chartArea);\n    return {\n        left: clip.left === false ? 0 : area.left - (clip.left === true ? 0 : clip.left),\n        right: clip.right === false ? chart.width : area.right + (clip.right === true ? 0 : clip.right),\n        top: clip.top === false ? 0 : area.top - (clip.top === true ? 0 : clip.top),\n        bottom: clip.bottom === false ? chart.height : area.bottom + (clip.bottom === true ? 0 : clip.bottom)\n    };\n}\n\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, getStyle as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, distanceBetweenPoints as aF, noop as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, Ticks as aM, log10 as aN, _longestText as aO, _filterBetween as aP, _lookup as aQ, isPatternOrGradient as aR, getHoverColor as aS, clone as aT, _merger as aU, _mergerIf as aV, _deprecated as aW, _splitKey as aX, toFontString as aY, splineCurve as aZ, splineCurveMonotone as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, getDatasetClipArea as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _normalizeAngle as al, _readValueToProps as am, _updateBezierControlPoints as an, _computeSegments as ao, _boundSegments as ap, _steppedInterpolation as aq, _bezierInterpolation as ar, _pointInLine as as, _steppedLineTo as at, _bezierCurveTo as au, drawPoint as av, addRoundedRectPath as aw, toTRBL as ax, toTRBLCorners as ay, _boundSegment as az, isArray as b, fontString as b0, toLineHeight as b1, PITAU as b2, INFINITY as b3, RAD_PER_DEG as b4, QUARTER_PI as b5, TWO_THIRDS_PI as b6, _angleDiff as b7, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,eAAe;;AAErC;AACA;AACA,GAFA,CAEI;AACJ;AACA;AAAI,SAASC,IAAIA,CAAA,EAAG;EACpB;AACA;AACA;AACA;AAAI,MAAMC,GAAG,GAAG,CAAC,MAAI;EACjB,IAAIC,EAAE,GAAG,CAAC;EACV,OAAO,MAAIA,EAAE,EAAE;AACnB,CAAC,EAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;AAAI,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,OAAOA,CAACF,KAAK,EAAE;EACxB,IAAIG,KAAK,CAACD,OAAO,IAAIC,KAAK,CAACD,OAAO,CAACF,KAAK,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMI,IAAI,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,KAAK,CAAC;EAClD,IAAII,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,IAAIL,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC/D,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,QAAQA,CAACV,KAAK,EAAE;EACzB,OAAOA,KAAK,KAAK,IAAI,IAAIK,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,KAAK,CAAC,KAAK,iBAAiB;AACxF;AACA;AACA;AACA;AACA;AAAI,SAASW,cAAcA,CAACX,KAAK,EAAE;EAC/B,OAAO,CAAC,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYY,MAAM,KAAKC,QAAQ,CAAC,CAACb,KAAK,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AAAI,SAASc,eAAeA,CAACd,KAAK,EAAEe,YAAY,EAAE;EAC9C,OAAOJ,cAAc,CAACX,KAAK,CAAC,GAAGA,KAAK,GAAGe,YAAY;AACvD;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,cAAcA,CAAChB,KAAK,EAAEe,YAAY,EAAE;EAC7C,OAAO,OAAOf,KAAK,KAAK,WAAW,GAAGe,YAAY,GAAGf,KAAK;AAC9D;AACA,MAAMiB,YAAY,GAAGA,CAACjB,KAAK,EAAEkB,SAAS,KAAG,OAAOlB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACmB,QAAQ,CAAC,GAAG,CAAC,GAAGC,UAAU,CAACpB,KAAK,CAAC,GAAG,GAAG,GAAG,CAACA,KAAK,GAAGkB,SAAS;AACxI,MAAMG,WAAW,GAAGA,CAACrB,KAAK,EAAEkB,SAAS,KAAG,OAAOlB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACmB,QAAQ,CAAC,GAAG,CAAC,GAAGC,UAAU,CAACpB,KAAK,CAAC,GAAG,GAAG,GAAGkB,SAAS,GAAG,CAAClB,KAAK;AACvI;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASsB,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACrC,IAAIF,EAAE,IAAI,OAAOA,EAAE,CAACf,IAAI,KAAK,UAAU,EAAE;IACrC,OAAOe,EAAE,CAACG,KAAK,CAACD,OAAO,EAAED,IAAI,CAAC;EAClC;AACJ;AACA,SAASG,IAAIA,CAACC,QAAQ,EAAEL,EAAE,EAAEE,OAAO,EAAEI,OAAO,EAAE;EAC1C,IAAIC,CAAC,EAAEC,GAAG,EAAEC,IAAI;EAChB,IAAI9B,OAAO,CAAC0B,QAAQ,CAAC,EAAE;IACnBG,GAAG,GAAGH,QAAQ,CAACK,MAAM;IACrB,IAAIJ,OAAO,EAAE;MACT,KAAIC,CAAC,GAAGC,GAAG,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAC;QACzBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MACpC;IACJ,CAAC,MAAM;MACH,KAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAC;QACpBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MACpC;IACJ;EACJ,CAAC,MAAM,IAAIpB,QAAQ,CAACkB,QAAQ,CAAC,EAAE;IAC3BI,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACJ,QAAQ,CAAC;IAC5BG,GAAG,GAAGC,IAAI,CAACC,MAAM;IACjB,KAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAC;MACpBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACI,IAAI,CAACF,CAAC,CAAC,CAAC,EAAEE,IAAI,CAACF,CAAC,CAAC,CAAC;IAChD;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAChC,IAAIN,CAAC,EAAEO,IAAI,EAAEC,EAAE,EAAEC,EAAE;EACnB,IAAI,CAACJ,EAAE,IAAI,CAACC,EAAE,IAAID,EAAE,CAACF,MAAM,KAAKG,EAAE,CAACH,MAAM,EAAE;IACvC,OAAO,KAAK;EAChB;EACA,KAAIH,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGF,EAAE,CAACF,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IACvCQ,EAAE,GAAGH,EAAE,CAACL,CAAC,CAAC;IACVS,EAAE,GAAGH,EAAE,CAACN,CAAC,CAAC;IACV,IAAIQ,EAAE,CAACE,YAAY,KAAKD,EAAE,CAACC,YAAY,IAAIF,EAAE,CAACG,KAAK,KAAKF,EAAE,CAACE,KAAK,EAAE;MAC9D,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AAAI,SAASC,KAAKA,CAACC,MAAM,EAAE;EACvB,IAAIzC,OAAO,CAACyC,MAAM,CAAC,EAAE;IACjB,OAAOA,MAAM,CAACC,GAAG,CAACF,KAAK,CAAC;EAC5B;EACA,IAAIhC,QAAQ,CAACiC,MAAM,CAAC,EAAE;IAClB,MAAME,MAAM,GAAGxC,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;IAClC,MAAMd,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACW,MAAM,CAAC;IAChC,MAAMI,IAAI,GAAGf,IAAI,CAACC,MAAM;IACxB,IAAIe,CAAC,GAAG,CAAC;IACT,OAAMA,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAC;MAChBH,MAAM,CAACb,IAAI,CAACgB,CAAC,CAAC,CAAC,GAAGN,KAAK,CAACC,MAAM,CAACX,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC;IAC5C;IACA,OAAOH,MAAM;EACjB;EACA,OAAOF,MAAM;AACjB;AACA,SAASM,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAO,CACH,WAAW,EACX,WAAW,EACX,aAAa,CAChB,CAACC,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AAAI,SAASE,OAAOA,CAACF,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EAC/C,IAAI,CAACJ,UAAU,CAACC,GAAG,CAAC,EAAE;IAClB;EACJ;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIxC,QAAQ,CAAC4C,IAAI,CAAC,IAAI5C,QAAQ,CAAC6C,IAAI,CAAC,EAAE;IAClC;IACAC,KAAK,CAACF,IAAI,EAAEC,IAAI,EAAEF,OAAO,CAAC;EAC9B,CAAC,MAAM;IACHR,MAAM,CAACK,GAAG,CAAC,GAAGR,KAAK,CAACa,IAAI,CAAC;EAC7B;AACJ;AACA,SAASC,KAAKA,CAACX,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EACpC,MAAMI,OAAO,GAAGvD,OAAO,CAACyC,MAAM,CAAC,GAAGA,MAAM,GAAG,CACvCA,MAAM,CACT;EACD,MAAMN,IAAI,GAAGoB,OAAO,CAACxB,MAAM;EAC3B,IAAI,CAACvB,QAAQ,CAACmC,MAAM,CAAC,EAAE;IACnB,OAAOA,MAAM;EACjB;EACAQ,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMK,MAAM,GAAGL,OAAO,CAACK,MAAM,IAAIN,OAAO;EACxC,IAAIO,OAAO;EACX,KAAI,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IACzB6B,OAAO,GAAGF,OAAO,CAAC3B,CAAC,CAAC;IACpB,IAAI,CAACpB,QAAQ,CAACiD,OAAO,CAAC,EAAE;MACpB;IACJ;IACA,MAAM3B,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAAC2B,OAAO,CAAC;IACjC,KAAI,IAAIX,CAAC,GAAG,CAAC,EAAED,IAAI,GAAGf,IAAI,CAACC,MAAM,EAAEe,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAC;MAC7CU,MAAM,CAAC1B,IAAI,CAACgB,CAAC,CAAC,EAAEH,MAAM,EAAEc,OAAO,EAAEN,OAAO,CAAC;IAC7C;EACJ;EACA,OAAOR,MAAM;AACjB;AACA,SAASe,OAAOA,CAACf,MAAM,EAAEF,MAAM,EAAE;EAC7B;EACA,OAAOa,KAAK,CAACX,MAAM,EAAEF,MAAM,EAAE;IACzBe,MAAM,EAAEG;EACZ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AAAI,SAASA,SAASA,CAACX,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAE;EACxC,IAAI,CAACM,UAAU,CAACC,GAAG,CAAC,EAAE;IAClB;EACJ;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIxC,QAAQ,CAAC4C,IAAI,CAAC,IAAI5C,QAAQ,CAAC6C,IAAI,CAAC,EAAE;IAClCK,OAAO,CAACN,IAAI,EAAEC,IAAI,CAAC;EACvB,CAAC,MAAM,IAAI,CAAClD,MAAM,CAACC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAM,EAAEK,GAAG,CAAC,EAAE;IAC3DL,MAAM,CAACK,GAAG,CAAC,GAAGR,KAAK,CAACa,IAAI,CAAC;EAC7B;AACJ;AACA;AACA;AACA;AAAI,SAASQ,WAAWA,CAACC,KAAK,EAAEhE,KAAK,EAAEiE,QAAQ,EAAEN,OAAO,EAAE;EACtD,IAAI3D,KAAK,KAAKC,SAAS,EAAE;IACrBiE,OAAO,CAACC,IAAI,CAACH,KAAK,GAAG,KAAK,GAAGC,QAAQ,GAAG,+BAA+B,GAAGN,OAAO,GAAG,WAAW,CAAC;EACpG;AACJ;AACA;AACA,MAAMS,YAAY,GAAG;EACjB;EACA,EAAE,EAAGC,CAAC,IAAGA,CAAC;EACV;EACAC,CAAC,EAAGC,CAAC,IAAGA,CAAC,CAACD,CAAC;EACXE,CAAC,EAAGD,CAAC,IAAGA,CAAC,CAACC;AACd,CAAC;AACD;AACA;AACA;AAAI,SAASC,SAASA,CAACvB,GAAG,EAAE;EACxB,MAAMwB,KAAK,GAAGxB,GAAG,CAACyB,KAAK,CAAC,GAAG,CAAC;EAC5B,MAAM3C,IAAI,GAAG,EAAE;EACf,IAAI4C,GAAG,GAAG,EAAE;EACZ,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAC;IACrBE,GAAG,IAAIC,IAAI;IACX,IAAID,GAAG,CAACzD,QAAQ,CAAC,IAAI,CAAC,EAAE;MACpByD,GAAG,GAAGA,GAAG,CAACnE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;IAChC,CAAC,MAAM;MACHuB,IAAI,CAAC8C,IAAI,CAACF,GAAG,CAAC;MACdA,GAAG,GAAG,EAAE;IACZ;EACJ;EACA,OAAO5C,IAAI;AACf;AACA,SAAS+C,eAAeA,CAAC7B,GAAG,EAAE;EAC1B,MAAMlB,IAAI,GAAGyC,SAAS,CAACvB,GAAG,CAAC;EAC3B,OAAQ8B,GAAG,IAAG;IACV,KAAK,MAAMhC,CAAC,IAAIhB,IAAI,EAAC;MACjB,IAAIgB,CAAC,KAAK,EAAE,EAAE;QACV;MACJ;MACAgC,GAAG,GAAGA,GAAG,IAAIA,GAAG,CAAChC,CAAC,CAAC;IACvB;IACA,OAAOgC,GAAG;EACd,CAAC;AACL;AACA,SAASC,gBAAgBA,CAACD,GAAG,EAAE9B,GAAG,EAAE;EAChC,MAAMgC,QAAQ,GAAGd,YAAY,CAAClB,GAAG,CAAC,KAAKkB,YAAY,CAAClB,GAAG,CAAC,GAAG6B,eAAe,CAAC7B,GAAG,CAAC,CAAC;EAChF,OAAOgC,QAAQ,CAACF,GAAG,CAAC;AACxB;AACA;AACA;AACA;AAAI,SAASG,WAAWA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAAC3E,KAAK,CAAC,CAAC,CAAC;AACrD;AACA,MAAM8E,OAAO,GAAIvF,KAAK,IAAG,OAAOA,KAAK,KAAK,WAAW;AACrD,MAAMwF,UAAU,GAAIxF,KAAK,IAAG,OAAOA,KAAK,KAAK,UAAU;AACvD;AACA,MAAMyF,SAAS,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAG;EACtB,IAAID,CAAC,CAACE,IAAI,KAAKD,CAAC,CAACC,IAAI,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,KAAK,MAAMC,IAAI,IAAIH,CAAC,EAAC;IACjB,IAAI,CAACC,CAAC,CAACG,GAAG,CAACD,IAAI,CAAC,EAAE;MACd,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AAAI,SAASE,aAAaA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAAC5F,IAAI,KAAK,SAAS,IAAI4F,CAAC,CAAC5F,IAAI,KAAK,OAAO,IAAI4F,CAAC,CAAC5F,IAAI,KAAK,aAAa;AACjF;;AAEA;AACA;AACA;AACA;AAAI,MAAM6F,EAAE,GAAGC,IAAI,CAACD,EAAE;AACtB,MAAME,GAAG,GAAG,CAAC,GAAGF,EAAE;AAClB,MAAMG,KAAK,GAAGD,GAAG,GAAGF,EAAE;AACtB,MAAMI,QAAQ,GAAGzF,MAAM,CAAC0F,iBAAiB;AACzC,MAAMC,WAAW,GAAGN,EAAE,GAAG,GAAG;AAC5B,MAAMO,OAAO,GAAGP,EAAE,GAAG,CAAC;AACtB,MAAMQ,UAAU,GAAGR,EAAE,GAAG,CAAC;AACzB,MAAMS,aAAa,GAAGT,EAAE,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMU,KAAK,GAAGT,IAAI,CAACS,KAAK;AACxB,MAAMC,IAAI,GAAGV,IAAI,CAACU,IAAI;AACtB,SAASC,YAAYA,CAACvC,CAAC,EAAEE,CAAC,EAAEsC,OAAO,EAAE;EACjC,OAAOZ,IAAI,CAACa,GAAG,CAACzC,CAAC,GAAGE,CAAC,CAAC,GAAGsC,OAAO;AACpC;AACA;AACA;AACA;AAAI,SAASE,OAAOA,CAACC,KAAK,EAAE;EACxB,MAAMC,YAAY,GAAGhB,IAAI,CAACiB,KAAK,CAACF,KAAK,CAAC;EACtCA,KAAK,GAAGJ,YAAY,CAACI,KAAK,EAAEC,YAAY,EAAED,KAAK,GAAG,IAAI,CAAC,GAAGC,YAAY,GAAGD,KAAK;EAC9E,MAAMG,SAAS,GAAGlB,IAAI,CAACmB,GAAG,CAAC,EAAE,EAAEnB,IAAI,CAACoB,KAAK,CAACX,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;EACxD,MAAMM,QAAQ,GAAGN,KAAK,GAAGG,SAAS;EAClC,MAAMI,YAAY,GAAGD,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EACnF,OAAOC,YAAY,GAAGJ,SAAS;AACnC;AACA;AACA;AACA;AACA;AAAI,SAASK,UAAUA,CAACzH,KAAK,EAAE;EAC3B,MAAM0H,MAAM,GAAG,EAAE;EACjB,MAAMC,IAAI,GAAGzB,IAAI,CAACyB,IAAI,CAAC3H,KAAK,CAAC;EAC7B,IAAI8B,CAAC;EACL,KAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,IAAI,EAAE7F,CAAC,EAAE,EAAC;IACrB,IAAI9B,KAAK,GAAG8B,CAAC,KAAK,CAAC,EAAE;MACjB4F,MAAM,CAAC5C,IAAI,CAAChD,CAAC,CAAC;MACd4F,MAAM,CAAC5C,IAAI,CAAC9E,KAAK,GAAG8B,CAAC,CAAC;IAC1B;EACJ;EACA,IAAI6F,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;IACrBD,MAAM,CAAC5C,IAAI,CAAC6C,IAAI,CAAC;EACrB;EACAD,MAAM,CAACE,IAAI,CAAC,CAAClC,CAAC,EAAEC,CAAC,KAAGD,CAAC,GAAGC,CAAC,CAAC,CAACkC,GAAG,CAAC,CAAC;EAChC,OAAOH,MAAM;AACjB;AACA;AACA;AACA;AAAI,SAASI,cAAcA,CAACC,CAAC,EAAE;EAC3B,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAI,EAAEC,MAAM,CAACC,WAAW,IAAIF,CAAC,IAAI,UAAU,IAAIA,CAAC,IAAI,SAAS,IAAIA,CAAC,CAAC;AAC1I;AACA,SAASG,QAAQA,CAACH,CAAC,EAAE;EACjB,OAAO,CAACD,cAAc,CAACC,CAAC,CAAC,IAAI,CAACI,KAAK,CAAC/G,UAAU,CAAC2G,CAAC,CAAC,CAAC,IAAIlH,QAAQ,CAACkH,CAAC,CAAC;AACrE;AACA,SAASK,WAAWA,CAAC9D,CAAC,EAAEwC,OAAO,EAAE;EAC7B,MAAMuB,OAAO,GAAGnC,IAAI,CAACiB,KAAK,CAAC7C,CAAC,CAAC;EAC7B,OAAO+D,OAAO,GAAGvB,OAAO,IAAIxC,CAAC,IAAI+D,OAAO,GAAGvB,OAAO,IAAIxC,CAAC;AAC3D;AACA;AACA;AACA;AAAI,SAASgE,kBAAkBA,CAACC,KAAK,EAAE1F,MAAM,EAAE2F,QAAQ,EAAE;EACrD,IAAI1G,CAAC,EAAEO,IAAI,EAAErC,KAAK;EAClB,KAAI8B,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGkG,KAAK,CAACtG,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAEP,CAAC,EAAE,EAAC;IAC1C9B,KAAK,GAAGuI,KAAK,CAACzG,CAAC,CAAC,CAAC0G,QAAQ,CAAC;IAC1B,IAAI,CAACL,KAAK,CAACnI,KAAK,CAAC,EAAE;MACf6C,MAAM,CAAC4F,GAAG,GAAGvC,IAAI,CAACuC,GAAG,CAAC5F,MAAM,CAAC4F,GAAG,EAAEzI,KAAK,CAAC;MACxC6C,MAAM,CAAC6F,GAAG,GAAGxC,IAAI,CAACwC,GAAG,CAAC7F,MAAM,CAAC6F,GAAG,EAAE1I,KAAK,CAAC;IAC5C;EACJ;AACJ;AACA,SAAS2I,SAASA,CAACC,OAAO,EAAE;EACxB,OAAOA,OAAO,IAAI3C,EAAE,GAAG,GAAG,CAAC;AAC/B;AACA,SAAS4C,SAASA,CAACC,OAAO,EAAE;EACxB,OAAOA,OAAO,IAAI,GAAG,GAAG7C,EAAE,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS8C,cAAcA,CAACzE,CAAC,EAAE;EAC3B,IAAI,CAAC3D,cAAc,CAAC2D,CAAC,CAAC,EAAE;IACpB;EACJ;EACA,IAAI0B,CAAC,GAAG,CAAC;EACT,IAAIgD,CAAC,GAAG,CAAC;EACT,OAAM9C,IAAI,CAACiB,KAAK,CAAC7C,CAAC,GAAG0B,CAAC,CAAC,GAAGA,CAAC,KAAK1B,CAAC,EAAC;IAC9B0B,CAAC,IAAI,EAAE;IACPgD,CAAC,EAAE;EACP;EACA,OAAOA,CAAC;AACZ;AACA;AACA,SAASC,iBAAiBA,CAACC,WAAW,EAAEC,UAAU,EAAE;EAChD,MAAMC,mBAAmB,GAAGD,UAAU,CAAC7E,CAAC,GAAG4E,WAAW,CAAC5E,CAAC;EACxD,MAAM+E,mBAAmB,GAAGF,UAAU,CAAC3E,CAAC,GAAG0E,WAAW,CAAC1E,CAAC;EACxD,MAAM8E,wBAAwB,GAAGpD,IAAI,CAACyB,IAAI,CAACyB,mBAAmB,GAAGA,mBAAmB,GAAGC,mBAAmB,GAAGA,mBAAmB,CAAC;EACjI,IAAIE,KAAK,GAAGrD,IAAI,CAACsD,KAAK,CAACH,mBAAmB,EAAED,mBAAmB,CAAC;EAChE,IAAIG,KAAK,GAAG,CAAC,GAAG,GAAGtD,EAAE,EAAE;IACnBsD,KAAK,IAAIpD,GAAG,CAAC,CAAC;EAClB;EACA,OAAO;IACHoD,KAAK;IACLE,QAAQ,EAAEH;EACd,CAAC;AACL;AACA,SAASI,qBAAqBA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACrC,OAAO1D,IAAI,CAACyB,IAAI,CAACzB,IAAI,CAACmB,GAAG,CAACuC,GAAG,CAACtF,CAAC,GAAGqF,GAAG,CAACrF,CAAC,EAAE,CAAC,CAAC,GAAG4B,IAAI,CAACmB,GAAG,CAACuC,GAAG,CAACpF,CAAC,GAAGmF,GAAG,CAACnF,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7E;AACA;AACA;AACA;AACA;AAAI,SAASqF,UAAUA,CAACnE,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAO,CAACD,CAAC,GAAGC,CAAC,GAAGS,KAAK,IAAID,GAAG,GAAGF,EAAE;AACrC;AACA;AACA;AACA;AACA;AAAI,SAAS6D,eAAeA,CAACpE,CAAC,EAAE;EAC5B,OAAO,CAACA,CAAC,GAAGS,GAAG,GAAGA,GAAG,IAAIA,GAAG;AAChC;AACA;AACA;AACA;AAAI,SAAS4D,aAAaA,CAACR,KAAK,EAAES,KAAK,EAAEC,GAAG,EAAEC,qBAAqB,EAAE;EACjE,MAAMxE,CAAC,GAAGoE,eAAe,CAACP,KAAK,CAAC;EAChC,MAAMY,CAAC,GAAGL,eAAe,CAACE,KAAK,CAAC;EAChC,MAAMhE,CAAC,GAAG8D,eAAe,CAACG,GAAG,CAAC;EAC9B,MAAMG,YAAY,GAAGN,eAAe,CAACK,CAAC,GAAGzE,CAAC,CAAC;EAC3C,MAAM2E,UAAU,GAAGP,eAAe,CAAC9D,CAAC,GAAGN,CAAC,CAAC;EACzC,MAAM4E,YAAY,GAAGR,eAAe,CAACpE,CAAC,GAAGyE,CAAC,CAAC;EAC3C,MAAMI,UAAU,GAAGT,eAAe,CAACpE,CAAC,GAAGM,CAAC,CAAC;EACzC,OAAON,CAAC,KAAKyE,CAAC,IAAIzE,CAAC,KAAKM,CAAC,IAAIkE,qBAAqB,IAAIC,CAAC,KAAKnE,CAAC,IAAIoE,YAAY,GAAGC,UAAU,IAAIC,YAAY,GAAGC,UAAU;AAC3H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,WAAWA,CAACxK,KAAK,EAAEyI,GAAG,EAAEC,GAAG,EAAE;EACtC,OAAOxC,IAAI,CAACwC,GAAG,CAACD,GAAG,EAAEvC,IAAI,CAACuC,GAAG,CAACC,GAAG,EAAE1I,KAAK,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AAAI,SAASyK,WAAWA,CAACzK,KAAK,EAAE;EAC5B,OAAOwK,WAAW,CAACxK,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS0K,UAAUA,CAAC1K,KAAK,EAAEgK,KAAK,EAAEC,GAAG,EAAEnD,OAAO,GAAG,IAAI,EAAE;EACvD,OAAO9G,KAAK,IAAIkG,IAAI,CAACuC,GAAG,CAACuB,KAAK,EAAEC,GAAG,CAAC,GAAGnD,OAAO,IAAI9G,KAAK,IAAIkG,IAAI,CAACwC,GAAG,CAACsB,KAAK,EAAEC,GAAG,CAAC,GAAGnD,OAAO;AAC7F;AAEA,SAAS6D,OAAOA,CAACC,KAAK,EAAE5K,KAAK,EAAE6K,GAAG,EAAE;EAChCA,GAAG,GAAGA,GAAG,KAAMpI,KAAK,IAAGmI,KAAK,CAACnI,KAAK,CAAC,GAAGzC,KAAK,CAAC;EAC5C,IAAI8K,EAAE,GAAGF,KAAK,CAAC3I,MAAM,GAAG,CAAC;EACzB,IAAI8I,EAAE,GAAG,CAAC;EACV,IAAIC,GAAG;EACP,OAAMF,EAAE,GAAGC,EAAE,GAAG,CAAC,EAAC;IACdC,GAAG,GAAGD,EAAE,GAAGD,EAAE,IAAI,CAAC;IAClB,IAAID,GAAG,CAACG,GAAG,CAAC,EAAE;MACVD,EAAE,GAAGC,GAAG;IACZ,CAAC,MAAM;MACHF,EAAE,GAAGE,GAAG;IACZ;EACJ;EACA,OAAO;IACHD,EAAE;IACFD;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMG,YAAY,GAAGA,CAACL,KAAK,EAAE1H,GAAG,EAAElD,KAAK,EAAEkL,IAAI,KAAGP,OAAO,CAACC,KAAK,EAAE5K,KAAK,EAAEkL,IAAI,GAAIzI,KAAK,IAAG;EAClF,MAAM0I,EAAE,GAAGP,KAAK,CAACnI,KAAK,CAAC,CAACS,GAAG,CAAC;EAC5B,OAAOiI,EAAE,GAAGnL,KAAK,IAAImL,EAAE,KAAKnL,KAAK,IAAI4K,KAAK,CAACnI,KAAK,GAAG,CAAC,CAAC,CAACS,GAAG,CAAC,KAAKlD,KAAK;AACxE,CAAC,GAAIyC,KAAK,IAAGmI,KAAK,CAACnI,KAAK,CAAC,CAACS,GAAG,CAAC,GAAGlD,KAAK,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMoL,aAAa,GAAGA,CAACR,KAAK,EAAE1H,GAAG,EAAElD,KAAK,KAAG2K,OAAO,CAACC,KAAK,EAAE5K,KAAK,EAAGyC,KAAK,IAAGmI,KAAK,CAACnI,KAAK,CAAC,CAACS,GAAG,CAAC,IAAIlD,KAAK,CAAC;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASqL,cAAcA,CAACC,MAAM,EAAE7C,GAAG,EAAEC,GAAG,EAAE;EAC1C,IAAIsB,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGqB,MAAM,CAACrJ,MAAM;EACvB,OAAM+H,KAAK,GAAGC,GAAG,IAAIqB,MAAM,CAACtB,KAAK,CAAC,GAAGvB,GAAG,EAAC;IACrCuB,KAAK,EAAE;EACX;EACA,OAAMC,GAAG,GAAGD,KAAK,IAAIsB,MAAM,CAACrB,GAAG,GAAG,CAAC,CAAC,GAAGvB,GAAG,EAAC;IACvCuB,GAAG,EAAE;EACT;EACA,OAAOD,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAGqB,MAAM,CAACrJ,MAAM,GAAGqJ,MAAM,CAAC7K,KAAK,CAACuJ,KAAK,EAAEC,GAAG,CAAC,GAAGqB,MAAM;AAC/E;AACA,MAAMC,WAAW,GAAG,CAChB,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,EACR,SAAS,CACZ;AACD,SAASC,iBAAiBA,CAACjD,KAAK,EAAEkD,QAAQ,EAAE;EACxC,IAAIlD,KAAK,CAACmD,QAAQ,EAAE;IAChBnD,KAAK,CAACmD,QAAQ,CAACC,SAAS,CAAC7G,IAAI,CAAC2G,QAAQ,CAAC;IACvC;EACJ;EACApL,MAAM,CAACuL,cAAc,CAACrD,KAAK,EAAE,UAAU,EAAE;IACrCsD,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,KAAK;IACjB9L,KAAK,EAAE;MACH2L,SAAS,EAAE,CACPF,QAAQ;IAEhB;EACJ,CAAC,CAAC;EACFF,WAAW,CAACQ,OAAO,CAAE7I,GAAG,IAAG;IACvB,MAAM8I,MAAM,GAAG,SAAS,GAAG7G,WAAW,CAACjC,GAAG,CAAC;IAC3C,MAAM+I,IAAI,GAAG1D,KAAK,CAACrF,GAAG,CAAC;IACvB7C,MAAM,CAACuL,cAAc,CAACrD,KAAK,EAAErF,GAAG,EAAE;MAC9B2I,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjB9L,KAAKA,CAAE,GAAGwB,IAAI,EAAE;QACZ,MAAM0K,GAAG,GAAGD,IAAI,CAACvK,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;QAClC+G,KAAK,CAACmD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAEI,MAAM,IAAG;UACvC,IAAI,OAAOA,MAAM,CAACH,MAAM,CAAC,KAAK,UAAU,EAAE;YACtCG,MAAM,CAACH,MAAM,CAAC,CAAC,GAAGxK,IAAI,CAAC;UAC3B;QACJ,CAAC,CAAC;QACF,OAAO0K,GAAG;MACd;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASE,mBAAmBA,CAAC7D,KAAK,EAAEkD,QAAQ,EAAE;EAC1C,MAAMY,IAAI,GAAG9D,KAAK,CAACmD,QAAQ;EAC3B,IAAI,CAACW,IAAI,EAAE;IACP;EACJ;EACA,MAAMV,SAAS,GAAGU,IAAI,CAACV,SAAS;EAChC,MAAMlJ,KAAK,GAAGkJ,SAAS,CAACxI,OAAO,CAACsI,QAAQ,CAAC;EACzC,IAAIhJ,KAAK,KAAK,CAAC,CAAC,EAAE;IACdkJ,SAAS,CAACW,MAAM,CAAC7J,KAAK,EAAE,CAAC,CAAC;EAC9B;EACA,IAAIkJ,SAAS,CAAC1J,MAAM,GAAG,CAAC,EAAE;IACtB;EACJ;EACAsJ,WAAW,CAACQ,OAAO,CAAE7I,GAAG,IAAG;IACvB,OAAOqF,KAAK,CAACrF,GAAG,CAAC;EACrB,CAAC,CAAC;EACF,OAAOqF,KAAK,CAACmD,QAAQ;AACzB;AACA;AACA;AACA;AAAI,SAASa,YAAYA,CAACC,KAAK,EAAE;EAC7B,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACF,KAAK,CAAC;EAC1B,IAAIC,GAAG,CAAC7G,IAAI,KAAK4G,KAAK,CAACvK,MAAM,EAAE;IAC3B,OAAOuK,KAAK;EAChB;EACA,OAAOrM,KAAK,CAACwM,IAAI,CAACF,GAAG,CAAC;AAC1B;AAEA,SAASG,UAAUA,CAACC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAClD,OAAOD,SAAS,GAAG,GAAG,GAAGD,SAAS,GAAG,KAAK,GAAGE,UAAU;AAC3D;AACA;AACA;AACA;AAAG,MAAMC,gBAAgB,GAAG,YAAW;EACnC,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAO,UAAS3L,QAAQ,EAAE;MACtB,OAAOA,QAAQ,CAAC,CAAC;IACrB,CAAC;EACL;EACA,OAAO2L,MAAM,CAACC,qBAAqB;AACvC,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AAAI,SAASC,SAASA,CAAC5L,EAAE,EAAEE,OAAO,EAAE;EAChC,IAAI2L,SAAS,GAAG,EAAE;EAClB,IAAIC,OAAO,GAAG,KAAK;EACnB,OAAO,UAAS,GAAG7L,IAAI,EAAE;IACrB;IACA4L,SAAS,GAAG5L,IAAI;IAChB,IAAI,CAAC6L,OAAO,EAAE;MACVA,OAAO,GAAG,IAAI;MACdL,gBAAgB,CAACxM,IAAI,CAACyM,MAAM,EAAE,MAAI;QAC9BI,OAAO,GAAG,KAAK;QACf9L,EAAE,CAACG,KAAK,CAACD,OAAO,EAAE2L,SAAS,CAAC;MAChC,CAAC,CAAC;IACN;EACJ,CAAC;AACL;AACA;AACA;AACA;AAAI,SAASE,QAAQA,CAAC/L,EAAE,EAAEgM,KAAK,EAAE;EAC7B,IAAIC,OAAO;EACX,OAAO,UAAS,GAAGhM,IAAI,EAAE;IACrB,IAAI+L,KAAK,EAAE;MACPE,YAAY,CAACD,OAAO,CAAC;MACrBA,OAAO,GAAGE,UAAU,CAACnM,EAAE,EAAEgM,KAAK,EAAE/L,IAAI,CAAC;IACzC,CAAC,MAAM;MACHD,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IACxB;IACA,OAAO+L,KAAK;EAChB,CAAC;AACL;AACA;AACA;AACA;AACA;AAAI,MAAMI,kBAAkB,GAAIC,KAAK,IAAGA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAGA,KAAK,KAAK,KAAK,GAAG,OAAO,GAAG,QAAQ;AACzG;AACA;AACA;AACA;AAAI,MAAMC,cAAc,GAAGA,CAACD,KAAK,EAAE5D,KAAK,EAAEC,GAAG,KAAG2D,KAAK,KAAK,OAAO,GAAG5D,KAAK,GAAG4D,KAAK,KAAK,KAAK,GAAG3D,GAAG,GAAG,CAACD,KAAK,GAAGC,GAAG,IAAI,CAAC;AACrH;AACA;AACA;AACA;AAAI,MAAM6D,MAAM,GAAGA,CAACF,KAAK,EAAEG,IAAI,EAAEC,KAAK,EAAEC,GAAG,KAAG;EAC1C,MAAMC,KAAK,GAAGD,GAAG,GAAG,MAAM,GAAG,OAAO;EACpC,OAAOL,KAAK,KAAKM,KAAK,GAAGF,KAAK,GAAGJ,KAAK,KAAK,QAAQ,GAAG,CAACG,IAAI,GAAGC,KAAK,IAAI,CAAC,GAAGD,IAAI;AACnF,CAAC;AACD;AACA;AACA;AACA;AAAI,SAASI,gCAAgCA,CAACC,IAAI,EAAEC,MAAM,EAAEC,kBAAkB,EAAE;EAC5E,MAAMC,UAAU,GAAGF,MAAM,CAACpM,MAAM;EAChC,IAAI+H,KAAK,GAAG,CAAC;EACb,IAAIwE,KAAK,GAAGD,UAAU;EACtB,IAAIH,IAAI,CAACK,OAAO,EAAE;IACd,MAAM;MAAEC,MAAM;MAAGC,MAAM;MAAGC;IAAS,CAAC,GAAGR,IAAI;IAC3C,MAAMS,QAAQ,GAAGT,IAAI,CAACU,OAAO,GAAGV,IAAI,CAACU,OAAO,CAACzL,OAAO,GAAG+K,IAAI,CAACU,OAAO,CAACzL,OAAO,CAACwL,QAAQ,GAAG,IAAI,GAAG,IAAI;IAClG,MAAME,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAM;MAAEtG,GAAG;MAAGC,GAAG;MAAGsG,UAAU;MAAGC;IAAY,CAAC,GAAGP,MAAM,CAACQ,aAAa,CAAC,CAAC;IACvE,IAAIF,UAAU,EAAE;MACZhF,KAAK,GAAG9D,IAAI,CAACuC,GAAG;MAAC;MACjBwC,YAAY,CAAC2D,OAAO,EAAEG,IAAI,EAAEtG,GAAG,CAAC,CAACsC,EAAE;MAAE;MACrCuD,kBAAkB,GAAGC,UAAU,GAAGtD,YAAY,CAACoD,MAAM,EAAEU,IAAI,EAAEL,MAAM,CAACS,gBAAgB,CAAC1G,GAAG,CAAC,CAAC,CAACsC,EAAE,CAAC;MAC9F,IAAI8D,QAAQ,EAAE;QACV,MAAMO,mBAAmB,GAAGR,OAAO,CAACnO,KAAK,CAAC,CAAC,EAAEuJ,KAAK,GAAG,CAAC,CAAC,CAACnI,OAAO,CAAC,CAAC,CAACwN,SAAS,CAAEC,KAAK,IAAG,CAACvP,aAAa,CAACuP,KAAK,CAACX,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC;QACxH/E,KAAK,IAAI9D,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE0G,mBAAmB,CAAC;MAC7C;MACApF,KAAK,GAAGQ,WAAW,CAACR,KAAK,EAAE,CAAC,EAAEuE,UAAU,GAAG,CAAC,CAAC;IACjD;IACA,IAAIU,UAAU,EAAE;MACZ,IAAIhF,GAAG,GAAG/D,IAAI,CAACwC,GAAG;MAAC;MACnBuC,YAAY,CAAC2D,OAAO,EAAEF,MAAM,CAACK,IAAI,EAAErG,GAAG,EAAE,IAAI,CAAC,CAACoC,EAAE,GAAG,CAAC;MAAE;MACtDwD,kBAAkB,GAAG,CAAC,GAAGrD,YAAY,CAACoD,MAAM,EAAEU,IAAI,EAAEL,MAAM,CAACS,gBAAgB,CAACzG,GAAG,CAAC,EAAE,IAAI,CAAC,CAACoC,EAAE,GAAG,CAAC,CAAC;MAC/F,IAAI+D,QAAQ,EAAE;QACV,MAAMU,mBAAmB,GAAGX,OAAO,CAACnO,KAAK,CAACwJ,GAAG,GAAG,CAAC,CAAC,CAACoF,SAAS,CAAEC,KAAK,IAAG,CAACvP,aAAa,CAACuP,KAAK,CAACX,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC;QACzG9E,GAAG,IAAI/D,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE6G,mBAAmB,CAAC;MAC3C;MACAf,KAAK,GAAGhE,WAAW,CAACP,GAAG,EAAED,KAAK,EAAEuE,UAAU,CAAC,GAAGvE,KAAK;IACvD,CAAC,MAAM;MACHwE,KAAK,GAAGD,UAAU,GAAGvE,KAAK;IAC9B;EACJ;EACA,OAAO;IACHA,KAAK;IACLwE;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASgB,mBAAmBA,CAACpB,IAAI,EAAE;EACnC,MAAM;IAAEqB,MAAM;IAAGC,MAAM;IAAGC;EAAc,CAAC,GAAGvB,IAAI;EAChD,MAAMwB,SAAS,GAAG;IACdC,IAAI,EAAEJ,MAAM,CAAChH,GAAG;IAChBqH,IAAI,EAAEL,MAAM,CAAC/G,GAAG;IAChBqH,IAAI,EAAEL,MAAM,CAACjH,GAAG;IAChBuH,IAAI,EAAEN,MAAM,CAAChH;EACjB,CAAC;EACD,IAAI,CAACiH,YAAY,EAAE;IACfvB,IAAI,CAACuB,YAAY,GAAGC,SAAS;IAC7B,OAAO,IAAI;EACf;EACA,MAAMK,OAAO,GAAGN,YAAY,CAACE,IAAI,KAAKJ,MAAM,CAAChH,GAAG,IAAIkH,YAAY,CAACG,IAAI,KAAKL,MAAM,CAAC/G,GAAG,IAAIiH,YAAY,CAACI,IAAI,KAAKL,MAAM,CAACjH,GAAG,IAAIkH,YAAY,CAACK,IAAI,KAAKN,MAAM,CAAChH,GAAG;EAC5JrI,MAAM,CAAC6P,MAAM,CAACP,YAAY,EAAEC,SAAS,CAAC;EACtC,OAAOK,OAAO;AAClB;AAEA,MAAME,MAAM,GAAIC,CAAC,IAAGA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC;AACtC,MAAMC,SAAS,GAAGA,CAACD,CAAC,EAAEjG,CAAC,EAAEnB,CAAC,KAAG,EAAE9C,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI+I,CAAC,IAAI,CAAC,CAAC,CAAC,GAAGlK,IAAI,CAACoK,GAAG,CAAC,CAACF,CAAC,GAAGjG,CAAC,IAAIhE,GAAG,GAAG6C,CAAC,CAAC,CAAC;AACxF,MAAMuH,UAAU,GAAGA,CAACH,CAAC,EAAEjG,CAAC,EAAEnB,CAAC,KAAG9C,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG+I,CAAC,CAAC,GAAGlK,IAAI,CAACoK,GAAG,CAAC,CAACF,CAAC,GAAGjG,CAAC,IAAIhE,GAAG,GAAG6C,CAAC,CAAC,GAAG,CAAC;AACpF;AACA;AACA;AACA;AACA;AAAI,MAAMwH,OAAO,GAAG;EAChBC,MAAM,EAAGL,CAAC,IAAGA,CAAC;EACdM,UAAU,EAAGN,CAAC,IAAGA,CAAC,GAAGA,CAAC;EACtBO,WAAW,EAAGP,CAAC,IAAG,CAACA,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;EAC9BQ,aAAa,EAAGR,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAEA,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7ES,WAAW,EAAGT,CAAC,IAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC3BU,YAAY,EAAGV,CAAC,IAAG,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC;EACvCW,cAAc,EAAGX,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACpFY,WAAW,EAAGZ,CAAC,IAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC/Ba,YAAY,EAAGb,CAAC,IAAG,EAAE,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC9Cc,cAAc,EAAGd,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC7Fe,WAAW,EAAGf,CAAC,IAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EACnCgB,YAAY,EAAGhB,CAAC,IAAG,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;EAC/CiB,cAAc,EAAGjB,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACpGkB,UAAU,EAAGlB,CAAC,IAAG,CAAClK,IAAI,CAACqL,GAAG,CAACnB,CAAC,GAAG5J,OAAO,CAAC,GAAG,CAAC;EAC3CgL,WAAW,EAAGpB,CAAC,IAAGlK,IAAI,CAACoK,GAAG,CAACF,CAAC,GAAG5J,OAAO,CAAC;EACvCiL,aAAa,EAAGrB,CAAC,IAAG,CAAC,GAAG,IAAIlK,IAAI,CAACqL,GAAG,CAACtL,EAAE,GAAGmK,CAAC,CAAC,GAAG,CAAC,CAAC;EACjDsB,UAAU,EAAGtB,CAAC,IAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGlK,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI+I,CAAC,GAAG,CAAC,CAAC,CAAC;EACxDuB,WAAW,EAAGvB,CAAC,IAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAClK,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG+I,CAAC,CAAC,GAAG,CAAC;EACzDwB,aAAa,EAAGxB,CAAC,IAAGD,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGlK,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI+I,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAClK,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI+I,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC/HyB,UAAU,EAAGzB,CAAC,IAAGA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,EAAElK,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAGyI,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC;EACzD0B,WAAW,EAAG1B,CAAC,IAAGlK,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG,CAACyI,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC;EAC7C2B,aAAa,EAAG3B,CAAC,IAAG,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAIlK,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAGyI,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAIlK,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG,CAACyI,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,CAAC;EAChH4B,aAAa,EAAG5B,CAAC,IAAGD,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGC,SAAS,CAACD,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC5D6B,cAAc,EAAG7B,CAAC,IAAGD,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGG,UAAU,CAACH,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC9D8B,gBAAgBA,CAAE9B,CAAC,EAAE;IACjB,MAAMjG,CAAC,GAAG,MAAM;IAChB,MAAMnB,CAAC,GAAG,IAAI;IACd,OAAOmH,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGC,SAAS,CAACD,CAAC,GAAG,CAAC,EAAEjG,CAAC,EAAEnB,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGuH,UAAU,CAACH,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEjG,CAAC,EAAEnB,CAAC,CAAC;EAC3G,CAAC;EACDmJ,UAAUA,CAAE/B,CAAC,EAAE;IACX,MAAMjG,CAAC,GAAG,OAAO;IACjB,OAAOiG,CAAC,GAAGA,CAAC,IAAI,CAACjG,CAAC,GAAG,CAAC,IAAIiG,CAAC,GAAGjG,CAAC,CAAC;EACpC,CAAC;EACDiI,WAAWA,CAAEhC,CAAC,EAAE;IACZ,MAAMjG,CAAC,GAAG,OAAO;IACjB,OAAO,CAACiG,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAACjG,CAAC,GAAG,CAAC,IAAIiG,CAAC,GAAGjG,CAAC,CAAC,GAAG,CAAC;EAC/C,CAAC;EACDkI,aAAaA,CAAEjC,CAAC,EAAE;IACd,IAAIjG,CAAC,GAAG,OAAO;IACf,IAAI,CAACiG,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;MAChB,OAAO,GAAG,IAAIA,CAAC,GAAGA,CAAC,IAAI,CAAC,CAACjG,CAAC,IAAI,KAAK,IAAI,CAAC,IAAIiG,CAAC,GAAGjG,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,GAAG,IAAI,CAACiG,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAACjG,CAAC,IAAI,KAAK,IAAI,CAAC,IAAIiG,CAAC,GAAGjG,CAAC,CAAC,GAAG,CAAC,CAAC;EAClE,CAAC;EACDmI,YAAY,EAAGlC,CAAC,IAAG,CAAC,GAAGI,OAAO,CAAC+B,aAAa,CAAC,CAAC,GAAGnC,CAAC,CAAC;EACnDmC,aAAaA,CAAEnC,CAAC,EAAE;IACd,MAAMoC,CAAC,GAAG,MAAM;IAChB,MAAMC,CAAC,GAAG,IAAI;IACd,IAAIrC,CAAC,GAAG,CAAC,GAAGqC,CAAC,EAAE;MACX,OAAOD,CAAC,GAAGpC,CAAC,GAAGA,CAAC;IACpB;IACA,IAAIA,CAAC,GAAG,CAAC,GAAGqC,CAAC,EAAE;MACX,OAAOD,CAAC,IAAIpC,CAAC,IAAI,GAAG,GAAGqC,CAAC,CAAC,GAAGrC,CAAC,GAAG,IAAI;IACxC;IACA,IAAIA,CAAC,GAAG,GAAG,GAAGqC,CAAC,EAAE;MACb,OAAOD,CAAC,IAAIpC,CAAC,IAAI,IAAI,GAAGqC,CAAC,CAAC,GAAGrC,CAAC,GAAG,MAAM;IAC3C;IACA,OAAOoC,CAAC,IAAIpC,CAAC,IAAI,KAAK,GAAGqC,CAAC,CAAC,GAAGrC,CAAC,GAAG,QAAQ;EAC9C,CAAC;EACDsC,eAAe,EAAGtC,CAAC,IAAGA,CAAC,GAAG,GAAG,GAAGI,OAAO,CAAC8B,YAAY,CAAClC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGI,OAAO,CAAC+B,aAAa,CAACnC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG;AACjH,CAAC;AAED,SAASuC,mBAAmBA,CAAC3S,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpC,MAAMI,IAAI,GAAGJ,KAAK,CAACO,QAAQ,CAAC,CAAC;IAC7B,OAAOH,IAAI,KAAK,wBAAwB,IAAIA,IAAI,KAAK,yBAAyB;EAClF;EACA,OAAO,KAAK;AAChB;AACA,SAASwS,KAAKA,CAAC5S,KAAK,EAAE;EAClB,OAAO2S,mBAAmB,CAAC3S,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,CAAC;AAChE;AACA,SAAS6S,aAAaA,CAAC7S,KAAK,EAAE;EAC1B,OAAO2S,mBAAmB,CAAC3S,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,CAAC,CAAC8S,QAAQ,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,SAAS,CAAC,CAAC;AACtG;AAEA,MAAMC,OAAO,GAAG,CACZ,GAAG,EACH,GAAG,EACH,aAAa,EACb,QAAQ,EACR,SAAS,CACZ;AACD,MAAMC,MAAM,GAAG,CACX,OAAO,EACP,aAAa,EACb,iBAAiB,CACpB;AACD,SAASC,uBAAuBA,CAACC,QAAQ,EAAE;EACvCA,QAAQ,CAAC3G,GAAG,CAAC,WAAW,EAAE;IACtBc,KAAK,EAAEtN,SAAS;IAChBoT,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,cAAc;IACtB/R,EAAE,EAAEtB,SAAS;IACb0M,IAAI,EAAE1M,SAAS;IACfsT,IAAI,EAAEtT,SAAS;IACfuT,EAAE,EAAEvT,SAAS;IACbG,IAAI,EAAEH;EACV,CAAC,CAAC;EACFmT,QAAQ,CAACK,QAAQ,CAAC,WAAW,EAAE;IAC3BC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAGC,IAAI,IAAGA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK;EACpF,CAAC,CAAC;EACFT,QAAQ,CAAC3G,GAAG,CAAC,YAAY,EAAE;IACvByG,MAAM,EAAE;MACJ9S,IAAI,EAAE,OAAO;MACb0T,UAAU,EAAEZ;IAChB,CAAC;IACDD,OAAO,EAAE;MACL7S,IAAI,EAAE,QAAQ;MACd0T,UAAU,EAAEb;IAChB;EACJ,CAAC,CAAC;EACFG,QAAQ,CAACK,QAAQ,CAAC,YAAY,EAAE;IAC5BC,SAAS,EAAE;EACf,CAAC,CAAC;EACFN,QAAQ,CAAC3G,GAAG,CAAC,aAAa,EAAE;IACxBsH,MAAM,EAAE;MACJC,SAAS,EAAE;QACPX,QAAQ,EAAE;MACd;IACJ,CAAC;IACDY,MAAM,EAAE;MACJD,SAAS,EAAE;QACPX,QAAQ,EAAE;MACd;IACJ,CAAC;IACDa,IAAI,EAAE;MACFC,UAAU,EAAE;QACRjB,MAAM,EAAE;UACJvG,IAAI,EAAE;QACV,CAAC;QACDyH,OAAO,EAAE;UACLhU,IAAI,EAAE,SAAS;UACfiT,QAAQ,EAAE;QACd;MACJ;IACJ,CAAC;IACDgB,IAAI,EAAE;MACFF,UAAU,EAAE;QACRjB,MAAM,EAAE;UACJM,EAAE,EAAE;QACR,CAAC;QACDY,OAAO,EAAE;UACLhU,IAAI,EAAE,SAAS;UACfkT,MAAM,EAAE,QAAQ;UAChB/R,EAAE,EAAG8C,CAAC,IAAGA,CAAC,GAAG;QACjB;MACJ;IACJ;EACJ,CAAC,CAAC;AACN;AAEA,SAASiQ,oBAAoBA,CAAClB,QAAQ,EAAE;EACpCA,QAAQ,CAAC3G,GAAG,CAAC,QAAQ,EAAE;IACnB8H,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE;MACLC,GAAG,EAAE,CAAC;MACNzG,KAAK,EAAE,CAAC;MACR0G,MAAM,EAAE,CAAC;MACT3G,IAAI,EAAE;IACV;EACJ,CAAC,CAAC;AACN;AAEA,MAAM4G,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,SAASC,eAAeA,CAACC,MAAM,EAAEzR,OAAO,EAAE;EACtCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAM0R,QAAQ,GAAGD,MAAM,GAAGE,IAAI,CAACC,SAAS,CAAC5R,OAAO,CAAC;EACjD,IAAI6R,SAAS,GAAGP,SAAS,CAACQ,GAAG,CAACJ,QAAQ,CAAC;EACvC,IAAI,CAACG,SAAS,EAAE;IACZA,SAAS,GAAG,IAAIE,IAAI,CAACC,YAAY,CAACP,MAAM,EAAEzR,OAAO,CAAC;IAClDsR,SAAS,CAAClI,GAAG,CAACsI,QAAQ,EAAEG,SAAS,CAAC;EACtC;EACA,OAAOA,SAAS;AACpB;AACA,SAASI,YAAYA,CAACC,GAAG,EAAET,MAAM,EAAEzR,OAAO,EAAE;EACxC,OAAOwR,eAAe,CAACC,MAAM,EAAEzR,OAAO,CAAC,CAACmS,MAAM,CAACD,GAAG,CAAC;AACvD;AAEA,MAAME,UAAU,GAAG;EAClBnK,MAAMA,CAAEtL,KAAK,EAAE;IACR,OAAOE,OAAO,CAACF,KAAK,CAAC,GAAIA,KAAK,GAAG,EAAE,GAAGA,KAAK;EAC/C,CAAC;EACJ0V,OAAOA,CAAEC,SAAS,EAAElT,KAAK,EAAEmT,KAAK,EAAE;IAC3B,IAAID,SAAS,KAAK,CAAC,EAAE;MACjB,OAAO,GAAG;IACd;IACA,MAAMb,MAAM,GAAG,IAAI,CAACe,KAAK,CAACxS,OAAO,CAACyR,MAAM;IACxC,IAAIgB,QAAQ;IACZ,IAAIC,KAAK,GAAGJ,SAAS;IACrB,IAAIC,KAAK,CAAC3T,MAAM,GAAG,CAAC,EAAE;MAClB,MAAM+T,OAAO,GAAG9P,IAAI,CAACwC,GAAG,CAACxC,IAAI,CAACa,GAAG,CAAC6O,KAAK,CAAC,CAAC,CAAC,CAAC5V,KAAK,CAAC,EAAEkG,IAAI,CAACa,GAAG,CAAC6O,KAAK,CAACA,KAAK,CAAC3T,MAAM,GAAG,CAAC,CAAC,CAACjC,KAAK,CAAC,CAAC;MAC3F,IAAIgW,OAAO,GAAG,IAAI,IAAIA,OAAO,GAAG,KAAK,EAAE;QACnCF,QAAQ,GAAG,YAAY;MAC3B;MACAC,KAAK,GAAGE,cAAc,CAACN,SAAS,EAAEC,KAAK,CAAC;IAC5C;IACA,MAAMM,QAAQ,GAAGvP,KAAK,CAACT,IAAI,CAACa,GAAG,CAACgP,KAAK,CAAC,CAAC;IACvC,MAAMI,UAAU,GAAGhO,KAAK,CAAC+N,QAAQ,CAAC,GAAG,CAAC,GAAGhQ,IAAI,CAACwC,GAAG,CAACxC,IAAI,CAACuC,GAAG,CAAC,CAAC,CAAC,GAAGvC,IAAI,CAACoB,KAAK,CAAC4O,QAAQ,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7F,MAAM7S,OAAO,GAAG;MACZyS,QAAQ;MACRM,qBAAqB,EAAED,UAAU;MACjCE,qBAAqB,EAAEF;IAC3B,CAAC;IACD9V,MAAM,CAAC6P,MAAM,CAAC7M,OAAO,EAAE,IAAI,CAACA,OAAO,CAACuS,KAAK,CAACJ,MAAM,CAAC;IACjD,OAAOF,YAAY,CAACK,SAAS,EAAEb,MAAM,EAAEzR,OAAO,CAAC;EACnD,CAAC;EACJiT,WAAWA,CAAEX,SAAS,EAAElT,KAAK,EAAEmT,KAAK,EAAE;IAC/B,IAAID,SAAS,KAAK,CAAC,EAAE;MACjB,OAAO,GAAG;IACd;IACA,MAAMY,MAAM,GAAGX,KAAK,CAACnT,KAAK,CAAC,CAAC+T,WAAW,IAAIb,SAAS,GAAGzP,IAAI,CAACmB,GAAG,CAAC,EAAE,EAAEnB,IAAI,CAACoB,KAAK,CAACX,KAAK,CAACgP,SAAS,CAAC,CAAC,CAAC;IACjG,IAAI,CACA,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,EAAE,EACF,EAAE,CACL,CAACc,QAAQ,CAACF,MAAM,CAAC,IAAI9T,KAAK,GAAG,GAAG,GAAGmT,KAAK,CAAC3T,MAAM,EAAE;MAC9C,OAAOwT,UAAU,CAACC,OAAO,CAAClV,IAAI,CAAC,IAAI,EAAEmV,SAAS,EAAElT,KAAK,EAAEmT,KAAK,CAAC;IACjE;IACA,OAAO,EAAE;EACb;AACJ,CAAC;AACD,SAASK,cAAcA,CAACN,SAAS,EAAEC,KAAK,EAAE;EACtC,IAAIG,KAAK,GAAGH,KAAK,CAAC3T,MAAM,GAAG,CAAC,GAAG2T,KAAK,CAAC,CAAC,CAAC,CAAC5V,KAAK,GAAG4V,KAAK,CAAC,CAAC,CAAC,CAAC5V,KAAK,GAAG4V,KAAK,CAAC,CAAC,CAAC,CAAC5V,KAAK,GAAG4V,KAAK,CAAC,CAAC,CAAC,CAAC5V,KAAK;EAChG,IAAIkG,IAAI,CAACa,GAAG,CAACgP,KAAK,CAAC,IAAI,CAAC,IAAIJ,SAAS,KAAKzP,IAAI,CAACoB,KAAK,CAACqO,SAAS,CAAC,EAAE;IAC7DI,KAAK,GAAGJ,SAAS,GAAGzP,IAAI,CAACoB,KAAK,CAACqO,SAAS,CAAC;EAC7C;EACA,OAAOI,KAAK;AAChB;AACC,IAAIW,KAAK,GAAG;EACTjB;AACJ,CAAC;AAED,SAASkB,kBAAkBA,CAACvD,QAAQ,EAAE;EAClCA,QAAQ,CAAC3G,GAAG,CAAC,OAAO,EAAE;IAClBmK,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,KAAK;IACbhV,OAAO,EAAE,KAAK;IACdiV,WAAW,EAAE,KAAK;IACzBC,MAAM,EAAE,OAAO;IACRC,IAAI,EAAE,IAAI;IACjBC,KAAK,EAAE,CAAC;IACDC,IAAI,EAAE;MACFN,OAAO,EAAE,IAAI;MACbO,SAAS,EAAE,CAAC;MACZC,eAAe,EAAE,IAAI;MACrBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAEA,CAACC,IAAI,EAAEnU,OAAO,KAAGA,OAAO,CAAC8T,SAAS;MAC7CM,SAAS,EAAEA,CAACD,IAAI,EAAEnU,OAAO,KAAGA,OAAO,CAACuP,KAAK;MACzCiE,MAAM,EAAE;IACZ,CAAC;IACDa,MAAM,EAAE;MACJd,OAAO,EAAE,IAAI;MACbe,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACHlB,OAAO,EAAE,KAAK;MACdmB,IAAI,EAAE,EAAE;MACRvD,OAAO,EAAE;QACLC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE;MACZ;IACJ,CAAC;IACDkB,KAAK,EAAE;MACHoC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,KAAK;MACbC,eAAe,EAAE,CAAC;MAClBC,eAAe,EAAE,EAAE;MACnB5D,OAAO,EAAE,CAAC;MACVoC,OAAO,EAAE,IAAI;MACbyB,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,CAAC;MAClBC,WAAW,EAAE,CAAC;MACdjX,QAAQ,EAAEoV,KAAK,CAACjB,UAAU,CAACnK,MAAM;MACjCkN,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE,CAAC,CAAC;MACT7K,KAAK,EAAE,QAAQ;MACf8K,UAAU,EAAE,MAAM;MAClBC,iBAAiB,EAAE,KAAK;MACxBC,aAAa,EAAE,2BAA2B;MAC1CC,eAAe,EAAE;IACrB;EACJ,CAAC,CAAC;EACFzF,QAAQ,CAAC0F,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC;EACnD1F,QAAQ,CAAC0F,KAAK,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,EAAE,aAAa,CAAC;EACxD1F,QAAQ,CAAC0F,KAAK,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE,EAAE,aAAa,CAAC;EAC1D1F,QAAQ,CAAC0F,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC;EACnD1F,QAAQ,CAACK,QAAQ,CAAC,OAAO,EAAE;IACvBC,SAAS,EAAE,KAAK;IAChBE,WAAW,EAAGC,IAAI,IAAG,CAACA,IAAI,CAACkF,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAClF,IAAI,CAACkF,UAAU,CAAC,OAAO,CAAC,IAAIlF,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,QAAQ;IACxHF,UAAU,EAAGE,IAAI,IAAGA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,gBAAgB,IAAIA,IAAI,KAAK;EACvF,CAAC,CAAC;EACFT,QAAQ,CAACK,QAAQ,CAAC,QAAQ,EAAE;IACxBC,SAAS,EAAE;EACf,CAAC,CAAC;EACFN,QAAQ,CAACK,QAAQ,CAAC,aAAa,EAAE;IAC7BG,WAAW,EAAGC,IAAI,IAAGA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,UAAU;IACtEF,UAAU,EAAGE,IAAI,IAAGA,IAAI,KAAK;EACjC,CAAC,CAAC;AACN;AAEA,MAAMmF,SAAS,GAAG3Y,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;AACrC,MAAMmW,WAAW,GAAG5Y,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;AACtC,SAASoW,UAAUA,CAACC,IAAI,EAAEjW,GAAG,EAAE;EAC5B,IAAI,CAACA,GAAG,EAAE;IACN,OAAOiW,IAAI;EACf;EACA,MAAMnX,IAAI,GAAGkB,GAAG,CAACyB,KAAK,CAAC,GAAG,CAAC;EAC3B,KAAI,IAAI7C,CAAC,GAAG,CAAC,EAAEiG,CAAC,GAAG/F,IAAI,CAACC,MAAM,EAAEH,CAAC,GAAGiG,CAAC,EAAE,EAAEjG,CAAC,EAAC;IACvC,MAAMkB,CAAC,GAAGhB,IAAI,CAACF,CAAC,CAAC;IACjBqX,IAAI,GAAGA,IAAI,CAACnW,CAAC,CAAC,KAAKmW,IAAI,CAACnW,CAAC,CAAC,GAAG3C,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC,CAAC;EACrD;EACA,OAAOqW,IAAI;AACf;AACA,SAAS1M,GAAGA,CAAC2M,IAAI,EAAEpV,KAAK,EAAEsH,MAAM,EAAE;EAC9B,IAAI,OAAOtH,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOR,KAAK,CAAC0V,UAAU,CAACE,IAAI,EAAEpV,KAAK,CAAC,EAAEsH,MAAM,CAAC;EACjD;EACA,OAAO9H,KAAK,CAAC0V,UAAU,CAACE,IAAI,EAAE,EAAE,CAAC,EAAEpV,KAAK,CAAC;AAC7C;AACC,MAAMqV,QAAQ,CAAC;EACZC,WAAWA,CAACC,YAAY,EAAEC,SAAS,EAAC;IAChC,IAAI,CAACxF,SAAS,GAAG/T,SAAS;IAC1B,IAAI,CAACwZ,eAAe,GAAG,iBAAiB;IACxC,IAAI,CAACC,WAAW,GAAG,iBAAiB;IACpC,IAAI,CAAC9G,KAAK,GAAG,MAAM;IACnB,IAAI,CAAC+G,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,gBAAgB,GAAIC,OAAO,IAAGA,OAAO,CAAChE,KAAK,CAACiE,QAAQ,CAACC,mBAAmB,CAAC,CAAC;IAC/E,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,MAAM,GAAG,CACV,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EACZ,WAAW,CACd;IACD,IAAI,CAACC,IAAI,GAAG;MACRC,MAAM,EAAE,oDAAoD;MAC5DvU,IAAI,EAAE,EAAE;MACRwU,KAAK,EAAE,QAAQ;MACfC,UAAU,EAAE,GAAG;MACfC,MAAM,EAAE;IACZ,CAAC;IACD,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,GAAG,CAACC,GAAG,EAAEpX,OAAO,KAAGwP,aAAa,CAACxP,OAAO,CAACoW,eAAe,CAAC;IAClF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,GAAG,EAAEpX,OAAO,KAAGwP,aAAa,CAACxP,OAAO,CAACqW,WAAW,CAAC;IAC1E,IAAI,CAACiB,UAAU,GAAG,CAACF,GAAG,EAAEpX,OAAO,KAAGwP,aAAa,CAACxP,OAAO,CAACuP,KAAK,CAAC;IAC9D,IAAI,CAACgI,SAAS,GAAG,GAAG;IACpB,IAAI,CAACC,WAAW,GAAG;MACfC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;IACtB,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAGtb,SAAS;IACtB,IAAI,CAACub,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACjI,QAAQ,CAAC8F,YAAY,CAAC;IAC3B,IAAI,CAAC7X,KAAK,CAAC8X,SAAS,CAAC;EACzB;EACH/M,GAAGA,CAACzI,KAAK,EAAEsH,MAAM,EAAE;IACZ,OAAOmB,GAAG,CAAC,IAAI,EAAEzI,KAAK,EAAEsH,MAAM,CAAC;EACnC;EACH6J,GAAGA,CAACnR,KAAK,EAAE;IACJ,OAAOkV,UAAU,CAAC,IAAI,EAAElV,KAAK,CAAC;EAClC;EACHyP,QAAQA,CAACzP,KAAK,EAAEsH,MAAM,EAAE;IACjB,OAAOmB,GAAG,CAACwM,WAAW,EAAEjV,KAAK,EAAEsH,MAAM,CAAC;EAC1C;EACAqQ,QAAQA,CAAC3X,KAAK,EAAEsH,MAAM,EAAE;IACpB,OAAOmB,GAAG,CAACuM,SAAS,EAAEhV,KAAK,EAAEsH,MAAM,CAAC;EACxC;EACHwN,KAAKA,CAAC9U,KAAK,EAAE6P,IAAI,EAAE+H,WAAW,EAAEC,UAAU,EAAE;IACrC,MAAMC,WAAW,GAAG5C,UAAU,CAAC,IAAI,EAAElV,KAAK,CAAC;IAC3C,MAAM+X,iBAAiB,GAAG7C,UAAU,CAAC,IAAI,EAAE0C,WAAW,CAAC;IACvD,MAAMI,WAAW,GAAG,GAAG,GAAGnI,IAAI;IAC9BxT,MAAM,CAAC4b,gBAAgB,CAACH,WAAW,EAAE;MACjC,CAACE,WAAW,GAAG;QACXhc,KAAK,EAAE8b,WAAW,CAACjI,IAAI,CAAC;QACxBqI,QAAQ,EAAE;MACd,CAAC;MACD,CAACrI,IAAI,GAAG;QACJ/H,UAAU,EAAE,IAAI;QAChBqJ,GAAGA,CAAA,EAAI;UACH,MAAMgH,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC;UAC/B,MAAMnZ,MAAM,GAAGkZ,iBAAiB,CAACF,UAAU,CAAC;UAC5C,IAAInb,QAAQ,CAACyb,KAAK,CAAC,EAAE;YACjB,OAAO9b,MAAM,CAAC6P,MAAM,CAAC,CAAC,CAAC,EAAErN,MAAM,EAAEsZ,KAAK,CAAC;UAC3C;UACA,OAAOnb,cAAc,CAACmb,KAAK,EAAEtZ,MAAM,CAAC;QACxC,CAAC;QACD4J,GAAGA,CAAEzM,KAAK,EAAE;UACR,IAAI,CAACgc,WAAW,CAAC,GAAGhc,KAAK;QAC7B;MACJ;IACJ,CAAC,CAAC;EACN;EACA0B,KAAKA,CAAC0a,QAAQ,EAAE;IACZA,QAAQ,CAACrQ,OAAO,CAAErK,KAAK,IAAGA,KAAK,CAAC,IAAI,CAAC,CAAC;EAC1C;AACJ;AACA,IAAI0R,QAAQ,GAAG,eAAgB,IAAIiG,QAAQ,CAAC;EACxCzF,WAAW,EAAGC,IAAI,IAAG,CAACA,IAAI,CAACkF,UAAU,CAAC,IAAI,CAAC;EAC3CpF,UAAU,EAAGE,IAAI,IAAGA,IAAI,KAAK,QAAQ;EACrC0G,KAAK,EAAE;IACH7G,SAAS,EAAE;EACf,CAAC;EACDmH,WAAW,EAAE;IACTjH,WAAW,EAAE,KAAK;IAClBD,UAAU,EAAE;EAChB;AACJ,CAAC,EAAE,CACCR,uBAAuB,EACvBmB,oBAAoB,EACpBqC,kBAAkB,CACrB,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS0F,YAAYA,CAACnC,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,IAAIna,aAAa,CAACma,IAAI,CAACtU,IAAI,CAAC,IAAI7F,aAAa,CAACma,IAAI,CAACC,MAAM,CAAC,EAAE;IACjE,OAAO,IAAI;EACf;EACA,OAAO,CAACD,IAAI,CAACE,KAAK,GAAGF,IAAI,CAACE,KAAK,GAAG,GAAG,GAAG,EAAE,KAAKF,IAAI,CAACI,MAAM,GAAGJ,IAAI,CAACI,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGJ,IAAI,CAACtU,IAAI,GAAG,KAAK,GAAGsU,IAAI,CAACC,MAAM;AAC1H;AACA;AACA;AACA;AAAI,SAASmC,YAAYA,CAAC7B,GAAG,EAAE8B,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACtD,IAAIC,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC;EAC5B,IAAI,CAACC,SAAS,EAAE;IACZA,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC,GAAGjC,GAAG,CAACmC,WAAW,CAACF,MAAM,CAAC,CAAC7E,KAAK;IACxD2E,EAAE,CAAC1X,IAAI,CAAC4X,MAAM,CAAC;EACnB;EACA,IAAIC,SAAS,GAAGF,OAAO,EAAE;IACrBA,OAAO,GAAGE,SAAS;EACvB;EACA,OAAOF,OAAO;AAClB;AACA;AACA;AACA,GAFA,CAEI;AACJ,SAASI,YAAYA,CAACpC,GAAG,EAAEP,IAAI,EAAE4C,aAAa,EAAEC,KAAK,EAAE;EACnDA,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;EACnB,IAAIR,IAAI,GAAGQ,KAAK,CAACR,IAAI,GAAGQ,KAAK,CAACR,IAAI,IAAI,CAAC,CAAC;EACxC,IAAIC,EAAE,GAAGO,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACC,cAAc,IAAI,EAAE;EAC1D,IAAID,KAAK,CAAC7C,IAAI,KAAKA,IAAI,EAAE;IACrBqC,IAAI,GAAGQ,KAAK,CAACR,IAAI,GAAG,CAAC,CAAC;IACtBC,EAAE,GAAGO,KAAK,CAACC,cAAc,GAAG,EAAE;IAC9BD,KAAK,CAAC7C,IAAI,GAAGA,IAAI;EACrB;EACAO,GAAG,CAACwC,IAAI,CAAC,CAAC;EACVxC,GAAG,CAACP,IAAI,GAAGA,IAAI;EACf,IAAIuC,OAAO,GAAG,CAAC;EACf,MAAMpa,IAAI,GAAGya,aAAa,CAAC7a,MAAM;EACjC,IAAIH,CAAC,EAAEob,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW;EAClC,KAAIvb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,IAAI,EAAEP,CAAC,EAAE,EAAC;IACrBsb,KAAK,GAAGN,aAAa,CAAChb,CAAC,CAAC;IACxB;IACA,IAAIsb,KAAK,KAAKnd,SAAS,IAAImd,KAAK,KAAK,IAAI,IAAI,CAACld,OAAO,CAACkd,KAAK,CAAC,EAAE;MAC1DX,OAAO,GAAGH,YAAY,CAAC7B,GAAG,EAAE8B,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEW,KAAK,CAAC;IACzD,CAAC,MAAM,IAAIld,OAAO,CAACkd,KAAK,CAAC,EAAE;MACvB;MACA;MACA,KAAIF,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGC,KAAK,CAACnb,MAAM,EAAEib,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAC;QAC1CG,WAAW,GAAGD,KAAK,CAACF,CAAC,CAAC;QACtB;QACA,IAAIG,WAAW,KAAKpd,SAAS,IAAIod,WAAW,KAAK,IAAI,IAAI,CAACnd,OAAO,CAACmd,WAAW,CAAC,EAAE;UAC5EZ,OAAO,GAAGH,YAAY,CAAC7B,GAAG,EAAE8B,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEY,WAAW,CAAC;QAC/D;MACJ;IACJ;EACJ;EACA5C,GAAG,CAAC6C,OAAO,CAAC,CAAC;EACb,MAAMC,KAAK,GAAGf,EAAE,CAACva,MAAM,GAAG,CAAC;EAC3B,IAAIsb,KAAK,GAAGT,aAAa,CAAC7a,MAAM,EAAE;IAC9B,KAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyb,KAAK,EAAEzb,CAAC,EAAE,EAAC;MACtB,OAAOya,IAAI,CAACC,EAAE,CAAC1a,CAAC,CAAC,CAAC;IACtB;IACA0a,EAAE,CAAClQ,MAAM,CAAC,CAAC,EAAEiR,KAAK,CAAC;EACvB;EACA,OAAOd,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASe,WAAWA,CAAC3H,KAAK,EAAE4H,KAAK,EAAE5F,KAAK,EAAE;EAC1C,MAAM+B,gBAAgB,GAAG/D,KAAK,CAAC6H,uBAAuB;EACtD,MAAMC,SAAS,GAAG9F,KAAK,KAAK,CAAC,GAAG3R,IAAI,CAACwC,GAAG,CAACmP,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EAC5D,OAAO3R,IAAI,CAACiB,KAAK,CAAC,CAACsW,KAAK,GAAGE,SAAS,IAAI/D,gBAAgB,CAAC,GAAGA,gBAAgB,GAAG+D,SAAS;AAC5F;AACA;AACA;AACA;AAAI,SAASC,WAAWA,CAACC,MAAM,EAAEpD,GAAG,EAAE;EAClC,IAAI,CAACA,GAAG,IAAI,CAACoD,MAAM,EAAE;IACjB;EACJ;EACApD,GAAG,GAAGA,GAAG,IAAIoD,MAAM,CAACC,UAAU,CAAC,IAAI,CAAC;EACpCrD,GAAG,CAACwC,IAAI,CAAC,CAAC;EACV;EACA;EACAxC,GAAG,CAACsD,cAAc,CAAC,CAAC;EACpBtD,GAAG,CAACuD,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,MAAM,CAAChG,KAAK,EAAEgG,MAAM,CAACI,MAAM,CAAC;EAChDxD,GAAG,CAAC6C,OAAO,CAAC,CAAC;AACjB;AACA,SAASY,SAASA,CAACzD,GAAG,EAAEpX,OAAO,EAAEiB,CAAC,EAAEE,CAAC,EAAE;EACnC;EACA2Z,eAAe,CAAC1D,GAAG,EAAEpX,OAAO,EAAEiB,CAAC,EAAEE,CAAC,EAAE,IAAI,CAAC;AAC7C;AACA;AACA,SAAS2Z,eAAeA,CAAC1D,GAAG,EAAEpX,OAAO,EAAEiB,CAAC,EAAEE,CAAC,EAAE4Z,CAAC,EAAE;EAC5C,IAAIhe,IAAI,EAAEie,OAAO,EAAEC,OAAO,EAAE1Y,IAAI,EAAE2Y,YAAY,EAAE1G,KAAK,EAAE2G,QAAQ,EAAEC,QAAQ;EACzE,MAAMrE,KAAK,GAAG/W,OAAO,CAACqb,UAAU;EAChC,MAAMC,QAAQ,GAAGtb,OAAO,CAACsb,QAAQ;EACjC,MAAMC,MAAM,GAAGvb,OAAO,CAACub,MAAM;EAC7B,IAAIC,GAAG,GAAG,CAACF,QAAQ,IAAI,CAAC,IAAIpY,WAAW;EACvC,IAAI6T,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpCha,IAAI,GAAGga,KAAK,CAAC7Z,QAAQ,CAAC,CAAC;IACvB,IAAIH,IAAI,KAAK,2BAA2B,IAAIA,IAAI,KAAK,4BAA4B,EAAE;MAC/Eqa,GAAG,CAACwC,IAAI,CAAC,CAAC;MACVxC,GAAG,CAACqE,SAAS,CAACxa,CAAC,EAAEE,CAAC,CAAC;MACnBiW,GAAG,CAACsE,MAAM,CAACF,GAAG,CAAC;MACfpE,GAAG,CAACuE,SAAS,CAAC5E,KAAK,EAAE,CAACA,KAAK,CAACvC,KAAK,GAAG,CAAC,EAAE,CAACuC,KAAK,CAAC6D,MAAM,GAAG,CAAC,EAAE7D,KAAK,CAACvC,KAAK,EAAEuC,KAAK,CAAC6D,MAAM,CAAC;MACpFxD,GAAG,CAAC6C,OAAO,CAAC,CAAC;MACb;IACJ;EACJ;EACA,IAAInV,KAAK,CAACyW,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,EAAE;IAC9B;EACJ;EACAnE,GAAG,CAACwE,SAAS,CAAC,CAAC;EACf,QAAO7E,KAAK;IACR;IACA;MACI,IAAIgE,CAAC,EAAE;QACH3D,GAAG,CAACyE,OAAO,CAAC5a,CAAC,EAAEE,CAAC,EAAE4Z,CAAC,GAAG,CAAC,EAAEQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEzY,GAAG,CAAC;MAC/C,CAAC,MAAM;QACHsU,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,EAAEE,CAAC,EAAEoa,MAAM,EAAE,CAAC,EAAEzY,GAAG,CAAC;MACjC;MACAsU,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,UAAU;MACXvH,KAAK,GAAGuG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM;MAC1BnE,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAG4B,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGhH,KAAK,EAAErT,CAAC,GAAG0B,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM,CAAC;MACjEC,GAAG,IAAInY,aAAa;MACpB+T,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAG4B,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGhH,KAAK,EAAErT,CAAC,GAAG0B,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM,CAAC;MACjEC,GAAG,IAAInY,aAAa;MACpB+T,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAG4B,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGhH,KAAK,EAAErT,CAAC,GAAG0B,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM,CAAC;MACjEnE,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,aAAa;MACd;MACA;MACA;MACA;MACA;MACA;MACA;MACAb,YAAY,GAAGK,MAAM,GAAG,KAAK;MAC7BhZ,IAAI,GAAGgZ,MAAM,GAAGL,YAAY;MAC5BF,OAAO,GAAGnY,IAAI,CAACqL,GAAG,CAACsN,GAAG,GAAGpY,UAAU,CAAC,GAAGb,IAAI;MAC3C4Y,QAAQ,GAAGtY,IAAI,CAACqL,GAAG,CAACsN,GAAG,GAAGpY,UAAU,CAAC,IAAI2X,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGG,YAAY,GAAG3Y,IAAI,CAAC;MACzE0Y,OAAO,GAAGpY,IAAI,CAACoK,GAAG,CAACuO,GAAG,GAAGpY,UAAU,CAAC,GAAGb,IAAI;MAC3C6Y,QAAQ,GAAGvY,IAAI,CAACoK,GAAG,CAACuO,GAAG,GAAGpY,UAAU,CAAC,IAAI2X,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGG,YAAY,GAAG3Y,IAAI,CAAC;MACzE6U,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,EAAEC,YAAY,EAAEM,GAAG,GAAG5Y,EAAE,EAAE4Y,GAAG,GAAGrY,OAAO,CAAC;MACzEiU,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,EAAEE,YAAY,EAAEM,GAAG,GAAGrY,OAAO,EAAEqY,GAAG,CAAC;MACpEpE,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,EAAEC,YAAY,EAAEM,GAAG,EAAEA,GAAG,GAAGrY,OAAO,CAAC;MACpEiU,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,EAAEE,YAAY,EAAEM,GAAG,GAAGrY,OAAO,EAAEqY,GAAG,GAAG5Y,EAAE,CAAC;MACzEwU,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,MAAM;MACP,IAAI,CAACT,QAAQ,EAAE;QACX/Y,IAAI,GAAGM,IAAI,CAACqZ,OAAO,GAAGX,MAAM;QAC5B/G,KAAK,GAAGuG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGxY,IAAI;QACxB6U,GAAG,CAAC+E,IAAI,CAAClb,CAAC,GAAGuT,KAAK,EAAErT,CAAC,GAAGoB,IAAI,EAAE,CAAC,GAAGiS,KAAK,EAAE,CAAC,GAAGjS,IAAI,CAAC;QAClD;MACJ;MACAiZ,GAAG,IAAIpY,UAAU;IACrB;IAAoB,KAAK,SAAS;MAC9B+X,QAAQ,GAAGtY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGnY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAGpY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAGvY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrC5D,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;IACJ,KAAK,UAAU;MACXP,GAAG,IAAIpY,UAAU;IACrB;IAAoB,KAAK,OAAO;MAC5B+X,QAAQ,GAAGtY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGnY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAGpY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAGvY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrC;IACJ,KAAK,MAAM;MACPG,QAAQ,GAAGtY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGnY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAGpY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAGvY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrCQ,GAAG,IAAIpY,UAAU;MACjB+X,QAAQ,GAAGtY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CP,OAAO,GAAGnY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAChCN,OAAO,GAAGpY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCH,QAAQ,GAAGvY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC;MAC/CnE,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGka,QAAQ,EAAEha,CAAC,GAAG8Z,OAAO,CAAC;MACrC7D,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrC5D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGma,QAAQ,EAAEja,CAAC,GAAG6Z,OAAO,CAAC;MACrC;IACJ,KAAK,MAAM;MACPA,OAAO,GAAGD,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGlY,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,GAAGD,MAAM;MAC5CN,OAAO,GAAGpY,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM;MAChCnE,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,GAAG+Z,OAAO,EAAE7Z,CAAC,GAAG8Z,OAAO,CAAC;MACpC7D,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAG+Z,OAAO,EAAE7Z,CAAC,GAAG8Z,OAAO,CAAC;MACpC;IACJ,KAAK,MAAM;MACP7D,GAAG,CAAC4E,MAAM,CAAC/a,CAAC,EAAEE,CAAC,CAAC;MAChBiW,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAG4B,IAAI,CAACqL,GAAG,CAACsN,GAAG,CAAC,IAAIT,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGQ,MAAM,CAAC,EAAEpa,CAAC,GAAG0B,IAAI,CAACoK,GAAG,CAACuO,GAAG,CAAC,GAAGD,MAAM,CAAC;MAChF;IACJ,KAAK,KAAK;MACNnE,GAAG,CAAC2E,SAAS,CAAC,CAAC;MACf;EACR;EACA3E,GAAG,CAACgF,IAAI,CAAC,CAAC;EACV,IAAIpc,OAAO,CAACqc,WAAW,GAAG,CAAC,EAAE;IACzBjF,GAAG,CAACkF,MAAM,CAAC,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,cAAcA,CAACtQ,KAAK,EAAEuQ,IAAI,EAAEC,MAAM,EAAE;EAC7CA,MAAM,GAAGA,MAAM,IAAI,GAAG,CAAC,CAAC;EACxB,OAAO,CAACD,IAAI,IAAIvQ,KAAK,IAAIA,KAAK,CAAChL,CAAC,GAAGub,IAAI,CAAC9R,IAAI,GAAG+R,MAAM,IAAIxQ,KAAK,CAAChL,CAAC,GAAGub,IAAI,CAAC7R,KAAK,GAAG8R,MAAM,IAAIxQ,KAAK,CAAC9K,CAAC,GAAGqb,IAAI,CAACpL,GAAG,GAAGqL,MAAM,IAAIxQ,KAAK,CAAC9K,CAAC,GAAGqb,IAAI,CAACnL,MAAM,GAAGoL,MAAM;AAC3J;AACA,SAASC,QAAQA,CAACtF,GAAG,EAAEoF,IAAI,EAAE;EACzBpF,GAAG,CAACwC,IAAI,CAAC,CAAC;EACVxC,GAAG,CAACwE,SAAS,CAAC,CAAC;EACfxE,GAAG,CAAC+E,IAAI,CAACK,IAAI,CAAC9R,IAAI,EAAE8R,IAAI,CAACpL,GAAG,EAAEoL,IAAI,CAAC7R,KAAK,GAAG6R,IAAI,CAAC9R,IAAI,EAAE8R,IAAI,CAACnL,MAAM,GAAGmL,IAAI,CAACpL,GAAG,CAAC;EAC7EgG,GAAG,CAACzD,IAAI,CAAC,CAAC;AACd;AACA,SAASgJ,UAAUA,CAACvF,GAAG,EAAE;EACrBA,GAAG,CAAC6C,OAAO,CAAC,CAAC;AACjB;AACA;AACA;AACA;AAAI,SAAS2C,cAAcA,CAACxF,GAAG,EAAExW,QAAQ,EAAEpB,MAAM,EAAEqd,IAAI,EAAEpF,IAAI,EAAE;EAC3D,IAAI,CAAC7W,QAAQ,EAAE;IACX,OAAOwW,GAAG,CAAC6E,MAAM,CAACzc,MAAM,CAACyB,CAAC,EAAEzB,MAAM,CAAC2B,CAAC,CAAC;EACzC;EACA,IAAIsW,IAAI,KAAK,QAAQ,EAAE;IACnB,MAAMqF,QAAQ,GAAG,CAAClc,QAAQ,CAACK,CAAC,GAAGzB,MAAM,CAACyB,CAAC,IAAI,GAAG;IAC9CmW,GAAG,CAAC6E,MAAM,CAACa,QAAQ,EAAElc,QAAQ,CAACO,CAAC,CAAC;IAChCiW,GAAG,CAAC6E,MAAM,CAACa,QAAQ,EAAEtd,MAAM,CAAC2B,CAAC,CAAC;EAClC,CAAC,MAAM,IAAIsW,IAAI,KAAK,OAAO,KAAK,CAAC,CAACoF,IAAI,EAAE;IACpCzF,GAAG,CAAC6E,MAAM,CAACrb,QAAQ,CAACK,CAAC,EAAEzB,MAAM,CAAC2B,CAAC,CAAC;EACpC,CAAC,MAAM;IACHiW,GAAG,CAAC6E,MAAM,CAACzc,MAAM,CAACyB,CAAC,EAAEL,QAAQ,CAACO,CAAC,CAAC;EACpC;EACAiW,GAAG,CAAC6E,MAAM,CAACzc,MAAM,CAACyB,CAAC,EAAEzB,MAAM,CAAC2B,CAAC,CAAC;AAClC;AACA;AACA;AACA;AAAI,SAAS4b,cAAcA,CAAC3F,GAAG,EAAExW,QAAQ,EAAEpB,MAAM,EAAEqd,IAAI,EAAE;EACrD,IAAI,CAACjc,QAAQ,EAAE;IACX,OAAOwW,GAAG,CAAC6E,MAAM,CAACzc,MAAM,CAACyB,CAAC,EAAEzB,MAAM,CAAC2B,CAAC,CAAC;EACzC;EACAiW,GAAG,CAAC4F,aAAa,CAACH,IAAI,GAAGjc,QAAQ,CAACqc,IAAI,GAAGrc,QAAQ,CAACsc,IAAI,EAAEL,IAAI,GAAGjc,QAAQ,CAACuc,IAAI,GAAGvc,QAAQ,CAACwc,IAAI,EAAEP,IAAI,GAAGrd,MAAM,CAAC0d,IAAI,GAAG1d,MAAM,CAACyd,IAAI,EAAEJ,IAAI,GAAGrd,MAAM,CAAC4d,IAAI,GAAG5d,MAAM,CAAC2d,IAAI,EAAE3d,MAAM,CAACyB,CAAC,EAAEzB,MAAM,CAAC2B,CAAC,CAAC;AACzL;AACA,SAASkc,aAAaA,CAACjG,GAAG,EAAEkG,IAAI,EAAE;EAC9B,IAAIA,IAAI,CAACC,WAAW,EAAE;IAClBnG,GAAG,CAACqE,SAAS,CAAC6B,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;EAC3D;EACA,IAAI,CAAC7gB,aAAa,CAAC4gB,IAAI,CAAChC,QAAQ,CAAC,EAAE;IAC/BlE,GAAG,CAACsE,MAAM,CAAC4B,IAAI,CAAChC,QAAQ,CAAC;EAC7B;EACA,IAAIgC,IAAI,CAAC/N,KAAK,EAAE;IACZ6H,GAAG,CAACoG,SAAS,GAAGF,IAAI,CAAC/N,KAAK;EAC9B;EACA,IAAI+N,IAAI,CAACG,SAAS,EAAE;IAChBrG,GAAG,CAACqG,SAAS,GAAGH,IAAI,CAACG,SAAS;EAClC;EACA,IAAIH,IAAI,CAACI,YAAY,EAAE;IACnBtG,GAAG,CAACsG,YAAY,GAAGJ,IAAI,CAACI,YAAY;EACxC;AACJ;AACA,SAASC,YAAYA,CAACvG,GAAG,EAAEnW,CAAC,EAAEE,CAAC,EAAEyc,IAAI,EAAEN,IAAI,EAAE;EACzC,IAAIA,IAAI,CAACO,aAAa,IAAIP,IAAI,CAACQ,SAAS,EAAE;IACtC;AACR;AACA;AACA;AACA;AACA;AACA;IAAQ,MAAMC,OAAO,GAAG3G,GAAG,CAACmC,WAAW,CAACqE,IAAI,CAAC;IACrC,MAAMlT,IAAI,GAAGzJ,CAAC,GAAG8c,OAAO,CAACC,qBAAqB;IAC9C,MAAMrT,KAAK,GAAG1J,CAAC,GAAG8c,OAAO,CAACE,sBAAsB;IAChD,MAAM7M,GAAG,GAAGjQ,CAAC,GAAG4c,OAAO,CAACG,uBAAuB;IAC/C,MAAM7M,MAAM,GAAGlQ,CAAC,GAAG4c,OAAO,CAACI,wBAAwB;IACnD,MAAMC,WAAW,GAAGd,IAAI,CAACO,aAAa,GAAG,CAACzM,GAAG,GAAGC,MAAM,IAAI,CAAC,GAAGA,MAAM;IACpE+F,GAAG,CAACiH,WAAW,GAAGjH,GAAG,CAACoG,SAAS;IAC/BpG,GAAG,CAACwE,SAAS,CAAC,CAAC;IACfxE,GAAG,CAACtD,SAAS,GAAGwJ,IAAI,CAACgB,eAAe,IAAI,CAAC;IACzClH,GAAG,CAAC4E,MAAM,CAACtR,IAAI,EAAE0T,WAAW,CAAC;IAC7BhH,GAAG,CAAC6E,MAAM,CAACtR,KAAK,EAAEyT,WAAW,CAAC;IAC9BhH,GAAG,CAACkF,MAAM,CAAC,CAAC;EAChB;AACJ;AACA,SAASiC,YAAYA,CAACnH,GAAG,EAAEkG,IAAI,EAAE;EAC7B,MAAMkB,QAAQ,GAAGpH,GAAG,CAACoG,SAAS;EAC9BpG,GAAG,CAACoG,SAAS,GAAGF,IAAI,CAAC/N,KAAK;EAC1B6H,GAAG,CAACqH,QAAQ,CAACnB,IAAI,CAAC5S,IAAI,EAAE4S,IAAI,CAAClM,GAAG,EAAEkM,IAAI,CAAC9I,KAAK,EAAE8I,IAAI,CAAC1C,MAAM,CAAC;EAC1DxD,GAAG,CAACoG,SAAS,GAAGgB,QAAQ;AAC5B;AACA;AACA;AACA;AAAI,SAASE,UAAUA,CAACtH,GAAG,EAAE1C,IAAI,EAAEzT,CAAC,EAAEE,CAAC,EAAE0V,IAAI,EAAEyG,IAAI,GAAG,CAAC,CAAC,EAAE;EACtD,MAAMqB,KAAK,GAAG9hB,OAAO,CAAC6X,IAAI,CAAC,GAAGA,IAAI,GAAG,CACjCA,IAAI,CACP;EACD,MAAM4H,MAAM,GAAGgB,IAAI,CAACsB,WAAW,GAAG,CAAC,IAAItB,IAAI,CAACuB,WAAW,KAAK,EAAE;EAC9D,IAAIpgB,CAAC,EAAEmf,IAAI;EACXxG,GAAG,CAACwC,IAAI,CAAC,CAAC;EACVxC,GAAG,CAACP,IAAI,GAAGA,IAAI,CAACwC,MAAM;EACtBgE,aAAa,CAACjG,GAAG,EAAEkG,IAAI,CAAC;EACxB,KAAI7e,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkgB,KAAK,CAAC/f,MAAM,EAAE,EAAEH,CAAC,EAAC;IAC7Bmf,IAAI,GAAGe,KAAK,CAAClgB,CAAC,CAAC;IACf,IAAI6e,IAAI,CAACwB,QAAQ,EAAE;MACfP,YAAY,CAACnH,GAAG,EAAEkG,IAAI,CAACwB,QAAQ,CAAC;IACpC;IACA,IAAIxC,MAAM,EAAE;MACR,IAAIgB,IAAI,CAACuB,WAAW,EAAE;QAClBzH,GAAG,CAACiH,WAAW,GAAGf,IAAI,CAACuB,WAAW;MACtC;MACA,IAAI,CAACniB,aAAa,CAAC4gB,IAAI,CAACsB,WAAW,CAAC,EAAE;QAClCxH,GAAG,CAACtD,SAAS,GAAGwJ,IAAI,CAACsB,WAAW;MACpC;MACAxH,GAAG,CAAC2H,UAAU,CAACnB,IAAI,EAAE3c,CAAC,EAAEE,CAAC,EAAEmc,IAAI,CAAC0B,QAAQ,CAAC;IAC7C;IACA5H,GAAG,CAAC6H,QAAQ,CAACrB,IAAI,EAAE3c,CAAC,EAAEE,CAAC,EAAEmc,IAAI,CAAC0B,QAAQ,CAAC;IACvCrB,YAAY,CAACvG,GAAG,EAAEnW,CAAC,EAAEE,CAAC,EAAEyc,IAAI,EAAEN,IAAI,CAAC;IACnCnc,CAAC,IAAI5D,MAAM,CAACsZ,IAAI,CAACG,UAAU,CAAC;EAChC;EACAI,GAAG,CAAC6C,OAAO,CAAC,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AAAI,SAASiF,kBAAkBA,CAAC9H,GAAG,EAAE+E,IAAI,EAAE;EACvC,MAAM;IAAElb,CAAC;IAAGE,CAAC;IAAG4Z,CAAC;IAAGoE,CAAC;IAAG5D;EAAQ,CAAC,GAAGY,IAAI;EACxC;EACA/E,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAGsa,MAAM,CAAC6D,OAAO,EAAEje,CAAC,GAAGoa,MAAM,CAAC6D,OAAO,EAAE7D,MAAM,CAAC6D,OAAO,EAAE,GAAG,GAAGxc,EAAE,EAAEA,EAAE,EAAE,IAAI,CAAC;EACnF;EACAwU,GAAG,CAAC6E,MAAM,CAAChb,CAAC,EAAEE,CAAC,GAAGge,CAAC,GAAG5D,MAAM,CAAC8D,UAAU,CAAC;EACxC;EACAjI,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAGsa,MAAM,CAAC8D,UAAU,EAAEle,CAAC,GAAGge,CAAC,GAAG5D,MAAM,CAAC8D,UAAU,EAAE9D,MAAM,CAAC8D,UAAU,EAAEzc,EAAE,EAAEO,OAAO,EAAE,IAAI,CAAC;EAC/F;EACAiU,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAG8Z,CAAC,GAAGQ,MAAM,CAAC+D,WAAW,EAAEne,CAAC,GAAGge,CAAC,CAAC;EAC7C;EACA/H,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAG8Z,CAAC,GAAGQ,MAAM,CAAC+D,WAAW,EAAEne,CAAC,GAAGge,CAAC,GAAG5D,MAAM,CAAC+D,WAAW,EAAE/D,MAAM,CAAC+D,WAAW,EAAEnc,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC;EACrG;EACAiU,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAG8Z,CAAC,EAAE5Z,CAAC,GAAGoa,MAAM,CAACgE,QAAQ,CAAC;EACtC;EACAnI,GAAG,CAAC0E,GAAG,CAAC7a,CAAC,GAAG8Z,CAAC,GAAGQ,MAAM,CAACgE,QAAQ,EAAEpe,CAAC,GAAGoa,MAAM,CAACgE,QAAQ,EAAEhE,MAAM,CAACgE,QAAQ,EAAE,CAAC,EAAE,CAACpc,OAAO,EAAE,IAAI,CAAC;EACzF;EACAiU,GAAG,CAAC6E,MAAM,CAAChb,CAAC,GAAGsa,MAAM,CAAC6D,OAAO,EAAEje,CAAC,CAAC;AACrC;AAEA,MAAMqe,WAAW,GAAG,sCAAsC;AAC1D,MAAMC,UAAU,GAAG,uEAAuE;AAC1F;AACA;AACA;AACA,GAHA,CAGI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASC,YAAYA,CAAC/iB,KAAK,EAAE4F,IAAI,EAAE;EACnC,MAAMod,OAAO,GAAG,CAAC,EAAE,GAAGhjB,KAAK,EAAEijB,KAAK,CAACJ,WAAW,CAAC;EAC/C,IAAI,CAACG,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACrC,OAAOpd,IAAI,GAAG,GAAG;EACrB;EACA5F,KAAK,GAAG,CAACgjB,OAAO,CAAC,CAAC,CAAC;EACnB,QAAOA,OAAO,CAAC,CAAC,CAAC;IACb,KAAK,IAAI;MACL,OAAOhjB,KAAK;IAChB,KAAK,GAAG;MACJA,KAAK,IAAI,GAAG;MACZ;EACR;EACA,OAAO4F,IAAI,GAAG5F,KAAK;AACvB;AACA,MAAMkjB,YAAY,GAAI7e,CAAC,IAAG,CAACA,CAAC,IAAI,CAAC;AACjC,SAAS8e,iBAAiBA,CAACnjB,KAAK,EAAEojB,KAAK,EAAE;EACrC,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,QAAQ,GAAG5iB,QAAQ,CAAC0iB,KAAK,CAAC;EAChC,MAAMphB,IAAI,GAAGshB,QAAQ,GAAGjjB,MAAM,CAAC2B,IAAI,CAACohB,KAAK,CAAC,GAAGA,KAAK;EAClD,MAAMG,IAAI,GAAG7iB,QAAQ,CAACV,KAAK,CAAC,GAAGsjB,QAAQ,GAAIE,IAAI,IAAGxiB,cAAc,CAAChB,KAAK,CAACwjB,IAAI,CAAC,EAAExjB,KAAK,CAACojB,KAAK,CAACI,IAAI,CAAC,CAAC,CAAC,GAAIA,IAAI,IAAGxjB,KAAK,CAACwjB,IAAI,CAAC,GAAG,MAAIxjB,KAAK;EACnI,KAAK,MAAMwjB,IAAI,IAAIxhB,IAAI,EAAC;IACpBqhB,GAAG,CAACG,IAAI,CAAC,GAAGN,YAAY,CAACK,IAAI,CAACC,IAAI,CAAC,CAAC;EACxC;EACA,OAAOH,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,MAAMA,CAACzjB,KAAK,EAAE;EACvB,OAAOmjB,iBAAiB,CAACnjB,KAAK,EAAE;IAC5ByU,GAAG,EAAE,GAAG;IACRzG,KAAK,EAAE,GAAG;IACV0G,MAAM,EAAE,GAAG;IACX3G,IAAI,EAAE;EACV,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS2V,aAAaA,CAAC1jB,KAAK,EAAE;EAC9B,OAAOmjB,iBAAiB,CAACnjB,KAAK,EAAE,CAC5B,SAAS,EACT,UAAU,EACV,YAAY,EACZ,aAAa,CAChB,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS2jB,SAASA,CAAC3jB,KAAK,EAAE;EAC1B,MAAMgF,GAAG,GAAGye,MAAM,CAACzjB,KAAK,CAAC;EACzBgF,GAAG,CAAC6S,KAAK,GAAG7S,GAAG,CAAC+I,IAAI,GAAG/I,GAAG,CAACgJ,KAAK;EAChChJ,GAAG,CAACiZ,MAAM,GAAGjZ,GAAG,CAACyP,GAAG,GAAGzP,GAAG,CAAC0P,MAAM;EACjC,OAAO1P,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS4e,MAAMA,CAACvgB,OAAO,EAAEwgB,QAAQ,EAAE;EACnCxgB,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBwgB,QAAQ,GAAGA,QAAQ,IAAIzQ,QAAQ,CAAC8G,IAAI;EACpC,IAAItU,IAAI,GAAG5E,cAAc,CAACqC,OAAO,CAACuC,IAAI,EAAEie,QAAQ,CAACje,IAAI,CAAC;EACtD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1BA,IAAI,GAAGke,QAAQ,CAACle,IAAI,EAAE,EAAE,CAAC;EAC7B;EACA,IAAIwU,KAAK,GAAGpZ,cAAc,CAACqC,OAAO,CAAC+W,KAAK,EAAEyJ,QAAQ,CAACzJ,KAAK,CAAC;EACzD,IAAIA,KAAK,IAAI,CAAC,CAAC,EAAE,GAAGA,KAAK,EAAE6I,KAAK,CAACH,UAAU,CAAC,EAAE;IAC1C5e,OAAO,CAACC,IAAI,CAAC,iCAAiC,GAAGiW,KAAK,GAAG,GAAG,CAAC;IAC7DA,KAAK,GAAGna,SAAS;EACrB;EACA,MAAMia,IAAI,GAAG;IACTC,MAAM,EAAEnZ,cAAc,CAACqC,OAAO,CAAC8W,MAAM,EAAE0J,QAAQ,CAAC1J,MAAM,CAAC;IACvDE,UAAU,EAAE0I,YAAY,CAAC/hB,cAAc,CAACqC,OAAO,CAACgX,UAAU,EAAEwJ,QAAQ,CAACxJ,UAAU,CAAC,EAAEzU,IAAI,CAAC;IACvFA,IAAI;IACJwU,KAAK;IACLE,MAAM,EAAEtZ,cAAc,CAACqC,OAAO,CAACiX,MAAM,EAAEuJ,QAAQ,CAACvJ,MAAM,CAAC;IACvDoC,MAAM,EAAE;EACZ,CAAC;EACDxC,IAAI,CAACwC,MAAM,GAAGL,YAAY,CAACnC,IAAI,CAAC;EAChC,OAAOA,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS6J,OAAOA,CAACC,MAAM,EAAEnK,OAAO,EAAEpX,KAAK,EAAEwhB,IAAI,EAAE;EAC/C,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIpiB,CAAC,EAAEO,IAAI,EAAErC,KAAK;EAClB,KAAI8B,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAG2hB,MAAM,CAAC/hB,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IAC3C9B,KAAK,GAAGgkB,MAAM,CAACliB,CAAC,CAAC;IACjB,IAAI9B,KAAK,KAAKC,SAAS,EAAE;MACrB;IACJ;IACA,IAAI4Z,OAAO,KAAK5Z,SAAS,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;MACtDA,KAAK,GAAGA,KAAK,CAAC6Z,OAAO,CAAC;MACtBqK,SAAS,GAAG,KAAK;IACrB;IACA,IAAIzhB,KAAK,KAAKxC,SAAS,IAAIC,OAAO,CAACF,KAAK,CAAC,EAAE;MACvCA,KAAK,GAAGA,KAAK,CAACyC,KAAK,GAAGzC,KAAK,CAACiC,MAAM,CAAC;MACnCiiB,SAAS,GAAG,KAAK;IACrB;IACA,IAAIlkB,KAAK,KAAKC,SAAS,EAAE;MACrB,IAAIgkB,IAAI,IAAI,CAACC,SAAS,EAAE;QACpBD,IAAI,CAACC,SAAS,GAAG,KAAK;MAC1B;MACA,OAAOlkB,KAAK;IAChB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASmkB,SAASA,CAACC,MAAM,EAAEnN,KAAK,EAAEH,WAAW,EAAE;EAC/C,MAAM;IAAErO,GAAG;IAAGC;EAAK,CAAC,GAAG0b,MAAM;EAC7B,MAAMC,MAAM,GAAGhjB,WAAW,CAAC4V,KAAK,EAAE,CAACvO,GAAG,GAAGD,GAAG,IAAI,CAAC,CAAC;EAClD,MAAM6b,QAAQ,GAAGA,CAACtkB,KAAK,EAAEukB,GAAG,KAAGzN,WAAW,IAAI9W,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAGukB,GAAG;EAC3E,OAAO;IACH9b,GAAG,EAAE6b,QAAQ,CAAC7b,GAAG,EAAE,CAACvC,IAAI,CAACa,GAAG,CAACsd,MAAM,CAAC,CAAC;IACrC3b,GAAG,EAAE4b,QAAQ,CAAC5b,GAAG,EAAE2b,MAAM;EAC7B,CAAC;AACL;AACA,SAASG,aAAaA,CAACC,aAAa,EAAE5K,OAAO,EAAE;EAC3C,OAAOxZ,MAAM,CAAC6P,MAAM,CAAC7P,MAAM,CAACyC,MAAM,CAAC2hB,aAAa,CAAC,EAAE5K,OAAO,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS6K,eAAeA,CAACC,MAAM,EAAEC,QAAQ,GAAG,CAC5C,EAAE,CACL,EAAEC,UAAU,EAAEhB,QAAQ,EAAEiB,SAAS,GAAGA,CAAA,KAAIH,MAAM,CAAC,CAAC,CAAC,EAAE;EAChD,MAAMI,eAAe,GAAGF,UAAU,IAAIF,MAAM;EAC5C,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;IACjCA,QAAQ,GAAGmB,QAAQ,CAAC,WAAW,EAAEL,MAAM,CAAC;EAC5C;EACA,MAAM5H,KAAK,GAAG;IACV,CAAC/U,MAAM,CAACid,WAAW,GAAG,QAAQ;IAC9BC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAER,MAAM;IACfS,WAAW,EAAEL,eAAe;IAC5BrR,SAAS,EAAEmQ,QAAQ;IACnBwB,UAAU,EAAEP,SAAS;IACrBnJ,QAAQ,EAAG3X,KAAK,IAAG0gB,eAAe,CAAC,CAC3B1gB,KAAK,EACL,GAAG2gB,MAAM,CACZ,EAAEC,QAAQ,EAAEG,eAAe,EAAElB,QAAQ;EAC9C,CAAC;EACD,OAAO,IAAIyB,KAAK,CAACvI,KAAK,EAAE;IACpB;AACR;AACA;IAAQwI,cAAcA,CAAE1iB,MAAM,EAAE2gB,IAAI,EAAE;MAC1B,OAAO3gB,MAAM,CAAC2gB,IAAI,CAAC,CAAC,CAAC;MACrB,OAAO3gB,MAAM,CAAC2iB,KAAK,CAAC,CAAC;MACrB,OAAOb,MAAM,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,CAAC;MACxB,OAAO,IAAI;IACf,CAAC;IACD;AACR;AACA;IAAQrO,GAAGA,CAAEtS,MAAM,EAAE2gB,IAAI,EAAE;MACf,OAAOiC,OAAO,CAAC5iB,MAAM,EAAE2gB,IAAI,EAAE,MAAIkC,oBAAoB,CAAClC,IAAI,EAAEoB,QAAQ,EAAED,MAAM,EAAE9hB,MAAM,CAAC,CAAC;IAC1F,CAAC;IACD;AACR;AACA;AACA;IAAQ8iB,wBAAwBA,CAAE9iB,MAAM,EAAE2gB,IAAI,EAAE;MACpC,OAAOoC,OAAO,CAACD,wBAAwB,CAAC9iB,MAAM,CAACsiB,OAAO,CAAC,CAAC,CAAC,EAAE3B,IAAI,CAAC;IACpE,CAAC;IACD;AACR;AACA;IAAQqC,cAAcA,CAAA,EAAI;MACd,OAAOD,OAAO,CAACC,cAAc,CAAClB,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD;AACR;AACA;IAAQ7e,GAAGA,CAAEjD,MAAM,EAAE2gB,IAAI,EAAE;MACf,OAAOsC,oBAAoB,CAACjjB,MAAM,CAAC,CAAC4T,QAAQ,CAAC+M,IAAI,CAAC;IACtD,CAAC;IACD;AACR;AACA;IAAQuC,OAAOA,CAAEljB,MAAM,EAAE;MACb,OAAOijB,oBAAoB,CAACjjB,MAAM,CAAC;IACvC,CAAC;IACD;AACR;AACA;IAAQ4J,GAAGA,CAAE5J,MAAM,EAAE2gB,IAAI,EAAExjB,KAAK,EAAE;MACtB,MAAMgmB,OAAO,GAAGnjB,MAAM,CAACojB,QAAQ,KAAKpjB,MAAM,CAACojB,QAAQ,GAAGnB,SAAS,CAAC,CAAC,CAAC;MAClEjiB,MAAM,CAAC2gB,IAAI,CAAC,GAAGwC,OAAO,CAACxC,IAAI,CAAC,GAAGxjB,KAAK,CAAC,CAAC;MACtC,OAAO6C,MAAM,CAAC2iB,KAAK,CAAC,CAAC;MACrB,OAAO,IAAI;IACf;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASU,cAAcA,CAACC,KAAK,EAAEtM,OAAO,EAAEuM,QAAQ,EAAEC,kBAAkB,EAAE;EACtE,MAAMtJ,KAAK,GAAG;IACVmI,UAAU,EAAE,KAAK;IACjBoB,MAAM,EAAEH,KAAK;IACbI,QAAQ,EAAE1M,OAAO;IACjB2M,SAAS,EAAEJ,QAAQ;IACnBK,MAAM,EAAE,IAAI/Z,GAAG,CAAC,CAAC;IACjB6M,YAAY,EAAEA,YAAY,CAAC4M,KAAK,EAAEE,kBAAkB,CAAC;IACrDK,UAAU,EAAGjM,GAAG,IAAGyL,cAAc,CAACC,KAAK,EAAE1L,GAAG,EAAE2L,QAAQ,EAAEC,kBAAkB,CAAC;IAC3E1K,QAAQ,EAAG3X,KAAK,IAAGkiB,cAAc,CAACC,KAAK,CAACxK,QAAQ,CAAC3X,KAAK,CAAC,EAAE6V,OAAO,EAAEuM,QAAQ,EAAEC,kBAAkB;EAClG,CAAC;EACD,OAAO,IAAIf,KAAK,CAACvI,KAAK,EAAE;IACpB;AACR;AACA;IAAQwI,cAAcA,CAAE1iB,MAAM,EAAE2gB,IAAI,EAAE;MAC1B,OAAO3gB,MAAM,CAAC2gB,IAAI,CAAC,CAAC,CAAC;MACrB,OAAO2C,KAAK,CAAC3C,IAAI,CAAC,CAAC,CAAC;MACpB,OAAO,IAAI;IACf,CAAC;IACD;AACR;AACA;IAAQrO,GAAGA,CAAEtS,MAAM,EAAE2gB,IAAI,EAAEmD,QAAQ,EAAE;MACzB,OAAOlB,OAAO,CAAC5iB,MAAM,EAAE2gB,IAAI,EAAE,MAAIoD,mBAAmB,CAAC/jB,MAAM,EAAE2gB,IAAI,EAAEmD,QAAQ,CAAC,CAAC;IACjF,CAAC;IACD;AACR;AACA;AACA;IAAQhB,wBAAwBA,CAAE9iB,MAAM,EAAE2gB,IAAI,EAAE;MACpC,OAAO3gB,MAAM,CAAC0W,YAAY,CAACsN,OAAO,GAAGjB,OAAO,CAAC9f,GAAG,CAACqgB,KAAK,EAAE3C,IAAI,CAAC,GAAG;QAC5D1X,UAAU,EAAE,IAAI;QAChBD,YAAY,EAAE;MAClB,CAAC,GAAG5L,SAAS,GAAG2lB,OAAO,CAACD,wBAAwB,CAACQ,KAAK,EAAE3C,IAAI,CAAC;IACjE,CAAC;IACD;AACR;AACA;IAAQqC,cAAcA,CAAA,EAAI;MACd,OAAOD,OAAO,CAACC,cAAc,CAACM,KAAK,CAAC;IACxC,CAAC;IACD;AACR;AACA;IAAQrgB,GAAGA,CAAEjD,MAAM,EAAE2gB,IAAI,EAAE;MACf,OAAOoC,OAAO,CAAC9f,GAAG,CAACqgB,KAAK,EAAE3C,IAAI,CAAC;IACnC,CAAC;IACD;AACR;AACA;IAAQuC,OAAOA,CAAA,EAAI;MACP,OAAOH,OAAO,CAACG,OAAO,CAACI,KAAK,CAAC;IACjC,CAAC;IACD;AACR;AACA;IAAQ1Z,GAAGA,CAAE5J,MAAM,EAAE2gB,IAAI,EAAExjB,KAAK,EAAE;MACtBmmB,KAAK,CAAC3C,IAAI,CAAC,GAAGxjB,KAAK,CAAC,CAAC;MACrB,OAAO6C,MAAM,CAAC2gB,IAAI,CAAC,CAAC,CAAC;MACrB,OAAO,IAAI;IACf;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AAAI,SAASjK,YAAYA,CAAC4M,KAAK,EAAE/S,QAAQ,GAAG;EACxC0T,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACf,CAAC,EAAE;EACC,MAAM;IAAEnT,WAAW,GAAER,QAAQ,CAAC0T,UAAU;IAAGnT,UAAU,GAAEP,QAAQ,CAAC2T,SAAS;IAAGC,QAAQ,GAAE5T,QAAQ,CAACyT;EAAS,CAAC,GAAGV,KAAK;EACjH,OAAO;IACHU,OAAO,EAAEG,QAAQ;IACjBF,UAAU,EAAElT,WAAW;IACvBmT,SAAS,EAAEpT,UAAU;IACrBsT,YAAY,EAAEzhB,UAAU,CAACoO,WAAW,CAAC,GAAGA,WAAW,GAAG,MAAIA,WAAW;IACrEsT,WAAW,EAAE1hB,UAAU,CAACmO,UAAU,CAAC,GAAGA,UAAU,GAAG,MAAIA;EAC3D,CAAC;AACL;AACA,MAAMwT,OAAO,GAAGA,CAACC,MAAM,EAAEvT,IAAI,KAAGuT,MAAM,GAAGA,MAAM,GAAGjiB,WAAW,CAAC0O,IAAI,CAAC,GAAGA,IAAI;AAC1E,MAAMwT,gBAAgB,GAAGA,CAAC7D,IAAI,EAAExjB,KAAK,KAAGU,QAAQ,CAACV,KAAK,CAAC,IAAIwjB,IAAI,KAAK,UAAU,KAAKnjB,MAAM,CAACwlB,cAAc,CAAC7lB,KAAK,CAAC,KAAK,IAAI,IAAIA,KAAK,CAACsZ,WAAW,KAAKjZ,MAAM,CAAC;AACzJ,SAASolB,OAAOA,CAAC5iB,MAAM,EAAE2gB,IAAI,EAAEO,OAAO,EAAE;EACpC,IAAI1jB,MAAM,CAACC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAM,EAAE2gB,IAAI,CAAC,IAAIA,IAAI,KAAK,aAAa,EAAE;IAC9E,OAAO3gB,MAAM,CAAC2gB,IAAI,CAAC;EACvB;EACA,MAAMxjB,KAAK,GAAG+jB,OAAO,CAAC,CAAC;EACvB;EACAlhB,MAAM,CAAC2gB,IAAI,CAAC,GAAGxjB,KAAK;EACpB,OAAOA,KAAK;AAChB;AACA,SAAS4mB,mBAAmBA,CAAC/jB,MAAM,EAAE2gB,IAAI,EAAEmD,QAAQ,EAAE;EACjD,MAAM;IAAEL,MAAM;IAAGC,QAAQ;IAAGC,SAAS;IAAGjN,YAAY,EAAEN;EAAa,CAAC,GAAGpW,MAAM;EAC7E,IAAI7C,KAAK,GAAGsmB,MAAM,CAAC9C,IAAI,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIhe,UAAU,CAACxF,KAAK,CAAC,IAAIiZ,WAAW,CAACgO,YAAY,CAACzD,IAAI,CAAC,EAAE;IACrDxjB,KAAK,GAAGsnB,kBAAkB,CAAC9D,IAAI,EAAExjB,KAAK,EAAE6C,MAAM,EAAE8jB,QAAQ,CAAC;EAC7D;EACA,IAAIzmB,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACiC,MAAM,EAAE;IAChCjC,KAAK,GAAGunB,aAAa,CAAC/D,IAAI,EAAExjB,KAAK,EAAE6C,MAAM,EAAEoW,WAAW,CAACiO,WAAW,CAAC;EACvE;EACA,IAAIG,gBAAgB,CAAC7D,IAAI,EAAExjB,KAAK,CAAC,EAAE;IAC/B;IACAA,KAAK,GAAGkmB,cAAc,CAAClmB,KAAK,EAAEumB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAAChD,IAAI,CAAC,EAAEvK,WAAW,CAAC;EACtF;EACA,OAAOjZ,KAAK;AAChB;AACA,SAASsnB,kBAAkBA,CAAC9D,IAAI,EAAEgE,QAAQ,EAAE3kB,MAAM,EAAE8jB,QAAQ,EAAE;EAC1D,MAAM;IAAEL,MAAM;IAAGC,QAAQ;IAAGC,SAAS;IAAGC;EAAQ,CAAC,GAAG5jB,MAAM;EAC1D,IAAI4jB,MAAM,CAAC3gB,GAAG,CAAC0d,IAAI,CAAC,EAAE;IAClB,MAAM,IAAIiE,KAAK,CAAC,sBAAsB,GAAGtnB,KAAK,CAACwM,IAAI,CAAC8Z,MAAM,CAAC,CAACiB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAGlE,IAAI,CAAC;EACzF;EACAiD,MAAM,CAAClC,GAAG,CAACf,IAAI,CAAC;EAChB,IAAIxjB,KAAK,GAAGwnB,QAAQ,CAACjB,QAAQ,EAAEC,SAAS,IAAIG,QAAQ,CAAC;EACrDF,MAAM,CAACkB,MAAM,CAACnE,IAAI,CAAC;EACnB,IAAI6D,gBAAgB,CAAC7D,IAAI,EAAExjB,KAAK,CAAC,EAAE;IAC/B;IACAA,KAAK,GAAG4nB,iBAAiB,CAACtB,MAAM,CAACnB,OAAO,EAAEmB,MAAM,EAAE9C,IAAI,EAAExjB,KAAK,CAAC;EAClE;EACA,OAAOA,KAAK;AAChB;AACA,SAASunB,aAAaA,CAAC/D,IAAI,EAAExjB,KAAK,EAAE6C,MAAM,EAAEqkB,WAAW,EAAE;EACrD,MAAM;IAAEZ,MAAM;IAAGC,QAAQ;IAAGC,SAAS;IAAGjN,YAAY,EAAEN;EAAa,CAAC,GAAGpW,MAAM;EAC7E,IAAI,OAAO0jB,QAAQ,CAAC9jB,KAAK,KAAK,WAAW,IAAIykB,WAAW,CAAC1D,IAAI,CAAC,EAAE;IAC5D,OAAOxjB,KAAK,CAACumB,QAAQ,CAAC9jB,KAAK,GAAGzC,KAAK,CAACiC,MAAM,CAAC;EAC/C,CAAC,MAAM,IAAIvB,QAAQ,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B;IACA,MAAM6nB,GAAG,GAAG7nB,KAAK;IACjB,MAAM2kB,MAAM,GAAG2B,MAAM,CAACnB,OAAO,CAAC2C,MAAM,CAAE3d,CAAC,IAAGA,CAAC,KAAK0d,GAAG,CAAC;IACpD7nB,KAAK,GAAG,EAAE;IACV,KAAK,MAAM6F,IAAI,IAAIgiB,GAAG,EAAC;MACnB,MAAM3iB,QAAQ,GAAG0iB,iBAAiB,CAACjD,MAAM,EAAE2B,MAAM,EAAE9C,IAAI,EAAE3d,IAAI,CAAC;MAC9D7F,KAAK,CAAC8E,IAAI,CAACohB,cAAc,CAAChhB,QAAQ,EAAEqhB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAAChD,IAAI,CAAC,EAAEvK,WAAW,CAAC,CAAC;IAC7F;EACJ;EACA,OAAOjZ,KAAK;AAChB;AACA,SAAS+nB,eAAeA,CAAClE,QAAQ,EAAEL,IAAI,EAAExjB,KAAK,EAAE;EAC5C,OAAOwF,UAAU,CAACqe,QAAQ,CAAC,GAAGA,QAAQ,CAACL,IAAI,EAAExjB,KAAK,CAAC,GAAG6jB,QAAQ;AAClE;AACA,MAAMmE,QAAQ,GAAGA,CAAC9kB,GAAG,EAAE+kB,MAAM,KAAG/kB,GAAG,KAAK,IAAI,GAAG+kB,MAAM,GAAG,OAAO/kB,GAAG,KAAK,QAAQ,GAAG+B,gBAAgB,CAACgjB,MAAM,EAAE/kB,GAAG,CAAC,GAAGjD,SAAS;AAC3H,SAASioB,SAASA,CAACzb,GAAG,EAAE0b,YAAY,EAAEjlB,GAAG,EAAEklB,cAAc,EAAEpoB,KAAK,EAAE;EAC9D,KAAK,MAAMioB,MAAM,IAAIE,YAAY,EAAC;IAC9B,MAAMnkB,KAAK,GAAGgkB,QAAQ,CAAC9kB,GAAG,EAAE+kB,MAAM,CAAC;IACnC,IAAIjkB,KAAK,EAAE;MACPyI,GAAG,CAAC8X,GAAG,CAACvgB,KAAK,CAAC;MACd,MAAM6f,QAAQ,GAAGkE,eAAe,CAAC/jB,KAAK,CAAC0P,SAAS,EAAExQ,GAAG,EAAElD,KAAK,CAAC;MAC7D,IAAI,OAAO6jB,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK3gB,GAAG,IAAI2gB,QAAQ,KAAKuE,cAAc,EAAE;QACpF;QACA;QACA,OAAOvE,QAAQ;MACnB;IACJ,CAAC,MAAM,IAAI7f,KAAK,KAAK,KAAK,IAAI,OAAOokB,cAAc,KAAK,WAAW,IAAIllB,GAAG,KAAKklB,cAAc,EAAE;MAC3F;MACA;MACA,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASR,iBAAiBA,CAACO,YAAY,EAAEjjB,QAAQ,EAAEse,IAAI,EAAExjB,KAAK,EAAE;EAC5D,MAAM6kB,UAAU,GAAG3f,QAAQ,CAACkgB,WAAW;EACvC,MAAMvB,QAAQ,GAAGkE,eAAe,CAAC7iB,QAAQ,CAACwO,SAAS,EAAE8P,IAAI,EAAExjB,KAAK,CAAC;EACjE,MAAMqoB,SAAS,GAAG,CACd,GAAGF,YAAY,EACf,GAAGtD,UAAU,CAChB;EACD,MAAMpY,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrBD,GAAG,CAAC8X,GAAG,CAACvkB,KAAK,CAAC;EACd,IAAIkD,GAAG,GAAGolB,gBAAgB,CAAC7b,GAAG,EAAE4b,SAAS,EAAE7E,IAAI,EAAEK,QAAQ,IAAIL,IAAI,EAAExjB,KAAK,CAAC;EACzE,IAAIkD,GAAG,KAAK,IAAI,EAAE;IACd,OAAO,KAAK;EAChB;EACA,IAAI,OAAO2gB,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAKL,IAAI,EAAE;IACtDtgB,GAAG,GAAGolB,gBAAgB,CAAC7b,GAAG,EAAE4b,SAAS,EAAExE,QAAQ,EAAE3gB,GAAG,EAAElD,KAAK,CAAC;IAC5D,IAAIkD,GAAG,KAAK,IAAI,EAAE;MACd,OAAO,KAAK;IAChB;EACJ;EACA,OAAOwhB,eAAe,CAACvkB,KAAK,CAACwM,IAAI,CAACF,GAAG,CAAC,EAAE,CACpC,EAAE,CACL,EAAEoY,UAAU,EAAEhB,QAAQ,EAAE,MAAI0E,YAAY,CAACrjB,QAAQ,EAAEse,IAAI,EAAExjB,KAAK,CAAC,CAAC;AACrE;AACA,SAASsoB,gBAAgBA,CAAC7b,GAAG,EAAE4b,SAAS,EAAEnlB,GAAG,EAAE2gB,QAAQ,EAAEhe,IAAI,EAAE;EAC3D,OAAM3C,GAAG,EAAC;IACNA,GAAG,GAAGglB,SAAS,CAACzb,GAAG,EAAE4b,SAAS,EAAEnlB,GAAG,EAAE2gB,QAAQ,EAAEhe,IAAI,CAAC;EACxD;EACA,OAAO3C,GAAG;AACd;AACA,SAASqlB,YAAYA,CAACrjB,QAAQ,EAAEse,IAAI,EAAExjB,KAAK,EAAE;EACzC,MAAMioB,MAAM,GAAG/iB,QAAQ,CAACmgB,UAAU,CAAC,CAAC;EACpC,IAAI,EAAE7B,IAAI,IAAIyE,MAAM,CAAC,EAAE;IACnBA,MAAM,CAACzE,IAAI,CAAC,GAAG,CAAC,CAAC;EACrB;EACA,MAAM3gB,MAAM,GAAGolB,MAAM,CAACzE,IAAI,CAAC;EAC3B,IAAItjB,OAAO,CAAC2C,MAAM,CAAC,IAAInC,QAAQ,CAACV,KAAK,CAAC,EAAE;IACpC;IACA,OAAOA,KAAK;EAChB;EACA,OAAO6C,MAAM,IAAI,CAAC,CAAC;AACvB;AACA,SAAS6iB,oBAAoBA,CAAClC,IAAI,EAAEoB,QAAQ,EAAED,MAAM,EAAEwB,KAAK,EAAE;EACzD,IAAInmB,KAAK;EACT,KAAK,MAAMonB,MAAM,IAAIxC,QAAQ,EAAC;IAC1B5kB,KAAK,GAAGglB,QAAQ,CAACmC,OAAO,CAACC,MAAM,EAAE5D,IAAI,CAAC,EAAEmB,MAAM,CAAC;IAC/C,IAAI,OAAO3kB,KAAK,KAAK,WAAW,EAAE;MAC9B,OAAOqnB,gBAAgB,CAAC7D,IAAI,EAAExjB,KAAK,CAAC,GAAG4nB,iBAAiB,CAACjD,MAAM,EAAEwB,KAAK,EAAE3C,IAAI,EAAExjB,KAAK,CAAC,GAAGA,KAAK;IAChG;EACJ;AACJ;AACA,SAASglB,QAAQA,CAAC9hB,GAAG,EAAEyhB,MAAM,EAAE;EAC3B,KAAK,MAAM3gB,KAAK,IAAI2gB,MAAM,EAAC;IACvB,IAAI,CAAC3gB,KAAK,EAAE;MACR;IACJ;IACA,MAAMhE,KAAK,GAAGgE,KAAK,CAACd,GAAG,CAAC;IACxB,IAAI,OAAOlD,KAAK,KAAK,WAAW,EAAE;MAC9B,OAAOA,KAAK;IAChB;EACJ;AACJ;AACA,SAAS8lB,oBAAoBA,CAACjjB,MAAM,EAAE;EAClC,IAAIb,IAAI,GAAGa,MAAM,CAAC2iB,KAAK;EACvB,IAAI,CAACxjB,IAAI,EAAE;IACPA,IAAI,GAAGa,MAAM,CAAC2iB,KAAK,GAAGgD,wBAAwB,CAAC3lB,MAAM,CAACsiB,OAAO,CAAC;EAClE;EACA,OAAOnjB,IAAI;AACf;AACA,SAASwmB,wBAAwBA,CAAC7D,MAAM,EAAE;EACtC,MAAMlY,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,KAAK,MAAM1I,KAAK,IAAI2gB,MAAM,EAAC;IACvB,KAAK,MAAMzhB,GAAG,IAAI7C,MAAM,CAAC2B,IAAI,CAACgC,KAAK,CAAC,CAAC8jB,MAAM,CAAE9kB,CAAC,IAAG,CAACA,CAAC,CAAC+V,UAAU,CAAC,GAAG,CAAC,CAAC,EAAC;MACjEtM,GAAG,CAAC8X,GAAG,CAACrhB,GAAG,CAAC;IAChB;EACJ;EACA,OAAO/C,KAAK,CAACwM,IAAI,CAACF,GAAG,CAAC;AAC1B;AACA,SAASgc,2BAA2BA,CAACra,IAAI,EAAEmO,IAAI,EAAEvS,KAAK,EAAEwE,KAAK,EAAE;EAC3D,MAAM;IAAEE;EAAQ,CAAC,GAAGN,IAAI;EACxB,MAAM;IAAElL,GAAG,GAAE;EAAK,CAAC,GAAG,IAAI,CAACwlB,QAAQ;EACnC,MAAMC,MAAM,GAAG,IAAIxoB,KAAK,CAACqO,KAAK,CAAC;EAC/B,IAAI1M,CAAC,EAAEO,IAAI,EAAEI,KAAK,EAAEoD,IAAI;EACxB,KAAI/D,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGmM,KAAK,EAAE1M,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IACnCW,KAAK,GAAGX,CAAC,GAAGkI,KAAK;IACjBnE,IAAI,GAAG0W,IAAI,CAAC9Z,KAAK,CAAC;IAClBkmB,MAAM,CAAC7mB,CAAC,CAAC,GAAG;MACR8mB,CAAC,EAAEla,MAAM,CAACma,KAAK,CAAC5jB,gBAAgB,CAACY,IAAI,EAAE3C,GAAG,CAAC,EAAET,KAAK;IACtD,CAAC;EACL;EACA,OAAOkmB,MAAM;AACjB;AAEA,MAAMG,OAAO,GAAGloB,MAAM,CAACkoB,OAAO,IAAI,KAAK;AACvC,MAAMC,QAAQ,GAAGA,CAAC1a,MAAM,EAAEvM,CAAC,KAAGA,CAAC,GAAGuM,MAAM,CAACpM,MAAM,IAAI,CAACoM,MAAM,CAACvM,CAAC,CAAC,CAACknB,IAAI,IAAI3a,MAAM,CAACvM,CAAC,CAAC;AAC/E,MAAMmnB,YAAY,GAAIrO,SAAS,IAAGA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/D,SAASsO,WAAWA,CAACC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEjZ,CAAC,EAAE;EACzD;EACA;EACA;EACA,MAAMnM,QAAQ,GAAGklB,UAAU,CAACH,IAAI,GAAGI,WAAW,GAAGD,UAAU;EAC3D,MAAMxlB,OAAO,GAAGylB,WAAW;EAC3B,MAAME,IAAI,GAAGD,UAAU,CAACL,IAAI,GAAGI,WAAW,GAAGC,UAAU;EACvD,MAAME,GAAG,GAAG7f,qBAAqB,CAAC/F,OAAO,EAAEM,QAAQ,CAAC;EACpD,MAAMulB,GAAG,GAAG9f,qBAAqB,CAAC4f,IAAI,EAAE3lB,OAAO,CAAC;EAChD,IAAI8lB,GAAG,GAAGF,GAAG,IAAIA,GAAG,GAAGC,GAAG,CAAC;EAC3B,IAAIE,GAAG,GAAGF,GAAG,IAAID,GAAG,GAAGC,GAAG,CAAC;EAC3B;EACAC,GAAG,GAAGthB,KAAK,CAACshB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1BC,GAAG,GAAGvhB,KAAK,CAACuhB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1B,MAAMC,EAAE,GAAGvZ,CAAC,GAAGqZ,GAAG,CAAC,CAAC;EACpB,MAAMG,EAAE,GAAGxZ,CAAC,GAAGsZ,GAAG;EAClB,OAAO;IACHzlB,QAAQ,EAAE;MACNK,CAAC,EAAEX,OAAO,CAACW,CAAC,GAAGqlB,EAAE,IAAIL,IAAI,CAAChlB,CAAC,GAAGL,QAAQ,CAACK,CAAC,CAAC;MACzCE,CAAC,EAAEb,OAAO,CAACa,CAAC,GAAGmlB,EAAE,IAAIL,IAAI,CAAC9kB,CAAC,GAAGP,QAAQ,CAACO,CAAC;IAC5C,CAAC;IACD8kB,IAAI,EAAE;MACFhlB,CAAC,EAAEX,OAAO,CAACW,CAAC,GAAGslB,EAAE,IAAIN,IAAI,CAAChlB,CAAC,GAAGL,QAAQ,CAACK,CAAC,CAAC;MACzCE,CAAC,EAAEb,OAAO,CAACa,CAAC,GAAGolB,EAAE,IAAIN,IAAI,CAAC9kB,CAAC,GAAGP,QAAQ,CAACO,CAAC;IAC5C;EACJ,CAAC;AACL;AACA;AACA;AACA;AAAI,SAASqlB,cAAcA,CAACxb,MAAM,EAAEyb,MAAM,EAAEC,EAAE,EAAE;EAC5C,MAAMC,SAAS,GAAG3b,MAAM,CAACpM,MAAM;EAC/B,IAAIgoB,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,YAAY;EACvD,IAAIC,UAAU,GAAGvB,QAAQ,CAAC1a,MAAM,EAAE,CAAC,CAAC;EACpC,KAAI,IAAIvM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkoB,SAAS,GAAG,CAAC,EAAE,EAAEloB,CAAC,EAAC;IAClCuoB,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAAC1a,MAAM,EAAEvM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACuoB,YAAY,IAAI,CAACC,UAAU,EAAE;MAC9B;IACJ;IACA,IAAIzjB,YAAY,CAACijB,MAAM,CAAChoB,CAAC,CAAC,EAAE,CAAC,EAAEgnB,OAAO,CAAC,EAAE;MACrCiB,EAAE,CAACjoB,CAAC,CAAC,GAAGioB,EAAE,CAACjoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACrB;IACJ;IACAmoB,MAAM,GAAGF,EAAE,CAACjoB,CAAC,CAAC,GAAGgoB,MAAM,CAAChoB,CAAC,CAAC;IAC1BooB,KAAK,GAAGH,EAAE,CAACjoB,CAAC,GAAG,CAAC,CAAC,GAAGgoB,MAAM,CAAChoB,CAAC,CAAC;IAC7BsoB,gBAAgB,GAAGlkB,IAAI,CAACmB,GAAG,CAAC4iB,MAAM,EAAE,CAAC,CAAC,GAAG/jB,IAAI,CAACmB,GAAG,CAAC6iB,KAAK,EAAE,CAAC,CAAC;IAC3D,IAAIE,gBAAgB,IAAI,CAAC,EAAE;MACvB;IACJ;IACAD,IAAI,GAAG,CAAC,GAAGjkB,IAAI,CAACyB,IAAI,CAACyiB,gBAAgB,CAAC;IACtCL,EAAE,CAACjoB,CAAC,CAAC,GAAGmoB,MAAM,GAAGE,IAAI,GAAGL,MAAM,CAAChoB,CAAC,CAAC;IACjCioB,EAAE,CAACjoB,CAAC,GAAG,CAAC,CAAC,GAAGooB,KAAK,GAAGC,IAAI,GAAGL,MAAM,CAAChoB,CAAC,CAAC;EACxC;AACJ;AACA,SAASyoB,eAAeA,CAAClc,MAAM,EAAE0b,EAAE,EAAEnP,SAAS,GAAG,GAAG,EAAE;EAClD,MAAM4P,SAAS,GAAGvB,YAAY,CAACrO,SAAS,CAAC;EACzC,MAAMoP,SAAS,GAAG3b,MAAM,CAACpM,MAAM;EAC/B,IAAI8T,KAAK,EAAE0U,WAAW,EAAEJ,YAAY;EACpC,IAAIC,UAAU,GAAGvB,QAAQ,CAAC1a,MAAM,EAAE,CAAC,CAAC;EACpC,KAAI,IAAIvM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkoB,SAAS,EAAE,EAAEloB,CAAC,EAAC;IAC9B2oB,WAAW,GAAGJ,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAAC1a,MAAM,EAAEvM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACuoB,YAAY,EAAE;MACf;IACJ;IACA,MAAMK,MAAM,GAAGL,YAAY,CAACzP,SAAS,CAAC;IACtC,MAAM+P,MAAM,GAAGN,YAAY,CAACG,SAAS,CAAC;IACtC,IAAIC,WAAW,EAAE;MACb1U,KAAK,GAAG,CAAC2U,MAAM,GAAGD,WAAW,CAAC7P,SAAS,CAAC,IAAI,CAAC;MAC7CyP,YAAY,CAAC,MAAMzP,SAAS,EAAE,CAAC,GAAG8P,MAAM,GAAG3U,KAAK;MAChDsU,YAAY,CAAC,MAAMG,SAAS,EAAE,CAAC,GAAGG,MAAM,GAAG5U,KAAK,GAAGgU,EAAE,CAACjoB,CAAC,CAAC;IAC5D;IACA,IAAIwoB,UAAU,EAAE;MACZvU,KAAK,GAAG,CAACuU,UAAU,CAAC1P,SAAS,CAAC,GAAG8P,MAAM,IAAI,CAAC;MAC5CL,YAAY,CAAC,MAAMzP,SAAS,EAAE,CAAC,GAAG8P,MAAM,GAAG3U,KAAK;MAChDsU,YAAY,CAAC,MAAMG,SAAS,EAAE,CAAC,GAAGG,MAAM,GAAG5U,KAAK,GAAGgU,EAAE,CAACjoB,CAAC,CAAC;IAC5D;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS8oB,mBAAmBA,CAACvc,MAAM,EAAEuM,SAAS,GAAG,GAAG,EAAE;EACtD,MAAM4P,SAAS,GAAGvB,YAAY,CAACrO,SAAS,CAAC;EACzC,MAAMoP,SAAS,GAAG3b,MAAM,CAACpM,MAAM;EAC/B,MAAM6nB,MAAM,GAAG3pB,KAAK,CAAC6pB,SAAS,CAAC,CAACvK,IAAI,CAAC,CAAC,CAAC;EACvC,MAAMsK,EAAE,GAAG5pB,KAAK,CAAC6pB,SAAS,CAAC;EAC3B;EACA,IAAIloB,CAAC,EAAE2oB,WAAW,EAAEJ,YAAY;EAChC,IAAIC,UAAU,GAAGvB,QAAQ,CAAC1a,MAAM,EAAE,CAAC,CAAC;EACpC,KAAIvM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkoB,SAAS,EAAE,EAAEloB,CAAC,EAAC;IAC1B2oB,WAAW,GAAGJ,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAAC1a,MAAM,EAAEvM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACuoB,YAAY,EAAE;MACf;IACJ;IACA,IAAIC,UAAU,EAAE;MACZ,MAAMO,UAAU,GAAGP,UAAU,CAAC1P,SAAS,CAAC,GAAGyP,YAAY,CAACzP,SAAS,CAAC;MAClE;MACAkP,MAAM,CAAChoB,CAAC,CAAC,GAAG+oB,UAAU,KAAK,CAAC,GAAG,CAACP,UAAU,CAACE,SAAS,CAAC,GAAGH,YAAY,CAACG,SAAS,CAAC,IAAIK,UAAU,GAAG,CAAC;IACrG;IACAd,EAAE,CAACjoB,CAAC,CAAC,GAAG,CAAC2oB,WAAW,GAAGX,MAAM,CAAChoB,CAAC,CAAC,GAAG,CAACwoB,UAAU,GAAGR,MAAM,CAAChoB,CAAC,GAAG,CAAC,CAAC,GAAG8E,IAAI,CAACkjB,MAAM,CAAChoB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK8E,IAAI,CAACkjB,MAAM,CAAChoB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAACgoB,MAAM,CAAChoB,CAAC,GAAG,CAAC,CAAC,GAAGgoB,MAAM,CAAChoB,CAAC,CAAC,IAAI,CAAC;EAClJ;EACA+nB,cAAc,CAACxb,MAAM,EAAEyb,MAAM,EAAEC,EAAE,CAAC;EAClCQ,eAAe,CAAClc,MAAM,EAAE0b,EAAE,EAAEnP,SAAS,CAAC;AAC1C;AACA,SAASkQ,eAAeA,CAACC,EAAE,EAAEtiB,GAAG,EAAEC,GAAG,EAAE;EACnC,OAAOxC,IAAI,CAACwC,GAAG,CAACxC,IAAI,CAACuC,GAAG,CAACsiB,EAAE,EAAEriB,GAAG,CAAC,EAAED,GAAG,CAAC;AAC3C;AACA,SAASuiB,eAAeA,CAAC3c,MAAM,EAAEwR,IAAI,EAAE;EACnC,IAAI/d,CAAC,EAAEO,IAAI,EAAEiN,KAAK,EAAE2b,MAAM,EAAEC,UAAU;EACtC,IAAIC,UAAU,GAAGvL,cAAc,CAACvR,MAAM,CAAC,CAAC,CAAC,EAAEwR,IAAI,CAAC;EAChD,KAAI/d,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGgM,MAAM,CAACpM,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;IAC3CopB,UAAU,GAAGD,MAAM;IACnBA,MAAM,GAAGE,UAAU;IACnBA,UAAU,GAAGrpB,CAAC,GAAGO,IAAI,GAAG,CAAC,IAAIud,cAAc,CAACvR,MAAM,CAACvM,CAAC,GAAG,CAAC,CAAC,EAAE+d,IAAI,CAAC;IAChE,IAAI,CAACoL,MAAM,EAAE;MACT;IACJ;IACA3b,KAAK,GAAGjB,MAAM,CAACvM,CAAC,CAAC;IACjB,IAAIopB,UAAU,EAAE;MACZ5b,KAAK,CAACgR,IAAI,GAAGwK,eAAe,CAACxb,KAAK,CAACgR,IAAI,EAAET,IAAI,CAAC9R,IAAI,EAAE8R,IAAI,CAAC7R,KAAK,CAAC;MAC/DsB,KAAK,CAACkR,IAAI,GAAGsK,eAAe,CAACxb,KAAK,CAACkR,IAAI,EAAEX,IAAI,CAACpL,GAAG,EAAEoL,IAAI,CAACnL,MAAM,CAAC;IACnE;IACA,IAAIyW,UAAU,EAAE;MACZ7b,KAAK,CAACiR,IAAI,GAAGuK,eAAe,CAACxb,KAAK,CAACiR,IAAI,EAAEV,IAAI,CAAC9R,IAAI,EAAE8R,IAAI,CAAC7R,KAAK,CAAC;MAC/DsB,KAAK,CAACmR,IAAI,GAAGqK,eAAe,CAACxb,KAAK,CAACmR,IAAI,EAAEZ,IAAI,CAACpL,GAAG,EAAEoL,IAAI,CAACnL,MAAM,CAAC;IACnE;EACJ;AACJ;AACA;AACA;AACA;AAAI,SAAS0W,0BAA0BA,CAAC/c,MAAM,EAAEhL,OAAO,EAAEwc,IAAI,EAAEtM,IAAI,EAAEqH,SAAS,EAAE;EAC5E,IAAI9Y,CAAC,EAAEO,IAAI,EAAEiN,KAAK,EAAE+b,aAAa;EACjC;EACA,IAAIhoB,OAAO,CAACwL,QAAQ,EAAE;IAClBR,MAAM,GAAGA,MAAM,CAACyZ,MAAM,CAAEiD,EAAE,IAAG,CAACA,EAAE,CAAC/B,IAAI,CAAC;EAC1C;EACA,IAAI3lB,OAAO,CAACioB,sBAAsB,KAAK,UAAU,EAAE;IAC/CV,mBAAmB,CAACvc,MAAM,EAAEuM,SAAS,CAAC;EAC1C,CAAC,MAAM;IACH,IAAI2Q,IAAI,GAAGhY,IAAI,GAAGlF,MAAM,CAACA,MAAM,CAACpM,MAAM,GAAG,CAAC,CAAC,GAAGoM,MAAM,CAAC,CAAC,CAAC;IACvD,KAAIvM,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGgM,MAAM,CAACpM,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;MAC3CwN,KAAK,GAAGjB,MAAM,CAACvM,CAAC,CAAC;MACjBupB,aAAa,GAAGnC,WAAW,CAACqC,IAAI,EAAEjc,KAAK,EAAEjB,MAAM,CAACnI,IAAI,CAACuC,GAAG,CAAC3G,CAAC,GAAG,CAAC,EAAEO,IAAI,IAAIkR,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGlR,IAAI,CAAC,EAAEgB,OAAO,CAACmoB,OAAO,CAAC;MAChHlc,KAAK,CAACgR,IAAI,GAAG+K,aAAa,CAACpnB,QAAQ,CAACK,CAAC;MACrCgL,KAAK,CAACkR,IAAI,GAAG6K,aAAa,CAACpnB,QAAQ,CAACO,CAAC;MACrC8K,KAAK,CAACiR,IAAI,GAAG8K,aAAa,CAAC/B,IAAI,CAAChlB,CAAC;MACjCgL,KAAK,CAACmR,IAAI,GAAG4K,aAAa,CAAC/B,IAAI,CAAC9kB,CAAC;MACjC+mB,IAAI,GAAGjc,KAAK;IAChB;EACJ;EACA,IAAIjM,OAAO,CAAC2nB,eAAe,EAAE;IACzBA,eAAe,CAAC3c,MAAM,EAAEwR,IAAI,CAAC;EACjC;AACJ;;AAEA;AACA;AACA;AAAI,SAAS4L,eAAeA,CAAA,EAAG;EAC3B,OAAO,OAAOxe,MAAM,KAAK,WAAW,IAAI,OAAOye,QAAQ,KAAK,WAAW;AAC3E;AACA;AACA;AACA;AAAI,SAASC,cAAcA,CAACC,OAAO,EAAE;EACjC,IAAI3D,MAAM,GAAG2D,OAAO,CAACC,UAAU;EAC/B,IAAI5D,MAAM,IAAIA,MAAM,CAAC1nB,QAAQ,CAAC,CAAC,KAAK,qBAAqB,EAAE;IACvD0nB,MAAM,GAAGA,MAAM,CAAC6D,IAAI;EACxB;EACA,OAAO7D,MAAM;AACjB;AACA;AACA;AACA;AACA;AAAI,SAAS8D,aAAaA,CAACC,UAAU,EAAE7S,IAAI,EAAE8S,cAAc,EAAE;EACzD,IAAIC,aAAa;EACjB,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAChCE,aAAa,GAAGpI,QAAQ,CAACkI,UAAU,EAAE,EAAE,CAAC;IACxC,IAAIA,UAAU,CAAC7oB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAChC;MACA+oB,aAAa,GAAGA,aAAa,GAAG,GAAG,GAAG/S,IAAI,CAAC0S,UAAU,CAACI,cAAc,CAAC;IACzE;EACJ,CAAC,MAAM;IACHC,aAAa,GAAGF,UAAU;EAC9B;EACA,OAAOE,aAAa;AACxB;AACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAGA,OAAO,CAACC,aAAa,CAACC,WAAW,CAACH,gBAAgB,CAACC,OAAO,EAAE,IAAI,CAAC;AACrG,SAASG,QAAQA,CAACC,EAAE,EAAEhkB,QAAQ,EAAE;EAC5B,OAAO2jB,gBAAgB,CAACK,EAAE,CAAC,CAACC,gBAAgB,CAACjkB,QAAQ,CAAC;AAC1D;AACA,MAAMkkB,SAAS,GAAG,CACd,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,CACT;AACD,SAASC,kBAAkBA,CAACC,MAAM,EAAExS,KAAK,EAAEyS,MAAM,EAAE;EAC/C,MAAMnlB,MAAM,GAAG,CAAC,CAAC;EACjBmlB,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE;EACnC,KAAI,IAAI/qB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAC;IACtB,MAAMgrB,GAAG,GAAGJ,SAAS,CAAC5qB,CAAC,CAAC;IACxB4F,MAAM,CAAColB,GAAG,CAAC,GAAG1rB,UAAU,CAACwrB,MAAM,CAACxS,KAAK,GAAG,GAAG,GAAG0S,GAAG,GAAGD,MAAM,CAAC,CAAC,IAAI,CAAC;EACrE;EACAnlB,MAAM,CAACmQ,KAAK,GAAGnQ,MAAM,CAACqG,IAAI,GAAGrG,MAAM,CAACsG,KAAK;EACzCtG,MAAM,CAACuW,MAAM,GAAGvW,MAAM,CAAC+M,GAAG,GAAG/M,MAAM,CAACgN,MAAM;EAC1C,OAAOhN,MAAM;AACjB;AACA,MAAMqlB,YAAY,GAAGA,CAACzoB,CAAC,EAAEE,CAAC,EAAE3B,MAAM,KAAG,CAACyB,CAAC,GAAG,CAAC,IAAIE,CAAC,GAAG,CAAC,MAAM,CAAC3B,MAAM,IAAI,CAACA,MAAM,CAACmqB,UAAU,CAAC;AACxF;AACA;AACA;AACA;AACA;AAAI,SAASC,iBAAiBA,CAACjnB,CAAC,EAAE6X,MAAM,EAAE;EACtC,MAAMqP,OAAO,GAAGlnB,CAAC,CAACknB,OAAO;EACzB,MAAMvqB,MAAM,GAAGuqB,OAAO,IAAIA,OAAO,CAACjrB,MAAM,GAAGirB,OAAO,CAAC,CAAC,CAAC,GAAGlnB,CAAC;EACzD,MAAM;IAAEmnB,OAAO;IAAGC;EAAS,CAAC,GAAGzqB,MAAM;EACrC,IAAI0qB,GAAG,GAAG,KAAK;EACf,IAAI/oB,CAAC,EAAEE,CAAC;EACR,IAAIuoB,YAAY,CAACI,OAAO,EAAEC,OAAO,EAAEpnB,CAAC,CAACnD,MAAM,CAAC,EAAE;IAC1CyB,CAAC,GAAG6oB,OAAO;IACX3oB,CAAC,GAAG4oB,OAAO;EACf,CAAC,MAAM;IACH,MAAM5N,IAAI,GAAG3B,MAAM,CAACyP,qBAAqB,CAAC,CAAC;IAC3ChpB,CAAC,GAAG3B,MAAM,CAAC4qB,OAAO,GAAG/N,IAAI,CAACzR,IAAI;IAC9BvJ,CAAC,GAAG7B,MAAM,CAAC6qB,OAAO,GAAGhO,IAAI,CAAC/K,GAAG;IAC7B4Y,GAAG,GAAG,IAAI;EACd;EACA,OAAO;IACH/oB,CAAC;IACDE,CAAC;IACD6oB;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,mBAAmBA,CAACC,KAAK,EAAE7X,KAAK,EAAE;EAC3C,IAAI,QAAQ,IAAI6X,KAAK,EAAE;IACnB,OAAOA,KAAK;EAChB;EACA,MAAM;IAAE7P,MAAM;IAAGH;EAAyB,CAAC,GAAG7H,KAAK;EACnD,MAAMuE,KAAK,GAAG+R,gBAAgB,CAACtO,MAAM,CAAC;EACtC,MAAM8P,SAAS,GAAGvT,KAAK,CAACwT,SAAS,KAAK,YAAY;EAClD,MAAMC,QAAQ,GAAGlB,kBAAkB,CAACvS,KAAK,EAAE,SAAS,CAAC;EACrD,MAAM0T,OAAO,GAAGnB,kBAAkB,CAACvS,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;EAC5D,MAAM;IAAE9V,CAAC;IAAGE,CAAC;IAAG6oB;EAAK,CAAC,GAAGJ,iBAAiB,CAACS,KAAK,EAAE7P,MAAM,CAAC;EACzD,MAAMQ,OAAO,GAAGwP,QAAQ,CAAC9f,IAAI,IAAIsf,GAAG,IAAIS,OAAO,CAAC/f,IAAI,CAAC;EACrD,MAAMuQ,OAAO,GAAGuP,QAAQ,CAACpZ,GAAG,IAAI4Y,GAAG,IAAIS,OAAO,CAACrZ,GAAG,CAAC;EACnD,IAAI;IAAEoD,KAAK;IAAGoG;EAAQ,CAAC,GAAGpI,KAAK;EAC/B,IAAI8X,SAAS,EAAE;IACX9V,KAAK,IAAIgW,QAAQ,CAAChW,KAAK,GAAGiW,OAAO,CAACjW,KAAK;IACvCoG,MAAM,IAAI4P,QAAQ,CAAC5P,MAAM,GAAG6P,OAAO,CAAC7P,MAAM;EAC9C;EACA,OAAO;IACH3Z,CAAC,EAAE4B,IAAI,CAACiB,KAAK,CAAC,CAAC7C,CAAC,GAAG+Z,OAAO,IAAIxG,KAAK,GAAGgG,MAAM,CAAChG,KAAK,GAAG6F,uBAAuB,CAAC;IAC7ElZ,CAAC,EAAE0B,IAAI,CAACiB,KAAK,CAAC,CAAC3C,CAAC,GAAG8Z,OAAO,IAAIL,MAAM,GAAGJ,MAAM,CAACI,MAAM,GAAGP,uBAAuB;EAClF,CAAC;AACL;AACA,SAASqQ,gBAAgBA,CAAClQ,MAAM,EAAEhG,KAAK,EAAEoG,MAAM,EAAE;EAC7C,IAAIoE,QAAQ,EAAE2L,SAAS;EACvB,IAAInW,KAAK,KAAK5X,SAAS,IAAIge,MAAM,KAAKhe,SAAS,EAAE;IAC7C,MAAMguB,SAAS,GAAGpQ,MAAM,IAAI8N,cAAc,CAAC9N,MAAM,CAAC;IAClD,IAAI,CAACoQ,SAAS,EAAE;MACZpW,KAAK,GAAGgG,MAAM,CAACqQ,WAAW;MAC1BjQ,MAAM,GAAGJ,MAAM,CAACsQ,YAAY;IAChC,CAAC,MAAM;MACH,MAAM3O,IAAI,GAAGyO,SAAS,CAACX,qBAAqB,CAAC,CAAC,CAAC,CAAC;MAChD,MAAMc,cAAc,GAAGjC,gBAAgB,CAAC8B,SAAS,CAAC;MAClD,MAAMI,eAAe,GAAG1B,kBAAkB,CAACyB,cAAc,EAAE,QAAQ,EAAE,OAAO,CAAC;MAC7E,MAAME,gBAAgB,GAAG3B,kBAAkB,CAACyB,cAAc,EAAE,SAAS,CAAC;MACtEvW,KAAK,GAAG2H,IAAI,CAAC3H,KAAK,GAAGyW,gBAAgB,CAACzW,KAAK,GAAGwW,eAAe,CAACxW,KAAK;MACnEoG,MAAM,GAAGuB,IAAI,CAACvB,MAAM,GAAGqQ,gBAAgB,CAACrQ,MAAM,GAAGoQ,eAAe,CAACpQ,MAAM;MACvEoE,QAAQ,GAAG0J,aAAa,CAACqC,cAAc,CAAC/L,QAAQ,EAAE4L,SAAS,EAAE,aAAa,CAAC;MAC3ED,SAAS,GAAGjC,aAAa,CAACqC,cAAc,CAACJ,SAAS,EAAEC,SAAS,EAAE,cAAc,CAAC;IAClF;EACJ;EACA,OAAO;IACHpW,KAAK;IACLoG,MAAM;IACNoE,QAAQ,EAAEA,QAAQ,IAAIhc,QAAQ;IAC9B2nB,SAAS,EAAEA,SAAS,IAAI3nB;EAC5B,CAAC;AACL;AACA,MAAMkoB,MAAM,GAAIlqB,CAAC,IAAG6B,IAAI,CAACiB,KAAK,CAAC9C,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;AAC3C;AACA,SAASmqB,cAAcA,CAAC3Q,MAAM,EAAE4Q,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAC5D,MAAMvU,KAAK,GAAG+R,gBAAgB,CAACtO,MAAM,CAAC;EACtC,MAAM+Q,OAAO,GAAGjC,kBAAkB,CAACvS,KAAK,EAAE,QAAQ,CAAC;EACnD,MAAMiI,QAAQ,GAAG0J,aAAa,CAAC3R,KAAK,CAACiI,QAAQ,EAAExE,MAAM,EAAE,aAAa,CAAC,IAAIxX,QAAQ;EACjF,MAAM2nB,SAAS,GAAGjC,aAAa,CAAC3R,KAAK,CAAC4T,SAAS,EAAEnQ,MAAM,EAAE,cAAc,CAAC,IAAIxX,QAAQ;EACpF,MAAMwoB,aAAa,GAAGd,gBAAgB,CAAClQ,MAAM,EAAE4Q,OAAO,EAAEC,QAAQ,CAAC;EACjE,IAAI;IAAE7W,KAAK;IAAGoG;EAAQ,CAAC,GAAG4Q,aAAa;EACvC,IAAIzU,KAAK,CAACwT,SAAS,KAAK,aAAa,EAAE;IACnC,MAAME,OAAO,GAAGnB,kBAAkB,CAACvS,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IAC5D,MAAMyT,QAAQ,GAAGlB,kBAAkB,CAACvS,KAAK,EAAE,SAAS,CAAC;IACrDvC,KAAK,IAAIgW,QAAQ,CAAChW,KAAK,GAAGiW,OAAO,CAACjW,KAAK;IACvCoG,MAAM,IAAI4P,QAAQ,CAAC5P,MAAM,GAAG6P,OAAO,CAAC7P,MAAM;EAC9C;EACApG,KAAK,GAAG3R,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAEmP,KAAK,GAAG+W,OAAO,CAAC/W,KAAK,CAAC;EAC1CoG,MAAM,GAAG/X,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAEimB,WAAW,GAAG9W,KAAK,GAAG8W,WAAW,GAAG1Q,MAAM,GAAG2Q,OAAO,CAAC3Q,MAAM,CAAC;EACjFpG,KAAK,GAAG0W,MAAM,CAACroB,IAAI,CAACuC,GAAG,CAACoP,KAAK,EAAEwK,QAAQ,EAAEwM,aAAa,CAACxM,QAAQ,CAAC,CAAC;EACjEpE,MAAM,GAAGsQ,MAAM,CAACroB,IAAI,CAACuC,GAAG,CAACwV,MAAM,EAAE+P,SAAS,EAAEa,aAAa,CAACb,SAAS,CAAC,CAAC;EACrE,IAAInW,KAAK,IAAI,CAACoG,MAAM,EAAE;IAClB;IACA;IACAA,MAAM,GAAGsQ,MAAM,CAAC1W,KAAK,GAAG,CAAC,CAAC;EAC9B;EACA,MAAMiX,cAAc,GAAGL,OAAO,KAAKxuB,SAAS,IAAIyuB,QAAQ,KAAKzuB,SAAS;EACtE,IAAI6uB,cAAc,IAAIH,WAAW,IAAIE,aAAa,CAAC5Q,MAAM,IAAIA,MAAM,GAAG4Q,aAAa,CAAC5Q,MAAM,EAAE;IACxFA,MAAM,GAAG4Q,aAAa,CAAC5Q,MAAM;IAC7BpG,KAAK,GAAG0W,MAAM,CAACroB,IAAI,CAACoB,KAAK,CAAC2W,MAAM,GAAG0Q,WAAW,CAAC,CAAC;EACpD;EACA,OAAO;IACH9W,KAAK;IACLoG;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAAS8Q,WAAWA,CAAClZ,KAAK,EAAEmZ,UAAU,EAAEC,UAAU,EAAE;EACpD,MAAMC,UAAU,GAAGF,UAAU,IAAI,CAAC;EAClC,MAAMG,YAAY,GAAGjpB,IAAI,CAACoB,KAAK,CAACuO,KAAK,CAACoI,MAAM,GAAGiR,UAAU,CAAC;EAC1D,MAAME,WAAW,GAAGlpB,IAAI,CAACoB,KAAK,CAACuO,KAAK,CAACgC,KAAK,GAAGqX,UAAU,CAAC;EACxDrZ,KAAK,CAACoI,MAAM,GAAG/X,IAAI,CAACoB,KAAK,CAACuO,KAAK,CAACoI,MAAM,CAAC;EACvCpI,KAAK,CAACgC,KAAK,GAAG3R,IAAI,CAACoB,KAAK,CAACuO,KAAK,CAACgC,KAAK,CAAC;EACrC,MAAMgG,MAAM,GAAGhI,KAAK,CAACgI,MAAM;EAC3B;EACA;EACA;EACA,IAAIA,MAAM,CAACzD,KAAK,KAAK6U,UAAU,IAAI,CAACpR,MAAM,CAACzD,KAAK,CAAC6D,MAAM,IAAI,CAACJ,MAAM,CAACzD,KAAK,CAACvC,KAAK,CAAC,EAAE;IAC7EgG,MAAM,CAACzD,KAAK,CAAC6D,MAAM,GAAG,GAAGpI,KAAK,CAACoI,MAAM,IAAI;IACzCJ,MAAM,CAACzD,KAAK,CAACvC,KAAK,GAAG,GAAGhC,KAAK,CAACgC,KAAK,IAAI;EAC3C;EACA,IAAIhC,KAAK,CAAC6H,uBAAuB,KAAKwR,UAAU,IAAIrR,MAAM,CAACI,MAAM,KAAKkR,YAAY,IAAItR,MAAM,CAAChG,KAAK,KAAKuX,WAAW,EAAE;IAChHvZ,KAAK,CAAC6H,uBAAuB,GAAGwR,UAAU;IAC1CrR,MAAM,CAACI,MAAM,GAAGkR,YAAY;IAC5BtR,MAAM,CAAChG,KAAK,GAAGuX,WAAW;IAC1BvZ,KAAK,CAAC4E,GAAG,CAAC4U,YAAY,CAACH,UAAU,EAAE,CAAC,EAAE,CAAC,EAAEA,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1D,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMI,4BAA4B,GAAG,YAAW;EAChD,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAI;IACA,MAAMlsB,OAAO,GAAG;MACZ,IAAImsB,OAAOA,CAAA,EAAI;QACXD,gBAAgB,GAAG,IAAI;QACvB,OAAO,KAAK;MAChB;IACJ,CAAC;IACD,IAAI9D,eAAe,CAAC,CAAC,EAAE;MACnBxe,MAAM,CAACwiB,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAEpsB,OAAO,CAAC;MAC9C4J,MAAM,CAACyiB,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAErsB,OAAO,CAAC;IACrD;EACJ,CAAC,CAAC,OAAO2C,CAAC,EAAE;IACZ;EAAA;EAEA,OAAOupB,gBAAgB;AAC3B,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,SAASI,YAAYA,CAACvD,OAAO,EAAE5jB,QAAQ,EAAE;EACzC,MAAMxI,KAAK,GAAGusB,QAAQ,CAACH,OAAO,EAAE5jB,QAAQ,CAAC;EACzC,MAAMwa,OAAO,GAAGhjB,KAAK,IAAIA,KAAK,CAACijB,KAAK,CAAC,mBAAmB,CAAC;EACzD,OAAOD,OAAO,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG/iB,SAAS;AAC5C;;AAEA;AACA;AACA;AAAI,SAAS2vB,YAAYA,CAACC,EAAE,EAAEC,EAAE,EAAE1f,CAAC,EAAE0K,IAAI,EAAE;EACvC,OAAO;IACHxW,CAAC,EAAEurB,EAAE,CAACvrB,CAAC,GAAG8L,CAAC,IAAI0f,EAAE,CAACxrB,CAAC,GAAGurB,EAAE,CAACvrB,CAAC,CAAC;IAC3BE,CAAC,EAAEqrB,EAAE,CAACrrB,CAAC,GAAG4L,CAAC,IAAI0f,EAAE,CAACtrB,CAAC,GAAGqrB,EAAE,CAACrrB,CAAC;EAC9B,CAAC;AACL;AACA;AACA;AACA;AAAI,SAASurB,qBAAqBA,CAACF,EAAE,EAAEC,EAAE,EAAE1f,CAAC,EAAE0K,IAAI,EAAE;EAChD,OAAO;IACHxW,CAAC,EAAEurB,EAAE,CAACvrB,CAAC,GAAG8L,CAAC,IAAI0f,EAAE,CAACxrB,CAAC,GAAGurB,EAAE,CAACvrB,CAAC,CAAC;IAC3BE,CAAC,EAAEsW,IAAI,KAAK,QAAQ,GAAG1K,CAAC,GAAG,GAAG,GAAGyf,EAAE,CAACrrB,CAAC,GAAGsrB,EAAE,CAACtrB,CAAC,GAAGsW,IAAI,KAAK,OAAO,GAAG1K,CAAC,GAAG,CAAC,GAAGyf,EAAE,CAACrrB,CAAC,GAAGsrB,EAAE,CAACtrB,CAAC,GAAG4L,CAAC,GAAG,CAAC,GAAG0f,EAAE,CAACtrB,CAAC,GAAGqrB,EAAE,CAACrrB;EAC9G,CAAC;AACL;AACA;AACA;AACA;AAAI,SAASwrB,oBAAoBA,CAACH,EAAE,EAAEC,EAAE,EAAE1f,CAAC,EAAE0K,IAAI,EAAE;EAC/C,MAAMmV,GAAG,GAAG;IACR3rB,CAAC,EAAEurB,EAAE,CAACtP,IAAI;IACV/b,CAAC,EAAEqrB,EAAE,CAACpP;EACV,CAAC;EACD,MAAMyP,GAAG,GAAG;IACR5rB,CAAC,EAAEwrB,EAAE,CAACxP,IAAI;IACV9b,CAAC,EAAEsrB,EAAE,CAACtP;EACV,CAAC;EACD,MAAM9a,CAAC,GAAGkqB,YAAY,CAACC,EAAE,EAAEI,GAAG,EAAE7f,CAAC,CAAC;EAClC,MAAMzK,CAAC,GAAGiqB,YAAY,CAACK,GAAG,EAAEC,GAAG,EAAE9f,CAAC,CAAC;EACnC,MAAM+f,CAAC,GAAGP,YAAY,CAACM,GAAG,EAAEJ,EAAE,EAAE1f,CAAC,CAAC;EAClC,MAAMqC,CAAC,GAAGmd,YAAY,CAAClqB,CAAC,EAAEC,CAAC,EAAEyK,CAAC,CAAC;EAC/B,MAAMpK,CAAC,GAAG4pB,YAAY,CAACjqB,CAAC,EAAEwqB,CAAC,EAAE/f,CAAC,CAAC;EAC/B,OAAOwf,YAAY,CAACnd,CAAC,EAAEzM,CAAC,EAAEoK,CAAC,CAAC;AAChC;AAEA,MAAMggB,qBAAqB,GAAG,SAAAA,CAASC,KAAK,EAAExY,KAAK,EAAE;EACjD,OAAO;IACHvT,CAACA,CAAEA,CAAC,EAAE;MACF,OAAO+rB,KAAK,GAAGA,KAAK,GAAGxY,KAAK,GAAGvT,CAAC;IACpC,CAAC;IACDgsB,QAAQA,CAAElS,CAAC,EAAE;MACTvG,KAAK,GAAGuG,CAAC;IACb,CAAC;IACD0C,SAASA,CAAElT,KAAK,EAAE;MACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;QACpB,OAAOA,KAAK;MAChB;MACA,OAAOA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC/C,CAAC;IACD2iB,KAAKA,CAAEjsB,CAAC,EAAEtE,KAAK,EAAE;MACb,OAAOsE,CAAC,GAAGtE,KAAK;IACpB,CAAC;IACDwwB,UAAUA,CAAElsB,CAAC,EAAEmsB,SAAS,EAAE;MACtB,OAAOnsB,CAAC,GAAGmsB,SAAS;IACxB;EACJ,CAAC;AACL,CAAC;AACD,MAAMC,qBAAqB,GAAG,SAAAA,CAAA,EAAW;EACrC,OAAO;IACHpsB,CAACA,CAAEA,CAAC,EAAE;MACF,OAAOA,CAAC;IACZ,CAAC;IACDgsB,QAAQA,CAAElS,CAAC,EAAE,CAAC,CAAC;IACf0C,SAASA,CAAElT,KAAK,EAAE;MACd,OAAOA,KAAK;IAChB,CAAC;IACD2iB,KAAKA,CAAEjsB,CAAC,EAAEtE,KAAK,EAAE;MACb,OAAOsE,CAAC,GAAGtE,KAAK;IACpB,CAAC;IACDwwB,UAAUA,CAAElsB,CAAC,EAAEqsB,UAAU,EAAE;MACvB,OAAOrsB,CAAC;IACZ;EACJ,CAAC;AACL,CAAC;AACD,SAASssB,aAAaA,CAAC3iB,GAAG,EAAEoiB,KAAK,EAAExY,KAAK,EAAE;EACtC,OAAO5J,GAAG,GAAGmiB,qBAAqB,CAACC,KAAK,EAAExY,KAAK,CAAC,GAAG6Y,qBAAqB,CAAC,CAAC;AAC9E;AACA,SAASG,qBAAqBA,CAACpW,GAAG,EAAEqW,SAAS,EAAE;EAC3C,IAAI1W,KAAK,EAAE2W,QAAQ;EACnB,IAAID,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,EAAE;IAC5C1W,KAAK,GAAGK,GAAG,CAACoD,MAAM,CAACzD,KAAK;IACxB2W,QAAQ,GAAG,CACP3W,KAAK,CAACqS,gBAAgB,CAAC,WAAW,CAAC,EACnCrS,KAAK,CAAC4W,mBAAmB,CAAC,WAAW,CAAC,CACzC;IACD5W,KAAK,CAAC6W,WAAW,CAAC,WAAW,EAAEH,SAAS,EAAE,WAAW,CAAC;IACtDrW,GAAG,CAACyW,iBAAiB,GAAGH,QAAQ;EACpC;AACJ;AACA,SAASI,oBAAoBA,CAAC1W,GAAG,EAAEsW,QAAQ,EAAE;EACzC,IAAIA,QAAQ,KAAK9wB,SAAS,EAAE;IACxB,OAAOwa,GAAG,CAACyW,iBAAiB;IAC5BzW,GAAG,CAACoD,MAAM,CAACzD,KAAK,CAAC6W,WAAW,CAAC,WAAW,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;EACvE;AACJ;AAEA,SAASK,UAAUA,CAAC5oB,QAAQ,EAAE;EAC1B,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACtB,OAAO;MACH6oB,OAAO,EAAEtnB,aAAa;MACtBunB,OAAO,EAAEznB,UAAU;MACnB0nB,SAAS,EAAEznB;IACf,CAAC;EACL;EACA,OAAO;IACHunB,OAAO,EAAE3mB,UAAU;IACnB4mB,OAAO,EAAEA,CAAC5rB,CAAC,EAAEC,CAAC,KAAGD,CAAC,GAAGC,CAAC;IACtB4rB,SAAS,EAAGjtB,CAAC,IAAGA;EACpB,CAAC;AACL;AACA,SAASktB,gBAAgBA,CAAC;EAAExnB,KAAK;EAAGC,GAAG;EAAGuE,KAAK;EAAG+E,IAAI;EAAG6G;AAAO,CAAC,EAAE;EAC/D,OAAO;IACHpQ,KAAK,EAAEA,KAAK,GAAGwE,KAAK;IACpBvE,GAAG,EAAEA,GAAG,GAAGuE,KAAK;IAChB+E,IAAI,EAAEA,IAAI,IAAI,CAACtJ,GAAG,GAAGD,KAAK,GAAG,CAAC,IAAIwE,KAAK,KAAK,CAAC;IAC7C4L;EACJ,CAAC;AACL;AACA,SAASqX,UAAUA,CAACC,OAAO,EAAErjB,MAAM,EAAE0I,MAAM,EAAE;EACzC,MAAM;IAAEvO,QAAQ;IAAGwB,KAAK,EAAE2nB,UAAU;IAAG1nB,GAAG,EAAE2nB;EAAU,CAAC,GAAG7a,MAAM;EAChE,MAAM;IAAEsa,OAAO;IAAGE;EAAW,CAAC,GAAGH,UAAU,CAAC5oB,QAAQ,CAAC;EACrD,MAAMgG,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,IAAI;IAAE+H,KAAK;IAAGC,GAAG;IAAGsJ;EAAM,CAAC,GAAGme,OAAO;EACrC,IAAI5vB,CAAC,EAAEO,IAAI;EACX,IAAIkR,IAAI,EAAE;IACNvJ,KAAK,IAAIwE,KAAK;IACdvE,GAAG,IAAIuE,KAAK;IACZ,KAAI1M,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGmM,KAAK,EAAE1M,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAC;MACnC,IAAI,CAACuvB,OAAO,CAACE,SAAS,CAACljB,MAAM,CAACrE,KAAK,GAAGwE,KAAK,CAAC,CAAChG,QAAQ,CAAC,CAAC,EAAEmpB,UAAU,EAAEC,QAAQ,CAAC,EAAE;QAC5E;MACJ;MACA5nB,KAAK,EAAE;MACPC,GAAG,EAAE;IACT;IACAD,KAAK,IAAIwE,KAAK;IACdvE,GAAG,IAAIuE,KAAK;EAChB;EACA,IAAIvE,GAAG,GAAGD,KAAK,EAAE;IACbC,GAAG,IAAIuE,KAAK;EAChB;EACA,OAAO;IACHxE,KAAK;IACLC,GAAG;IACHsJ,IAAI;IACJ6G,KAAK,EAAEsX,OAAO,CAACtX;EACnB,CAAC;AACL;AACC,SAASyX,aAAaA,CAACH,OAAO,EAAErjB,MAAM,EAAE0I,MAAM,EAAE;EAC7C,IAAI,CAACA,MAAM,EAAE;IACT,OAAO,CACH2a,OAAO,CACV;EACL;EACA,MAAM;IAAElpB,QAAQ;IAAGwB,KAAK,EAAE2nB,UAAU;IAAG1nB,GAAG,EAAE2nB;EAAU,CAAC,GAAG7a,MAAM;EAChE,MAAMvI,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,MAAM;IAAEqvB,OAAO;IAAGD,OAAO;IAAGE;EAAW,CAAC,GAAGH,UAAU,CAAC5oB,QAAQ,CAAC;EAC/D,MAAM;IAAEwB,KAAK;IAAGC,GAAG;IAAGsJ,IAAI;IAAG6G;EAAO,CAAC,GAAGqX,UAAU,CAACC,OAAO,EAAErjB,MAAM,EAAE0I,MAAM,CAAC;EAC3E,MAAMrP,MAAM,GAAG,EAAE;EACjB,IAAIoqB,MAAM,GAAG,KAAK;EAClB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAI/xB,KAAK,EAAEsP,KAAK,EAAE0iB,SAAS;EAC3B,MAAMC,aAAa,GAAGA,CAAA,KAAIZ,OAAO,CAACM,UAAU,EAAEK,SAAS,EAAEhyB,KAAK,CAAC,IAAIsxB,OAAO,CAACK,UAAU,EAAEK,SAAS,CAAC,KAAK,CAAC;EACvG,MAAME,WAAW,GAAGA,CAAA,KAAIZ,OAAO,CAACM,QAAQ,EAAE5xB,KAAK,CAAC,KAAK,CAAC,IAAIqxB,OAAO,CAACO,QAAQ,EAAEI,SAAS,EAAEhyB,KAAK,CAAC;EAC7F,MAAMmyB,WAAW,GAAGA,CAAA,KAAIL,MAAM,IAAIG,aAAa,CAAC,CAAC;EACjD,MAAMG,UAAU,GAAGA,CAAA,KAAI,CAACN,MAAM,IAAII,WAAW,CAAC,CAAC;EAC/C,KAAI,IAAIpwB,CAAC,GAAGkI,KAAK,EAAEuhB,IAAI,GAAGvhB,KAAK,EAAElI,CAAC,IAAImI,GAAG,EAAE,EAAEnI,CAAC,EAAC;IAC3CwN,KAAK,GAAGjB,MAAM,CAACvM,CAAC,GAAG0M,KAAK,CAAC;IACzB,IAAIc,KAAK,CAAC0Z,IAAI,EAAE;MACZ;IACJ;IACAhpB,KAAK,GAAGuxB,SAAS,CAACjiB,KAAK,CAAC9G,QAAQ,CAAC,CAAC;IAClC,IAAIxI,KAAK,KAAKgyB,SAAS,EAAE;MACrB;IACJ;IACAF,MAAM,GAAGT,OAAO,CAACrxB,KAAK,EAAE2xB,UAAU,EAAEC,QAAQ,CAAC;IAC7C,IAAIG,QAAQ,KAAK,IAAI,IAAII,WAAW,CAAC,CAAC,EAAE;MACpCJ,QAAQ,GAAGT,OAAO,CAACtxB,KAAK,EAAE2xB,UAAU,CAAC,KAAK,CAAC,GAAG7vB,CAAC,GAAGypB,IAAI;IAC1D;IACA,IAAIwG,QAAQ,KAAK,IAAI,IAAIK,UAAU,CAAC,CAAC,EAAE;MACnC1qB,MAAM,CAAC5C,IAAI,CAAC0sB,gBAAgB,CAAC;QACzBxnB,KAAK,EAAE+nB,QAAQ;QACf9nB,GAAG,EAAEnI,CAAC;QACNyR,IAAI;QACJ/E,KAAK;QACL4L;MACJ,CAAC,CAAC,CAAC;MACH2X,QAAQ,GAAG,IAAI;IACnB;IACAxG,IAAI,GAAGzpB,CAAC;IACRkwB,SAAS,GAAGhyB,KAAK;EACrB;EACA,IAAI+xB,QAAQ,KAAK,IAAI,EAAE;IACnBrqB,MAAM,CAAC5C,IAAI,CAAC0sB,gBAAgB,CAAC;MACzBxnB,KAAK,EAAE+nB,QAAQ;MACf9nB,GAAG;MACHsJ,IAAI;MACJ/E,KAAK;MACL4L;IACJ,CAAC,CAAC,CAAC;EACP;EACA,OAAO1S,MAAM;AACjB;AACC,SAAS2qB,cAAcA,CAACpR,IAAI,EAAElK,MAAM,EAAE;EACnC,MAAMrP,MAAM,GAAG,EAAE;EACjB,MAAM4qB,QAAQ,GAAGrR,IAAI,CAACqR,QAAQ;EAC9B,KAAI,IAAIxwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwwB,QAAQ,CAACrwB,MAAM,EAAEH,CAAC,EAAE,EAAC;IACpC,MAAMywB,GAAG,GAAGV,aAAa,CAACS,QAAQ,CAACxwB,CAAC,CAAC,EAAEmf,IAAI,CAAC5S,MAAM,EAAE0I,MAAM,CAAC;IAC3D,IAAIwb,GAAG,CAACtwB,MAAM,EAAE;MACZyF,MAAM,CAAC5C,IAAI,CAAC,GAAGytB,GAAG,CAAC;IACvB;EACJ;EACA,OAAO7qB,MAAM;AACjB;AACC,SAAS8qB,eAAeA,CAACnkB,MAAM,EAAEG,KAAK,EAAE+E,IAAI,EAAE1E,QAAQ,EAAE;EACrD,IAAI7E,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGuE,KAAK,GAAG,CAAC;EACnB,IAAI+E,IAAI,IAAI,CAAC1E,QAAQ,EAAE;IACnB,OAAM7E,KAAK,GAAGwE,KAAK,IAAI,CAACH,MAAM,CAACrE,KAAK,CAAC,CAACgf,IAAI,EAAC;MACvChf,KAAK,EAAE;IACX;EACJ;EACA,OAAMA,KAAK,GAAGwE,KAAK,IAAIH,MAAM,CAACrE,KAAK,CAAC,CAACgf,IAAI,EAAC;IACtChf,KAAK,EAAE;EACX;EACAA,KAAK,IAAIwE,KAAK;EACd,IAAI+E,IAAI,EAAE;IACNtJ,GAAG,IAAID,KAAK;EAChB;EACA,OAAMC,GAAG,GAAGD,KAAK,IAAIqE,MAAM,CAACpE,GAAG,GAAGuE,KAAK,CAAC,CAACwa,IAAI,EAAC;IAC1C/e,GAAG,EAAE;EACT;EACAA,GAAG,IAAIuE,KAAK;EACZ,OAAO;IACHxE,KAAK;IACLC;EACJ,CAAC;AACL;AACC,SAASwoB,aAAaA,CAACpkB,MAAM,EAAErE,KAAK,EAAEtB,GAAG,EAAE6K,IAAI,EAAE;EAC9C,MAAM/E,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,MAAMyF,MAAM,GAAG,EAAE;EACjB,IAAIwD,IAAI,GAAGlB,KAAK;EAChB,IAAIuhB,IAAI,GAAGld,MAAM,CAACrE,KAAK,CAAC;EACxB,IAAIC,GAAG;EACP,KAAIA,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAEC,GAAG,IAAIvB,GAAG,EAAE,EAAEuB,GAAG,EAAC;IACnC,MAAMyoB,GAAG,GAAGrkB,MAAM,CAACpE,GAAG,GAAGuE,KAAK,CAAC;IAC/B,IAAIkkB,GAAG,CAAC1J,IAAI,IAAI0J,GAAG,CAACC,IAAI,EAAE;MACtB,IAAI,CAACpH,IAAI,CAACvC,IAAI,EAAE;QACZzV,IAAI,GAAG,KAAK;QACZ7L,MAAM,CAAC5C,IAAI,CAAC;UACRkF,KAAK,EAAEA,KAAK,GAAGwE,KAAK;UACpBvE,GAAG,EAAE,CAACA,GAAG,GAAG,CAAC,IAAIuE,KAAK;UACtB+E;QACJ,CAAC,CAAC;QACFvJ,KAAK,GAAGkB,IAAI,GAAGwnB,GAAG,CAACC,IAAI,GAAG1oB,GAAG,GAAG,IAAI;MACxC;IACJ,CAAC,MAAM;MACHiB,IAAI,GAAGjB,GAAG;MACV,IAAIshB,IAAI,CAACvC,IAAI,EAAE;QACXhf,KAAK,GAAGC,GAAG;MACf;IACJ;IACAshB,IAAI,GAAGmH,GAAG;EACd;EACA,IAAIxnB,IAAI,KAAK,IAAI,EAAE;IACfxD,MAAM,CAAC5C,IAAI,CAAC;MACRkF,KAAK,EAAEA,KAAK,GAAGwE,KAAK;MACpBvE,GAAG,EAAEiB,IAAI,GAAGsD,KAAK;MACjB+E;IACJ,CAAC,CAAC;EACN;EACA,OAAO7L,MAAM;AACjB;AACC,SAASkrB,gBAAgBA,CAAC3R,IAAI,EAAE4R,cAAc,EAAE;EAC7C,MAAMxkB,MAAM,GAAG4S,IAAI,CAAC5S,MAAM;EAC1B,MAAMQ,QAAQ,GAAGoS,IAAI,CAAC5d,OAAO,CAACwL,QAAQ;EACtC,MAAML,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,IAAI,CAACuM,KAAK,EAAE;IACR,OAAO,EAAE;EACb;EACA,MAAM+E,IAAI,GAAG,CAAC,CAAC0N,IAAI,CAAC6R,KAAK;EACzB,MAAM;IAAE9oB,KAAK;IAAGC;EAAK,CAAC,GAAGuoB,eAAe,CAACnkB,MAAM,EAAEG,KAAK,EAAE+E,IAAI,EAAE1E,QAAQ,CAAC;EACvE,IAAIA,QAAQ,KAAK,IAAI,EAAE;IACnB,OAAOkkB,aAAa,CAAC9R,IAAI,EAAE,CACvB;MACIjX,KAAK;MACLC,GAAG;MACHsJ;IACJ,CAAC,CACJ,EAAElF,MAAM,EAAEwkB,cAAc,CAAC;EAC9B;EACA,MAAMnqB,GAAG,GAAGuB,GAAG,GAAGD,KAAK,GAAGC,GAAG,GAAGuE,KAAK,GAAGvE,GAAG;EAC3C,MAAM+oB,YAAY,GAAG,CAAC,CAAC/R,IAAI,CAACgS,SAAS,IAAIjpB,KAAK,KAAK,CAAC,IAAIC,GAAG,KAAKuE,KAAK,GAAG,CAAC;EACzE,OAAOukB,aAAa,CAAC9R,IAAI,EAAEwR,aAAa,CAACpkB,MAAM,EAAErE,KAAK,EAAEtB,GAAG,EAAEsqB,YAAY,CAAC,EAAE3kB,MAAM,EAAEwkB,cAAc,CAAC;AACvG;AACC,SAASE,aAAaA,CAAC9R,IAAI,EAAEqR,QAAQ,EAAEjkB,MAAM,EAAEwkB,cAAc,EAAE;EAC5D,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAACnM,UAAU,IAAI,CAACrY,MAAM,EAAE;IAC1D,OAAOikB,QAAQ;EACnB;EACA,OAAOY,eAAe,CAACjS,IAAI,EAAEqR,QAAQ,EAAEjkB,MAAM,EAAEwkB,cAAc,CAAC;AAClE;AACC,SAASK,eAAeA,CAACjS,IAAI,EAAEqR,QAAQ,EAAEjkB,MAAM,EAAEwkB,cAAc,EAAE;EAC9D,MAAMM,YAAY,GAAGlS,IAAI,CAACmS,MAAM,CAACtV,UAAU,CAAC,CAAC;EAC7C,MAAMuV,SAAS,GAAGC,SAAS,CAACrS,IAAI,CAAC5d,OAAO,CAAC;EACzC,MAAM;IAAEkwB,aAAa,EAAE/wB,YAAY;IAAGa,OAAO,EAAE;MAAEwL;IAAU;EAAG,CAAC,GAAGoS,IAAI;EACtE,MAAMzS,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,MAAMyF,MAAM,GAAG,EAAE;EACjB,IAAI8rB,SAAS,GAAGH,SAAS;EACzB,IAAIrpB,KAAK,GAAGsoB,QAAQ,CAAC,CAAC,CAAC,CAACtoB,KAAK;EAC7B,IAAIlI,CAAC,GAAGkI,KAAK;EACb,SAASypB,QAAQA,CAACtpB,CAAC,EAAEnE,CAAC,EAAE0tB,CAAC,EAAEC,EAAE,EAAE;IAC3B,MAAMC,GAAG,GAAG/kB,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7B,IAAI1E,CAAC,KAAKnE,CAAC,EAAE;MACT;IACJ;IACAmE,CAAC,IAAIqE,KAAK;IACV,OAAMH,MAAM,CAAClE,CAAC,GAAGqE,KAAK,CAAC,CAACwa,IAAI,EAAC;MACzB7e,CAAC,IAAIypB,GAAG;IACZ;IACA,OAAMvlB,MAAM,CAACrI,CAAC,GAAGwI,KAAK,CAAC,CAACwa,IAAI,EAAC;MACzBhjB,CAAC,IAAI4tB,GAAG;IACZ;IACA,IAAIzpB,CAAC,GAAGqE,KAAK,KAAKxI,CAAC,GAAGwI,KAAK,EAAE;MACzB9G,MAAM,CAAC5C,IAAI,CAAC;QACRkF,KAAK,EAAEG,CAAC,GAAGqE,KAAK;QAChBvE,GAAG,EAAEjE,CAAC,GAAGwI,KAAK;QACd+E,IAAI,EAAEmgB,CAAC;QACPtZ,KAAK,EAAEuZ;MACX,CAAC,CAAC;MACFH,SAAS,GAAGG,EAAE;MACd3pB,KAAK,GAAGhE,CAAC,GAAGwI,KAAK;IACrB;EACJ;EACA,KAAK,MAAMkjB,OAAO,IAAIY,QAAQ,EAAC;IAC3BtoB,KAAK,GAAG6E,QAAQ,GAAG7E,KAAK,GAAG0nB,OAAO,CAAC1nB,KAAK;IACxC,IAAIuhB,IAAI,GAAGld,MAAM,CAACrE,KAAK,GAAGwE,KAAK,CAAC;IAChC,IAAI4L,KAAK;IACT,KAAItY,CAAC,GAAGkI,KAAK,GAAG,CAAC,EAAElI,CAAC,IAAI4vB,OAAO,CAACznB,GAAG,EAAEnI,CAAC,EAAE,EAAC;MACrC,MAAMipB,EAAE,GAAG1c,MAAM,CAACvM,CAAC,GAAG0M,KAAK,CAAC;MAC5B4L,KAAK,GAAGkZ,SAAS,CAACT,cAAc,CAACnM,UAAU,CAAClC,aAAa,CAAC2O,YAAY,EAAE;QACpE/yB,IAAI,EAAE,SAAS;QACfyzB,EAAE,EAAEtI,IAAI;QACRsE,EAAE,EAAE9E,EAAE;QACN+I,WAAW,EAAE,CAAChyB,CAAC,GAAG,CAAC,IAAI0M,KAAK;QAC5BulB,WAAW,EAAEjyB,CAAC,GAAG0M,KAAK;QACtBhM;MACJ,CAAC,CAAC,CAAC,CAAC;MACJ,IAAIwxB,YAAY,CAAC5Z,KAAK,EAAEoZ,SAAS,CAAC,EAAE;QAChCC,QAAQ,CAACzpB,KAAK,EAAElI,CAAC,GAAG,CAAC,EAAE4vB,OAAO,CAACne,IAAI,EAAEigB,SAAS,CAAC;MACnD;MACAjI,IAAI,GAAGR,EAAE;MACTyI,SAAS,GAAGpZ,KAAK;IACrB;IACA,IAAIpQ,KAAK,GAAGlI,CAAC,GAAG,CAAC,EAAE;MACf2xB,QAAQ,CAACzpB,KAAK,EAAElI,CAAC,GAAG,CAAC,EAAE4vB,OAAO,CAACne,IAAI,EAAEigB,SAAS,CAAC;IACnD;EACJ;EACA,OAAO9rB,MAAM;AACjB;AACA,SAAS4rB,SAASA,CAACjwB,OAAO,EAAE;EACxB,OAAO;IACHoW,eAAe,EAAEpW,OAAO,CAACoW,eAAe;IACxCwa,cAAc,EAAE5wB,OAAO,CAAC4wB,cAAc;IACtCC,UAAU,EAAE7wB,OAAO,CAAC6wB,UAAU;IAC9BC,gBAAgB,EAAE9wB,OAAO,CAAC8wB,gBAAgB;IAC1CC,eAAe,EAAE/wB,OAAO,CAAC+wB,eAAe;IACxC1U,WAAW,EAAErc,OAAO,CAACqc,WAAW;IAChChG,WAAW,EAAErW,OAAO,CAACqW;EACzB,CAAC;AACL;AACA,SAASsa,YAAYA,CAAC5Z,KAAK,EAAEoZ,SAAS,EAAE;EACpC,IAAI,CAACA,SAAS,EAAE;IACZ,OAAO,KAAK;EAChB;EACA,MAAMzW,KAAK,GAAG,EAAE;EAChB,MAAMsX,QAAQ,GAAG,SAAAA,CAASnxB,GAAG,EAAElD,KAAK,EAAE;IAClC,IAAI,CAAC2S,mBAAmB,CAAC3S,KAAK,CAAC,EAAE;MAC7B,OAAOA,KAAK;IAChB;IACA,IAAI,CAAC+c,KAAK,CAACtG,QAAQ,CAACzW,KAAK,CAAC,EAAE;MACxB+c,KAAK,CAACjY,IAAI,CAAC9E,KAAK,CAAC;IACrB;IACA,OAAO+c,KAAK,CAAC5Z,OAAO,CAACnD,KAAK,CAAC;EAC/B,CAAC;EACD,OAAOgV,IAAI,CAACC,SAAS,CAACmF,KAAK,EAAEia,QAAQ,CAAC,KAAKrf,IAAI,CAACC,SAAS,CAACue,SAAS,EAAEa,QAAQ,CAAC;AAClF;AAEA,SAASC,cAAcA,CAAC/Y,KAAK,EAAEgZ,SAAS,EAAEC,KAAK,EAAE;EAC7C,OAAOjZ,KAAK,CAAClY,OAAO,CAAC2T,IAAI,GAAGuE,KAAK,CAACiZ,KAAK,CAAC,GAAGD,SAAS,CAACC,KAAK,CAAC;AAC/D;AACA,SAASC,cAAcA,CAACrmB,IAAI,EAAEmmB,SAAS,EAAE;EACrC,MAAM;IAAE9kB,MAAM;IAAGC;EAAQ,CAAC,GAAGtB,IAAI;EACjC,IAAIqB,MAAM,IAAIC,MAAM,EAAE;IAClB,OAAO;MACH3B,IAAI,EAAEumB,cAAc,CAAC7kB,MAAM,EAAE8kB,SAAS,EAAE,MAAM,CAAC;MAC/CvmB,KAAK,EAAEsmB,cAAc,CAAC7kB,MAAM,EAAE8kB,SAAS,EAAE,OAAO,CAAC;MACjD9f,GAAG,EAAE6f,cAAc,CAAC5kB,MAAM,EAAE6kB,SAAS,EAAE,KAAK,CAAC;MAC7C7f,MAAM,EAAE4f,cAAc,CAAC5kB,MAAM,EAAE6kB,SAAS,EAAE,QAAQ;IACtD,CAAC;EACL;EACA,OAAOA,SAAS;AACpB;AACA,SAASG,kBAAkBA,CAAC7e,KAAK,EAAEzH,IAAI,EAAE;EACrC,MAAM4I,IAAI,GAAG5I,IAAI,CAACumB,KAAK;EACvB,IAAI3d,IAAI,CAAC4d,QAAQ,EAAE;IACf,OAAO,KAAK;EAChB;EACA,MAAM/U,IAAI,GAAG4U,cAAc,CAACrmB,IAAI,EAAEyH,KAAK,CAAC0e,SAAS,CAAC;EAClD,OAAO;IACHxmB,IAAI,EAAEiJ,IAAI,CAACjJ,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG8R,IAAI,CAAC9R,IAAI,IAAIiJ,IAAI,CAACjJ,IAAI,KAAK,IAAI,GAAG,CAAC,GAAGiJ,IAAI,CAACjJ,IAAI,CAAC;IAChFC,KAAK,EAAEgJ,IAAI,CAAChJ,KAAK,KAAK,KAAK,GAAG6H,KAAK,CAACgC,KAAK,GAAGgI,IAAI,CAAC7R,KAAK,IAAIgJ,IAAI,CAAChJ,KAAK,KAAK,IAAI,GAAG,CAAC,GAAGgJ,IAAI,CAAChJ,KAAK,CAAC;IAC/FyG,GAAG,EAAEuC,IAAI,CAACvC,GAAG,KAAK,KAAK,GAAG,CAAC,GAAGoL,IAAI,CAACpL,GAAG,IAAIuC,IAAI,CAACvC,GAAG,KAAK,IAAI,GAAG,CAAC,GAAGuC,IAAI,CAACvC,GAAG,CAAC;IAC3EC,MAAM,EAAEsC,IAAI,CAACtC,MAAM,KAAK,KAAK,GAAGmB,KAAK,CAACoI,MAAM,GAAG4B,IAAI,CAACnL,MAAM,IAAIsC,IAAI,CAACtC,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGsC,IAAI,CAACtC,MAAM;EACxG,CAAC;AACL;AAEA,SAASsL,UAAU,IAAI6U,CAAC,EAAEzpB,aAAa,IAAI0pB,CAAC,EAAE7pB,YAAY,IAAI8pB,CAAC,EAAEnV,cAAc,IAAIoV,CAAC,EAAE/rB,iBAAiB,IAAIgsB,CAAC,EAAEtR,SAAS,IAAIuR,CAAC,EAAEvzB,IAAI,IAAIwzB,CAAC,EAAE3G,cAAc,IAAI4G,CAAC,EAAE5uB,OAAO,IAAI6uB,CAAC,EAAE1J,cAAc,IAAI2J,CAAC,EAAE3F,YAAY,IAAI4F,CAAC,EAAEjG,4BAA4B,IAAIkG,CAAC,EAAEroB,SAAS,IAAIsoB,CAAC,EAAEhK,eAAe,IAAIiK,CAAC,EAAEjuB,UAAU,IAAIkuB,CAAC,EAAE70B,eAAe,IAAI80B,CAAC,EAAE3vB,EAAE,IAAI4vB,CAAC,EAAEv0B,QAAQ,IAAIw0B,CAAC,EAAE3R,SAAS,IAAI4R,CAAC,EAAEvrB,WAAW,IAAIwrB,CAAC,EAAE7vB,GAAG,IAAI8vB,CAAC,EAAEptB,SAAS,IAAIqtB,CAAC,EAAE5Z,YAAY,IAAI6Z,CAAC,EAAE1rB,WAAW,IAAI2rB,CAAC,EAAE5Y,WAAW,IAAI6Y,CAAC,EAAEtW,QAAQ,IAAIuW,CAAC,EAAEvU,UAAU,IAAIwU,CAAC,EAAEhqB,YAAY,IAAIiqB,CAAC,EAAEzS,OAAO,IAAIre,CAAC,EAAE6mB,QAAQ,IAAIkK,EAAE,EAAE7S,MAAM,IAAIzhB,EAAE,EAAEwL,kBAAkB,IAAIvL,EAAE,EAAEyL,cAAc,IAAI6oB,EAAE,EAAE1d,SAAS,IAAI2d,EAAE,EAAEnzB,KAAK,IAAIozB,EAAE,EAAEzxB,WAAW,IAAI0xB,EAAE,EAAE5d,WAAW,IAAI6d,EAAE,EAAEtxB,UAAU,IAAIuxB,EAAE,EAAE7Q,cAAc,IAAI8Q,EAAE,EAAEtS,eAAe,IAAIuS,EAAE,EAAErG,aAAa,IAAIsG,EAAE,EAAErG,qBAAqB,IAAIsG,EAAE,EAAErpB,MAAM,IAAIspB,EAAE,EAAEjG,oBAAoB,IAAIkG,EAAE,EAAElZ,eAAe,IAAImZ,EAAE,EAAE5tB,qBAAqB,IAAI6tB,EAAE,EAAE33B,IAAI,IAAI43B,EAAE,EAAElvB,kBAAkB,IAAImvB,EAAE,EAAEzwB,OAAO,IAAI0wB,EAAE,EAAEtvB,WAAW,IAAIuvB,EAAE,EAAE9wB,YAAY,IAAI+wB,EAAE,EAAE7uB,cAAc,IAAI8uB,EAAE,EAAEnhB,KAAK,IAAIohB,EAAE,EAAEnxB,KAAK,IAAIoxB,EAAE,EAAElb,YAAY,IAAImb,EAAE,EAAE3sB,cAAc,IAAI4sB,EAAE,EAAEttB,OAAO,IAAIutB,EAAE,EAAEvlB,mBAAmB,IAAIwlB,EAAE,EAAEtlB,aAAa,IAAIulB,EAAE,EAAE11B,KAAK,IAAI21B,EAAE,EAAEj1B,OAAO,IAAIk1B,EAAE,EAAEz0B,SAAS,IAAI00B,EAAE,EAAEx0B,WAAW,IAAIy0B,EAAE,EAAE/zB,SAAS,IAAIg0B,EAAE,EAAEpc,YAAY,IAAIqc,EAAE,EAAExP,WAAW,IAAIyP,EAAE,EAAE/N,mBAAmB,IAAIgO,EAAE,EAAErf,YAAY,IAAIsf,EAAE,EAAEj1B,OAAO,IAAIk1B,EAAE,EAAEj5B,GAAG,IAAIk5B,EAAE,EAAEzrB,QAAQ,IAAI0rB,EAAE,EAAEjK,WAAW,IAAIkK,EAAE,EAAErb,WAAW,IAAIsb,EAAE,EAAEzzB,SAAS,IAAI0zB,EAAE,EAAEzE,kBAAkB,IAAI0E,EAAE,EAAEl3B,cAAc,IAAIm3B,EAAE,EAAEtzB,aAAa,IAAIuzB,EAAE,EAAE5uB,UAAU,IAAI6uB,EAAE,EAAEzvB,eAAe,IAAI0vB,EAAE,EAAErW,iBAAiB,IAAIsW,EAAE,EAAErO,0BAA0B,IAAIsO,EAAE,EAAE9G,gBAAgB,IAAI+G,EAAE,EAAEtH,cAAc,IAAIuH,EAAE,EAAE7J,qBAAqB,IAAI8J,EAAE,EAAE7J,oBAAoB,IAAI8J,EAAE,EAAElK,YAAY,IAAImK,EAAE,EAAE9Z,cAAc,IAAI+Z,EAAE,EAAE5Z,cAAc,IAAI6Z,EAAE,EAAE/b,SAAS,IAAIgc,EAAE,EAAE3X,kBAAkB,IAAI4X,EAAE,EAAE1W,MAAM,IAAI2W,EAAE,EAAE1W,aAAa,IAAI2W,EAAE,EAAExI,aAAa,IAAIyI,EAAE,EAAEp6B,OAAO,IAAIyF,CAAC,EAAEiH,UAAU,IAAI2tB,EAAE,EAAExX,YAAY,IAAIyX,EAAE,EAAEp0B,KAAK,IAAIq0B,EAAE,EAAEp0B,QAAQ,IAAIq0B,EAAE,EAAEn0B,WAAW,IAAIo0B,EAAE,EAAEl0B,UAAU,IAAIm0B,EAAE,EAAEl0B,aAAa,IAAIm0B,EAAE,EAAEhxB,UAAU,IAAIixB,EAAE,EAAEloB,KAAK,IAAIud,CAAC,EAAE/c,QAAQ,IAAIX,CAAC,EAAEjC,OAAO,IAAIxK,CAAC,EAAEf,gBAAgB,IAAI81B,CAAC,EAAEp6B,cAAc,IAAIq6B,CAAC,EAAEz1B,OAAO,IAAIid,CAAC,EAAE9hB,QAAQ,IAAIoB,CAAC,EAAE0iB,aAAa,IAAItH,CAAC,EAAEnd,aAAa,IAAIiD,CAAC,EAAEwI,iBAAiB,IAAIkoB,CAAC,EAAEzyB,YAAY,IAAIuR,CAAC,EAAEnR,WAAW,IAAI0G,CAAC,EAAEuN,YAAY,IAAI/Q,CAAC,EAAEwF,aAAa,IAAIf,CAAC,EAAEmF,gCAAgC,IAAI8sB,CAAC,EAAEjuB,gBAAgB,IAAI4b,CAAC,EAAEhiB,IAAI,IAAIuD,CAAC,EAAExB,SAAS,IAAIyH,CAAC,EAAEhE,mBAAmB,IAAI8uB,CAAC,EAAEl6B,cAAc,IAAIqD,CAAC,EAAEmL,mBAAmB,IAAI4O,CAAC,EAAElW,QAAQ,IAAI5D,CAAC,EAAEmkB,2BAA2B,IAAIjkB,CAAC,EAAEipB,mBAAmB,IAAI0N,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}