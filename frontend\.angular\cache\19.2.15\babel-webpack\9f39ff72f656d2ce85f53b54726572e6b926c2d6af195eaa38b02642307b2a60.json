{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/icon\";\nconst _c0 = a0 => [\"/doc\", a0];\nfunction DocumentationNavComponent_li_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 21)(1, \"a\", 22)(2, \"div\", 23)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 24)(6, \"span\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 26);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r1.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.description);\n  }\n}\nfunction DocumentationNavComponent_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 21)(1, \"a\", 22)(2, \"div\", 23)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 24)(6, \"span\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 26);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r2.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.description);\n  }\n}\nfunction DocumentationNavComponent_li_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 21)(1, \"a\", 22)(2, \"div\", 23)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 24)(6, \"span\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 26);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r3.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n  }\n}\nexport class DocumentationNavComponent {\n  constructor() {\n    this.getStartedItems = [{\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    }, {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    }, {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    }];\n    this.developmentItems = [{\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    }, {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    }];\n    this.referenceItems = [{\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    }, {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }];\n  }\n  static {\n    this.ɵfac = function DocumentationNavComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DocumentationNavComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DocumentationNavComponent,\n      selectors: [[\"app-documentation-nav\"]],\n      decls: 48,\n      vars: 3,\n      consts: [[1, \"spt-nav-container\"], [1, \"spt-nav-header\"], [1, \"spt-nav-brand\"], [1, \"spt-nav-icon\"], [1, \"spt-nav-text\"], [1, \"spt-nav-title\"], [1, \"spt-nav-subtitle\"], [1, \"spt-nav-menu\"], [1, \"spt-nav-section\"], [1, \"spt-nav-section-title\"], [1, \"spt-nav-list\"], [\"class\", \"spt-nav-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-nav-footer\"], [1, \"spt-version-card\"], [1, \"spt-version-icon\"], [1, \"spt-version-info\"], [1, \"spt-version-number\"], [1, \"spt-version-status\"], [1, \"spt-nav-links\"], [\"href\", \"https://github.com/blockchain-spt\", \"target\", \"_blank\", 1, \"spt-external-link\"], [\"href\", \"#\", 1, \"spt-external-link\"], [1, \"spt-nav-item\"], [\"routerLinkActive\", \"spt-nav-active\", 1, \"spt-nav-link\", 3, \"routerLink\"], [1, \"spt-nav-link-icon\"], [1, \"spt-nav-link-content\"], [1, \"spt-nav-link-title\"], [1, \"spt-nav-link-desc\"]],\n      template: function DocumentationNavComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"menu_book\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"h3\", 5);\n          i0.ɵɵtext(8, \"Documentation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 6);\n          i0.ɵɵtext(10, \"Comprehensive SPT Guide\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"h4\", 9);\n          i0.ɵɵtext(14, \"Getting Started\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ul\", 10);\n          i0.ɵɵtemplate(16, DocumentationNavComponent_li_16_Template, 10, 6, \"li\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"h4\", 9);\n          i0.ɵɵtext(19, \"Development\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"ul\", 10);\n          i0.ɵɵtemplate(21, DocumentationNavComponent_li_21_Template, 10, 6, \"li\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 8)(23, \"h4\", 9);\n          i0.ɵɵtext(24, \"Reference\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"ul\", 10);\n          i0.ɵɵtemplate(26, DocumentationNavComponent_li_26_Template, 10, 6, \"li\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"footer\", 12)(28, \"div\", 13)(29, \"div\", 14)(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"verified\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 15)(33, \"span\", 16);\n          i0.ɵɵtext(34, \"SPT v1.0.0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 17);\n          i0.ɵɵtext(36, \"Stable Release\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 18)(38, \"a\", 19)(39, \"mat-icon\");\n          i0.ɵɵtext(40, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"span\");\n          i0.ɵɵtext(42, \"GitHub Repository\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"a\", 20)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"span\");\n          i0.ɵɵtext(47, \"Support\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getStartedItems);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.developmentItems);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.referenceItems);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, MatListModule, MatIconModule, i3.MatIcon, MatButtonModule, MatDividerModule],\n      styles: [\"\\n\\n.spt-nav-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  background: white;\\n  font-family: \\\"Inter\\\", sans-serif;\\n}\\n\\n\\n\\n.spt-nav-header[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n  border-bottom: 1px solid var(--spt-gray-200);\\n  background: var(--spt-gray-50);\\n}\\n\\n.spt-nav-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n}\\n\\n.spt-nav-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);\\n  border-radius: var(--spt-radius-xl);\\n  padding: var(--spt-space-3);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: var(--spt-shadow-md);\\n}\\n\\n.spt-nav-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.spt-nav-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.spt-nav-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-1) 0;\\n  letter-spacing: -0.025em;\\n}\\n\\n.spt-nav-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n  font-weight: var(--spt-font-medium);\\n}\\n\\n\\n\\n.spt-nav-menu[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--spt-space-4) 0;\\n}\\n\\n.spt-nav-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-6);\\n}\\n\\n.spt-nav-section-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-500);\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n  margin: 0 0 var(--spt-space-3) 0;\\n  padding: 0 var(--spt-space-6);\\n}\\n\\n.spt-nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0 var(--spt-space-4);\\n}\\n\\n.spt-nav-item[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-1);\\n}\\n\\n\\n\\n.spt-nav-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  padding: var(--spt-space-3) var(--spt-space-4);\\n  border-radius: var(--spt-radius-lg);\\n  text-decoration: none;\\n  color: var(--spt-gray-700);\\n  transition: all 0.2s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.spt-nav-link[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-gray-100);\\n  color: var(--spt-gray-900);\\n  transform: translateX(2px);\\n}\\n\\n.spt-nav-link.spt-nav-active[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-50);\\n  color: var(--spt-primary-700);\\n  border-left: 3px solid var(--spt-primary-600);\\n  box-shadow: var(--spt-shadow-sm);\\n}\\n\\n.spt-nav-link.spt-nav-active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 3px;\\n  background: var(--spt-primary-600);\\n}\\n\\n.spt-nav-link-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: var(--spt-radius-lg);\\n  background: var(--spt-gray-200);\\n  transition: all 0.2s ease;\\n}\\n\\n.spt-nav-link[_ngcontent-%COMP%]:hover   .spt-nav-link-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-100);\\n  color: var(--spt-primary-600);\\n}\\n\\n.spt-nav-link.spt-nav-active[_ngcontent-%COMP%]   .spt-nav-link-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-600);\\n  color: white;\\n  box-shadow: var(--spt-shadow-sm);\\n}\\n\\n.spt-nav-link-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.spt-nav-link-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-1);\\n  flex: 1;\\n}\\n\\n.spt-nav-link-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-medium);\\n  line-height: 1.2;\\n}\\n\\n.spt-nav-link-desc[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-gray-500);\\n  line-height: 1.3;\\n}\\n\\n.spt-nav-link.spt-nav-active[_ngcontent-%COMP%]   .spt-nav-link-desc[_ngcontent-%COMP%] {\\n  color: var(--spt-primary-600);\\n}\\n\\n\\n\\n.spt-nav-footer[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-4) var(--spt-space-6) var(--spt-space-6);\\n  border-top: 1px solid var(--spt-gray-200);\\n  background: var(--spt-gray-50);\\n}\\n\\n.spt-version-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  padding: var(--spt-space-4);\\n  background: white;\\n  border-radius: var(--spt-radius-xl);\\n  border: 1px solid var(--spt-gray-200);\\n  box-shadow: var(--spt-shadow-sm);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.spt-version-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-success-500) 0%, var(--spt-success-600) 100%);\\n  border-radius: var(--spt-radius-lg);\\n  padding: var(--spt-space-2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spt-version-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.spt-version-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-1);\\n}\\n\\n.spt-version-number[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-900);\\n}\\n\\n.spt-version-status[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-success-600);\\n  font-weight: var(--spt-font-medium);\\n}\\n\\n.spt-nav-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-2);\\n}\\n\\n.spt-external-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  padding: var(--spt-space-2) var(--spt-space-3);\\n  border-radius: var(--spt-radius-lg);\\n  text-decoration: none;\\n  color: var(--spt-gray-600);\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-medium);\\n  transition: all 0.2s ease;\\n  border: 1px solid var(--spt-gray-200);\\n  background: white;\\n}\\n\\n.spt-external-link[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-gray-100);\\n  color: var(--spt-gray-900);\\n  border-color: var(--spt-gray-300);\\n  transform: translateY(-1px);\\n  box-shadow: var(--spt-shadow-sm);\\n}\\n\\n.spt-external-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\nmat-icon[_ngcontent-%COMP%] {\\n  display: inline-flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  vertical-align: middle !important;\\n  line-height: 1 !important;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .spt-nav-header[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-4);\\n  }\\n  .spt-nav-icon[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-2);\\n  }\\n  .spt-nav-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    width: 20px;\\n    height: 20px;\\n  }\\n  .spt-nav-title[_ngcontent-%COMP%] {\\n    font-size: var(--spt-text-base);\\n  }\\n  .spt-nav-subtitle[_ngcontent-%COMP%] {\\n    font-size: var(--spt-text-xs);\\n  }\\n  .spt-nav-list[_ngcontent-%COMP%] {\\n    padding: 0 var(--spt-space-3);\\n  }\\n  .spt-nav-footer[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-3) var(--spt-space-4) var(--spt-space-4);\\n  }\\n  .spt-version-card[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-3);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .spt-nav-header[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-3);\\n  }\\n  .spt-nav-brand[_ngcontent-%COMP%] {\\n    gap: var(--spt-space-2);\\n  }\\n  .spt-nav-list[_ngcontent-%COMP%] {\\n    padding: 0 var(--spt-space-2);\\n  }\\n  .spt-nav-link[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-2) var(--spt-space-3);\\n  }\\n  .spt-nav-link-icon[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .spt-nav-link-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    width: 16px;\\n    height: 16px;\\n  }\\n  .spt-nav-footer[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-2) var(--spt-space-3) var(--spt-space-3);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatListModule", "MatIconModule", "MatButtonModule", "MatDividerModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "item_r1", "route", "ɵɵtextInterpolate", "icon", "label", "description", "item_r2", "item_r3", "DocumentationNavComponent", "constructor", "getStartedItems", "developmentItems", "referenceItems", "selectors", "decls", "vars", "consts", "template", "DocumentationNavComponent_Template", "rf", "ctx", "ɵɵtemplate", "DocumentationNavComponent_li_16_Template", "DocumentationNavComponent_li_21_Template", "DocumentationNavComponent_li_26_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "RouterLink", "RouterLinkActive", "i3", "MatIcon", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\documentation-nav\\documentation-nav.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\n\ninterface NavItem {\n  label: string;\n  route: string;\n  icon: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-documentation-nav',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatDividerModule\n  ],\n  template: `\n    <nav class=\"spt-nav-container\">\n      <!-- Navigation Header -->\n      <header class=\"spt-nav-header\">\n        <div class=\"spt-nav-brand\">\n          <div class=\"spt-nav-icon\">\n            <mat-icon>menu_book</mat-icon>\n          </div>\n          <div class=\"spt-nav-text\">\n            <h3 class=\"spt-nav-title\">Documentation</h3>\n            <p class=\"spt-nav-subtitle\">Comprehensive SPT Guide</p>\n          </div>\n        </div>\n      </header>\n\n      <!-- Navigation Menu -->\n      <div class=\"spt-nav-menu\">\n        <div class=\"spt-nav-section\">\n          <h4 class=\"spt-nav-section-title\">Getting Started</h4>\n          <ul class=\"spt-nav-list\">\n            <li *ngFor=\"let item of getStartedItems\" class=\"spt-nav-item\">\n              <a\n                [routerLink]=\"['/doc', item.route]\"\n                routerLinkActive=\"spt-nav-active\"\n                class=\"spt-nav-link\">\n                <div class=\"spt-nav-link-icon\">\n                  <mat-icon>{{ item.icon }}</mat-icon>\n                </div>\n                <div class=\"spt-nav-link-content\">\n                  <span class=\"spt-nav-link-title\">{{ item.label }}</span>\n                  <span class=\"spt-nav-link-desc\">{{ item.description }}</span>\n                </div>\n              </a>\n            </li>\n          </ul>\n        </div>\n\n        <div class=\"spt-nav-section\">\n          <h4 class=\"spt-nav-section-title\">Development</h4>\n          <ul class=\"spt-nav-list\">\n            <li *ngFor=\"let item of developmentItems\" class=\"spt-nav-item\">\n              <a\n                [routerLink]=\"['/doc', item.route]\"\n                routerLinkActive=\"spt-nav-active\"\n                class=\"spt-nav-link\">\n                <div class=\"spt-nav-link-icon\">\n                  <mat-icon>{{ item.icon }}</mat-icon>\n                </div>\n                <div class=\"spt-nav-link-content\">\n                  <span class=\"spt-nav-link-title\">{{ item.label }}</span>\n                  <span class=\"spt-nav-link-desc\">{{ item.description }}</span>\n                </div>\n              </a>\n            </li>\n          </ul>\n        </div>\n\n        <div class=\"spt-nav-section\">\n          <h4 class=\"spt-nav-section-title\">Reference</h4>\n          <ul class=\"spt-nav-list\">\n            <li *ngFor=\"let item of referenceItems\" class=\"spt-nav-item\">\n              <a\n                [routerLink]=\"['/doc', item.route]\"\n                routerLinkActive=\"spt-nav-active\"\n                class=\"spt-nav-link\">\n                <div class=\"spt-nav-link-icon\">\n                  <mat-icon>{{ item.icon }}</mat-icon>\n                </div>\n                <div class=\"spt-nav-link-content\">\n                  <span class=\"spt-nav-link-title\">{{ item.label }}</span>\n                  <span class=\"spt-nav-link-desc\">{{ item.description }}</span>\n                </div>\n              </a>\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <!-- Navigation Footer -->\n      <footer class=\"spt-nav-footer\">\n        <div class=\"spt-version-card\">\n          <div class=\"spt-version-icon\">\n            <mat-icon>verified</mat-icon>\n          </div>\n          <div class=\"spt-version-info\">\n            <span class=\"spt-version-number\">SPT v1.0.0</span>\n            <span class=\"spt-version-status\">Stable Release</span>\n          </div>\n        </div>\n\n        <div class=\"spt-nav-links\">\n          <a href=\"https://github.com/blockchain-spt\" target=\"_blank\" class=\"spt-external-link\">\n            <mat-icon>code</mat-icon>\n            <span>GitHub Repository</span>\n          </a>\n          <a href=\"#\" class=\"spt-external-link\">\n            <mat-icon>help</mat-icon>\n            <span>Support</span>\n          </a>\n        </div>\n      </footer>\n    </nav>\n  `,\n  styles: [`\n    /* Navigation Container */\n    .spt-nav-container {\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      background: white;\n      font-family: 'Inter', sans-serif;\n    }\n\n    /* Navigation Header */\n    .spt-nav-header {\n      padding: var(--spt-space-6);\n      border-bottom: 1px solid var(--spt-gray-200);\n      background: var(--spt-gray-50);\n    }\n\n    .spt-nav-brand {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-3);\n    }\n\n    .spt-nav-icon {\n      background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);\n      border-radius: var(--spt-radius-xl);\n      padding: var(--spt-space-3);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: var(--spt-shadow-md);\n    }\n\n    .spt-nav-icon mat-icon {\n      color: white;\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .spt-nav-text {\n      flex: 1;\n    }\n\n    .spt-nav-title {\n      font-size: var(--spt-text-lg);\n      font-weight: var(--spt-font-bold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-1) 0;\n      letter-spacing: -0.025em;\n    }\n\n    .spt-nav-subtitle {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-gray-600);\n      margin: 0;\n      font-weight: var(--spt-font-medium);\n    }\n\n    /* Navigation Menu */\n    .spt-nav-menu {\n      flex: 1;\n      overflow-y: auto;\n      padding: var(--spt-space-4) 0;\n    }\n\n    .spt-nav-section {\n      margin-bottom: var(--spt-space-6);\n    }\n\n    .spt-nav-section-title {\n      font-size: var(--spt-text-xs);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-500);\n      text-transform: uppercase;\n      letter-spacing: 0.05em;\n      margin: 0 0 var(--spt-space-3) 0;\n      padding: 0 var(--spt-space-6);\n    }\n\n    .spt-nav-list {\n      list-style: none;\n      margin: 0;\n      padding: 0 var(--spt-space-4);\n    }\n\n    .spt-nav-item {\n      margin-bottom: var(--spt-space-1);\n    }\n\n    /* Navigation Links */\n    .spt-nav-link {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-3);\n      padding: var(--spt-space-3) var(--spt-space-4);\n      border-radius: var(--spt-radius-lg);\n      text-decoration: none;\n      color: var(--spt-gray-700);\n      transition: all 0.2s ease;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .spt-nav-link:hover {\n      background: var(--spt-gray-100);\n      color: var(--spt-gray-900);\n      transform: translateX(2px);\n    }\n\n    .spt-nav-link.spt-nav-active {\n      background: var(--spt-primary-50);\n      color: var(--spt-primary-700);\n      border-left: 3px solid var(--spt-primary-600);\n      box-shadow: var(--spt-shadow-sm);\n    }\n\n    .spt-nav-link.spt-nav-active::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 0;\n      bottom: 0;\n      width: 3px;\n      background: var(--spt-primary-600);\n    }\n\n    .spt-nav-link-icon {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 36px;\n      height: 36px;\n      border-radius: var(--spt-radius-lg);\n      background: var(--spt-gray-200);\n      transition: all 0.2s ease;\n    }\n\n    .spt-nav-link:hover .spt-nav-link-icon {\n      background: var(--spt-primary-100);\n      color: var(--spt-primary-600);\n    }\n\n    .spt-nav-link.spt-nav-active .spt-nav-link-icon {\n      background: var(--spt-primary-600);\n      color: white;\n      box-shadow: var(--spt-shadow-sm);\n    }\n\n    .spt-nav-link-icon mat-icon {\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .spt-nav-link-content {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spt-space-1);\n      flex: 1;\n    }\n\n    .spt-nav-link-title {\n      font-size: var(--spt-text-sm);\n      font-weight: var(--spt-font-medium);\n      line-height: 1.2;\n    }\n\n    .spt-nav-link-desc {\n      font-size: var(--spt-text-xs);\n      color: var(--spt-gray-500);\n      line-height: 1.3;\n    }\n\n    .spt-nav-link.spt-nav-active .spt-nav-link-desc {\n      color: var(--spt-primary-600);\n    }\n\n    /* Navigation Footer */\n    .spt-nav-footer {\n      padding: var(--spt-space-4) var(--spt-space-6) var(--spt-space-6);\n      border-top: 1px solid var(--spt-gray-200);\n      background: var(--spt-gray-50);\n    }\n\n    .spt-version-card {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-3);\n      padding: var(--spt-space-4);\n      background: white;\n      border-radius: var(--spt-radius-xl);\n      border: 1px solid var(--spt-gray-200);\n      box-shadow: var(--spt-shadow-sm);\n      margin-bottom: var(--spt-space-4);\n    }\n\n    .spt-version-icon {\n      background: linear-gradient(135deg, var(--spt-success-500) 0%, var(--spt-success-600) 100%);\n      border-radius: var(--spt-radius-lg);\n      padding: var(--spt-space-2);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .spt-version-icon mat-icon {\n      color: white;\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    .spt-version-info {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spt-space-1);\n    }\n\n    .spt-version-number {\n      font-size: var(--spt-text-sm);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-900);\n    }\n\n    .spt-version-status {\n      font-size: var(--spt-text-xs);\n      color: var(--spt-success-600);\n      font-weight: var(--spt-font-medium);\n    }\n\n    .spt-nav-links {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spt-space-2);\n    }\n\n    .spt-external-link {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n      padding: var(--spt-space-2) var(--spt-space-3);\n      border-radius: var(--spt-radius-lg);\n      text-decoration: none;\n      color: var(--spt-gray-600);\n      font-size: var(--spt-text-sm);\n      font-weight: var(--spt-font-medium);\n      transition: all 0.2s ease;\n      border: 1px solid var(--spt-gray-200);\n      background: white;\n    }\n\n    .spt-external-link:hover {\n      background: var(--spt-gray-100);\n      color: var(--spt-gray-900);\n      border-color: var(--spt-gray-300);\n      transform: translateY(-1px);\n      box-shadow: var(--spt-shadow-sm);\n    }\n\n    .spt-external-link mat-icon {\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    /* Global Icon Fixes */\n    mat-icon {\n      display: inline-flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      vertical-align: middle !important;\n      line-height: 1 !important;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 768px) {\n      .spt-nav-header {\n        padding: var(--spt-space-4);\n      }\n\n      .spt-nav-icon {\n        padding: var(--spt-space-2);\n      }\n\n      .spt-nav-icon mat-icon {\n        font-size: 20px;\n        width: 20px;\n        height: 20px;\n      }\n\n      .spt-nav-title {\n        font-size: var(--spt-text-base);\n      }\n\n      .spt-nav-subtitle {\n        font-size: var(--spt-text-xs);\n      }\n\n      .spt-nav-list {\n        padding: 0 var(--spt-space-3);\n      }\n\n      .spt-nav-footer {\n        padding: var(--spt-space-3) var(--spt-space-4) var(--spt-space-4);\n      }\n\n      .spt-version-card {\n        padding: var(--spt-space-3);\n      }\n    }\n\n    @media (max-width: 480px) {\n      .spt-nav-header {\n        padding: var(--spt-space-3);\n      }\n\n      .spt-nav-brand {\n        gap: var(--spt-space-2);\n      }\n\n      .spt-nav-list {\n        padding: 0 var(--spt-space-2);\n      }\n\n      .spt-nav-link {\n        padding: var(--spt-space-2) var(--spt-space-3);\n      }\n\n      .spt-nav-link-icon {\n        width: 32px;\n        height: 32px;\n      }\n\n      .spt-nav-link-icon mat-icon {\n        font-size: 16px;\n        width: 16px;\n        height: 16px;\n      }\n\n      .spt-nav-footer {\n        padding: var(--spt-space-2) var(--spt-space-3) var(--spt-space-3);\n      }\n    }\n  `]\n})\nexport class DocumentationNavComponent {\n  getStartedItems: NavItem[] = [\n    {\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    },\n    {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    },\n    {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    }\n  ];\n\n  developmentItems: NavItem[] = [\n    {\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    },\n    {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    }\n  ];\n\n  referenceItems: NavItem[] = [\n    {\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    },\n    {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;IA8C1CC,EANN,CAAAC,cAAA,aAA8D,YAIrC,cACU,eACnB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,cAAkC,eACC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAG5DF,EAH4D,CAAAG,YAAA,EAAO,EACzD,EACJ,EACD;;;;IAXDH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,KAAA,EAAmC;IAIvBT,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAU,iBAAA,CAAAF,OAAA,CAAAG,IAAA,CAAe;IAGQX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAU,iBAAA,CAAAF,OAAA,CAAAI,KAAA,CAAgB;IACjBZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAU,iBAAA,CAAAF,OAAA,CAAAK,WAAA,CAAsB;;;;;IAgBtDb,EANN,CAAAC,cAAA,aAA+D,YAItC,cACU,eACnB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,cAAkC,eACC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAG5DF,EAH4D,CAAAG,YAAA,EAAO,EACzD,EACJ,EACD;;;;IAXDH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAO,OAAA,CAAAL,KAAA,EAAmC;IAIvBT,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAU,iBAAA,CAAAI,OAAA,CAAAH,IAAA,CAAe;IAGQX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAU,iBAAA,CAAAI,OAAA,CAAAF,KAAA,CAAgB;IACjBZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAU,iBAAA,CAAAI,OAAA,CAAAD,WAAA,CAAsB;;;;;IAgBtDb,EANN,CAAAC,cAAA,aAA6D,YAIpC,cACU,eACnB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,cAAkC,eACC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAG5DF,EAH4D,CAAAG,YAAA,EAAO,EACzD,EACJ,EACD;;;;IAXDH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAQ,OAAA,CAAAN,KAAA,EAAmC;IAIvBT,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAU,iBAAA,CAAAK,OAAA,CAAAJ,IAAA,CAAe;IAGQX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAU,iBAAA,CAAAK,OAAA,CAAAH,KAAA,CAAgB;IACjBZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAU,iBAAA,CAAAK,OAAA,CAAAF,WAAA,CAAsB;;;AA2XxE,OAAM,MAAOG,yBAAyB;EA5ctCC,YAAA;IA6cE,KAAAC,eAAe,GAAc,CAC3B;MACEN,KAAK,EAAE,UAAU;MACjBH,KAAK,EAAE,UAAU;MACjBE,IAAI,EAAE,MAAM;MACZE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBH,KAAK,EAAE,iBAAiB;MACxBE,IAAI,EAAE,YAAY;MAClBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,oBAAoB;MAC3BH,KAAK,EAAE,oBAAoB;MAC3BE,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,CACF;IAED,KAAAM,gBAAgB,GAAc,CAC5B;MACEP,KAAK,EAAE,WAAW;MAClBH,KAAK,EAAE,WAAW;MAClBE,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,mBAAmB;MAC1BH,KAAK,EAAE,kBAAkB;MACzBE,IAAI,EAAE,WAAW;MACjBE,WAAW,EAAE;KACd,CACF;IAED,KAAAO,cAAc,GAAc,CAC1B;MACER,KAAK,EAAE,eAAe;MACtBH,KAAK,EAAE,eAAe;MACtBE,IAAI,EAAE,KAAK;MACXE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,cAAc;MACrBH,KAAK,EAAE,cAAc;MACrBE,IAAI,EAAE,cAAc;MACpBE,WAAW,EAAE;KACd,CACF;;;;uCAlDUG,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3b1B3B,EALR,CAAAC,cAAA,aAA+B,gBAEE,aACF,aACC,eACd;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EAC1B;UAEJH,EADF,CAAAC,cAAA,aAA0B,YACE;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAGzDF,EAHyD,CAAAG,YAAA,EAAI,EACnD,EACF,EACC;UAKLH,EAFJ,CAAAC,cAAA,cAA0B,cACK,aACO;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtDH,EAAA,CAAAC,cAAA,cAAyB;UACvBD,EAAA,CAAA6B,UAAA,KAAAC,wCAAA,kBAA8D;UAelE9B,EADE,CAAAG,YAAA,EAAK,EACD;UAGJH,EADF,CAAAC,cAAA,cAA6B,aACO;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAyB;UACvBD,EAAA,CAAA6B,UAAA,KAAAE,wCAAA,kBAA+D;UAenE/B,EADE,CAAAG,YAAA,EAAK,EACD;UAGJH,EADF,CAAAC,cAAA,cAA6B,aACO;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChDH,EAAA,CAAAC,cAAA,cAAyB;UACvBD,EAAA,CAAA6B,UAAA,KAAAG,wCAAA,kBAA6D;UAgBnEhC,EAFI,CAAAG,YAAA,EAAK,EACD,EACF;UAMAH,EAHN,CAAAC,cAAA,kBAA+B,eACC,eACE,gBAClB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;UAEJH,EADF,CAAAC,cAAA,eAA8B,gBACK;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClDH,EAAA,CAAAC,cAAA,gBAAiC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAEnDF,EAFmD,CAAAG,YAAA,EAAO,EAClD,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA2B,aAC6D,gBAC1E;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzBF,EADyB,CAAAG,YAAA,EAAO,EAC5B;UAEFH,EADF,CAAAC,cAAA,aAAsC,gBAC1B;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAIrBF,EAJqB,CAAAG,YAAA,EAAO,EAClB,EACA,EACC,EACL;;;UAjFuBH,EAAA,CAAAI,SAAA,IAAkB;UAAlBJ,EAAA,CAAAK,UAAA,YAAAuB,GAAA,CAAAV,eAAA,CAAkB;UAoBlBlB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAK,UAAA,YAAAuB,GAAA,CAAAT,gBAAA,CAAmB;UAoBnBnB,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAK,UAAA,YAAAuB,GAAA,CAAAR,cAAA,CAAiB;;;qBAnE9C1B,YAAY,EAAAuC,EAAA,CAAAC,OAAA,EACZvC,YAAY,EAAAwC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA,EACZzC,aAAa,EACbC,aAAa,EAAAyC,EAAA,CAAAC,OAAA,EACbzC,eAAe,EACfC,gBAAgB;MAAAyC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}