import { Component, OnInit } from '@angular/core';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { RouterOutlet, RouterModule, Router, NavigationEnd } from '@angular/router';
import { Observable } from 'rxjs';
import { filter, map, startWith } from 'rxjs/operators';
import { AuthService, User } from './services/auth.service';
import { NavigationComponent } from './shared/navigation/navigation.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    NavigationComponent
  ],
  template: `
    <!-- Documentation Route - No Authentication Required -->
    <div *ngIf="isDocumentationRoute$ | async" class="doc-container">
      <router-outlet></router-outlet>
    </div>

    <!-- Main App - Authentication Required -->
    <div *ngIf="!(isDocumentationRoute$ | async)" class="app-container">
      <!-- Authenticated Layout -->
      <div *ngIf="currentUser$ | async as user; else loginView" class="authenticated-layout">
        <!-- Enhanced Navigation -->
        <app-navigation></app-navigation>

        <!-- Main Content Area -->
        <main class="main-content">
          <router-outlet></router-outlet>
        </main>
      </div>

      <!-- Login View -->
      <ng-template #loginView>
        <div class="login-layout">
          <router-outlet></router-outlet>
        </div>
      </ng-template>
    </div>
  `,


  styles: [`
    /* Enhanced App Layout */
    .app-container {
      height: 100vh;
      overflow: hidden;
      background: var(--spt-bg-primary);
    }

    .doc-container {
      height: 100vh;
      background: var(--spt-bg-primary);
    }

    .login-layout {
      height: 100vh;
      background: var(--spt-bg-primary);
    }

    .authenticated-layout {
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: var(--spt-bg-primary);
    }

    .main-content {
      flex: 1;
      overflow-y: auto;
      background: var(--spt-bg-secondary);
      min-height: 0; /* Important for flex child scrolling */
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .authenticated-layout {
        flex-direction: column;
      }
    }
  `]
})
export class AppComponent implements OnInit {
  title = 'SPT - Blockchain Security Protocol Tool';
  currentUser$: Observable<User | null>;
  isDocumentationRoute$: Observable<boolean>;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {
    this.currentUser$ = this.authService.currentUser$;

    // Check if current route is documentation - include initial route
    this.isDocumentationRoute$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map((event: NavigationEnd) => event.url.startsWith('/doc')),
      // Start with current URL check
      startWith(this.router.url.startsWith('/doc'))
    );
  }

  ngOnInit(): void {
    // Initialize component
  }
}
