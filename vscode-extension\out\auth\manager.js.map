{"version": 3, "file": "manager.js", "sourceRoot": "", "sources": ["../../src/auth/manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AAejC,MAAa,qBAAqB;IAQ9B,YACY,OAAgC,EAChC,aAAmC;QADnC,YAAO,GAAP,OAAO,CAAyB;QAChC,kBAAa,GAAb,aAAa,CAAsB;QANvC,cAAS,GAAc,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;QAClD,sBAAiB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAa,CAAC;QACjD,uBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAM9D,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,eAAe;QACX,2CAA2C;QAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACpD,OAAO,KAAK,CAAC;SAChB;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,QAAQ;QACJ,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;SAC/B;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,IAAI;YACA,iDAAiD;YACjD,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;YAEpD,2BAA2B;YAC3B,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,EAAE;gBAC1E,KAAK;gBACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,OAAO,GAAG,GAAG,SAAS,sBAAsB,KAAK,uEAAuE,CAAC;YAE/H,mCAAmC;YACnC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACrD,mFAAmF,EACnF,cAAc,EACd,QAAQ,CACX,CAAC;YAEF,IAAI,MAAM,KAAK,cAAc,EAAE;gBAC3B,qDAAqD;gBACrD,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBAEzD,iDAAiD;gBACjD,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBACpC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;oBAC9C,KAAK,EAAE,+BAA+B;oBACtC,WAAW,EAAE,IAAI;iBACpB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;oBACzB,OAAO,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,EAAE;wBACpC,6BAA6B;wBAC7B,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;4BAC5B,OAAO,CAAC,KAAK,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;wBAElB,uCAAuC;wBACvC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,SAAS,EAAE,EAAE;4BACrD,IAAI,SAAS,CAAC,eAAe,EAAE;gCAC3B,YAAY,CAAC,OAAO,CAAC,CAAC;gCACtB,UAAU,CAAC,OAAO,EAAE,CAAC;gCACrB,OAAO,CAAC,IAAI,CAAC,CAAC;6BACjB;wBACL,CAAC,CAAC,CAAC;wBAEH,sBAAsB;wBACtB,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE;4BAC/B,YAAY,CAAC,OAAO,CAAC,CAAC;4BACtB,UAAU,CAAC,OAAO,EAAE,CAAC;4BACrB,OAAO,CAAC,KAAK,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;aACN;YAED,OAAO,KAAK,CAAC;SAChB;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,KAAa,EAAE,IAAS,EAAE,SAAiB;QAChE,IAAI;YACA,4BAA4B;YAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;YAElD,oBAAoB;YACpB,IAAI,CAAC,SAAS,GAAG;gBACb,eAAe,EAAE,IAAI;gBACrB,KAAK;gBACL,IAAI,EAAE;oBACF,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAClB;gBACD,SAAS;aACZ,CAAC;YAEF,6BAA6B;YAC7B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAE3B,qBAAqB;YACrB,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;YAEzF,mBAAmB;YACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE5C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iCAAiC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC1F;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sCAAsC,KAAK,EAAE,CAAC,CAAC;SACjF;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,KAAa;QACnC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,qBAAqB,CAAC,gBAAgB,CAAQ,CAAC;QAEhG,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,KAAK,KAAK,KAAK,EAAE;YAC7C,OAAO,KAAK,CAAC;SAChB;QAED,wDAAwD;QACxD,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC9B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,SAAS,GAAG,MAAM,EAAE;YAC7C,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;YACzF,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,aAAa;QACjB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,qBAAqB,CAAC,cAAc,CAAc,CAAC;QACnG,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;YAE5B,4BAA4B;YAC5B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;gBACzB,IAAI,CAAC,cAAc,EAAE,CAAC;aACzB;SACJ;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa;QACvB,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAChG,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,IAAI,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;IACrC,CAAC;;AAzNL,sDA0NC;AAzN2B,oCAAc,GAAG,eAAe,CAAC;AACjC,sCAAgB,GAAG,iBAAiB,CAAC"}