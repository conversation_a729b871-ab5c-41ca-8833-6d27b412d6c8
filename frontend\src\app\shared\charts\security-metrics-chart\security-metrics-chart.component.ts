import { Component, Input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

export interface SecurityMetric {
  label: string;
  value: number;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  trend?: number; // percentage change
  color?: string;
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  tension?: number;
}

@Component({
  selector: 'app-security-metrics-chart',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule
  ],
  templateUrl: './security-metrics-chart.component.html',
  styleUrls: ['./security-metrics-chart.component.scss']
})
export class SecurityMetricsChartComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chartCanvas', { static: true }) chartCanvas!: ElementRef<HTMLCanvasElement>;
  
  @Input() title: string = 'Security Metrics';
  @Input() chartType: ChartType = 'doughnut';
  @Input() data: SecurityMetric[] = [];
  @Input() height: number = 300;
  @Input() showLegend: boolean = true;
  @Input() showTrends: boolean = true;
  @Input() animated: boolean = true;

  chart: Chart | null = null;
  selectedTimeRange: string = '7d';
  isLoading: boolean = false;

  timeRanges = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' }
  ];

  severityColors = {
    critical: '#dc2626',
    high: '#ea580c',
    medium: '#d97706',
    low: '#65a30d',
    info: '#2563eb'
  };

  ngOnInit(): void {
    // Component initialization
  }

  ngAfterViewInit(): void {
    this.createChart();
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  createChart(): void {
    if (!this.chartCanvas?.nativeElement) {
      return;
    }

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) {
      return;
    }

    // Destroy existing chart
    if (this.chart) {
      this.chart.destroy();
    }

    const chartData = this.prepareChartData();
    const config = this.getChartConfiguration(chartData);

    this.chart = new Chart(ctx, config);
  }

  private prepareChartData() {
    const labels = this.data.map(item => item.label);
    const values = this.data.map(item => item.value);
    const colors = this.data.map(item => 
      item.color || this.severityColors[item.severity] || '#6b7280'
    );

    return {
      labels,
      datasets: [{
        data: values,
        backgroundColor: colors,
        borderColor: colors.map(color => this.adjustColorOpacity(color, 1)),
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverOffset: 4
      }]
    };
  }

  private getChartConfiguration(chartData: any): ChartConfiguration {
    const baseConfig: ChartConfiguration = {
      type: this.chartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: this.showLegend,
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              font: {
                family: 'Inter, sans-serif',
                size: 12,
                weight: 500
              }
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true,
            callbacks: {
              label: (context) => {
                const dataItem = this.data[context.dataIndex];
                const percentage = ((context.parsed / this.getTotalValue()) * 100).toFixed(1);
                let label = `${context.label}: ${context.parsed}`;
                
                if (this.showTrends && dataItem.trend !== undefined) {
                  const trendIcon = dataItem.trend > 0 ? '↗' : dataItem.trend < 0 ? '↘' : '→';
                  label += ` (${trendIcon} ${Math.abs(dataItem.trend)}%)`;
                }
                
                return `${label} (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: this.animated ? 1000 : 0,
          easing: 'easeInOutQuart'
        }
      }
    };

    // Chart type specific configurations
    if (this.chartType === 'doughnut' || this.chartType === 'pie') {
      baseConfig.options!.cutout = this.chartType === 'doughnut' ? '60%' : 0;
    } else if (this.chartType === 'bar' || this.chartType === 'line') {
      baseConfig.options!.scales = {
        y: {
          beginAtZero: true,
          grid: {
            color: '#f3f4f6'
          },
          ticks: {
            font: {
              family: 'Inter, sans-serif'
            }
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              family: 'Inter, sans-serif'
            }
          }
        }
      };
    }

    return baseConfig;
  }

  private adjustColorOpacity(color: string, opacity: number): string {
    // Convert hex to rgba
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  onTimeRangeChange(range: string): void {
    this.selectedTimeRange = range;
    this.refreshData();
  }

  refreshData(): void {
    this.isLoading = true;
    // Simulate data refresh
    setTimeout(() => {
      this.createChart();
      this.isLoading = false;
    }, 500);
  }

  exportChart(): void {
    if (this.chart) {
      const url = this.chart.toBase64Image();
      const link = document.createElement('a');
      link.download = `${this.title.toLowerCase().replace(/\s+/g, '-')}-chart.png`;
      link.href = url;
      link.click();
    }
  }

  toggleChartType(): void {
    const types: ChartType[] = ['doughnut', 'bar', 'line', 'pie'];
    const currentIndex = types.indexOf(this.chartType);
    this.chartType = types[(currentIndex + 1) % types.length];
    this.createChart();
  }

  // Template helper methods
  getTotalValue(): number {
    return this.data.reduce((sum, item) => sum + item.value, 0);
  }

  getCriticalCount(): number {
    return this.data.filter(item => item.severity === 'critical').reduce((sum, item) => sum + item.value, 0);
  }

  getHighCount(): number {
    return this.data.filter(item => item.severity === 'high').reduce((sum, item) => sum + item.value, 0);
  }

  getMediumCount(): number {
    return this.data.filter(item => item.severity === 'medium').reduce((sum, item) => sum + item.value, 0);
  }

  getLowCount(): number {
    return this.data.filter(item => item.severity === 'low').reduce((sum, item) => sum + item.value, 0);
  }
}
