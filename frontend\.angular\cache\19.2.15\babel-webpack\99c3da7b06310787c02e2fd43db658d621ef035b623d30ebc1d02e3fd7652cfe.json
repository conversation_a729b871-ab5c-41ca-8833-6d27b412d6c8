{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/card\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nexport class ReportsComponent {\n  static {\n    this.ɵfac = function ReportsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReportsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReportsComponent,\n      selectors: [[\"app-reports\"]],\n      decls: 17,\n      vars: 0,\n      consts: [[1, \"reports-container\"], [1, \"actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"]],\n      template: function ReportsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"\\uD83D\\uDCCA Security Reports\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-card\")(4, \"mat-card-header\")(5, \"mat-card-title\");\n          i0.ɵɵtext(6, \"Report Generation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \"Generate comprehensive security reports\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\");\n          i0.ɵɵtext(11, \"This feature will allow you to generate detailed security reports in various formats.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 1)(13, \"button\", 2)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Generate Report \");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [CommonModule, MatCardModule, i1.MatCard, i1.MatCardContent, i1.MatCardHeader, i1.MatCardSubtitle, i1.MatCardTitle, MatButtonModule, i2.MatButton, MatIconModule, i3.MatIcon],\n      styles: [\".reports-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.actions[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9yZXBvcnRzL3JlcG9ydHMuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsYUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQUFOOztBQUVJO0VBQ0UsZ0JBQUE7QUFDTiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5yZXBvcnRzLWNvbnRhaW5lciB7XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgICAgbWF4LXdpZHRoOiA4MDBweDtcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xuICAgIH1cbiAgICAuYWN0aW9ucyB7XG4gICAgICBtYXJnaW4tdG9wOiAyMHB4O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "ReportsComponent", "selectors", "decls", "vars", "consts", "template", "ReportsComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "i1", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i2", "MatButton", "i3", "MatIcon", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\reports\\reports.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\n\n@Component({\n  selector: 'app-reports',\n  standalone: true,\n  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],\n  template: `\n    <div class=\"reports-container\">\n      <h1>📊 Security Reports</h1>\n      \n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>Report Generation</mat-card-title>\n          <mat-card-subtitle>Generate comprehensive security reports</mat-card-subtitle>\n        </mat-card-header>\n        <mat-card-content>\n          <p>This feature will allow you to generate detailed security reports in various formats.</p>\n          <div class=\"actions\">\n            <button mat-raised-button color=\"primary\">\n              <mat-icon>assessment</mat-icon>\n              Generate Report\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .reports-container {\n      padding: 20px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n    .actions {\n      margin-top: 20px;\n    }\n  `]\n})\nexport class ReportsComponent {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;;;;;AAsCtD,OAAM,MAAOC,gBAAgB;;;uCAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9BvBE,EADF,CAAAC,cAAA,aAA+B,SACzB;UAAAD,EAAA,CAAAE,MAAA,oCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIxBH,EAFJ,CAAAC,cAAA,eAAU,sBACS,qBACC;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAClDH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,8CAAuC;UAC5DF,EAD4D,CAAAG,YAAA,EAAoB,EAC9D;UAEhBH,EADF,CAAAC,cAAA,uBAAkB,SACb;UAAAD,EAAA,CAAAE,MAAA,6FAAqF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGxFH,EAFJ,CAAAC,cAAA,cAAqB,iBACuB,gBAC9B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,yBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;qBApBEhB,YAAY,EAAEC,aAAa,EAAAgB,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAEpB,eAAe,EAAAqB,EAAA,CAAAC,SAAA,EAAErB,aAAa,EAAAsB,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}