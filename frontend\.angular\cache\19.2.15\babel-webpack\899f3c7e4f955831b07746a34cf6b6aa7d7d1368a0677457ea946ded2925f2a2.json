{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/chips\";\nfunction OverviewComponent_mat_card_25_mat_chip_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tag_r1);\n  }\n}\nfunction OverviewComponent_mat_card_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 21)(1, \"mat-card-header\")(2, \"mat-icon\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip-listbox\", 22);\n    i0.ɵɵtemplate(10, OverviewComponent_mat_card_25_mat_chip_10_Template, 2, 1, \"mat-chip\", 23);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", feature_r2.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", feature_r2.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", feature_r2.tags);\n  }\n}\nfunction OverviewComponent_mat_card_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 24)(1, \"mat-card-header\")(2, \"mat-icon\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-card-actions\")(12, \"button\", 25)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Documentation \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tool_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tool_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r3.type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tool_r3.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", tool_r3.docLink);\n  }\n}\nfunction OverviewComponent_div_42_code_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"code\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r4.code);\n  }\n}\nfunction OverviewComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, OverviewComponent_div_42_code_8_Template, 2, 1, \"code\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r5 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.code);\n  }\n}\nexport let OverviewComponent = /*#__PURE__*/(() => {\n  class OverviewComponent {\n    constructor() {\n      this.features = [{\n        title: 'Smart Contract Analysis',\n        description: 'Comprehensive security analysis for Ethereum smart contracts with vulnerability detection and gas optimization suggestions.',\n        icon: 'psychology',\n        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        tags: ['Solidity', 'Ethereum', 'Security']\n      }, {\n        title: 'Bitcoin Security',\n        description: 'Advanced security checks for Bitcoin applications including wallet security, transaction validation, and UTXO management.',\n        icon: 'account_balance_wallet',\n        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n        tags: ['Bitcoin', 'Wallet', 'UTXO']\n      }, {\n        title: 'Real-time Monitoring',\n        description: 'Live security monitoring with WebSocket connections, real-time alerts, and continuous vulnerability scanning.',\n        icon: 'radar',\n        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n        tags: ['Real-time', 'Monitoring', 'Alerts']\n      }, {\n        title: 'Comprehensive Reports',\n        description: 'Detailed security reports with executive summaries, technical details, and actionable recommendations.',\n        icon: 'analytics',\n        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n        tags: ['Reports', 'Analytics', 'PDF']\n      }];\n      this.tools = [{\n        name: 'Web Dashboard',\n        type: 'Angular Application',\n        description: 'Modern web interface for security scanning, project management, and report generation.',\n        icon: 'web',\n        docLink: '/doc/getting-started'\n      }, {\n        name: 'CLI Tool',\n        type: 'Command Line',\n        description: 'Powerful command-line interface for automated security scanning and CI/CD integration.',\n        icon: 'terminal',\n        docLink: '/doc/cli-guide'\n      }, {\n        name: 'VS Code Extension',\n        type: 'IDE Integration',\n        description: 'Real-time security highlighting and inline suggestions directly in your code editor.',\n        icon: 'integration_instructions',\n        docLink: '/doc/vscode-extension'\n      }, {\n        name: 'REST API',\n        type: 'Backend Service',\n        description: 'RESTful API for integrating SPT security scanning into your development workflow.',\n        icon: 'api',\n        docLink: '/doc/api-reference'\n      }];\n      this.quickStartSteps = [{\n        title: 'Clone Repository',\n        description: 'Get the SPT source code from GitHub',\n        code: 'git clone https://github.com/blockchain-spt/spt.git'\n      }, {\n        title: 'Start Backend',\n        description: 'Launch the Go backend server',\n        code: 'cd backend && go run cmd/main.go'\n      }, {\n        title: 'Start Frontend',\n        description: 'Launch the Angular development server',\n        code: 'cd frontend && npm start'\n      }, {\n        title: 'Begin Scanning',\n        description: 'Access the web dashboard and start your first security scan',\n        code: 'Open http://localhost:4200'\n      }];\n    }\n    static {\n      this.ɵfac = function OverviewComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || OverviewComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OverviewComponent,\n        selectors: [[\"app-overview\"]],\n        decls: 48,\n        vars: 3,\n        consts: [[1, \"spt-overview-container\"], [1, \"spt-hero-section\"], [1, \"spt-hero-content\"], [1, \"spt-hero-icon\"], [1, \"spt-hero-title\"], [1, \"spt-hero-subtitle\"], [1, \"spt-hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/doc/getting-started\", 1, \"spt-primary-btn\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/doc/api-reference\", 1, \"spt-secondary-btn\"], [1, \"features-section\"], [1, \"features-grid\"], [\"class\", \"feature-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"tools-section\"], [1, \"tools-grid\"], [\"class\", \"tool-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"quick-start-section\"], [1, \"quick-start-card\"], [\"mat-card-avatar\", \"\"], [1, \"quick-start-steps\"], [\"class\", \"step\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/doc/getting-started\"], [1, \"feature-card\"], [1, \"feature-tags\"], [4, \"ngFor\", \"ngForOf\"], [1, \"tool-card\"], [\"mat-button\", \"\", 3, \"routerLink\"], [1, \"step\"], [1, \"step-number\"], [1, \"step-content\"], [\"class\", \"step-code\", 4, \"ngIf\"], [1, \"step-code\"]],\n        template: function OverviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"shield\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"h1\", 4);\n            i0.ɵɵtext(7, \"Blockchain Security Protocol Tool\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 5);\n            i0.ɵɵtext(9, \" Comprehensive security analysis and auditing for Ethereum and Bitcoin blockchain applications with real-time vulnerability detection. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 6)(11, \"button\", 7)(12, \"mat-icon\");\n            i0.ɵɵtext(13, \"play_arrow\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"span\");\n            i0.ɵɵtext(15, \"Get Started\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"button\", 8)(17, \"mat-icon\");\n            i0.ɵɵtext(18, \"api\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"span\");\n            i0.ɵɵtext(20, \"API Reference\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(21, \"div\", 9)(22, \"h2\");\n            i0.ɵɵtext(23, \"Key Features\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\", 10);\n            i0.ɵɵtemplate(25, OverviewComponent_mat_card_25_Template, 11, 6, \"mat-card\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 12)(27, \"h2\");\n            i0.ɵɵtext(28, \"Available Tools\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 13);\n            i0.ɵɵtemplate(30, OverviewComponent_mat_card_30_Template, 16, 5, \"mat-card\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 15)(32, \"mat-card\", 16)(33, \"mat-card-header\")(34, \"mat-icon\", 17);\n            i0.ɵɵtext(35, \"rocket_launch\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"mat-card-title\");\n            i0.ɵɵtext(37, \"Quick Start\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"mat-card-subtitle\");\n            i0.ɵɵtext(39, \"Get SPT running in minutes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"mat-card-content\")(41, \"div\", 18);\n            i0.ɵɵtemplate(42, OverviewComponent_div_42_Template, 9, 4, \"div\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"mat-card-actions\")(44, \"button\", 20)(45, \"mat-icon\");\n            i0.ɵɵtext(46, \"arrow_forward\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(47, \" Full Installation Guide \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(25);\n            i0.ɵɵproperty(\"ngForOf\", ctx.features);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.tools);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngForOf\", ctx.quickStartSteps);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink, MatCardModule, i3.MatCard, i3.MatCardActions, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, MatChipsModule, i6.MatChip, i6.MatChipListbox],\n        styles: [\".overview-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.hero-section[_ngcontent-%COMP%]{text-align:center;padding:64px 32px;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border-radius:24px;margin-bottom:48px;position:relative;overflow:hidden;box-shadow:0 20px 40px #667eea4d}.hero-content[_ngcontent-%COMP%]{padding:0 24px;position:relative;z-index:1}.hero-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:20px;font-size:2.8em;font-weight:700;margin:0 0 20px;letter-spacing:-1px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.hero-icon[_ngcontent-%COMP%]{font-size:56px;width:56px;height:56px;background:#fff3;border-radius:16px;padding:12px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:inline-flex!important;align-items:center!important;justify-content:center!important;line-height:1!important}.hero-subtitle[_ngcontent-%COMP%]{font-size:1.3em;opacity:.95;max-width:700px;margin:0 auto 40px;line-height:1.5;font-weight:400}.hero-actions[_ngcontent-%COMP%]{display:flex;gap:20px;justify-content:center;flex-wrap:wrap}.hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:12px 32px;border-radius:50px;background:#fff3;border:2px solid rgba(255,255,255,.3)}.hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px)}.features-section[_ngcontent-%COMP%], .tools-section[_ngcontent-%COMP%]{margin-bottom:64px}.features-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .tools-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{text-align:center;margin-bottom:40px;color:#4c63d2;font-size:2.2em;font-weight:700;letter-spacing:-.5px}.features-grid[_ngcontent-%COMP%], .tools-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(320px,1fr));gap:32px}.feature-card[_ngcontent-%COMP%], .tool-card[_ngcontent-%COMP%]{border-radius:20px;background:#fff}.feature-tags[_ngcontent-%COMP%]{margin-top:20px}.feature-tags[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c);color:#fff;border-radius:20px;font-weight:500;font-size:.85em;margin:4px 8px 4px 0;padding:8px 16px;border:none}.quick-start-section[_ngcontent-%COMP%]{margin-bottom:64px}.quick-start-card[_ngcontent-%COMP%]{max-width:900px;margin:0 auto;border-radius:24px;background:linear-gradient(135deg,#fff,#f0f4ff);border:1px solid #e8eaff;box-shadow:0 12px 40px #667eea26;overflow:hidden;position:relative}.quick-start-steps[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:32px;padding:8px 0}.step[_ngcontent-%COMP%]{display:flex;gap:20px;align-items:flex-start;padding:20px;background:#ffffffb3;border-radius:16px;border:1px solid rgba(102,126,234,.1);transition:all .3s ease}.step-number[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:1.1em;flex-shrink:0;box-shadow:0 4px 12px #667eea4d}.step-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#4c63d2;font-weight:600;font-size:1.1em}.step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 12px;color:#64748b;line-height:1.6}.step-code[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8fafc,#f1f5f9);padding:12px 16px;border-radius:12px;font-family:JetBrains Mono,Fira Code,Courier New,monospace;font-size:.9em;display:block;margin-top:12px;border:1px solid #e2e8f0;color:#475569;font-weight:500}mat-icon[_ngcontent-%COMP%]{display:inline-flex!important;align-items:center!important;justify-content:center!important;vertical-align:middle!important;line-height:1!important}@media (max-width: 768px){.hero-title[_ngcontent-%COMP%]{font-size:2em;flex-direction:column;gap:8px}.hero-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.features-grid[_ngcontent-%COMP%], .tools-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return OverviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}