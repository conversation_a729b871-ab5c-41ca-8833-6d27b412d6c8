{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nlet LoginComponent = class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  loginWithDemo() {\n    this.isLoading = true;\n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', {\n        duration: 3000\n      });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n};\nLoginComponent = __decorate([Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, RouterModule, MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatProgressSpinnerModule, MatSnackBarModule, MatCheckboxModule],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})], LoginComponent);\nexport { LoginComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "ReactiveFormsModule", "Validators", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "hidePassword", "isLoading", "returnUrl", "loginForm", "group", "username", "required", "password", "rememberMe", "ngOnInit", "snapshot", "queryParams", "onSubmit", "valid", "credentials", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "message", "loginWithDemo", "setTimeout", "mockLogin", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\n\nimport { AuthService, LoginRequest } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  hidePassword = true;\n  isLoading = false;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      \n      const credentials: LoginRequest = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n\n  loginWithDemo(): void {\n    this.isLoading = true;\n    \n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACxF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;AAwBvD,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACuB,QAAQ,CAAC,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACuB,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MAErB,MAAMa,WAAW,GAAiB;QAChCT,QAAQ,EAAE,IAAI,CAACF,SAAS,CAACY,KAAK,CAACV,QAAQ;QACvCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACY,KAAK,CAACR;OAChC;MAED,IAAI,CAACX,WAAW,CAACoB,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;QACxC,CAAC;QACDqB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;OACD,CAAC;IACJ;EACF;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACxB,SAAS,GAAG,IAAI;IAErB;IACAyB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9B,WAAW,CAAC+B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;MAC5C,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;IACxC,CAAC,EAAE,IAAI,CAAC;EACV;CACD;AA3DYT,cAAc,GAAAmC,UAAA,EApB1BhD,SAAS,CAAC;EACTiD,QAAQ,EAAE,WAAW;EACrBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlD,YAAY,EACZC,mBAAmB,EACnBE,YAAY,EACZC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,wBAAwB,EACxBC,iBAAiB,EACjBC,iBAAiB,CAClB;EACDwC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB;CAErC,CAAC,C,EACWxC,cAAc,CA2D1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}