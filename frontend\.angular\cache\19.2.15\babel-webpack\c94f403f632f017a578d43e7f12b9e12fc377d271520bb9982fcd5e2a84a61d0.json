{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/icon\";\nconst _c0 = a0 => [\"/doc\", a0];\nfunction DocumentationNavComponent_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 20)(1, \"a\", 21)(2, \"div\", 22)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 23)(6, \"span\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r1.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.description);\n  }\n}\nfunction DocumentationNavComponent_li_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 20)(1, \"a\", 21)(2, \"div\", 22)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 23)(6, \"span\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r2.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.description);\n  }\n}\nfunction DocumentationNavComponent_li_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 20)(1, \"a\", 21)(2, \"div\", 22)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 23)(6, \"span\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r3.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n  }\n}\nexport let DocumentationNavComponent = /*#__PURE__*/(() => {\n  class DocumentationNavComponent {\n    constructor() {\n      this.getStartedItems = [{\n        label: 'Overview',\n        route: 'overview',\n        icon: 'home',\n        description: 'Introduction to SPT'\n      }, {\n        label: 'Getting Started',\n        route: 'getting-started',\n        icon: 'play_arrow',\n        description: 'Installation and setup'\n      }, {\n        label: 'Security Practices',\n        route: 'security-practices',\n        icon: 'security',\n        description: 'Best practices guide'\n      }];\n      this.developmentItems = [{\n        label: 'CLI Guide',\n        route: 'cli-guide',\n        icon: 'terminal',\n        description: 'Command line interface'\n      }, {\n        label: 'VS Code Extension',\n        route: 'vscode-extension',\n        icon: 'extension',\n        description: 'IDE integration'\n      }];\n      this.referenceItems = [{\n        label: 'API Reference',\n        route: 'api-reference',\n        icon: 'api',\n        description: 'REST API endpoints'\n      }, {\n        label: 'Architecture',\n        route: 'architecture',\n        icon: 'account_tree',\n        description: 'System architecture'\n      }];\n    }\n    static {\n      this.ɵfac = function DocumentationNavComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DocumentationNavComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DocumentationNavComponent,\n        selectors: [[\"app-documentation-nav\"]],\n        decls: 45,\n        vars: 3,\n        consts: [[1, \"spt-nav-container\"], [1, \"spt-nav-header\"], [1, \"spt-nav-brand\"], [1, \"spt-nav-text\"], [1, \"spt-nav-title\"], [1, \"spt-nav-subtitle\"], [1, \"spt-nav-menu\"], [1, \"spt-nav-section\"], [1, \"spt-nav-section-title\"], [1, \"spt-nav-list\"], [\"class\", \"spt-nav-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-nav-footer\"], [1, \"spt-version-card\"], [1, \"spt-version-icon\"], [1, \"spt-version-info\"], [1, \"spt-version-number\"], [1, \"spt-version-status\"], [1, \"spt-nav-links\"], [\"href\", \"https://github.com/blockchain-spt\", \"target\", \"_blank\", 1, \"spt-external-link\"], [\"href\", \"#\", 1, \"spt-external-link\"], [1, \"spt-nav-item\"], [\"routerLinkActive\", \"spt-nav-active\", 1, \"spt-nav-link\", 3, \"routerLink\"], [1, \"spt-nav-link-icon\"], [1, \"spt-nav-link-content\"], [1, \"spt-nav-link-title\"], [1, \"spt-nav-link-desc\"]],\n        template: function DocumentationNavComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nav\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n            i0.ɵɵtext(5, \"Documentation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 5);\n            i0.ɵɵtext(7, \"Comprehensive SPT Guide\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"h4\", 8);\n            i0.ɵɵtext(11, \"Getting Started\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"ul\", 9);\n            i0.ɵɵtemplate(13, DocumentationNavComponent_li_13_Template, 10, 6, \"li\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 7)(15, \"h4\", 8);\n            i0.ɵɵtext(16, \"Development\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"ul\", 9);\n            i0.ɵɵtemplate(18, DocumentationNavComponent_li_18_Template, 10, 6, \"li\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 7)(20, \"h4\", 8);\n            i0.ɵɵtext(21, \"Reference\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"ul\", 9);\n            i0.ɵɵtemplate(23, DocumentationNavComponent_li_23_Template, 10, 6, \"li\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(24, \"footer\", 11)(25, \"div\", 12)(26, \"div\", 13)(27, \"mat-icon\");\n            i0.ɵɵtext(28, \"verified\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 14)(30, \"span\", 15);\n            i0.ɵɵtext(31, \"SPT v1.0.0\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"span\", 16);\n            i0.ɵɵtext(33, \"Stable Release\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(34, \"div\", 17)(35, \"a\", 18)(36, \"mat-icon\");\n            i0.ɵɵtext(37, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"span\");\n            i0.ɵɵtext(39, \"GitHub Repository\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"a\", 19)(41, \"mat-icon\");\n            i0.ɵɵtext(42, \"help\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"span\");\n            i0.ɵɵtext(44, \"Support\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getStartedItems);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.developmentItems);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.referenceItems);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, MatListModule, MatIconModule, i3.MatIcon, MatButtonModule, MatDividerModule],\n        styles: [\".spt-nav-container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;background:#fff;font-family:Inter,sans-serif}.spt-nav-header[_ngcontent-%COMP%]{padding:var(--spt-space-6);border-bottom:1px solid var(--spt-gray-200);background:var(--spt-gray-50)}.spt-nav-brand[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3)}.spt-nav-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-primary-600) 0%,var(--spt-secondary-600) 100%);border-radius:var(--spt-radius-xl);padding:var(--spt-space-3);display:flex;align-items:center;justify-content:center;box-shadow:var(--spt-shadow-md)}.spt-nav-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff;font-size:24px;width:24px;height:24px}.spt-nav-text[_ngcontent-%COMP%]{flex:1}.spt-nav-title[_ngcontent-%COMP%]{font-size:var(--spt-text-lg);font-weight:var(--spt-font-bold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-1) 0;letter-spacing:-.025em}.spt-nav-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-gray-600);margin:0;font-weight:var(--spt-font-medium)}.spt-nav-menu[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:var(--spt-space-4) 0}.spt-nav-section[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-6)}.spt-nav-section-title[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);font-weight:var(--spt-font-semibold);color:var(--spt-gray-500);text-transform:uppercase;letter-spacing:.05em;margin:0 0 var(--spt-space-3) 0;padding:0 var(--spt-space-6)}.spt-nav-list[_ngcontent-%COMP%]{list-style:none;margin:0;padding:0 var(--spt-space-4)}.spt-nav-item[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-1)}.spt-nav-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);padding:var(--spt-space-3) var(--spt-space-4);border-radius:var(--spt-radius-lg);text-decoration:none;color:var(--spt-gray-700);transition:all .2s ease;position:relative;overflow:hidden}.spt-nav-link[_ngcontent-%COMP%]:hover{background:var(--spt-gray-100);color:var(--spt-gray-900);transform:translate(2px)}.spt-nav-link.spt-nav-active[_ngcontent-%COMP%]{background:var(--spt-primary-50);color:var(--spt-primary-700);border-left:3px solid var(--spt-primary-600);box-shadow:var(--spt-shadow-sm)}.spt-nav-link.spt-nav-active[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:0;bottom:0;width:3px;background:var(--spt-primary-600)}.spt-nav-link-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:36px;height:36px;border-radius:var(--spt-radius-lg);background:var(--spt-gray-200);transition:all .2s ease}.spt-nav-link[_ngcontent-%COMP%]:hover   .spt-nav-link-icon[_ngcontent-%COMP%]{background:var(--spt-primary-100);color:var(--spt-primary-600)}.spt-nav-link.spt-nav-active[_ngcontent-%COMP%]   .spt-nav-link-icon[_ngcontent-%COMP%]{background:var(--spt-primary-600);color:#fff;box-shadow:var(--spt-shadow-sm)}.spt-nav-link-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.spt-nav-link-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-1);flex:1}.spt-nav-link-title[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);font-weight:var(--spt-font-medium);line-height:1.2}.spt-nav-link-desc[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-gray-500);line-height:1.3}.spt-nav-link.spt-nav-active[_ngcontent-%COMP%]   .spt-nav-link-desc[_ngcontent-%COMP%]{color:var(--spt-primary-600)}.spt-nav-footer[_ngcontent-%COMP%]{padding:var(--spt-space-4) var(--spt-space-6) var(--spt-space-6);border-top:1px solid var(--spt-gray-200);background:var(--spt-gray-50)}.spt-version-card[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);padding:var(--spt-space-4);background:#fff;border-radius:var(--spt-radius-xl);border:1px solid var(--spt-gray-200);box-shadow:var(--spt-shadow-sm);margin-bottom:var(--spt-space-4)}.spt-version-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-success-500) 0%,var(--spt-success-600) 100%);border-radius:var(--spt-radius-lg);padding:var(--spt-space-2);display:flex;align-items:center;justify-content:center}.spt-version-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff;font-size:16px;width:16px;height:16px}.spt-version-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-1)}.spt-version-number[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);font-weight:var(--spt-font-semibold);color:var(--spt-gray-900)}.spt-version-status[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-success-600);font-weight:var(--spt-font-medium)}.spt-nav-links[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-2)}.spt-external-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);padding:var(--spt-space-2) var(--spt-space-3);border-radius:var(--spt-radius-lg);text-decoration:none;color:var(--spt-gray-600);font-size:var(--spt-text-sm);font-weight:var(--spt-font-medium);transition:all .2s ease;border:1px solid var(--spt-gray-200);background:#fff}.spt-external-link[_ngcontent-%COMP%]:hover{background:var(--spt-gray-100);color:var(--spt-gray-900);border-color:var(--spt-gray-300);transform:translateY(-1px);box-shadow:var(--spt-shadow-sm)}.spt-external-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}mat-icon[_ngcontent-%COMP%]{display:inline-flex!important;align-items:center!important;justify-content:center!important;vertical-align:middle!important;line-height:1!important}@media (max-width: 768px){.spt-nav-header[_ngcontent-%COMP%]{padding:var(--spt-space-4)}.spt-nav-icon[_ngcontent-%COMP%]{padding:var(--spt-space-2)}.spt-nav-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}.spt-nav-title[_ngcontent-%COMP%]{font-size:var(--spt-text-base)}.spt-nav-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-xs)}.spt-nav-list[_ngcontent-%COMP%]{padding:0 var(--spt-space-3)}.spt-nav-footer[_ngcontent-%COMP%]{padding:var(--spt-space-3) var(--spt-space-4) var(--spt-space-4)}.spt-version-card[_ngcontent-%COMP%]{padding:var(--spt-space-3)}}@media (max-width: 480px){.spt-nav-header[_ngcontent-%COMP%]{padding:var(--spt-space-3)}.spt-nav-brand[_ngcontent-%COMP%]{gap:var(--spt-space-2)}.spt-nav-list[_ngcontent-%COMP%]{padding:0 var(--spt-space-2)}.spt-nav-link[_ngcontent-%COMP%]{padding:var(--spt-space-2) var(--spt-space-3)}.spt-nav-link-icon[_ngcontent-%COMP%]{width:32px;height:32px}.spt-nav-link-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.spt-nav-footer[_ngcontent-%COMP%]{padding:var(--spt-space-2) var(--spt-space-3) var(--spt-space-3)}}\"]\n      });\n    }\n  }\n  return DocumentationNavComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}