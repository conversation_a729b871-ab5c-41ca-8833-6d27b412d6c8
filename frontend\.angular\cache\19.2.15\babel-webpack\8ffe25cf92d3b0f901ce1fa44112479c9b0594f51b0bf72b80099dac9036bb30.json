{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { Chart, registerables } from 'chart.js';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/tabs\";\nconst _c0 = [\"trendsCanvas\"];\nconst _c1 = [\"scoreCanvas\"];\nconst _c2 = [\"volumeCanvas\"];\nfunction SecurityTrendsDashboardComponent_mat_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const period_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", period_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", period_r1.label, \" \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"bug_report\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 31);\n    i0.ɵɵtext(6, \"Total Issues\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"div\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 34)(11, \"mat-icon\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 35)(16, \"div\", 30)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 31);\n    i0.ɵɵtext(20, \"Avg Security Score\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 32)(22, \"div\", 33);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 36);\n    i0.ɵɵelement(25, \"div\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 38)(27, \"div\", 30)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 31);\n    i0.ɵɵtext(31, \"Scan Frequency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 32)(33, \"div\", 33);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 39);\n    i0.ɵɵtext(36, \"scans per day\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 40)(38, \"div\", 30)(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"span\", 31);\n    i0.ɵɵtext(42, \"Overall Trend\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 32)(44, \"div\", 41)(45, \"mat-icon\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.trendsData.summary.totalIssues);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getTrendColor(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend), false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTrendIcon(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.Math.abs(ctx_r1.trendsData.summary.criticalTrend), \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.trendsData.summary.averageScore, \"/100\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.trendsData.summary.averageScore, \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.trendsData.summary.scanFrequency);\n    i0.ɵɵadvance(10);\n    i0.ɵɵclassMap(\"trend-\" + ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTrendIcon(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend) === \"up\" ? \"Increasing\" : ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend) === \"down\" ? \"Improving\" : \"Stable\");\n  }\n}\nfunction SecurityTrendsDashboardComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Security Trends\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Loading trends data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_canvas_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 44, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Security trends chart for \" + ctx_r1.selectedPeriod);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Trends Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No security trends data available for the selected period.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Security Scores\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Loading score data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_canvas_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 44, 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Security scores chart for \" + ctx_r1.selectedPeriod);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Score Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No security score data available for the selected period.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Scan Volume\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Loading volume data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_canvas_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 44, 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Scan volume chart for \" + ctx_r1.selectedPeriod);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Volume Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No scan volume data available for the selected period.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 51);\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Critical issues are increasing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" by \", ctx_r1.trendsData.summary.criticalTrend, \"%. Consider reviewing security practices and increasing scan frequency. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Security is improving!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Critical issues decreased by \", ctx_r1.Math.abs(ctx_r1.trendsData.summary.criticalTrend), \"%. Keep up the good security practices. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 54);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Low security score detected.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Average score is \", ctx_r1.trendsData.summary.averageScore, \"/100. Focus on addressing high and critical severity issues. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 55);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Low scan frequency.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Consider increasing scan frequency to \", ctx_r1.trendsData.summary.scanFrequency, \" per day for better security monitoring. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Key Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtemplate(7, SecurityTrendsDashboardComponent_div_61_div_7_Template, 7, 1, \"div\", 49)(8, SecurityTrendsDashboardComponent_div_61_div_8_Template, 7, 1, \"div\", 49)(9, SecurityTrendsDashboardComponent_div_61_div_9_Template, 7, 1, \"div\", 49)(10, SecurityTrendsDashboardComponent_div_61_div_10_Template, 7, 1, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.criticalTrend > 10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.criticalTrend < -10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.averageScore < 60);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.scanFrequency < 1);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"mat-icon\", 57);\n    i0.ɵɵtext(2, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Trends Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start running security scans to see trends and analytics here.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 58)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Start First Scan \");\n    i0.ɵɵelementEnd()();\n  }\n}\n// Register Chart.js components\nChart.register(...registerables);\nexport class SecurityTrendsDashboardComponent {\n  constructor() {\n    this.trendsData = null;\n    this.showComparison = true;\n    this.autoRefresh = false;\n    this.destroy$ = new Subject();\n    this.trendsChart = null;\n    this.scoreChart = null;\n    this.volumeChart = null;\n    this.selectedPeriod = '30d';\n    this.selectedView = 'trends';\n    this.selectedTabIndex = 0;\n    this.isLoading = false;\n    // Make Math available in template\n    this.Math = Math;\n    this.periods = [{\n      value: '7d',\n      label: 'Last 7 Days'\n    }, {\n      value: '30d',\n      label: 'Last 30 Days'\n    }, {\n      value: '90d',\n      label: 'Last 90 Days'\n    }, {\n      value: '1y',\n      label: 'Last Year'\n    }];\n    this.chartViews = [{\n      value: 'trends',\n      label: 'Security Trends',\n      icon: 'trending_up'\n    }, {\n      value: 'scores',\n      label: 'Security Scores',\n      icon: 'grade'\n    }, {\n      value: 'volume',\n      label: 'Scan Volume',\n      icon: 'bar_chart'\n    }];\n  }\n  ngOnInit() {\n    // Component initialization\n  }\n  ngAfterViewInit() {\n    this.createCharts();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.destroyCharts();\n  }\n  destroyCharts() {\n    if (this.trendsChart) {\n      this.trendsChart.destroy();\n      this.trendsChart = null;\n    }\n    if (this.scoreChart) {\n      this.scoreChart.destroy();\n      this.scoreChart = null;\n    }\n    if (this.volumeChart) {\n      this.volumeChart.destroy();\n      this.volumeChart = null;\n    }\n  }\n  createCharts() {\n    this.createTrendsChart();\n    this.createScoreChart();\n    this.createVolumeChart();\n  }\n  createTrendsChart() {\n    if (!this.trendsCanvas?.nativeElement || !this.trendsData) {\n      return;\n    }\n    const ctx = this.trendsCanvas.nativeElement.getContext('2d');\n    if (!ctx) return;\n    if (this.trendsChart) {\n      this.trendsChart.destroy();\n    }\n    const labels = this.trendsData.data.map(point => point.date.toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric'\n    }));\n    const config = {\n      type: 'line',\n      data: {\n        labels,\n        datasets: [{\n          label: 'Critical',\n          data: this.trendsData.data.map(point => point.critical),\n          borderColor: '#dc2626',\n          backgroundColor: 'rgba(220, 38, 38, 0.1)',\n          borderWidth: 3,\n          fill: false,\n          tension: 0.4\n        }, {\n          label: 'High',\n          data: this.trendsData.data.map(point => point.high),\n          borderColor: '#ea580c',\n          backgroundColor: 'rgba(234, 88, 12, 0.1)',\n          borderWidth: 2,\n          fill: false,\n          tension: 0.4\n        }, {\n          label: 'Medium',\n          data: this.trendsData.data.map(point => point.medium),\n          borderColor: '#d97706',\n          backgroundColor: 'rgba(217, 119, 6, 0.1)',\n          borderWidth: 2,\n          fill: false,\n          tension: 0.4\n        }, {\n          label: 'Low',\n          data: this.trendsData.data.map(point => point.low),\n          borderColor: '#65a30d',\n          backgroundColor: 'rgba(101, 163, 13, 0.1)',\n          borderWidth: 2,\n          fill: false,\n          tension: 0.4\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          mode: 'index',\n          intersect: false\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              usePointStyle: true,\n              padding: 20,\n              font: {\n                family: 'Inter, sans-serif',\n                size: 12,\n                weight: 500\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Date',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            grid: {\n              color: '#f3f4f6'\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Number of Issues',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            beginAtZero: true,\n            grid: {\n              color: '#f3f4f6'\n            }\n          }\n        },\n        animation: {\n          duration: 1000,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n    this.trendsChart = new Chart(ctx, config);\n  }\n  createScoreChart() {\n    if (!this.scoreCanvas?.nativeElement || !this.trendsData) {\n      return;\n    }\n    const ctx = this.scoreCanvas.nativeElement.getContext('2d');\n    if (!ctx) return;\n    if (this.scoreChart) {\n      this.scoreChart.destroy();\n    }\n    const labels = this.trendsData.data.map(point => point.date.toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric'\n    }));\n    const config = {\n      type: 'bar',\n      data: {\n        labels,\n        datasets: [{\n          label: 'Security Score',\n          data: this.trendsData.data.map(point => point.averageScore),\n          backgroundColor: this.trendsData.data.map(point => point.averageScore >= 80 ? '#10b981' : point.averageScore >= 60 ? '#f59e0b' : point.averageScore >= 40 ? '#ef4444' : '#dc2626'),\n          borderColor: this.trendsData.data.map(point => point.averageScore >= 80 ? '#059669' : point.averageScore >= 60 ? '#d97706' : point.averageScore >= 40 ? '#dc2626' : '#b91c1c'),\n          borderWidth: 1,\n          borderRadius: 4\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8,\n            callbacks: {\n              label: context => {\n                return `Security Score: ${context.parsed.y}/100`;\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Date',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            grid: {\n              display: false\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Security Score',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            min: 0,\n            max: 100,\n            grid: {\n              color: '#f3f4f6'\n            }\n          }\n        },\n        animation: {\n          duration: 1000,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n    this.scoreChart = new Chart(ctx, config);\n  }\n  createVolumeChart() {\n    if (!this.volumeCanvas?.nativeElement || !this.trendsData) {\n      return;\n    }\n    const ctx = this.volumeCanvas.nativeElement.getContext('2d');\n    if (!ctx) return;\n    if (this.volumeChart) {\n      this.volumeChart.destroy();\n    }\n    const labels = this.trendsData.data.map(point => point.date.toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric'\n    }));\n    const config = {\n      type: 'bar',\n      data: {\n        labels,\n        datasets: [{\n          label: 'Scans Performed',\n          data: this.trendsData.data.map(point => point.totalScans),\n          backgroundColor: '#3b82f6',\n          borderColor: '#2563eb',\n          borderWidth: 1,\n          borderRadius: 4\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Date',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            grid: {\n              display: false\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Number of Scans',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            beginAtZero: true,\n            grid: {\n              color: '#f3f4f6'\n            }\n          }\n        },\n        animation: {\n          duration: 1000,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n    this.volumeChart = new Chart(ctx, config);\n  }\n  onPeriodChange(period) {\n    this.selectedPeriod = period;\n    this.refreshData();\n  }\n  onViewChange(view) {\n    this.selectedView = view;\n  }\n  refreshData() {\n    this.isLoading = true;\n    // Simulate data refresh\n    setTimeout(() => {\n      this.createCharts();\n      this.isLoading = false;\n    }, 500);\n  }\n  exportChart() {\n    let chart = null;\n    let filename = '';\n    switch (this.selectedView) {\n      case 'trends':\n        chart = this.trendsChart;\n        filename = 'security-trends';\n        break;\n      case 'scores':\n        chart = this.scoreChart;\n        filename = 'security-scores';\n        break;\n      case 'volume':\n        chart = this.volumeChart;\n        filename = 'scan-volume';\n        break;\n    }\n    if (chart) {\n      const url = chart.toBase64Image();\n      const link = document.createElement('a');\n      link.download = `${filename}-${this.selectedPeriod}.png`;\n      link.href = url;\n      link.click();\n    }\n  }\n  getTrendDirection(value) {\n    if (value > 5) return 'up';\n    if (value < -5) return 'down';\n    return 'flat';\n  }\n  getTrendIcon(direction) {\n    switch (direction) {\n      case 'up':\n        return 'trending_up';\n      case 'down':\n        return 'trending_down';\n      case 'flat':\n        return 'trending_flat';\n    }\n  }\n  getTrendColor(direction, isGoodTrend = false) {\n    if (direction === 'flat') return 'var(--spt-text-secondary)';\n    if (isGoodTrend) {\n      return direction === 'up' ? 'var(--spt-success-600)' : 'var(--spt-error-600)';\n    } else {\n      return direction === 'up' ? 'var(--spt-error-600)' : 'var(--spt-success-600)';\n    }\n  }\n  static {\n    this.ɵfac = function SecurityTrendsDashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SecurityTrendsDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SecurityTrendsDashboardComponent,\n      selectors: [[\"app-security-trends-dashboard\"]],\n      viewQuery: function SecurityTrendsDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trendsCanvas = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scoreCanvas = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.volumeCanvas = _t.first);\n        }\n      },\n      inputs: {\n        trendsData: \"trendsData\",\n        showComparison: \"showComparison\",\n        autoRefresh: \"autoRefresh\"\n      },\n      decls: 63,\n      vars: 24,\n      consts: [[\"trendsCanvas\", \"\"], [\"scoreCanvas\", \"\"], [\"volumeCanvas\", \"\"], [1, \"trends-dashboard\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"dashboard-title\"], [1, \"dashboard-subtitle\"], [1, \"header-controls\"], [\"appearance\", \"outline\", 1, \"period-select\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 1, \"action-btn\", 3, \"click\", \"disabled\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export Chart\", 1, \"action-btn\", 3, \"click\"], [\"class\", \"summary-cards\", 4, \"ngIf\"], [1, \"chart-section\"], [1, \"chart-tabs\", 3, \"selectedIndexChange\", \"selectedTabChange\", \"selectedIndex\"], [\"mat-tab-label\", \"\"], [1, \"chart-container\"], [1, \"chart-header\"], [1, \"chart-content\"], [\"class\", \"chart-loading\", 4, \"ngIf\"], [\"class\", \"chart-canvas\", 4, \"ngIf\"], [\"class\", \"chart-empty\", 4, \"ngIf\"], [\"class\", \"insights-panel\", 4, \"ngIf\"], [\"class\", \"empty-dashboard\", 4, \"ngIf\"], [3, \"value\"], [1, \"summary-cards\"], [1, \"summary-card\", \"total-issues\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-content\"], [1, \"metric-value\"], [1, \"metric-trend\"], [1, \"summary-card\", \"security-score\"], [1, \"score-bar\"], [1, \"score-fill\"], [1, \"summary-card\", \"scan-frequency\"], [1, \"metric-label\"], [1, \"summary-card\", \"trend-indicator\"], [1, \"trend-status\"], [1, \"chart-loading\"], [1, \"spinning\"], [1, \"chart-canvas\"], [1, \"chart-empty\"], [1, \"insights-panel\"], [1, \"insights-header\"], [1, \"insights-content\"], [\"class\", \"insight-item\", 4, \"ngIf\"], [1, \"insight-item\"], [1, \"insight-icon\", \"warning\"], [1, \"insight-text\"], [1, \"insight-icon\", \"success\"], [1, \"insight-icon\", \"error\"], [1, \"insight-icon\", \"info\"], [1, \"empty-dashboard\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"]],\n      template: function SecurityTrendsDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h2\", 6)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"trending_up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Security Trends Analysis \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 7);\n          i0.ɵɵtext(8, \"Monitor security metrics and trends over time\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"mat-form-field\", 9)(11, \"mat-label\");\n          i0.ɵɵtext(12, \"Time Period\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-select\", 10);\n          i0.ɵɵtwoWayListener(\"valueChange\", function SecurityTrendsDashboardComponent_Template_mat_select_valueChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedPeriod, $event) || (ctx.selectedPeriod = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function SecurityTrendsDashboardComponent_Template_mat_select_selectionChange_13_listener($event) {\n            return ctx.onPeriodChange($event.value);\n          });\n          i0.ɵɵtemplate(14, SecurityTrendsDashboardComponent_mat_option_14_Template, 2, 2, \"mat-option\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function SecurityTrendsDashboardComponent_Template_button_click_16_listener() {\n            return ctx.refreshData();\n          });\n          i0.ɵɵelementStart(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"refresh\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SecurityTrendsDashboardComponent_Template_button_click_19_listener() {\n            return ctx.exportChart();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"download\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(22, SecurityTrendsDashboardComponent_div_22_Template, 49, 13, \"div\", 15);\n          i0.ɵɵelementStart(23, \"div\", 16)(24, \"mat-tab-group\", 17);\n          i0.ɵɵtwoWayListener(\"selectedIndexChange\", function SecurityTrendsDashboardComponent_Template_mat_tab_group_selectedIndexChange_24_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedTabIndex, $event) || (ctx.selectedTabIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedTabChange\", function SecurityTrendsDashboardComponent_Template_mat_tab_group_selectedTabChange_24_listener($event) {\n            return ctx.onViewChange(ctx.chartViews[$event.index].value);\n          });\n          i0.ɵɵelementStart(25, \"mat-tab\");\n          i0.ɵɵtemplate(26, SecurityTrendsDashboardComponent_ng_template_26_Template, 4, 0, \"ng-template\", 18);\n          i0.ɵɵelementStart(27, \"div\", 19)(28, \"div\", 20)(29, \"h3\");\n          i0.ɵɵtext(30, \"Security Issues Over Time\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"p\");\n          i0.ɵɵtext(32, \"Track the evolution of security issues by severity level\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 21);\n          i0.ɵɵtemplate(34, SecurityTrendsDashboardComponent_div_34_Template, 5, 0, \"div\", 22)(35, SecurityTrendsDashboardComponent_canvas_35_Template, 2, 1, \"canvas\", 23)(36, SecurityTrendsDashboardComponent_div_36_Template, 7, 0, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"mat-tab\");\n          i0.ɵɵtemplate(38, SecurityTrendsDashboardComponent_ng_template_38_Template, 4, 0, \"ng-template\", 18);\n          i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 20)(41, \"h3\");\n          i0.ɵɵtext(42, \"Security Score History\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Monitor overall security score improvements over time\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 21);\n          i0.ɵɵtemplate(46, SecurityTrendsDashboardComponent_div_46_Template, 5, 0, \"div\", 22)(47, SecurityTrendsDashboardComponent_canvas_47_Template, 2, 1, \"canvas\", 23)(48, SecurityTrendsDashboardComponent_div_48_Template, 7, 0, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"mat-tab\");\n          i0.ɵɵtemplate(50, SecurityTrendsDashboardComponent_ng_template_50_Template, 4, 0, \"ng-template\", 18);\n          i0.ɵɵelementStart(51, \"div\", 19)(52, \"div\", 20)(53, \"h3\");\n          i0.ɵɵtext(54, \"Scan Activity Volume\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"p\");\n          i0.ɵɵtext(56, \"Track scanning frequency and activity patterns\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 21);\n          i0.ɵɵtemplate(58, SecurityTrendsDashboardComponent_div_58_Template, 5, 0, \"div\", 22)(59, SecurityTrendsDashboardComponent_canvas_59_Template, 2, 1, \"canvas\", 23)(60, SecurityTrendsDashboardComponent_div_60_Template, 7, 0, \"div\", 24);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(61, SecurityTrendsDashboardComponent_div_61_Template, 11, 4, \"div\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(62, SecurityTrendsDashboardComponent_div_62_Template, 11, 0, \"div\", 26);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedPeriod);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.periods);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"spinning\", ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.trendsData);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"selectedIndex\", ctx.selectedTabIndex);\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.trendsData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.trendsData);\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.trendsData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.trendsData);\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.trendsData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.trendsData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.trendsData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.trendsData);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatSelectModule, i4.MatFormField, i4.MatLabel, i5.MatSelect, i5.MatOption, MatFormFieldModule, MatTabsModule, i6.MatTabLabel, i6.MatTab, i6.MatTabGroup],\n      styles: [\"\\n\\n.trends-dashboard[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-6);\\n}\\n\\n\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: var(--spt-space-6);\\n  background: var(--spt-surface);\\n  border-radius: var(--spt-radius-xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.dashboard-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  margin: 0 0 var(--spt-space-2) 0;\\n  font-size: var(--spt-text-2xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.dashboard-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: var(--spt-primary-600);\\n}\\n\\n.dashboard-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-base);\\n  color: var(--spt-text-secondary);\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n.header-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-4);\\n}\\n\\n.period-select[_ngcontent-%COMP%] {\\n  min-width: 160px;\\n}\\n.period-select[_ngcontent-%COMP%]     .mat-mdc-form-field-wrapper {\\n  padding-bottom: 0;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-2);\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: var(--spt-radius-lg);\\n  color: var(--spt-text-secondary);\\n  transition: all 0.2s ease;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-50);\\n  color: var(--spt-primary-600);\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spt-space-4);\\n}\\n\\n.summary-card[_ngcontent-%COMP%] {\\n  background: var(--spt-surface);\\n  border-radius: var(--spt-radius-xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n  padding: var(--spt-space-6);\\n  transition: all 0.3s ease;\\n}\\n\\n.summary-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--spt-shadow-md);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.summary-card.total-issues[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.summary-card.security-score[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n}\\n\\n.summary-card.scan-frequency[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-info-600);\\n}\\n\\n.summary-card.trend-indicator[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-2);\\n}\\n\\n.metric-value[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-3xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-text-primary);\\n  line-height: var(--spt-leading-none);\\n}\\n\\n.metric-trend[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-1);\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.metric-trend[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.metric-label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.score-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: var(--spt-gray-200);\\n  border-radius: var(--spt-radius-full);\\n  overflow: hidden;\\n}\\n\\n.score-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, var(--spt-error-500) 0%, var(--spt-warning-500) 50%, var(--spt-success-500) 100%);\\n  border-radius: var(--spt-radius-full);\\n  transition: width 0.5s ease;\\n}\\n\\n.trend-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.trend-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.trend-status.trend-up[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.trend-status.trend-down[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n}\\n\\n.trend-status.trend-flat[_ngcontent-%COMP%] {\\n  color: var(--spt-text-secondary);\\n}\\n\\n\\n\\n.chart-section[_ngcontent-%COMP%] {\\n  background: var(--spt-surface);\\n  border-radius: var(--spt-radius-xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n  overflow: hidden;\\n}\\n\\n.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab-group {\\n  --mdc-tab-indicator-active-indicator-color: var(--spt-primary-600);\\n}\\n.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab {\\n  min-width: 120px;\\n}\\n.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab .mdc-tab__content {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n}\\n.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab-header {\\n  border-bottom: 1px solid var(--spt-border-light);\\n}\\n\\n.chart-container[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n}\\n\\n.chart-header[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-6);\\n}\\n\\n.chart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-2) 0;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.chart-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n.chart-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 400px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.chart-content.loading[_ngcontent-%COMP%] {\\n  background: var(--spt-bg-secondary);\\n  border-radius: var(--spt-radius-lg);\\n}\\n\\n.chart-canvas[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n}\\n\\n.chart-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.chart-loading[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: var(--spt-primary-500);\\n}\\n\\n.chart-empty[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  color: var(--spt-text-secondary);\\n}\\n\\n.chart-empty[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: var(--spt-text-tertiary);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.chart-empty[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-2) 0;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.chart-empty[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-sm);\\n  max-width: 300px;\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n\\n\\n.insights-panel[_ngcontent-%COMP%] {\\n  background: var(--spt-surface);\\n  border-radius: var(--spt-radius-xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n  padding: var(--spt-space-6);\\n}\\n\\n.insights-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.insights-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  color: var(--spt-warning-600);\\n}\\n\\n.insights-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.insights-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-4);\\n}\\n\\n.insight-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: var(--spt-space-3);\\n  padding: var(--spt-space-4);\\n  background: var(--spt-bg-secondary);\\n  border-radius: var(--spt-radius-lg);\\n  border-left: 4px solid transparent;\\n}\\n\\n.insight-item[_ngcontent-%COMP%]   .insight-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  margin-top: var(--spt-space-1);\\n  flex-shrink: 0;\\n}\\n\\n.insight-item[_ngcontent-%COMP%]   .insight-icon.success[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n}\\n\\n.insight-item[_ngcontent-%COMP%]   .insight-icon.warning[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n}\\n\\n.insight-item[_ngcontent-%COMP%]   .insight-icon.error[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.insight-item[_ngcontent-%COMP%]   .insight-icon.info[_ngcontent-%COMP%] {\\n  color: var(--spt-info-600);\\n}\\n\\n.insight-item[_ngcontent-%COMP%]:has(.insight-icon.success) {\\n  border-left-color: var(--spt-success-600);\\n  background: var(--spt-success-50);\\n}\\n\\n.insight-item[_ngcontent-%COMP%]:has(.insight-icon.warning) {\\n  border-left-color: var(--spt-warning-600);\\n  background: var(--spt-warning-50);\\n}\\n\\n.insight-item[_ngcontent-%COMP%]:has(.insight-icon.error) {\\n  border-left-color: var(--spt-error-600);\\n  background: var(--spt-error-50);\\n}\\n\\n.insight-item[_ngcontent-%COMP%]:has(.insight-icon.info) {\\n  border-left-color: var(--spt-info-600);\\n  background: var(--spt-info-50);\\n}\\n\\n.insight-text[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-primary);\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n.insight-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n\\n\\n.empty-dashboard[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spt-space-16);\\n  text-align: center;\\n  background: var(--spt-surface);\\n  border-radius: var(--spt-radius-xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 80px;\\n  width: 80px;\\n  height: 80px;\\n  color: var(--spt-text-tertiary);\\n  margin-bottom: var(--spt-space-6);\\n}\\n\\n.empty-dashboard[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-3) 0;\\n  font-size: var(--spt-text-xl);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.empty-dashboard[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-8) 0;\\n  font-size: var(--spt-text-base);\\n  color: var(--spt-text-secondary);\\n  max-width: 400px;\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n\\n\\n.spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spt-space-4);\\n    align-items: stretch;\\n  }\\n  .header-controls[_ngcontent-%COMP%] {\\n    justify-content: space-between;\\n  }\\n  .summary-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .chart-content[_ngcontent-%COMP%] {\\n    height: 300px;\\n  }\\n  .insights-content[_ngcontent-%COMP%] {\\n    gap: var(--spt-space-3);\\n  }\\n  .insight-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spt-space-2);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatTabsModule", "Chart", "registerables", "Subject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "period_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r1", "trendsData", "summary", "totalIssues", "ɵɵstyleProp", "getTrendColor", "getTrendDirection", "criticalTrend", "getTrendIcon", "Math", "abs", "averageScore", "scanFrequency", "ɵɵclassMap", "ɵɵtemplate", "SecurityTrendsDashboardComponent_div_61_div_7_Template", "SecurityTrendsDashboardComponent_div_61_div_8_Template", "SecurityTrendsDashboardComponent_div_61_div_9_Template", "SecurityTrendsDashboardComponent_div_61_div_10_Template", "register", "SecurityTrendsDashboardComponent", "constructor", "showComparison", "autoRefresh", "destroy$", "trendsChart", "scoreChart", "volumeChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "selectedTabIndex", "isLoading", "periods", "chartViews", "icon", "ngOnInit", "ngAfterViewInit", "createCharts", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "createTrendsChart", "createScoreChart", "createVolumeChart", "trendsCanvas", "nativeElement", "ctx", "getContext", "labels", "data", "map", "point", "date", "toLocaleDateString", "month", "day", "config", "type", "datasets", "critical", "borderColor", "backgroundColor", "borderWidth", "fill", "tension", "high", "medium", "low", "options", "responsive", "maintainAspectRatio", "interaction", "mode", "intersect", "plugins", "legend", "display", "position", "usePointStyle", "padding", "font", "family", "size", "weight", "tooltip", "titleColor", "bodyColor", "cornerRadius", "scales", "x", "title", "text", "grid", "color", "y", "beginAtZero", "animation", "duration", "easing", "scoreCanvas", "borderRadius", "callbacks", "context", "parsed", "min", "max", "volumeCanvas", "totalScans", "onPeriodChange", "period", "refreshData", "onViewChange", "view", "setTimeout", "exportChart", "chart", "filename", "url", "toBase64Image", "link", "document", "createElement", "download", "href", "click", "direction", "isGoodTrend", "selectors", "viewQuery", "SecurityTrendsDashboardComponent_Query", "rf", "ɵɵtwoWayListener", "SecurityTrendsDashboardComponent_Template_mat_select_valueChange_13_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "SecurityTrendsDashboardComponent_Template_mat_select_selectionChange_13_listener", "SecurityTrendsDashboardComponent_mat_option_14_Template", "SecurityTrendsDashboardComponent_Template_button_click_16_listener", "SecurityTrendsDashboardComponent_Template_button_click_19_listener", "SecurityTrendsDashboardComponent_div_22_Template", "SecurityTrendsDashboardComponent_Template_mat_tab_group_selectedIndexChange_24_listener", "SecurityTrendsDashboardComponent_Template_mat_tab_group_selectedTabChange_24_listener", "index", "SecurityTrendsDashboardComponent_ng_template_26_Template", "SecurityTrendsDashboardComponent_div_34_Template", "SecurityTrendsDashboardComponent_canvas_35_Template", "SecurityTrendsDashboardComponent_div_36_Template", "SecurityTrendsDashboardComponent_ng_template_38_Template", "SecurityTrendsDashboardComponent_div_46_Template", "SecurityTrendsDashboardComponent_canvas_47_Template", "SecurityTrendsDashboardComponent_div_48_Template", "SecurityTrendsDashboardComponent_ng_template_50_Template", "SecurityTrendsDashboardComponent_div_58_Template", "SecurityTrendsDashboardComponent_canvas_59_Template", "SecurityTrendsDashboardComponent_div_60_Template", "SecurityTrendsDashboardComponent_div_61_Template", "SecurityTrendsDashboardComponent_div_62_Template", "ɵɵtwoWayProperty", "ɵɵclassProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "MatIconButton", "i3", "MatIcon", "i4", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "i5", "MatSelect", "MatOption", "i6", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\shared\\charts\\security-trends-dashboard\\security-trends-dashboard.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\shared\\charts\\security-trends-dashboard\\security-trends-dashboard.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { Chart, ChartConfiguration, registerables } from 'chart.js';\nimport { Subject } from 'rxjs';\n\n// Register Chart.js components\nChart.register(...registerables);\n\nexport interface TrendDataPoint {\n  date: Date;\n  critical: number;\n  high: number;\n  medium: number;\n  low: number;\n  totalScans: number;\n  averageScore: number;\n}\n\nexport interface SecurityTrend {\n  period: string;\n  data: TrendDataPoint[];\n  summary: {\n    totalIssues: number;\n    criticalTrend: number;\n    averageScore: number;\n    scanFrequency: number;\n  };\n}\n\n@Component({\n  selector: 'app-security-trends-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatTabsModule\n  ],\n  templateUrl: './security-trends-dashboard.component.html',\n  styleUrls: ['./security-trends-dashboard.component.scss']\n})\nexport class SecurityTrendsDashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('trendsCanvas', { static: true }) trendsCanvas!: ElementRef<HTMLCanvasElement>;\n  @ViewChild('scoreCanvas', { static: true }) scoreCanvas!: ElementRef<HTMLCanvasElement>;\n  @ViewChild('volumeCanvas', { static: true }) volumeCanvas!: ElementRef<HTMLCanvasElement>;\n\n  @Input() trendsData: SecurityTrend | null = null;\n  @Input() showComparison: boolean = true;\n  @Input() autoRefresh: boolean = false;\n\n  private destroy$ = new Subject<void>();\n  private trendsChart: Chart | null = null;\n  private scoreChart: Chart | null = null;\n  private volumeChart: Chart | null = null;\n\n  selectedPeriod: string = '30d';\n  selectedView: string = 'trends';\n  selectedTabIndex: number = 0;\n  isLoading: boolean = false;\n\n  // Make Math available in template\n  Math = Math;\n\n  periods = [\n    { value: '7d', label: 'Last 7 Days' },\n    { value: '30d', label: 'Last 30 Days' },\n    { value: '90d', label: 'Last 90 Days' },\n    { value: '1y', label: 'Last Year' }\n  ];\n\n  chartViews = [\n    { value: 'trends', label: 'Security Trends', icon: 'trending_up' },\n    { value: 'scores', label: 'Security Scores', icon: 'grade' },\n    { value: 'volume', label: 'Scan Volume', icon: 'bar_chart' }\n  ];\n\n  ngOnInit(): void {\n    // Component initialization\n  }\n\n  ngAfterViewInit(): void {\n    this.createCharts();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.destroyCharts();\n  }\n\n  private destroyCharts(): void {\n    if (this.trendsChart) {\n      this.trendsChart.destroy();\n      this.trendsChart = null;\n    }\n    if (this.scoreChart) {\n      this.scoreChart.destroy();\n      this.scoreChart = null;\n    }\n    if (this.volumeChart) {\n      this.volumeChart.destroy();\n      this.volumeChart = null;\n    }\n  }\n\n  private createCharts(): void {\n    this.createTrendsChart();\n    this.createScoreChart();\n    this.createVolumeChart();\n  }\n\n  private createTrendsChart(): void {\n    if (!this.trendsCanvas?.nativeElement || !this.trendsData) {\n      return;\n    }\n\n    const ctx = this.trendsCanvas.nativeElement.getContext('2d');\n    if (!ctx) return;\n\n    if (this.trendsChart) {\n      this.trendsChart.destroy();\n    }\n\n    const labels = this.trendsData.data.map(point => \n      point.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })\n    );\n\n    const config: ChartConfiguration = {\n      type: 'line',\n      data: {\n        labels,\n        datasets: [\n          {\n            label: 'Critical',\n            data: this.trendsData.data.map(point => point.critical),\n            borderColor: '#dc2626',\n            backgroundColor: 'rgba(220, 38, 38, 0.1)',\n            borderWidth: 3,\n            fill: false,\n            tension: 0.4\n          },\n          {\n            label: 'High',\n            data: this.trendsData.data.map(point => point.high),\n            borderColor: '#ea580c',\n            backgroundColor: 'rgba(234, 88, 12, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4\n          },\n          {\n            label: 'Medium',\n            data: this.trendsData.data.map(point => point.medium),\n            borderColor: '#d97706',\n            backgroundColor: 'rgba(217, 119, 6, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4\n          },\n          {\n            label: 'Low',\n            data: this.trendsData.data.map(point => point.low),\n            borderColor: '#65a30d',\n            backgroundColor: 'rgba(101, 163, 13, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          mode: 'index',\n          intersect: false,\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              usePointStyle: true,\n              padding: 20,\n              font: {\n                family: 'Inter, sans-serif',\n                size: 12,\n                weight: 500\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Date',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            grid: {\n              color: '#f3f4f6'\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Number of Issues',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            beginAtZero: true,\n            grid: {\n              color: '#f3f4f6'\n            }\n          }\n        },\n        animation: {\n          duration: 1000,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n\n    this.trendsChart = new Chart(ctx, config);\n  }\n\n  private createScoreChart(): void {\n    if (!this.scoreCanvas?.nativeElement || !this.trendsData) {\n      return;\n    }\n\n    const ctx = this.scoreCanvas.nativeElement.getContext('2d');\n    if (!ctx) return;\n\n    if (this.scoreChart) {\n      this.scoreChart.destroy();\n    }\n\n    const labels = this.trendsData.data.map(point => \n      point.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })\n    );\n\n    const config: ChartConfiguration = {\n      type: 'bar',\n      data: {\n        labels,\n        datasets: [\n          {\n            label: 'Security Score',\n            data: this.trendsData.data.map(point => point.averageScore),\n            backgroundColor: this.trendsData.data.map(point => \n              point.averageScore >= 80 ? '#10b981' :\n              point.averageScore >= 60 ? '#f59e0b' :\n              point.averageScore >= 40 ? '#ef4444' : '#dc2626'\n            ),\n            borderColor: this.trendsData.data.map(point => \n              point.averageScore >= 80 ? '#059669' :\n              point.averageScore >= 60 ? '#d97706' :\n              point.averageScore >= 40 ? '#dc2626' : '#b91c1c'\n            ),\n            borderWidth: 1,\n            borderRadius: 4\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8,\n            callbacks: {\n              label: (context) => {\n                return `Security Score: ${context.parsed.y}/100`;\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Date',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            grid: {\n              display: false\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Security Score',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            min: 0,\n            max: 100,\n            grid: {\n              color: '#f3f4f6'\n            }\n          }\n        },\n        animation: {\n          duration: 1000,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n\n    this.scoreChart = new Chart(ctx, config);\n  }\n\n  private createVolumeChart(): void {\n    if (!this.volumeCanvas?.nativeElement || !this.trendsData) {\n      return;\n    }\n\n    const ctx = this.volumeCanvas.nativeElement.getContext('2d');\n    if (!ctx) return;\n\n    if (this.volumeChart) {\n      this.volumeChart.destroy();\n    }\n\n    const labels = this.trendsData.data.map(point => \n      point.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })\n    );\n\n    const config: ChartConfiguration = {\n      type: 'bar',\n      data: {\n        labels,\n        datasets: [\n          {\n            label: 'Scans Performed',\n            data: this.trendsData.data.map(point => point.totalScans),\n            backgroundColor: '#3b82f6',\n            borderColor: '#2563eb',\n            borderWidth: 1,\n            borderRadius: 4\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Date',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            grid: {\n              display: false\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Number of Scans',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            beginAtZero: true,\n            grid: {\n              color: '#f3f4f6'\n            }\n          }\n        },\n        animation: {\n          duration: 1000,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n\n    this.volumeChart = new Chart(ctx, config);\n  }\n\n  onPeriodChange(period: string): void {\n    this.selectedPeriod = period;\n    this.refreshData();\n  }\n\n  onViewChange(view: string): void {\n    this.selectedView = view;\n  }\n\n  refreshData(): void {\n    this.isLoading = true;\n    // Simulate data refresh\n    setTimeout(() => {\n      this.createCharts();\n      this.isLoading = false;\n    }, 500);\n  }\n\n  exportChart(): void {\n    let chart: Chart | null = null;\n    let filename = '';\n\n    switch (this.selectedView) {\n      case 'trends':\n        chart = this.trendsChart;\n        filename = 'security-trends';\n        break;\n      case 'scores':\n        chart = this.scoreChart;\n        filename = 'security-scores';\n        break;\n      case 'volume':\n        chart = this.volumeChart;\n        filename = 'scan-volume';\n        break;\n    }\n\n    if (chart) {\n      const url = chart.toBase64Image();\n      const link = document.createElement('a');\n      link.download = `${filename}-${this.selectedPeriod}.png`;\n      link.href = url;\n      link.click();\n    }\n  }\n\n  getTrendDirection(value: number): 'up' | 'down' | 'flat' {\n    if (value > 5) return 'up';\n    if (value < -5) return 'down';\n    return 'flat';\n  }\n\n  getTrendIcon(direction: 'up' | 'down' | 'flat'): string {\n    switch (direction) {\n      case 'up': return 'trending_up';\n      case 'down': return 'trending_down';\n      case 'flat': return 'trending_flat';\n    }\n  }\n\n  getTrendColor(direction: 'up' | 'down' | 'flat', isGoodTrend: boolean = false): string {\n    if (direction === 'flat') return 'var(--spt-text-secondary)';\n    \n    if (isGoodTrend) {\n      return direction === 'up' ? 'var(--spt-success-600)' : 'var(--spt-error-600)';\n    } else {\n      return direction === 'up' ? 'var(--spt-error-600)' : 'var(--spt-success-600)';\n    }\n  }\n}\n", "<div class=\"trends-dashboard\">\n  <!-- Dashboard Header -->\n  <div class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <h2 class=\"dashboard-title\">\n        <mat-icon>trending_up</mat-icon>\n        Security Trends Analysis\n      </h2>\n      <p class=\"dashboard-subtitle\">Monitor security metrics and trends over time</p>\n    </div>\n\n    <div class=\"header-controls\">\n      <!-- Period Selector -->\n      <mat-form-field appearance=\"outline\" class=\"period-select\">\n        <mat-label>Time Period</mat-label>\n        <mat-select \n          [(value)]=\"selectedPeriod\" \n          (selectionChange)=\"onPeriodChange($event.value)\">\n          <mat-option *ngFor=\"let period of periods\" [value]=\"period.value\">\n            {{ period.label }}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n\n      <!-- Actions -->\n      <div class=\"header-actions\">\n        <button \n          mat-icon-button \n          matTooltip=\"Refresh Data\"\n          (click)=\"refreshData()\"\n          class=\"action-btn\"\n          [disabled]=\"isLoading\">\n          <mat-icon [class.spinning]=\"isLoading\">refresh</mat-icon>\n        </button>\n        \n        <button \n          mat-icon-button \n          matTooltip=\"Export Chart\"\n          (click)=\"exportChart()\"\n          class=\"action-btn\">\n          <mat-icon>download</mat-icon>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Summary Cards -->\n  <div class=\"summary-cards\" *ngIf=\"trendsData\">\n    <div class=\"summary-card total-issues\">\n      <div class=\"card-header\">\n        <mat-icon>bug_report</mat-icon>\n        <span class=\"card-title\">Total Issues</span>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"metric-value\">{{ trendsData.summary.totalIssues }}</div>\n        <div class=\"metric-trend\" \n             [style.color]=\"getTrendColor(getTrendDirection(trendsData.summary.criticalTrend), false)\">\n          <mat-icon>{{ getTrendIcon(getTrendDirection(trendsData.summary.criticalTrend)) }}</mat-icon>\n          <span>{{ Math.abs(trendsData.summary.criticalTrend) }}%</span>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"summary-card security-score\">\n      <div class=\"card-header\">\n        <mat-icon>grade</mat-icon>\n        <span class=\"card-title\">Avg Security Score</span>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"metric-value\">{{ trendsData.summary.averageScore }}/100</div>\n        <div class=\"score-bar\">\n          <div class=\"score-fill\" [style.width.%]=\"trendsData.summary.averageScore\"></div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"summary-card scan-frequency\">\n      <div class=\"card-header\">\n        <mat-icon>schedule</mat-icon>\n        <span class=\"card-title\">Scan Frequency</span>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"metric-value\">{{ trendsData.summary.scanFrequency }}</div>\n        <div class=\"metric-label\">scans per day</div>\n      </div>\n    </div>\n\n    <div class=\"summary-card trend-indicator\">\n      <div class=\"card-header\">\n        <mat-icon>insights</mat-icon>\n        <span class=\"card-title\">Overall Trend</span>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"trend-status\" \n             [class]=\"'trend-' + getTrendDirection(trendsData.summary.criticalTrend)\">\n          <mat-icon>{{ getTrendIcon(getTrendDirection(trendsData.summary.criticalTrend)) }}</mat-icon>\n          <span>{{ getTrendDirection(trendsData.summary.criticalTrend) === 'up' ? 'Increasing' : \n                    getTrendDirection(trendsData.summary.criticalTrend) === 'down' ? 'Improving' : 'Stable' }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Chart Tabs -->\n  <div class=\"chart-section\">\n    <mat-tab-group \n      [(selectedIndex)]=\"selectedTabIndex\" \n      (selectedTabChange)=\"onViewChange(chartViews[$event.index].value)\"\n      class=\"chart-tabs\">\n      \n      <!-- Security Trends Tab -->\n      <mat-tab>\n        <ng-template mat-tab-label>\n          <mat-icon>trending_up</mat-icon>\n          <span>Security Trends</span>\n        </ng-template>\n        \n        <div class=\"chart-container\">\n          <div class=\"chart-header\">\n            <h3>Security Issues Over Time</h3>\n            <p>Track the evolution of security issues by severity level</p>\n          </div>\n          \n          <div class=\"chart-content\" [class.loading]=\"isLoading\">\n            <div *ngIf=\"isLoading\" class=\"chart-loading\">\n              <mat-icon class=\"spinning\">refresh</mat-icon>\n              <span>Loading trends data...</span>\n            </div>\n            \n            <canvas \n              #trendsCanvas\n              *ngIf=\"!isLoading && trendsData\"\n              class=\"chart-canvas\"\n              [attr.aria-label]=\"'Security trends chart for ' + selectedPeriod\">\n            </canvas>\n            \n            <div *ngIf=\"!isLoading && !trendsData\" class=\"chart-empty\">\n              <mat-icon>trending_up</mat-icon>\n              <h4>No Trends Data</h4>\n              <p>No security trends data available for the selected period.</p>\n            </div>\n          </div>\n        </div>\n      </mat-tab>\n\n      <!-- Security Scores Tab -->\n      <mat-tab>\n        <ng-template mat-tab-label>\n          <mat-icon>grade</mat-icon>\n          <span>Security Scores</span>\n        </ng-template>\n        \n        <div class=\"chart-container\">\n          <div class=\"chart-header\">\n            <h3>Security Score History</h3>\n            <p>Monitor overall security score improvements over time</p>\n          </div>\n          \n          <div class=\"chart-content\" [class.loading]=\"isLoading\">\n            <div *ngIf=\"isLoading\" class=\"chart-loading\">\n              <mat-icon class=\"spinning\">refresh</mat-icon>\n              <span>Loading score data...</span>\n            </div>\n            \n            <canvas \n              #scoreCanvas\n              *ngIf=\"!isLoading && trendsData\"\n              class=\"chart-canvas\"\n              [attr.aria-label]=\"'Security scores chart for ' + selectedPeriod\">\n            </canvas>\n            \n            <div *ngIf=\"!isLoading && !trendsData\" class=\"chart-empty\">\n              <mat-icon>grade</mat-icon>\n              <h4>No Score Data</h4>\n              <p>No security score data available for the selected period.</p>\n            </div>\n          </div>\n        </div>\n      </mat-tab>\n\n      <!-- Scan Volume Tab -->\n      <mat-tab>\n        <ng-template mat-tab-label>\n          <mat-icon>bar_chart</mat-icon>\n          <span>Scan Volume</span>\n        </ng-template>\n        \n        <div class=\"chart-container\">\n          <div class=\"chart-header\">\n            <h3>Scan Activity Volume</h3>\n            <p>Track scanning frequency and activity patterns</p>\n          </div>\n          \n          <div class=\"chart-content\" [class.loading]=\"isLoading\">\n            <div *ngIf=\"isLoading\" class=\"chart-loading\">\n              <mat-icon class=\"spinning\">refresh</mat-icon>\n              <span>Loading volume data...</span>\n            </div>\n            \n            <canvas \n              #volumeCanvas\n              *ngIf=\"!isLoading && trendsData\"\n              class=\"chart-canvas\"\n              [attr.aria-label]=\"'Scan volume chart for ' + selectedPeriod\">\n            </canvas>\n            \n            <div *ngIf=\"!isLoading && !trendsData\" class=\"chart-empty\">\n              <mat-icon>bar_chart</mat-icon>\n              <h4>No Volume Data</h4>\n              <p>No scan volume data available for the selected period.</p>\n            </div>\n          </div>\n        </div>\n      </mat-tab>\n    </mat-tab-group>\n  </div>\n\n  <!-- Insights Panel -->\n  <div class=\"insights-panel\" *ngIf=\"trendsData\">\n    <div class=\"insights-header\">\n      <mat-icon>lightbulb</mat-icon>\n      <h3>Key Insights</h3>\n    </div>\n    \n    <div class=\"insights-content\">\n      <div class=\"insight-item\" *ngIf=\"trendsData.summary.criticalTrend > 10\">\n        <mat-icon class=\"insight-icon warning\">warning</mat-icon>\n        <div class=\"insight-text\">\n          <strong>Critical issues are increasing</strong> by {{ trendsData.summary.criticalTrend }}%. \n          Consider reviewing security practices and increasing scan frequency.\n        </div>\n      </div>\n      \n      <div class=\"insight-item\" *ngIf=\"trendsData.summary.criticalTrend < -10\">\n        <mat-icon class=\"insight-icon success\">check_circle</mat-icon>\n        <div class=\"insight-text\">\n          <strong>Security is improving!</strong> Critical issues decreased by {{ Math.abs(trendsData.summary.criticalTrend) }}%. \n          Keep up the good security practices.\n        </div>\n      </div>\n      \n      <div class=\"insight-item\" *ngIf=\"trendsData.summary.averageScore < 60\">\n        <mat-icon class=\"insight-icon error\">error</mat-icon>\n        <div class=\"insight-text\">\n          <strong>Low security score detected.</strong> Average score is {{ trendsData.summary.averageScore }}/100. \n          Focus on addressing high and critical severity issues.\n        </div>\n      </div>\n      \n      <div class=\"insight-item\" *ngIf=\"trendsData.summary.scanFrequency < 1\">\n        <mat-icon class=\"insight-icon info\">info</mat-icon>\n        <div class=\"insight-text\">\n          <strong>Low scan frequency.</strong> Consider increasing scan frequency to {{ trendsData.summary.scanFrequency }} per day \n          for better security monitoring.\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Empty State -->\n<div class=\"empty-dashboard\" *ngIf=\"!trendsData\">\n  <mat-icon class=\"empty-icon\">trending_up</mat-icon>\n  <h3>No Trends Data Available</h3>\n  <p>Start running security scans to see trends and analytics here.</p>\n  <button mat-raised-button color=\"primary\">\n    <mat-icon>security</mat-icon>\n    Start First Scan\n  </button>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,KAAK,EAAsBC,aAAa,QAAQ,UAAU;AACnE,SAASC,OAAO,QAAQ,MAAM;;;;;;;;;;;;;ICSpBC,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IAC/DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IA8BFT,EAHN,CAAAC,cAAA,cAA8C,cACL,cACZ,eACb;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACxC;IAEJH,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGlEH,EAFF,CAAAC,cAAA,eAC+F,gBACnF;IAAAD,EAAA,CAAAE,MAAA,IAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5FH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAiD;IAG7DF,EAH6D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAyC,eACd,gBACb;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;IAEJH,EADF,CAAAC,cAAA,eAA0B,eACE;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,SAAA,eAAgF;IAGtFV,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAyC,eACd,gBACb;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IACzCF,EADyC,CAAAG,YAAA,EAAO,EAC1C;IAEJH,EADF,CAAAC,cAAA,eAA0B,eACE;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAE3CF,EAF2C,CAAAG,YAAA,EAAM,EACzC,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA0C,eACf,gBACb;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACxCF,EADwC,CAAAG,YAAA,EAAO,EACzC;IAIFH,EAHJ,CAAAC,cAAA,eAA0B,eAEsD,gBAClE;IAAAD,EAAA,CAAAE,MAAA,IAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5FH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAC8F;IAI5GF,EAJ4G,CAAAG,YAAA,EAAO,EACvG,EACF,EACF,EACF;;;;IA/C0BH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAoC;IAEzDf,EAAA,CAAAO,SAAA,EAAyF;IAAzFP,EAAA,CAAAgB,WAAA,UAAAJ,MAAA,CAAAK,aAAA,CAAAL,MAAA,CAAAM,iBAAA,CAAAN,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,UAAyF;IAClFnB,EAAA,CAAAO,SAAA,GAAuE;IAAvEP,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAQ,YAAA,CAAAR,MAAA,CAAAM,iBAAA,CAAAN,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,GAAuE;IAC3EnB,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAQ,kBAAA,KAAAI,MAAA,CAAAS,IAAA,CAAAC,GAAA,CAAAV,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,OAAiD;IAW/BnB,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAQ,kBAAA,KAAAI,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAS,YAAA,SAAyC;IAEzCvB,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAgB,WAAA,UAAAJ,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAS,YAAA,MAAiD;IAWjDvB,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAU,aAAA,CAAsC;IAY3DxB,EAAA,CAAAO,SAAA,IAAwE;IAAxEP,EAAA,CAAAyB,UAAA,YAAAb,MAAA,CAAAM,iBAAA,CAAAN,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,EAAwE;IACjEnB,EAAA,CAAAO,SAAA,GAAuE;IAAvEP,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAQ,YAAA,CAAAR,MAAA,CAAAM,iBAAA,CAAAN,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,GAAuE;IAC3EnB,EAAA,CAAAO,SAAA,GAC8F;IAD9FP,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAM,iBAAA,CAAAN,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,4BAAAP,MAAA,CAAAM,iBAAA,CAAAN,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,sCAC8F;;;;;IAgBpGnB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAWxBH,EADF,CAAAC,cAAA,cAA6C,mBAChB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;;;;;IAENH,EAAA,CAAAU,SAAA,oBAKS;;;;;;;;;IAGPV,EADF,CAAAC,cAAA,cAA2D,eAC/C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iEAA0D;IAC/DF,EAD+D,CAAAG,YAAA,EAAI,EAC7D;;;;;IAQRH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAWxBH,EADF,CAAAC,cAAA,cAA6C,mBAChB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAC7BF,EAD6B,CAAAG,YAAA,EAAO,EAC9B;;;;;IAENH,EAAA,CAAAU,SAAA,oBAKS;;;;;;;;;IAGPV,EADF,CAAAC,cAAA,cAA2D,eAC/C;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gEAAyD;IAC9DF,EAD8D,CAAAG,YAAA,EAAI,EAC5D;;;;;IAQRH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAWpBH,EADF,CAAAC,cAAA,cAA6C,mBAChB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;;;;;IAENH,EAAA,CAAAU,SAAA,oBAKS;;;;;;;;;IAGPV,EADF,CAAAC,cAAA,cAA2D,eAC/C;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6DAAsD;IAC3DF,EAD2D,CAAAG,YAAA,EAAI,EACzD;;;;;IAgBVH,EADF,CAAAC,cAAA,cAAwE,mBAC/B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEvDH,EADF,CAAAC,cAAA,cAA0B,aAChB;IAAAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAElD;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAH8CH,EAAA,CAAAO,SAAA,GAElD;IAFkDP,EAAA,CAAAQ,kBAAA,SAAAI,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,6EAElD;;;;;IAIAnB,EADF,CAAAC,cAAA,cAAyE,mBAChC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE5DH,EADF,CAAAC,cAAA,cAA0B,aAChB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAE1C;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAHsCH,EAAA,CAAAO,SAAA,GAE1C;IAF0CP,EAAA,CAAAQ,kBAAA,mCAAAI,MAAA,CAAAS,IAAA,CAAAC,GAAA,CAAAV,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,8CAE1C;;;;;IAIAnB,EADF,CAAAC,cAAA,cAAuE,mBAChC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnDH,EADF,CAAAC,cAAA,cAA0B,aAChB;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAEhD;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAH4CH,EAAA,CAAAO,SAAA,GAEhD;IAFgDP,EAAA,CAAAQ,kBAAA,uBAAAI,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAS,YAAA,kEAEhD;;;;;IAIAvB,EADF,CAAAC,cAAA,cAAuE,mBACjC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjDH,EADF,CAAAC,cAAA,cAA0B,aAChB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAEvC;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAHmCH,EAAA,CAAAO,SAAA,GAEvC;IAFuCP,EAAA,CAAAQ,kBAAA,4CAAAI,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAU,aAAA,8CAEvC;;;;;IAlCFxB,EAFJ,CAAAC,cAAA,cAA+C,cAChB,eACjB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAClBF,EADkB,CAAAG,YAAA,EAAK,EACjB;IAENH,EAAA,CAAAC,cAAA,cAA8B;IAyB5BD,EAxBA,CAAA0B,UAAA,IAAAC,sDAAA,kBAAwE,IAAAC,sDAAA,kBAQC,IAAAC,sDAAA,kBAQF,KAAAC,uDAAA,kBAQA;IAQ3E9B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhCyBH,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,MAA2C;IAQ3CnB,EAAA,CAAAO,SAAA,EAA4C;IAA5CP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAK,aAAA,OAA4C;IAQ5CnB,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAS,YAAA,MAA0C;IAQ1CvB,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAU,aAAA,KAA0C;;;;;IAazExB,EADF,CAAAC,cAAA,cAAiD,mBAClB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEnEH,EADF,CAAAC,cAAA,iBAA0C,eAC9B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,0BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;ADlQN;AACAN,KAAK,CAACkC,QAAQ,CAAC,GAAGjC,aAAa,CAAC;AAsChC,OAAM,MAAOkC,gCAAgC;EAf7CC,YAAA;IAoBW,KAAApB,UAAU,GAAyB,IAAI;IACvC,KAAAqB,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,KAAK;IAE7B,KAAAC,QAAQ,GAAG,IAAIrC,OAAO,EAAQ;IAC9B,KAAAsC,WAAW,GAAiB,IAAI;IAChC,KAAAC,UAAU,GAAiB,IAAI;IAC/B,KAAAC,WAAW,GAAiB,IAAI;IAExC,KAAAC,cAAc,GAAW,KAAK;IAC9B,KAAAC,YAAY,GAAW,QAAQ;IAC/B,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAtB,IAAI,GAAGA,IAAI;IAEX,KAAAuB,OAAO,GAAG,CACR;MAAEtC,KAAK,EAAE,IAAI;MAAEG,KAAK,EAAE;IAAa,CAAE,EACrC;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAc,CAAE,EACvC;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAc,CAAE,EACvC;MAAEH,KAAK,EAAE,IAAI;MAAEG,KAAK,EAAE;IAAW,CAAE,CACpC;IAED,KAAAoC,UAAU,GAAG,CACX;MAAEvC,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE,iBAAiB;MAAEqC,IAAI,EAAE;IAAa,CAAE,EAClE;MAAExC,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE,iBAAiB;MAAEqC,IAAI,EAAE;IAAO,CAAE,EAC5D;MAAExC,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE,aAAa;MAAEqC,IAAI,EAAE;IAAW,CAAE,CAC7D;;EAEDC,QAAQA,CAAA;IACN;EAAA;EAGFC,eAAeA,CAAA;IACb,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACd,QAAQ,CAACe,IAAI,EAAE;IACpB,IAAI,CAACf,QAAQ,CAACgB,QAAQ,EAAE;IACxB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQA,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAChB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACiB,OAAO,EAAE;MAC1B,IAAI,CAACjB,WAAW,GAAG,IAAI;IACzB;IACA,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACgB,OAAO,EAAE;MACzB,IAAI,CAAChB,UAAU,GAAG,IAAI;IACxB;IACA,IAAI,IAAI,CAACC,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACe,OAAO,EAAE;MAC1B,IAAI,CAACf,WAAW,GAAG,IAAI;IACzB;EACF;EAEQU,YAAYA,CAAA;IAClB,IAAI,CAACM,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEQF,iBAAiBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACG,YAAY,EAAEC,aAAa,IAAI,CAAC,IAAI,CAAC9C,UAAU,EAAE;MACzD;IACF;IAEA,MAAM+C,GAAG,GAAG,IAAI,CAACF,YAAY,CAACC,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACD,GAAG,EAAE;IAEV,IAAI,IAAI,CAACvB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACiB,OAAO,EAAE;IAC5B;IAEA,MAAMQ,MAAM,GAAG,IAAI,CAACjD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAC3CA,KAAK,CAACC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAS,CAAE,CAAC,CAC3E;IAED,MAAMC,MAAM,GAAuB;MACjCC,IAAI,EAAE,MAAM;MACZR,IAAI,EAAE;QACJD,MAAM;QACNU,QAAQ,EAAE,CACR;UACE/D,KAAK,EAAE,UAAU;UACjBsD,IAAI,EAAE,IAAI,CAAClD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACQ,QAAQ,CAAC;UACvDC,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,wBAAwB;UACzCC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE;SACV,EACD;UACErE,KAAK,EAAE,MAAM;UACbsD,IAAI,EAAE,IAAI,CAAClD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACc,IAAI,CAAC;UACnDL,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,wBAAwB;UACzCC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE;SACV,EACD;UACErE,KAAK,EAAE,QAAQ;UACfsD,IAAI,EAAE,IAAI,CAAClD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACe,MAAM,CAAC;UACrDN,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,wBAAwB;UACzCC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE;SACV,EACD;UACErE,KAAK,EAAE,KAAK;UACZsD,IAAI,EAAE,IAAI,CAAClD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACgB,GAAG,CAAC;UAClDP,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE;SACV;OAEJ;MACDI,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,WAAW,EAAE;UACXC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE;SACZ;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,KAAK;YACf7B,MAAM,EAAE;cACN8B,aAAa,EAAE,IAAI;cACnBC,OAAO,EAAE,EAAE;cACXC,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BC,IAAI,EAAE,EAAE;gBACRC,MAAM,EAAE;;;WAGb;UACDC,OAAO,EAAE;YACPvB,eAAe,EAAE,oBAAoB;YACrCwB,UAAU,EAAE,SAAS;YACrBC,SAAS,EAAE,SAAS;YACpB1B,WAAW,EAAE,SAAS;YACtBE,WAAW,EAAE,CAAC;YACdyB,YAAY,EAAE;;SAEjB;QACDC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDb,OAAO,EAAE,IAAI;YACbc,KAAK,EAAE;cACLd,OAAO,EAAE,IAAI;cACbe,IAAI,EAAE,MAAM;cACZX,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDS,IAAI,EAAE;cACJC,KAAK,EAAE;;WAEV;UACDC,CAAC,EAAE;YACDlB,OAAO,EAAE,IAAI;YACbc,KAAK,EAAE;cACLd,OAAO,EAAE,IAAI;cACbe,IAAI,EAAE,kBAAkB;cACxBX,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDY,WAAW,EAAE,IAAI;YACjBH,IAAI,EAAE;cACJC,KAAK,EAAE;;;SAGZ;QACDG,SAAS,EAAE;UACTC,QAAQ,EAAE,IAAI;UACdC,MAAM,EAAE;;;KAGb;IAED,IAAI,CAAC3E,WAAW,GAAG,IAAIxC,KAAK,CAAC+D,GAAG,EAAEU,MAAM,CAAC;EAC3C;EAEQd,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACyD,WAAW,EAAEtD,aAAa,IAAI,CAAC,IAAI,CAAC9C,UAAU,EAAE;MACxD;IACF;IAEA,MAAM+C,GAAG,GAAG,IAAI,CAACqD,WAAW,CAACtD,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;IAC3D,IAAI,CAACD,GAAG,EAAE;IAEV,IAAI,IAAI,CAACtB,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACgB,OAAO,EAAE;IAC3B;IAEA,MAAMQ,MAAM,GAAG,IAAI,CAACjD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAC3CA,KAAK,CAACC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAS,CAAE,CAAC,CAC3E;IAED,MAAMC,MAAM,GAAuB;MACjCC,IAAI,EAAE,KAAK;MACXR,IAAI,EAAE;QACJD,MAAM;QACNU,QAAQ,EAAE,CACR;UACE/D,KAAK,EAAE,gBAAgB;UACvBsD,IAAI,EAAE,IAAI,CAAClD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAAC1C,YAAY,CAAC;UAC3DoD,eAAe,EAAE,IAAI,CAAC9D,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAC7CA,KAAK,CAAC1C,YAAY,IAAI,EAAE,GAAG,SAAS,GACpC0C,KAAK,CAAC1C,YAAY,IAAI,EAAE,GAAG,SAAS,GACpC0C,KAAK,CAAC1C,YAAY,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS,CACjD;UACDmD,WAAW,EAAE,IAAI,CAAC7D,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IACzCA,KAAK,CAAC1C,YAAY,IAAI,EAAE,GAAG,SAAS,GACpC0C,KAAK,CAAC1C,YAAY,IAAI,EAAE,GAAG,SAAS,GACpC0C,KAAK,CAAC1C,YAAY,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS,CACjD;UACDqD,WAAW,EAAE,CAAC;UACdsC,YAAY,EAAE;SACf;OAEJ;MACDhC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BI,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;WACV;UACDQ,OAAO,EAAE;YACPvB,eAAe,EAAE,oBAAoB;YACrCwB,UAAU,EAAE,SAAS;YACrBC,SAAS,EAAE,SAAS;YACpB1B,WAAW,EAAE,SAAS;YACtBE,WAAW,EAAE,CAAC;YACdyB,YAAY,EAAE,CAAC;YACfc,SAAS,EAAE;cACT1G,KAAK,EAAG2G,OAAO,IAAI;gBACjB,OAAO,mBAAmBA,OAAO,CAACC,MAAM,CAACT,CAAC,MAAM;cAClD;;;SAGL;QACDN,MAAM,EAAE;UACNC,CAAC,EAAE;YACDb,OAAO,EAAE,IAAI;YACbc,KAAK,EAAE;cACLd,OAAO,EAAE,IAAI;cACbe,IAAI,EAAE,MAAM;cACZX,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDS,IAAI,EAAE;cACJhB,OAAO,EAAE;;WAEZ;UACDkB,CAAC,EAAE;YACDlB,OAAO,EAAE,IAAI;YACbc,KAAK,EAAE;cACLd,OAAO,EAAE,IAAI;cACbe,IAAI,EAAE,gBAAgB;cACtBX,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDqB,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,GAAG;YACRb,IAAI,EAAE;cACJC,KAAK,EAAE;;;SAGZ;QACDG,SAAS,EAAE;UACTC,QAAQ,EAAE,IAAI;UACdC,MAAM,EAAE;;;KAGb;IAED,IAAI,CAAC1E,UAAU,GAAG,IAAIzC,KAAK,CAAC+D,GAAG,EAAEU,MAAM,CAAC;EAC1C;EAEQb,iBAAiBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAAC+D,YAAY,EAAE7D,aAAa,IAAI,CAAC,IAAI,CAAC9C,UAAU,EAAE;MACzD;IACF;IAEA,MAAM+C,GAAG,GAAG,IAAI,CAAC4D,YAAY,CAAC7D,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACD,GAAG,EAAE;IAEV,IAAI,IAAI,CAACrB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACe,OAAO,EAAE;IAC5B;IAEA,MAAMQ,MAAM,GAAG,IAAI,CAACjD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAC3CA,KAAK,CAACC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAS,CAAE,CAAC,CAC3E;IAED,MAAMC,MAAM,GAAuB;MACjCC,IAAI,EAAE,KAAK;MACXR,IAAI,EAAE;QACJD,MAAM;QACNU,QAAQ,EAAE,CACR;UACE/D,KAAK,EAAE,iBAAiB;UACxBsD,IAAI,EAAE,IAAI,CAAClD,UAAU,CAACkD,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACwD,UAAU,CAAC;UACzD9C,eAAe,EAAE,SAAS;UAC1BD,WAAW,EAAE,SAAS;UACtBE,WAAW,EAAE,CAAC;UACdsC,YAAY,EAAE;SACf;OAEJ;MACDhC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BI,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;WACV;UACDQ,OAAO,EAAE;YACPvB,eAAe,EAAE,oBAAoB;YACrCwB,UAAU,EAAE,SAAS;YACrBC,SAAS,EAAE,SAAS;YACpB1B,WAAW,EAAE,SAAS;YACtBE,WAAW,EAAE,CAAC;YACdyB,YAAY,EAAE;;SAEjB;QACDC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDb,OAAO,EAAE,IAAI;YACbc,KAAK,EAAE;cACLd,OAAO,EAAE,IAAI;cACbe,IAAI,EAAE,MAAM;cACZX,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDS,IAAI,EAAE;cACJhB,OAAO,EAAE;;WAEZ;UACDkB,CAAC,EAAE;YACDlB,OAAO,EAAE,IAAI;YACbc,KAAK,EAAE;cACLd,OAAO,EAAE,IAAI;cACbe,IAAI,EAAE,iBAAiB;cACvBX,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDY,WAAW,EAAE,IAAI;YACjBH,IAAI,EAAE;cACJC,KAAK,EAAE;;;SAGZ;QACDG,SAAS,EAAE;UACTC,QAAQ,EAAE,IAAI;UACdC,MAAM,EAAE;;;KAGb;IAED,IAAI,CAACzE,WAAW,GAAG,IAAI1C,KAAK,CAAC+D,GAAG,EAAEU,MAAM,CAAC;EAC3C;EAEAoD,cAAcA,CAACC,MAAc;IAC3B,IAAI,CAACnF,cAAc,GAAGmF,MAAM;IAC5B,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,YAAYA,CAACC,IAAY;IACvB,IAAI,CAACrF,YAAY,GAAGqF,IAAI;EAC1B;EAEAF,WAAWA,CAAA;IACT,IAAI,CAACjF,SAAS,GAAG,IAAI;IACrB;IACAoF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9E,YAAY,EAAE;MACnB,IAAI,CAACN,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAqF,WAAWA,CAAA;IACT,IAAIC,KAAK,GAAiB,IAAI;IAC9B,IAAIC,QAAQ,GAAG,EAAE;IAEjB,QAAQ,IAAI,CAACzF,YAAY;MACvB,KAAK,QAAQ;QACXwF,KAAK,GAAG,IAAI,CAAC5F,WAAW;QACxB6F,QAAQ,GAAG,iBAAiB;QAC5B;MACF,KAAK,QAAQ;QACXD,KAAK,GAAG,IAAI,CAAC3F,UAAU;QACvB4F,QAAQ,GAAG,iBAAiB;QAC5B;MACF,KAAK,QAAQ;QACXD,KAAK,GAAG,IAAI,CAAC1F,WAAW;QACxB2F,QAAQ,GAAG,aAAa;QACxB;IACJ;IAEA,IAAID,KAAK,EAAE;MACT,MAAME,GAAG,GAAGF,KAAK,CAACG,aAAa,EAAE;MACjC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,QAAQ,GAAG,GAAGN,QAAQ,IAAI,IAAI,CAAC1F,cAAc,MAAM;MACxD6F,IAAI,CAACI,IAAI,GAAGN,GAAG;MACfE,IAAI,CAACK,KAAK,EAAE;IACd;EACF;EAEAxH,iBAAiBA,CAACZ,KAAa;IAC7B,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAO,IAAI;IAC1B,IAAIA,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,MAAM;IAC7B,OAAO,MAAM;EACf;EAEAc,YAAYA,CAACuH,SAAiC;IAC5C,QAAQA,SAAS;MACf,KAAK,IAAI;QAAE,OAAO,aAAa;MAC/B,KAAK,MAAM;QAAE,OAAO,eAAe;MACnC,KAAK,MAAM;QAAE,OAAO,eAAe;IACrC;EACF;EAEA1H,aAAaA,CAAC0H,SAAiC,EAAEC,WAAA,GAAuB,KAAK;IAC3E,IAAID,SAAS,KAAK,MAAM,EAAE,OAAO,2BAA2B;IAE5D,IAAIC,WAAW,EAAE;MACf,OAAOD,SAAS,KAAK,IAAI,GAAG,wBAAwB,GAAG,sBAAsB;IAC/E,CAAC,MAAM;MACL,OAAOA,SAAS,KAAK,IAAI,GAAG,sBAAsB,GAAG,wBAAwB;IAC/E;EACF;;;uCA1cW3G,gCAAgC;IAAA;EAAA;;;YAAhCA,gCAAgC;MAAA6G,SAAA;MAAAC,SAAA,WAAAC,uCAAAC,EAAA,EAAApF,GAAA;QAAA,IAAAoF,EAAA;;;;;;;;;;;;;;;;;;;;;;UC7CrChJ,EALR,CAAAC,cAAA,aAA8B,aAEE,aACA,YACE,eAChB;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,iCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA8B;UAAAD,EAAA,CAAAE,MAAA,oDAA6C;UAC7EF,EAD6E,CAAAG,YAAA,EAAI,EAC3E;UAKFH,EAHJ,CAAAC,cAAA,aAA6B,yBAEgC,iBAC9C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAC,cAAA,sBAEmD;UADjDD,EAAA,CAAAiJ,gBAAA,yBAAAC,6EAAAC,MAAA;YAAAnJ,EAAA,CAAAoJ,kBAAA,CAAAxF,GAAA,CAAApB,cAAA,EAAA2G,MAAA,MAAAvF,GAAA,CAAApB,cAAA,GAAA2G,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAC1BnJ,EAAA,CAAAqJ,UAAA,6BAAAC,iFAAAH,MAAA;YAAA,OAAmBvF,GAAA,CAAA8D,cAAA,CAAAyB,MAAA,CAAA7I,KAAA,CAA4B;UAAA,EAAC;UAChDN,EAAA,CAAA0B,UAAA,KAAA6H,uDAAA,yBAAkE;UAItEvJ,EADE,CAAAG,YAAA,EAAa,EACE;UAIfH,EADF,CAAAC,cAAA,eAA4B,kBAMD;UAFvBD,EAAA,CAAAqJ,UAAA,mBAAAG,mEAAA;YAAA,OAAS5F,GAAA,CAAAgE,WAAA,EAAa;UAAA,EAAC;UAGvB5H,EAAA,CAAAC,cAAA,gBAAuC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAChDF,EADgD,CAAAG,YAAA,EAAW,EAClD;UAETH,EAAA,CAAAC,cAAA,kBAIqB;UADnBD,EAAA,CAAAqJ,UAAA,mBAAAI,mEAAA;YAAA,OAAS7F,GAAA,CAAAoE,WAAA,EAAa;UAAA,EAAC;UAEvBhI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAI1BF,EAJ0B,CAAAG,YAAA,EAAW,EACtB,EACL,EACF,EACF;UAGNH,EAAA,CAAA0B,UAAA,KAAAgI,gDAAA,oBAA8C;UA0D5C1J,EADF,CAAAC,cAAA,eAA2B,yBAIJ;UAFnBD,EAAA,CAAAiJ,gBAAA,iCAAAU,wFAAAR,MAAA;YAAAnJ,EAAA,CAAAoJ,kBAAA,CAAAxF,GAAA,CAAAlB,gBAAA,EAAAyG,MAAA,MAAAvF,GAAA,CAAAlB,gBAAA,GAAAyG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoC;UACpCnJ,EAAA,CAAAqJ,UAAA,+BAAAO,sFAAAT,MAAA;YAAA,OAAqBvF,GAAA,CAAAiE,YAAA,CAAAjE,GAAA,CAAAf,UAAA,CAAAsG,MAAA,CAAAU,KAAA,EAAAvJ,KAAA,CAA4C;UAAA,EAAC;UAIlEN,EAAA,CAAAC,cAAA,eAAS;UACPD,EAAA,CAAA0B,UAAA,KAAAoI,wDAAA,0BAA2B;UAOvB9J,EAFJ,CAAAC,cAAA,eAA6B,eACD,UACpB;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,gEAAwD;UAC7DF,EAD6D,CAAAG,YAAA,EAAI,EAC3D;UAENH,EAAA,CAAAC,cAAA,eAAuD;UAarDD,EAZA,CAAA0B,UAAA,KAAAqI,gDAAA,kBAA6C,KAAAC,mDAAA,qBASuB,KAAAC,gDAAA,kBAGT;UAOjEjK,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAGVH,EAAA,CAAAC,cAAA,eAAS;UACPD,EAAA,CAAA0B,UAAA,KAAAwI,wDAAA,0BAA2B;UAOvBlK,EAFJ,CAAAC,cAAA,eAA6B,eACD,UACpB;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6DAAqD;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACxD;UAENH,EAAA,CAAAC,cAAA,eAAuD;UAarDD,EAZA,CAAA0B,UAAA,KAAAyI,gDAAA,kBAA6C,KAAAC,mDAAA,qBASuB,KAAAC,gDAAA,kBAGT;UAOjErK,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAGVH,EAAA,CAAAC,cAAA,eAAS;UACPD,EAAA,CAAA0B,UAAA,KAAA4I,wDAAA,0BAA2B;UAOvBtK,EAFJ,CAAAC,cAAA,eAA6B,eACD,UACpB;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sDAA8C;UACnDF,EADmD,CAAAG,YAAA,EAAI,EACjD;UAENH,EAAA,CAAAC,cAAA,eAAuD;UAarDD,EAZA,CAAA0B,UAAA,KAAA6I,gDAAA,kBAA6C,KAAAC,mDAAA,qBASmB,KAAAC,gDAAA,kBAGL;UASrEzK,EAJQ,CAAAG,YAAA,EAAM,EACF,EACE,EACI,EACZ;UAGNH,EAAA,CAAA0B,UAAA,KAAAgJ,gDAAA,mBAA+C;UAwCjD1K,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA0B,UAAA,KAAAiJ,gDAAA,mBAAiD;;;UArPvC3K,EAAA,CAAAO,SAAA,IAA0B;UAA1BP,EAAA,CAAA4K,gBAAA,UAAAhH,GAAA,CAAApB,cAAA,CAA0B;UAEKxC,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAwD,GAAA,CAAAhB,OAAA,CAAU;UAazC5C,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,aAAAwD,GAAA,CAAAjB,SAAA,CAAsB;UACZ3C,EAAA,CAAAO,SAAA,EAA4B;UAA5BP,EAAA,CAAA6K,WAAA,aAAAjH,GAAA,CAAAjB,SAAA,CAA4B;UAelB3C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,SAAAwD,GAAA,CAAA/C,UAAA,CAAgB;UA2DxCb,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAA4K,gBAAA,kBAAAhH,GAAA,CAAAlB,gBAAA,CAAoC;UAiBL1C,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAA6K,WAAA,YAAAjH,GAAA,CAAAjB,SAAA,CAA2B;UAC9C3C,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAwD,GAAA,CAAAjB,SAAA,CAAe;UAOlB3C,EAAA,CAAAO,SAAA,EAA8B;UAA9BP,EAAA,CAAAI,UAAA,UAAAwD,GAAA,CAAAjB,SAAA,IAAAiB,GAAA,CAAA/C,UAAA,CAA8B;UAK3Bb,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,UAAAwD,GAAA,CAAAjB,SAAA,KAAAiB,GAAA,CAAA/C,UAAA,CAA+B;UAsBZb,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAA6K,WAAA,YAAAjH,GAAA,CAAAjB,SAAA,CAA2B;UAC9C3C,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAwD,GAAA,CAAAjB,SAAA,CAAe;UAOlB3C,EAAA,CAAAO,SAAA,EAA8B;UAA9BP,EAAA,CAAAI,UAAA,UAAAwD,GAAA,CAAAjB,SAAA,IAAAiB,GAAA,CAAA/C,UAAA,CAA8B;UAK3Bb,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,UAAAwD,GAAA,CAAAjB,SAAA,KAAAiB,GAAA,CAAA/C,UAAA,CAA+B;UAsBZb,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAA6K,WAAA,YAAAjH,GAAA,CAAAjB,SAAA,CAA2B;UAC9C3C,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAwD,GAAA,CAAAjB,SAAA,CAAe;UAOlB3C,EAAA,CAAAO,SAAA,EAA8B;UAA9BP,EAAA,CAAAI,UAAA,UAAAwD,GAAA,CAAAjB,SAAA,IAAAiB,GAAA,CAAA/C,UAAA,CAA8B;UAK3Bb,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,UAAAwD,GAAA,CAAAjB,SAAA,KAAAiB,GAAA,CAAA/C,UAAA,CAA+B;UAYlBb,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,SAAAwD,GAAA,CAAA/C,UAAA,CAAgB;UA2CjBb,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,UAAAwD,GAAA,CAAA/C,UAAA,CAAiB;;;qBD9N3CvB,YAAY,EAAAwL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZzL,aAAa,EACbC,eAAe,EAAAyL,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf1L,aAAa,EAAA2L,EAAA,CAAAC,OAAA,EACb3L,eAAe,EAAA4L,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAC,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EACfhM,kBAAkB,EAClBC,aAAa,EAAAgM,EAAA,CAAAC,WAAA,EAAAD,EAAA,CAAAE,MAAA,EAAAF,EAAA,CAAAG,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}