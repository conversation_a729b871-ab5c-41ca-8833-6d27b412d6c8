{"ast": null, "code": "import { AuthGuard, GuestGuard, RoleGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n},\n// Authentication routes\n{\n  path: 'login',\n  loadComponent: () => import('./components/auth/login.component').then(m => m.LoginComponent),\n  canActivate: [GuestGuard]\n}, {\n  path: 'register',\n  loadComponent: () => import('./components/auth/register.component').then(m => m.RegisterComponent),\n  canActivate: [GuestGuard]\n},\n// Protected routes (temporarily without AuthGuard for testing)\n{\n  path: 'dashboard',\n  loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent)\n  // canActivate: [AuthGuard] // Temporarily disabled for testing\n}, {\n  path: 'scan',\n  loadComponent: () => import('./components/scan/scan.component').then(m => m.ScanComponent)\n  // canActivate: [AuthGuard] // Temporarily disabled for testing\n}, {\n  path: 'scan/:id',\n  loadComponent: () => import('./components/scan-details/scan-details.component').then(m => m.ScanDetailsComponent)\n  // canActivate: [AuthGuard] // Temporarily disabled for testing\n}, {\n  path: 'checklist',\n  loadComponent: () => import('./components/checklist/checklist.component').then(m => m.ChecklistComponent)\n  // canActivate: [AuthGuard] // Temporarily disabled for testing\n}, {\n  path: 'reports',\n  loadComponent: () => import('./components/reports/reports.component').then(m => m.ReportsComponent)\n  // canActivate: [AuthGuard] // Temporarily disabled for testing\n}, {\n  path: 'settings',\n  loadComponent: () => import('./components/settings/settings.component').then(m => m.SettingsComponent),\n  canActivate: [AuthGuard]\n},\n// Projects management (admin only)\n{\n  path: 'projects',\n  loadComponent: () => import('./components/projects/projects.component').then(m => m.ProjectsComponent),\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: ['admin', 'manager']\n  }\n},\n// Documentation routes (no authentication required)\n{\n  path: 'doc',\n  loadChildren: () => import('./modules/documentation/documentation.module').then(m => m.DocumentationModule)\n},\n// Test route (no authentication required)\n{\n  path: 'test',\n  loadComponent: () => import('./components/test/test.component').then(m => m.TestComponent)\n},\n// Error pages\n{\n  path: 'unauthorized',\n  loadComponent: () => import('./components/error/unauthorized.component').then(m => m.UnauthorizedComponent)\n}, {\n  path: '**',\n  redirectTo: '/dashboard'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "LoginComponent", "canActivate", "RegisterComponent", "DashboardComponent", "ScanComponent", "ScanDetailsComponent", "ChecklistComponent", "ReportsComponent", "SettingsComponent", "ProjectsComponent", "data", "roles", "loadChildren", "DocumentationModule", "TestComponent", "UnauthorizedComponent"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard, GuestGuard, RoleGuard } from './guards/auth.guard';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    redirectTo: '/dashboard',\n    pathMatch: 'full'\n  },\n  // Authentication routes\n  {\n    path: 'login',\n    loadComponent: () => import('./components/auth/login.component').then(m => m.LoginComponent),\n    canActivate: [GuestGuard]\n  },\n  {\n    path: 'register',\n    loadComponent: () => import('./components/auth/register.component').then(m => m.RegisterComponent),\n    canActivate: [GuestGuard]\n  },\n  // Protected routes (temporarily without AuthGuard for testing)\n  {\n    path: 'dashboard',\n    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent)\n    // canActivate: [AuthGuard] // Temporarily disabled for testing\n  },\n  {\n    path: 'scan',\n    loadComponent: () => import('./components/scan/scan.component').then(m => m.ScanComponent)\n    // canActivate: [AuthGuard] // Temporarily disabled for testing\n  },\n  {\n    path: 'scan/:id',\n    loadComponent: () => import('./components/scan-details/scan-details.component').then(m => m.ScanDetailsComponent)\n    // canActivate: [AuthGuard] // Temporarily disabled for testing\n  },\n  {\n    path: 'checklist',\n    loadComponent: () => import('./components/checklist/checklist.component').then(m => m.ChecklistComponent)\n    // canActivate: [AuthGuard] // Temporarily disabled for testing\n  },\n  {\n    path: 'reports',\n    loadComponent: () => import('./components/reports/reports.component').then(m => m.ReportsComponent)\n    // canActivate: [AuthGuard] // Temporarily disabled for testing\n  },\n  {\n    path: 'settings',\n    loadComponent: () => import('./components/settings/settings.component').then(m => m.SettingsComponent),\n    canActivate: [AuthGuard]\n  },\n  // Projects management (admin only)\n  {\n    path: 'projects',\n    loadComponent: () => import('./components/projects/projects.component').then(m => m.ProjectsComponent),\n    canActivate: [AuthGuard, RoleGuard],\n    data: { roles: ['admin', 'manager'] }\n  },\n  // Documentation routes (no authentication required)\n  {\n    path: 'doc',\n    loadChildren: () => import('./modules/documentation/documentation.module').then(m => m.DocumentationModule)\n  },\n  // Test route (no authentication required)\n  {\n    path: 'test',\n    loadComponent: () => import('./components/test/test.component').then(m => m.TestComponent)\n  },\n  // Error pages\n  {\n    path: 'unauthorized',\n    loadComponent: () => import('./components/error/unauthorized.component').then(m => m.UnauthorizedComponent)\n  },\n  {\n    path: '**',\n    redirectTo: '/dashboard'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,EAAEC,UAAU,EAAEC,SAAS,QAAQ,qBAAqB;AAEtE,OAAO,MAAMC,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ;AACD;AACA;EACEF,IAAI,EAAE,OAAO;EACbG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC;EAC5FC,WAAW,EAAE,CAACV,UAAU;CACzB,EACD;EACEG,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,iBAAiB,CAAC;EAClGD,WAAW,EAAE,CAACV,UAAU;CACzB;AACD;AACA;EACEG,IAAI,EAAE,WAAW;EACjBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,kBAAkB;EACxG;CACD,EACD;EACET,IAAI,EAAE,MAAM;EACZG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa;EACzF;CACD,EACD;EACEV,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,oBAAoB;EAChH;CACD,EACD;EACEX,IAAI,EAAE,WAAW;EACjBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,kBAAkB;EACxG;CACD,EACD;EACEZ,IAAI,EAAE,SAAS;EACfG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,gBAAgB;EAClG;CACD,EACD;EACEb,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,iBAAiB,CAAC;EACtGP,WAAW,EAAE,CAACX,SAAS;CACxB;AACD;AACA;EACEI,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,iBAAiB,CAAC;EACtGR,WAAW,EAAE,CAACX,SAAS,EAAEE,SAAS,CAAC;EACnCkB,IAAI,EAAE;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS;EAAC;CACpC;AACD;AACA;EACEjB,IAAI,EAAE,KAAK;EACXkB,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACd,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,mBAAmB;CAC3G;AACD;AACA;EACEnB,IAAI,EAAE,MAAM;EACZG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACe,aAAa;CAC1F;AACD;AACA;EACEpB,IAAI,EAAE,cAAc;EACpBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACgB,qBAAqB;CAC3G,EACD;EACErB,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}