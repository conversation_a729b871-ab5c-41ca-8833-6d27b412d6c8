{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/theme.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/badge\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@angular/material/list\";\nfunction NavigationComponent_a_10_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 35);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"matBadge\", item_r2.badge);\n  }\n}\nfunction NavigationComponent_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, NavigationComponent_a_10_span_5_Template, 1, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r2.route)(\"matTooltip\", item_r2.tooltip || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.badge);\n  }\n}\nfunction NavigationComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"p\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 40);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notification_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"notification-icon \" + notification_r3.type);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(notification_r3.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notification_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r3.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r3.time);\n  }\n}\nexport class NavigationComponent {\n  constructor(themeService, authService, router) {\n    this.themeService = themeService;\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n    this.navigationItems = [{\n      label: 'Dashboard',\n      route: '/dashboard',\n      icon: 'dashboard',\n      tooltip: 'Security Overview'\n    }, {\n      label: 'Scan',\n      route: '/scan',\n      icon: 'security',\n      tooltip: 'Start New Scan'\n    }, {\n      label: 'Reports',\n      route: '/reports',\n      icon: 'assessment',\n      tooltip: 'Security Reports'\n    }, {\n      label: 'Checklist',\n      route: '/checklist',\n      icon: 'checklist',\n      tooltip: 'Security Checklist'\n    }, {\n      label: 'Projects',\n      route: '/projects',\n      icon: 'folder',\n      tooltip: 'Manage Projects'\n    }];\n    this.notifications = [{\n      type: 'success',\n      icon: 'check_circle',\n      title: 'Scan Completed',\n      message: 'Project scan finished successfully',\n      time: '2 minutes ago'\n    }, {\n      type: 'warning',\n      icon: 'warning',\n      title: 'High Risk Detected',\n      message: '3 critical vulnerabilities found',\n      time: '5 minutes ago'\n    }, {\n      type: 'error',\n      icon: 'error',\n      title: 'Scan Failed',\n      message: 'Unable to access project files',\n      time: '10 minutes ago'\n    }];\n    this.isDark$ = this.themeService.isDark$;\n  }\n  ngOnInit() {\n    // Get current user info\n    this.currentUser = this.authService.getCurrentUser();\n  }\n  toggleTheme() {\n    this.themeService.toggleTheme();\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function NavigationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavigationComponent)(i0.ɵɵdirectiveInject(i1.ThemeService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavigationComponent,\n      selectors: [[\"app-navigation\"]],\n      decls: 69,\n      vars: 13,\n      consts: [[\"notificationsMenu\", \"matMenu\"], [\"userMenu\", \"matMenu\"], [1, \"spt-navbar\"], [1, \"navbar-brand\"], [\"mat-icon-button\", \"\", \"routerLink\", \"/dashboard\", 1, \"brand-button\"], [1, \"brand-icon\"], [\"routerLink\", \"/dashboard\", 1, \"brand-text\"], [1, \"brand-subtitle\"], [1, \"navbar-nav\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", \"class\", \"nav-link\", \"matTooltipPosition\", \"below\", 3, \"routerLink\", \"matTooltip\", 4, \"ngFor\", \"ngForOf\"], [1, \"navbar-spacer\"], [1, \"navbar-actions\"], [\"mat-icon-button\", \"\", 1, \"theme-toggle\", 3, \"click\", \"matTooltip\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Notifications\", 1, \"notifications-button\", 3, \"matMenuTriggerFor\"], [\"matBadge\", \"3\", \"matBadgeColor\", \"warn\", \"matBadgeSize\", \"small\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-avatar\"], [1, \"user-name\"], [1, \"dropdown-icon\"], [1, \"notifications-menu\"], [1, \"menu-header\"], [\"mat-icon-button\", \"\"], [\"class\", \"notification-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"menu-footer\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"user-menu\"], [1, \"user-menu-header\"], [1, \"user-avatar-large\"], [1, \"user-info\"], [1, \"user-email\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/settings\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", \"matTooltipPosition\", \"below\", 1, \"nav-link\", 3, \"routerLink\", \"matTooltip\"], [\"matBadgeColor\", \"warn\", \"matBadgeSize\", \"small\", \"class\", \"nav-badge\", 3, \"matBadge\", 4, \"ngIf\"], [\"matBadgeColor\", \"warn\", \"matBadgeSize\", \"small\", 1, \"nav-badge\", 3, \"matBadge\"], [1, \"notification-item\"], [1, \"notification-content\"], [1, \"notification-title\"], [1, \"notification-message\"], [1, \"notification-time\"]],\n      template: function NavigationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 2)(1, \"div\", 3)(2, \"button\", 4)(3, \"mat-icon\", 5);\n          i0.ɵɵtext(4, \"security\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"span\", 6);\n          i0.ɵɵtext(6, \"SPT\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 7);\n          i0.ɵɵtext(8, \"Security Platform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"nav\", 8);\n          i0.ɵɵtemplate(10, NavigationComponent_a_10_Template, 6, 5, \"a\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"div\", 10);\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"button\", 12);\n          i0.ɵɵpipe(14, \"async\");\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleTheme());\n          });\n          i0.ɵɵelementStart(15, \"mat-icon\");\n          i0.ɵɵtext(16);\n          i0.ɵɵpipe(17, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 13)(19, \"mat-icon\", 14);\n          i0.ɵɵtext(20, \"notifications\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"button\", 15)(22, \"div\", 16)(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"account_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"span\", 17);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-icon\", 18);\n          i0.ɵɵtext(28, \"expand_more\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(29, \"mat-menu\", 19, 0)(31, \"div\", 20)(32, \"h3\");\n          i0.ɵɵtext(33, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 21)(35, \"mat-icon\");\n          i0.ɵɵtext(36, \"settings\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(37, NavigationComponent_div_37_Template, 10, 6, \"div\", 22);\n          i0.ɵɵelementStart(38, \"div\", 23)(39, \"button\", 24);\n          i0.ɵɵtext(40, \"View All\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"mat-menu\", 25, 1)(43, \"div\", 26)(44, \"div\", 27)(45, \"mat-icon\");\n          i0.ɵɵtext(46, \"account_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 28)(48, \"p\", 17);\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p\", 29);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(52, \"mat-divider\");\n          i0.ɵɵelementStart(53, \"button\", 30)(54, \"mat-icon\");\n          i0.ɵɵtext(55, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\");\n          i0.ɵɵtext(57, \"Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"button\", 31)(59, \"mat-icon\");\n          i0.ɵɵtext(60, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"span\");\n          i0.ɵɵtext(62, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(63, \"mat-divider\");\n          i0.ɵɵelementStart(64, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_64_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelementStart(65, \"mat-icon\");\n          i0.ɵɵtext(66, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"span\");\n          i0.ɵɵtext(68, \"Logout\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const notificationsMenu_r4 = i0.ɵɵreference(30);\n          const userMenu_r5 = i0.ɵɵreference(42);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.navigationItems);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", i0.ɵɵpipeBind1(14, 9, ctx.isDark$) ? \"Switch to light mode\" : \"Switch to dark mode\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 11, ctx.isDark$) ? \"light_mode\" : \"dark_mode\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", notificationsMenu_r4);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.username) || \"Admin\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.username) || \"Admin\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.email) || \"<EMAIL>\");\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.AsyncPipe, RouterModule, i3.RouterLink, i3.RouterLinkActive, MatToolbarModule, i5.MatToolbar, MatButtonModule, i6.MatAnchor, i6.MatButton, i6.MatIconButton, MatIconModule, i7.MatIcon, MatMenuModule, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, MatBadgeModule, i9.MatBadge, MatTooltipModule, i10.MatTooltip, MatSlideToggleModule, MatDividerModule, i11.MatDivider],\n      styles: [\".spt-navbar[_ngcontent-%COMP%] {\\n  background: var(--spt-surface);\\n  border-bottom: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n  height: 64px;\\n  padding: 0 var(--spt-space-6);\\n  position: sticky;\\n  top: 0;\\n  z-index: var(--spt-z-sticky);\\n  transition: all 0.2s ease;\\n}\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  margin-right: var(--spt-space-8);\\n}\\n\\n.brand-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: var(--spt-primary-600);\\n  color: white;\\n  border-radius: var(--spt-radius-lg);\\n}\\n\\n.brand-button[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-700);\\n  transform: scale(1.05);\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-text-primary);\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n\\n.brand-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n  font-weight: var(--spt-font-medium);\\n}\\n\\n.navbar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-2);\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  padding: var(--spt-space-2) var(--spt-space-4);\\n  border-radius: var(--spt-radius-lg);\\n  color: var(--spt-text-secondary);\\n  text-decoration: none;\\n  transition: all 0.2s ease;\\n  font-weight: var(--spt-font-medium);\\n  position: relative;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-50);\\n  color: var(--spt-primary-700);\\n  transform: translateY(-1px);\\n}\\n\\n.nav-link.active[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-100);\\n  color: var(--spt-primary-700);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.navbar-spacer[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.navbar-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n}\\n\\n.theme-toggle[_ngcontent-%COMP%], \\n.notifications-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: var(--spt-radius-lg);\\n  color: var(--spt-text-secondary);\\n  transition: all 0.2s ease;\\n}\\n\\n.theme-toggle[_ngcontent-%COMP%]:hover, \\n.notifications-button[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-gray-100);\\n  color: var(--spt-text-primary);\\n  transform: scale(1.05);\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  padding: var(--spt-space-2) var(--spt-space-3);\\n  border-radius: var(--spt-radius-lg);\\n  color: var(--spt-text-primary);\\n  transition: all 0.2s ease;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-gray-100);\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: var(--spt-radius-full);\\n  background: var(--spt-primary-100);\\n  color: var(--spt-primary-600);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  font-weight: var(--spt-font-medium);\\n  font-size: var(--spt-text-sm);\\n}\\n\\n.dropdown-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  color: var(--spt-text-secondary);\\n}\\n\\n\\n\\n.notifications-menu[_ngcontent-%COMP%], \\n.user-menu[_ngcontent-%COMP%] {\\n  border-radius: var(--spt-radius-xl);\\n  box-shadow: var(--spt-shadow-xl);\\n  border: 1px solid var(--spt-border);\\n  overflow: hidden;\\n}\\n\\n.menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spt-space-4);\\n  border-bottom: 1px solid var(--spt-border);\\n  background: var(--spt-bg-secondary);\\n}\\n\\n.menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.notification-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-3);\\n  padding: var(--spt-space-3) var(--spt-space-4);\\n  border-bottom: 1px solid var(--spt-border-light);\\n  transition: background 0.2s ease;\\n}\\n\\n.notification-item[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-bg-secondary);\\n}\\n\\n.notification-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  font-size: 20px;\\n  margin-top: var(--spt-space-1);\\n}\\n\\n.notification-icon.success[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n}\\n\\n.notification-icon.warning[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n}\\n\\n.notification-icon.error[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.notification-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.notification-title[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-1) 0;\\n  font-weight: var(--spt-font-medium);\\n  font-size: var(--spt-text-sm);\\n}\\n\\n.notification-message[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-1) 0;\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.notification-time[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-tertiary);\\n}\\n\\n.user-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-3);\\n  padding: var(--spt-space-4);\\n  background: var(--spt-bg-secondary);\\n}\\n\\n.user-avatar-large[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: var(--spt-radius-full);\\n  background: var(--spt-primary-100);\\n  color: var(--spt-primary-600);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.user-avatar-large[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-1) 0;\\n  font-weight: var(--spt-font-semibold);\\n  font-size: var(--spt-text-base);\\n}\\n\\n.user-info[_ngcontent-%COMP%]   .user-email[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.menu-footer[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-3) var(--spt-space-4);\\n  border-top: 1px solid var(--spt-border);\\n  background: var(--spt-bg-secondary);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .navbar-nav[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .brand-subtitle[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .user-name[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatMenuModule", "MatBadgeModule", "MatTooltipModule", "MatSlideToggleModule", "MatDividerModule", "i0", "ɵɵelement", "ɵɵpropertyInterpolate", "item_r2", "badge", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "NavigationComponent_a_10_span_5_Template", "ɵɵproperty", "route", "tooltip", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "label", "ɵɵclassMap", "notification_r3", "type", "title", "message", "time", "NavigationComponent", "constructor", "themeService", "authService", "router", "currentUser", "navigationItems", "notifications", "isDark$", "ngOnInit", "getCurrentUser", "toggleTheme", "logout", "navigate", "ɵɵdirectiveInject", "i1", "ThemeService", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "NavigationComponent_Template", "rf", "ctx", "NavigationComponent_a_10_Template", "ɵɵlistener", "NavigationComponent_Template_button_click_13_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "NavigationComponent_div_37_Template", "NavigationComponent_Template_button_click_64_listener", "ɵɵpipeBind1", "notificationsMenu_r4", "userMenu_r5", "username", "email", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "AsyncPipe", "RouterLink", "RouterLinkActive", "i5", "MatToolbar", "i6", "<PERSON><PERSON><PERSON><PERSON>", "MatButton", "MatIconButton", "i7", "MatIcon", "i8", "MatMenu", "MatMenuItem", "MatMenuTrigger", "i9", "MatBadge", "i10", "MatTooltip", "i11", "<PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\shared\\navigation\\navigation.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { ThemeService } from '../../services/theme.service';\nimport { AuthService } from '../../services/auth.service';\nimport { Observable } from 'rxjs';\n\ninterface NavigationItem {\n  label: string;\n  route: string;\n  icon: string;\n  badge?: number;\n  tooltip?: string;\n}\n\n@Component({\n  selector: 'app-navigation',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatMenuModule,\n    MatBadgeModule,\n    MatTooltipModule,\n    MatSlideToggleModule,\n    MatDividerModule\n  ],\n  template: `\n    <mat-toolbar class=\"spt-navbar\">\n      <!-- Brand Section -->\n      <div class=\"navbar-brand\">\n        <button mat-icon-button routerLink=\"/dashboard\" class=\"brand-button\">\n          <mat-icon class=\"brand-icon\">security</mat-icon>\n        </button>\n        <span class=\"brand-text\" routerLink=\"/dashboard\">SPT</span>\n        <span class=\"brand-subtitle\">Security Platform</span>\n      </div>\n\n      <!-- Navigation Links -->\n      <nav class=\"navbar-nav\">\n        <a \n          *ngFor=\"let item of navigationItems\" \n          mat-button \n          [routerLink]=\"item.route\"\n          routerLinkActive=\"active\"\n          class=\"nav-link\"\n          [matTooltip]=\"item.tooltip || ''\"\n          matTooltipPosition=\"below\">\n          <mat-icon>{{ item.icon }}</mat-icon>\n          <span>{{ item.label }}</span>\n          <span *ngIf=\"item.badge\" \n                matBadge=\"{{ item.badge }}\" \n                matBadgeColor=\"warn\" \n                matBadgeSize=\"small\"\n                class=\"nav-badge\">\n          </span>\n        </a>\n      </nav>\n\n      <!-- Spacer -->\n      <div class=\"navbar-spacer\"></div>\n\n      <!-- Actions Section -->\n      <div class=\"navbar-actions\">\n        <!-- Theme Toggle -->\n        <button \n          mat-icon-button \n          (click)=\"toggleTheme()\"\n          [matTooltip]=\"(isDark$ | async) ? 'Switch to light mode' : 'Switch to dark mode'\"\n          class=\"theme-toggle\">\n          <mat-icon>{{ (isDark$ | async) ? 'light_mode' : 'dark_mode' }}</mat-icon>\n        </button>\n\n        <!-- Notifications -->\n        <button \n          mat-icon-button \n          [matMenuTriggerFor]=\"notificationsMenu\"\n          matTooltip=\"Notifications\"\n          class=\"notifications-button\">\n          <mat-icon matBadge=\"3\" matBadgeColor=\"warn\" matBadgeSize=\"small\">notifications</mat-icon>\n        </button>\n\n        <!-- User Menu -->\n        <button \n          mat-button \n          [matMenuTriggerFor]=\"userMenu\"\n          class=\"user-menu-button\">\n          <div class=\"user-avatar\">\n            <mat-icon>account_circle</mat-icon>\n          </div>\n          <span class=\"user-name\">{{ currentUser?.username || 'Admin' }}</span>\n          <mat-icon class=\"dropdown-icon\">expand_more</mat-icon>\n        </button>\n      </div>\n    </mat-toolbar>\n\n    <!-- Notifications Menu -->\n    <mat-menu #notificationsMenu=\"matMenu\" class=\"notifications-menu\">\n      <div class=\"menu-header\">\n        <h3>Notifications</h3>\n        <button mat-icon-button>\n          <mat-icon>settings</mat-icon>\n        </button>\n      </div>\n      <div class=\"notification-item\" *ngFor=\"let notification of notifications\">\n        <mat-icon [class]=\"'notification-icon ' + notification.type\">{{ notification.icon }}</mat-icon>\n        <div class=\"notification-content\">\n          <p class=\"notification-title\">{{ notification.title }}</p>\n          <p class=\"notification-message\">{{ notification.message }}</p>\n          <span class=\"notification-time\">{{ notification.time }}</span>\n        </div>\n      </div>\n      <div class=\"menu-footer\">\n        <button mat-button color=\"primary\">View All</button>\n      </div>\n    </mat-menu>\n\n    <!-- User Menu -->\n    <mat-menu #userMenu=\"matMenu\" class=\"user-menu\">\n      <div class=\"user-menu-header\">\n        <div class=\"user-avatar-large\">\n          <mat-icon>account_circle</mat-icon>\n        </div>\n        <div class=\"user-info\">\n          <p class=\"user-name\">{{ currentUser?.username || 'Admin' }}</p>\n          <p class=\"user-email\">{{ currentUser?.email || '<EMAIL>' }}</p>\n        </div>\n      </div>\n      <mat-divider></mat-divider>\n      <button mat-menu-item routerLink=\"/settings\">\n        <mat-icon>settings</mat-icon>\n        <span>Settings</span>\n      </button>\n      <button mat-menu-item routerLink=\"/profile\">\n        <mat-icon>person</mat-icon>\n        <span>Profile</span>\n      </button>\n      <mat-divider></mat-divider>\n      <button mat-menu-item (click)=\"logout()\">\n        <mat-icon>logout</mat-icon>\n        <span>Logout</span>\n      </button>\n    </mat-menu>\n  `,\n  styles: [`\n    .spt-navbar {\n      background: var(--spt-surface);\n      border-bottom: 1px solid var(--spt-border);\n      box-shadow: var(--spt-shadow-sm);\n      height: 64px;\n      padding: 0 var(--spt-space-6);\n      position: sticky;\n      top: 0;\n      z-index: var(--spt-z-sticky);\n      transition: all 0.2s ease;\n    }\n\n    .navbar-brand {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-3);\n      margin-right: var(--spt-space-8);\n    }\n\n    .brand-button {\n      width: 40px;\n      height: 40px;\n      background: var(--spt-primary-600);\n      color: white;\n      border-radius: var(--spt-radius-lg);\n    }\n\n    .brand-button:hover {\n      background: var(--spt-primary-700);\n      transform: scale(1.05);\n    }\n\n    .brand-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .brand-text {\n      font-size: var(--spt-text-xl);\n      font-weight: var(--spt-font-bold);\n      color: var(--spt-text-primary);\n      cursor: pointer;\n      text-decoration: none;\n    }\n\n    .brand-subtitle {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-text-secondary);\n      font-weight: var(--spt-font-medium);\n    }\n\n    .navbar-nav {\n      display: flex;\n      gap: var(--spt-space-2);\n    }\n\n    .nav-link {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n      padding: var(--spt-space-2) var(--spt-space-4);\n      border-radius: var(--spt-radius-lg);\n      color: var(--spt-text-secondary);\n      text-decoration: none;\n      transition: all 0.2s ease;\n      font-weight: var(--spt-font-medium);\n      position: relative;\n    }\n\n    .nav-link:hover {\n      background: var(--spt-primary-50);\n      color: var(--spt-primary-700);\n      transform: translateY(-1px);\n    }\n\n    .nav-link.active {\n      background: var(--spt-primary-100);\n      color: var(--spt-primary-700);\n      font-weight: var(--spt-font-semibold);\n    }\n\n    .navbar-spacer {\n      flex: 1;\n    }\n\n    .navbar-actions {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n    }\n\n    .theme-toggle,\n    .notifications-button {\n      width: 40px;\n      height: 40px;\n      border-radius: var(--spt-radius-lg);\n      color: var(--spt-text-secondary);\n      transition: all 0.2s ease;\n    }\n\n    .theme-toggle:hover,\n    .notifications-button:hover {\n      background: var(--spt-gray-100);\n      color: var(--spt-text-primary);\n      transform: scale(1.05);\n    }\n\n    .user-menu-button {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n      padding: var(--spt-space-2) var(--spt-space-3);\n      border-radius: var(--spt-radius-lg);\n      color: var(--spt-text-primary);\n      transition: all 0.2s ease;\n    }\n\n    .user-menu-button:hover {\n      background: var(--spt-gray-100);\n    }\n\n    .user-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: var(--spt-radius-full);\n      background: var(--spt-primary-100);\n      color: var(--spt-primary-600);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .user-name {\n      font-weight: var(--spt-font-medium);\n      font-size: var(--spt-text-sm);\n    }\n\n    .dropdown-icon {\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n      color: var(--spt-text-secondary);\n    }\n\n    /* Menu Styles */\n    .notifications-menu,\n    .user-menu {\n      border-radius: var(--spt-radius-xl);\n      box-shadow: var(--spt-shadow-xl);\n      border: 1px solid var(--spt-border);\n      overflow: hidden;\n    }\n\n    .menu-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: var(--spt-space-4);\n      border-bottom: 1px solid var(--spt-border);\n      background: var(--spt-bg-secondary);\n    }\n\n    .menu-header h3 {\n      margin: 0;\n      font-size: var(--spt-text-base);\n      font-weight: var(--spt-font-semibold);\n    }\n\n    .notification-item {\n      display: flex;\n      gap: var(--spt-space-3);\n      padding: var(--spt-space-3) var(--spt-space-4);\n      border-bottom: 1px solid var(--spt-border-light);\n      transition: background 0.2s ease;\n    }\n\n    .notification-item:hover {\n      background: var(--spt-bg-secondary);\n    }\n\n    .notification-icon {\n      width: 20px;\n      height: 20px;\n      font-size: 20px;\n      margin-top: var(--spt-space-1);\n    }\n\n    .notification-icon.success { color: var(--spt-success-600); }\n    .notification-icon.warning { color: var(--spt-warning-600); }\n    .notification-icon.error { color: var(--spt-error-600); }\n\n    .notification-content {\n      flex: 1;\n    }\n\n    .notification-title {\n      margin: 0 0 var(--spt-space-1) 0;\n      font-weight: var(--spt-font-medium);\n      font-size: var(--spt-text-sm);\n    }\n\n    .notification-message {\n      margin: 0 0 var(--spt-space-1) 0;\n      font-size: var(--spt-text-xs);\n      color: var(--spt-text-secondary);\n    }\n\n    .notification-time {\n      font-size: var(--spt-text-xs);\n      color: var(--spt-text-tertiary);\n    }\n\n    .user-menu-header {\n      display: flex;\n      gap: var(--spt-space-3);\n      padding: var(--spt-space-4);\n      background: var(--spt-bg-secondary);\n    }\n\n    .user-avatar-large {\n      width: 48px;\n      height: 48px;\n      border-radius: var(--spt-radius-full);\n      background: var(--spt-primary-100);\n      color: var(--spt-primary-600);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .user-avatar-large mat-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n    }\n\n    .user-info .user-name {\n      margin: 0 0 var(--spt-space-1) 0;\n      font-weight: var(--spt-font-semibold);\n      font-size: var(--spt-text-base);\n    }\n\n    .user-info .user-email {\n      margin: 0;\n      font-size: var(--spt-text-sm);\n      color: var(--spt-text-secondary);\n    }\n\n    .menu-footer {\n      padding: var(--spt-space-3) var(--spt-space-4);\n      border-top: 1px solid var(--spt-border);\n      background: var(--spt-bg-secondary);\n    }\n\n    /* Responsive Design */\n    @media (max-width: 768px) {\n      .navbar-nav {\n        display: none;\n      }\n      \n      .brand-subtitle {\n        display: none;\n      }\n      \n      .user-name {\n        display: none;\n      }\n    }\n  `]\n})\nexport class NavigationComponent implements OnInit {\n  isDark$: Observable<boolean>;\n  currentUser: any = null;\n\n  navigationItems: NavigationItem[] = [\n    { label: 'Dashboard', route: '/dashboard', icon: 'dashboard', tooltip: 'Security Overview' },\n    { label: 'Scan', route: '/scan', icon: 'security', tooltip: 'Start New Scan' },\n    { label: 'Reports', route: '/reports', icon: 'assessment', tooltip: 'Security Reports' },\n    { label: 'Checklist', route: '/checklist', icon: 'checklist', tooltip: 'Security Checklist' },\n    { label: 'Projects', route: '/projects', icon: 'folder', tooltip: 'Manage Projects' }\n  ];\n\n  notifications = [\n    {\n      type: 'success',\n      icon: 'check_circle',\n      title: 'Scan Completed',\n      message: 'Project scan finished successfully',\n      time: '2 minutes ago'\n    },\n    {\n      type: 'warning',\n      icon: 'warning',\n      title: 'High Risk Detected',\n      message: '3 critical vulnerabilities found',\n      time: '5 minutes ago'\n    },\n    {\n      type: 'error',\n      icon: 'error',\n      title: 'Scan Failed',\n      message: 'Unable to access project files',\n      time: '10 minutes ago'\n    }\n  ];\n\n  constructor(\n    private themeService: ThemeService,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.isDark$ = this.themeService.isDark$;\n  }\n\n  ngOnInit(): void {\n    // Get current user info\n    this.currentUser = this.authService.getCurrentUser();\n  }\n\n  toggleTheme(): void {\n    this.themeService.toggleTheme();\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;;;;;;IAmDlDC,EAAA,CAAAC,SAAA,eAKO;;;;IAJDD,EAAA,CAAAE,qBAAA,aAAAC,OAAA,CAAAC,KAAA,CAA2B;;;;;IAHjCJ,EARF,CAAAK,cAAA,YAO6B,eACjB;IAAAL,EAAA,CAAAM,MAAA,GAAe;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACpCP,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,GAAgB;IAAAN,EAAA,CAAAO,YAAA,EAAO;IAC7BP,EAAA,CAAAQ,UAAA,IAAAC,wCAAA,mBAIwB;IAE1BT,EAAA,CAAAO,YAAA,EAAI;;;;IAVFP,EAHA,CAAAU,UAAA,eAAAP,OAAA,CAAAQ,KAAA,CAAyB,eAAAR,OAAA,CAAAS,OAAA,OAGQ;IAEvBZ,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAc,iBAAA,CAAAX,OAAA,CAAAY,IAAA,CAAe;IACnBf,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,iBAAA,CAAAX,OAAA,CAAAa,KAAA,CAAgB;IACfhB,EAAA,CAAAa,SAAA,EAAgB;IAAhBb,EAAA,CAAAU,UAAA,SAAAP,OAAA,CAAAC,KAAA,CAAgB;;;;;IAuDzBJ,EADF,CAAAK,cAAA,cAA0E,eACX;IAAAL,EAAA,CAAAM,MAAA,GAAuB;IAAAN,EAAA,CAAAO,YAAA,EAAW;IAE7FP,EADF,CAAAK,cAAA,cAAkC,YACF;IAAAL,EAAA,CAAAM,MAAA,GAAwB;IAAAN,EAAA,CAAAO,YAAA,EAAI;IAC1DP,EAAA,CAAAK,cAAA,YAAgC;IAAAL,EAAA,CAAAM,MAAA,GAA0B;IAAAN,EAAA,CAAAO,YAAA,EAAI;IAC9DP,EAAA,CAAAK,cAAA,eAAgC;IAAAL,EAAA,CAAAM,MAAA,GAAuB;IAE3DN,EAF2D,CAAAO,YAAA,EAAO,EAC1D,EACF;;;;IANMP,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAiB,UAAA,wBAAAC,eAAA,CAAAC,IAAA,CAAkD;IAACnB,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAc,iBAAA,CAAAI,eAAA,CAAAH,IAAA,CAAuB;IAEpDf,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,iBAAA,CAAAI,eAAA,CAAAE,KAAA,CAAwB;IACtBpB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,iBAAA,CAAAI,eAAA,CAAAG,OAAA,CAA0B;IAC1BrB,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,iBAAA,CAAAI,eAAA,CAAAI,IAAA,CAAuB;;;AAmTjE,OAAM,MAAOC,mBAAmB;EAoC9BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IArChB,KAAAC,WAAW,GAAQ,IAAI;IAEvB,KAAAC,eAAe,GAAqB,CAClC;MAAEb,KAAK,EAAE,WAAW;MAAEL,KAAK,EAAE,YAAY;MAAEI,IAAI,EAAE,WAAW;MAAEH,OAAO,EAAE;IAAmB,CAAE,EAC5F;MAAEI,KAAK,EAAE,MAAM;MAAEL,KAAK,EAAE,OAAO;MAAEI,IAAI,EAAE,UAAU;MAAEH,OAAO,EAAE;IAAgB,CAAE,EAC9E;MAAEI,KAAK,EAAE,SAAS;MAAEL,KAAK,EAAE,UAAU;MAAEI,IAAI,EAAE,YAAY;MAAEH,OAAO,EAAE;IAAkB,CAAE,EACxF;MAAEI,KAAK,EAAE,WAAW;MAAEL,KAAK,EAAE,YAAY;MAAEI,IAAI,EAAE,WAAW;MAAEH,OAAO,EAAE;IAAoB,CAAE,EAC7F;MAAEI,KAAK,EAAE,UAAU;MAAEL,KAAK,EAAE,WAAW;MAAEI,IAAI,EAAE,QAAQ;MAAEH,OAAO,EAAE;IAAiB,CAAE,CACtF;IAED,KAAAkB,aAAa,GAAG,CACd;MACEX,IAAI,EAAE,SAAS;MACfJ,IAAI,EAAE,cAAc;MACpBK,KAAK,EAAE,gBAAgB;MACvBC,OAAO,EAAE,oCAAoC;MAC7CC,IAAI,EAAE;KACP,EACD;MACEH,IAAI,EAAE,SAAS;MACfJ,IAAI,EAAE,SAAS;MACfK,KAAK,EAAE,oBAAoB;MAC3BC,OAAO,EAAE,kCAAkC;MAC3CC,IAAI,EAAE;KACP,EACD;MACEH,IAAI,EAAE,OAAO;MACbJ,IAAI,EAAE,OAAO;MACbK,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE,gCAAgC;MACzCC,IAAI,EAAE;KACP,CACF;IAOC,IAAI,CAACS,OAAO,GAAG,IAAI,CAACN,YAAY,CAACM,OAAO;EAC1C;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,WAAW,GAAG,IAAI,CAACF,WAAW,CAACO,cAAc,EAAE;EACtD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACS,WAAW,EAAE;EACjC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACT,WAAW,CAACS,MAAM,EAAE;IACzB,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uCAxDWb,mBAAmB,EAAAvB,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAvC,EAAA,CAAAqC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzC,EAAA,CAAAqC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAnBpB,mBAAmB;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UAhYtBlD,EAJN,CAAAK,cAAA,qBAAgC,aAEJ,gBAC6C,kBACtC;UAAAL,EAAA,CAAAM,MAAA,eAAQ;UACvCN,EADuC,CAAAO,YAAA,EAAW,EACzC;UACTP,EAAA,CAAAK,cAAA,cAAiD;UAAAL,EAAA,CAAAM,MAAA,UAAG;UAAAN,EAAA,CAAAO,YAAA,EAAO;UAC3DP,EAAA,CAAAK,cAAA,cAA6B;UAAAL,EAAA,CAAAM,MAAA,wBAAiB;UAChDN,EADgD,CAAAO,YAAA,EAAO,EACjD;UAGNP,EAAA,CAAAK,cAAA,aAAwB;UACtBL,EAAA,CAAAQ,UAAA,KAAA4C,iCAAA,eAO6B;UAU/BpD,EAAA,CAAAO,YAAA,EAAM;UAGNP,EAAA,CAAAC,SAAA,eAAiC;UAK/BD,EAFF,CAAAK,cAAA,eAA4B,kBAMH;;UAFrBL,EAAA,CAAAqD,UAAA,mBAAAC,sDAAA;YAAAtD,EAAA,CAAAuD,aAAA,CAAAC,GAAA;YAAA,OAAAxD,EAAA,CAAAyD,WAAA,CAASN,GAAA,CAAAjB,WAAA,EAAa;UAAA,EAAC;UAGvBlC,EAAA,CAAAK,cAAA,gBAAU;UAAAL,EAAA,CAAAM,MAAA,IAAoD;;UAChEN,EADgE,CAAAO,YAAA,EAAW,EAClE;UAQPP,EALF,CAAAK,cAAA,kBAI+B,oBACoC;UAAAL,EAAA,CAAAM,MAAA,qBAAa;UAChFN,EADgF,CAAAO,YAAA,EAAW,EAClF;UAQLP,EALJ,CAAAK,cAAA,kBAG2B,eACA,gBACb;UAAAL,EAAA,CAAAM,MAAA,sBAAc;UAC1BN,EAD0B,CAAAO,YAAA,EAAW,EAC/B;UACNP,EAAA,CAAAK,cAAA,gBAAwB;UAAAL,EAAA,CAAAM,MAAA,IAAsC;UAAAN,EAAA,CAAAO,YAAA,EAAO;UACrEP,EAAA,CAAAK,cAAA,oBAAgC;UAAAL,EAAA,CAAAM,MAAA,mBAAW;UAGjDN,EAHiD,CAAAO,YAAA,EAAW,EAC/C,EACL,EACM;UAKVP,EAFJ,CAAAK,cAAA,uBAAkE,eACvC,UACnB;UAAAL,EAAA,CAAAM,MAAA,qBAAa;UAAAN,EAAA,CAAAO,YAAA,EAAK;UAEpBP,EADF,CAAAK,cAAA,kBAAwB,gBACZ;UAAAL,EAAA,CAAAM,MAAA,gBAAQ;UAEtBN,EAFsB,CAAAO,YAAA,EAAW,EACtB,EACL;UACNP,EAAA,CAAAQ,UAAA,KAAAkD,mCAAA,mBAA0E;UASxE1D,EADF,CAAAK,cAAA,eAAyB,kBACY;UAAAL,EAAA,CAAAM,MAAA,gBAAQ;UAE/CN,EAF+C,CAAAO,YAAA,EAAS,EAChD,EACG;UAMLP,EAHN,CAAAK,cAAA,uBAAgD,eAChB,eACG,gBACnB;UAAAL,EAAA,CAAAM,MAAA,sBAAc;UAC1BN,EAD0B,CAAAO,YAAA,EAAW,EAC/B;UAEJP,EADF,CAAAK,cAAA,eAAuB,aACA;UAAAL,EAAA,CAAAM,MAAA,IAAsC;UAAAN,EAAA,CAAAO,YAAA,EAAI;UAC/DP,EAAA,CAAAK,cAAA,aAAsB;UAAAL,EAAA,CAAAM,MAAA,IAA2C;UAErEN,EAFqE,CAAAO,YAAA,EAAI,EACjE,EACF;UACNP,EAAA,CAAAC,SAAA,mBAA2B;UAEzBD,EADF,CAAAK,cAAA,kBAA6C,gBACjC;UAAAL,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAO,YAAA,EAAW;UAC7BP,EAAA,CAAAK,cAAA,YAAM;UAAAL,EAAA,CAAAM,MAAA,gBAAQ;UAChBN,EADgB,CAAAO,YAAA,EAAO,EACd;UAEPP,EADF,CAAAK,cAAA,kBAA4C,gBAChC;UAAAL,EAAA,CAAAM,MAAA,cAAM;UAAAN,EAAA,CAAAO,YAAA,EAAW;UAC3BP,EAAA,CAAAK,cAAA,YAAM;UAAAL,EAAA,CAAAM,MAAA,eAAO;UACfN,EADe,CAAAO,YAAA,EAAO,EACb;UACTP,EAAA,CAAAC,SAAA,mBAA2B;UAC3BD,EAAA,CAAAK,cAAA,kBAAyC;UAAnBL,EAAA,CAAAqD,UAAA,mBAAAM,sDAAA;YAAA3D,EAAA,CAAAuD,aAAA,CAAAC,GAAA;YAAA,OAAAxD,EAAA,CAAAyD,WAAA,CAASN,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UACtCnC,EAAA,CAAAK,cAAA,gBAAU;UAAAL,EAAA,CAAAM,MAAA,cAAM;UAAAN,EAAA,CAAAO,YAAA,EAAW;UAC3BP,EAAA,CAAAK,cAAA,YAAM;UAAAL,EAAA,CAAAM,MAAA,cAAM;UAEhBN,EAFgB,CAAAO,YAAA,EAAO,EACZ,EACA;;;;;UArGYP,EAAA,CAAAa,SAAA,IAAkB;UAAlBb,EAAA,CAAAU,UAAA,YAAAyC,GAAA,CAAAtB,eAAA,CAAkB;UA2BnC7B,EAAA,CAAAa,SAAA,GAAiF;UAAjFb,EAAA,CAAAU,UAAA,eAAAV,EAAA,CAAA4D,WAAA,QAAAT,GAAA,CAAApB,OAAA,mDAAiF;UAEvE/B,EAAA,CAAAa,SAAA,GAAoD;UAApDb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAA4D,WAAA,SAAAT,GAAA,CAAApB,OAAA,+BAAoD;UAM9D/B,EAAA,CAAAa,SAAA,GAAuC;UAAvCb,EAAA,CAAAU,UAAA,sBAAAmD,oBAAA,CAAuC;UASvC7D,EAAA,CAAAa,SAAA,GAA8B;UAA9Bb,EAAA,CAAAU,UAAA,sBAAAoD,WAAA,CAA8B;UAKN9D,EAAA,CAAAa,SAAA,GAAsC;UAAtCb,EAAA,CAAAc,iBAAA,EAAAqC,GAAA,CAAAvB,WAAA,kBAAAuB,GAAA,CAAAvB,WAAA,CAAAmC,QAAA,aAAsC;UAcV/D,EAAA,CAAAa,SAAA,IAAgB;UAAhBb,EAAA,CAAAU,UAAA,YAAAyC,GAAA,CAAArB,aAAA,CAAgB;UAoB/C9B,EAAA,CAAAa,SAAA,IAAsC;UAAtCb,EAAA,CAAAc,iBAAA,EAAAqC,GAAA,CAAAvB,WAAA,kBAAAuB,GAAA,CAAAvB,WAAA,CAAAmC,QAAA,aAAsC;UACrC/D,EAAA,CAAAa,SAAA,GAA2C;UAA3Cb,EAAA,CAAAc,iBAAA,EAAAqC,GAAA,CAAAvB,WAAA,kBAAAuB,GAAA,CAAAvB,WAAA,CAAAoC,KAAA,qBAA2C;;;qBA7GvE1E,YAAY,EAAA2E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,SAAA,EACZ7E,YAAY,EAAAmD,EAAA,CAAA2B,UAAA,EAAA3B,EAAA,CAAA4B,gBAAA,EACZ9E,gBAAgB,EAAA+E,EAAA,CAAAC,UAAA,EAChB/E,eAAe,EAAAgF,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EAAAF,EAAA,CAAAG,aAAA,EACflF,aAAa,EAAAmF,EAAA,CAAAC,OAAA,EACbnF,aAAa,EAAAoF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,cAAA,EACbtF,cAAc,EAAAuF,EAAA,CAAAC,QAAA,EACdvF,gBAAgB,EAAAwF,GAAA,CAAAC,UAAA,EAChBxF,oBAAoB,EACpBC,gBAAgB,EAAAwF,GAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}