{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nexport let TestComponent = /*#__PURE__*/(() => {\n  class TestComponent {\n    constructor(router, authService) {\n      this.router = router;\n      this.authService = authService;\n      this.currentUrl = '';\n      this.isAuthenticated = false;\n      this.currentUser = null;\n      this.currentUrl = this.router.url;\n      this.isAuthenticated = this.authService.isAuthenticated();\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        this.isAuthenticated = this.authService.isAuthenticated();\n      });\n    }\n    testLogin() {\n      console.log('Testing mock login...');\n      this.authService.mockLogin('admin', 'admin');\n      setTimeout(() => {\n        this.isAuthenticated = this.authService.isAuthenticated();\n        this.authService.currentUser$.subscribe(user => {\n          this.currentUser = user;\n        });\n      }, 100);\n    }\n    testLogout() {\n      console.log('Testing logout...');\n      this.authService.logout();\n      setTimeout(() => {\n        this.isAuthenticated = this.authService.isAuthenticated();\n        this.currentUser = null;\n      }, 100);\n    }\n    navigateTo(path) {\n      console.log('Navigating to:', path);\n      this.router.navigate([path]);\n    }\n    static {\n      this.ɵfac = function TestComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TestComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TestComponent,\n        selectors: [[\"app-test\"]],\n        decls: 29,\n        vars: 5,\n        consts: [[2, \"padding\", \"20px\"], [2, \"margin-top\", \"20px\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", 2, \"margin-left\", \"10px\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"]],\n        template: function TestComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4, \"Navigation Test Component\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"p\");\n            i0.ɵɵtext(7, \"This is a test component to verify routing is working.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\");\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"p\");\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"p\");\n            i0.ɵɵtext(13);\n            i0.ɵɵpipe(14, \"json\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 1)(16, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function TestComponent_Template_button_click_16_listener() {\n              return ctx.testLogin();\n            });\n            i0.ɵɵtext(17, \" Test Mock Login \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function TestComponent_Template_button_click_18_listener() {\n              return ctx.testLogout();\n            });\n            i0.ɵɵtext(19, \" Test Logout \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 1)(21, \"h3\");\n            i0.ɵɵtext(22, \"Test Navigation:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function TestComponent_Template_button_click_23_listener() {\n              return ctx.navigateTo(\"/dashboard\");\n            });\n            i0.ɵɵtext(24, \"Dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function TestComponent_Template_button_click_25_listener() {\n              return ctx.navigateTo(\"/scan\");\n            });\n            i0.ɵɵtext(26, \"Scan\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function TestComponent_Template_button_click_27_listener() {\n              return ctx.navigateTo(\"/reports\");\n            });\n            i0.ɵɵtext(28, \"Reports\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\"Current URL: \", ctx.currentUrl, \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"User authenticated: \", ctx.isAuthenticated, \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"Current user: \", i0.ɵɵpipeBind1(14, 3, ctx.currentUser), \"\");\n          }\n        },\n        dependencies: [CommonModule, i3.JsonPipe, MatCardModule, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardTitle, MatButtonModule, i5.MatButton],\n        encapsulation: 2\n      });\n    }\n  }\n  return TestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}