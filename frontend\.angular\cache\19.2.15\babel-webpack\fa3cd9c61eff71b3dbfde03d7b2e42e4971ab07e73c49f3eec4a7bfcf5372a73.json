{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { Chart, registerables } from 'chart.js';\nimport { Subject, interval, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/progress-bar\";\nconst _c0 = [\"progressCanvas\"];\nfunction ScanProgressChartComponent_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ScanProgressChartComponent_div_0_div_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.pauseScan());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"pause\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ScanProgressChartComponent_div_0_div_16_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.cancelScan());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"stop\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getEstimatedTimeRemaining(), \" remaining \");\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"h4\");\n    i0.ɵɵtext(3, \"Real-time Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 38)(5, \"div\", 39);\n    i0.ɵɵelement(6, \"div\", 40);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Progress\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵelement(10, \"div\", 41);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Issues Found\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 42);\n    i0.ɵɵelement(14, \"canvas\", 43, 0);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(14);\n    i0.ɵɵattribute(\"aria-label\", \"Real-time progress chart for \" + ctx_r1.scanProgress.projectName);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 57);\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 58);\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_progress_bar_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-progress-bar\", 59);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵtemplate(2, ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_2_Template, 2, 0, \"mat-icon\", 49)(3, ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_3_Template, 2, 0, \"mat-icon\", 50)(4, ScanProgressChartComponent_div_0_div_80_div_4_span_4_Template, 2, 1, \"span\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 51)(6, \"div\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 53);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 54);\n    i0.ɵɵtemplate(11, ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_11_Template, 2, 0, \"mat-icon\", 55)(12, ScanProgressChartComponent_div_0_div_80_div_4_mat_progress_bar_12_Template, 1, 0, \"mat-progress-bar\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"completed\", i_r3 < ctx_r1.scanProgress.completedSteps)(\"current\", i_r3 === ctx_r1.scanProgress.completedSteps)(\"pending\", i_r3 > ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r3 < ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r3 === ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r3 > ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r3 < ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r3 === ctx_r1.scanProgress.completedSteps);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"h4\");\n    i0.ɵɵtext(2, \"Scan Steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtemplate(4, ScanProgressChartComponent_div_0_div_80_div_4_Template, 13, 13, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getScanSteps());\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_81_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 70)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" View Report \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_81_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 71)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Retry Scan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 63)(8, \"div\", 64)(9, \"span\", 65);\n    i0.ɵɵtext(10, \"Total Files Scanned:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 66);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 64)(14, \"span\", 65);\n    i0.ɵɵtext(15, \"Issues Found:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 66);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 64)(19, \"span\", 65);\n    i0.ɵɵtext(20, \"Total Time:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 66);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 67);\n    i0.ɵɵtemplate(24, ScanProgressChartComponent_div_0_div_81_button_24_Template, 4, 0, \"button\", 68)(25, ScanProgressChartComponent_div_0_div_81_button_25_Template, 4, 0, \"button\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusIcon(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Scan \", i0.ɵɵpipeBind1(6, 9, ctx_r1.scanProgress.status), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.filesScanned);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.issuesFound);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getElapsedTime());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"completed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"failed\");\n  }\n}\nfunction ScanProgressChartComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"mat-icon\", 7);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 8);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"span\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 11);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, ScanProgressChartComponent_div_0_div_16_Template, 7, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 13)(18, \"div\", 14)(19, \"span\", 15);\n    i0.ɵɵtext(20, \"Overall Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 16);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(24, \"mat-progress-bar\", 17);\n    i0.ɵɵelementStart(25, \"div\", 18)(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 19)(32, \"div\", 20)(33, \"div\", 21)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"description\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 22)(37, \"div\", 23);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 24);\n    i0.ɵɵtext(41, \"Files Scanned\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 25);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 26)(45, \"div\", 21)(46, \"mat-icon\");\n    i0.ɵɵtext(47, \"bug_report\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 22)(49, \"div\", 23);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 24);\n    i0.ɵɵtext(53, \"Issues Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 25);\n    i0.ɵɵtext(55);\n    i0.ɵɵpipe(56, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 27)(58, \"div\", 21)(59, \"mat-icon\");\n    i0.ɵɵtext(60, \"schedule\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 22)(62, \"div\", 23);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 24);\n    i0.ɵɵtext(65, \"Elapsed Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(66, ScanProgressChartComponent_div_0_div_66_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 29)(68, \"div\", 21)(69, \"mat-icon\");\n    i0.ɵɵtext(70, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 22)(72, \"div\", 23);\n    i0.ɵɵtext(73);\n    i0.ɵɵpipe(74, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 24);\n    i0.ɵɵtext(76, \"Files/Min\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 25);\n    i0.ɵɵtext(78, \"Scan Speed\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, ScanProgressChartComponent_div_0_div_79_Template, 16, 1, \"div\", 30)(80, ScanProgressChartComponent_div_0_div_80_Template, 5, 1, \"div\", 31)(81, ScanProgressChartComponent_div_0_div_81_Template, 26, 11, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusIcon(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.projectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", ctx_r1.scanProgress.scanId, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + ctx_r1.scanProgress.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 26, ctx_r1.scanProgress.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.currentStep);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"running\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(23, 28, ctx_r1.animatedProgress, \"1.1-1\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.animatedProgress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"Step \", ctx_r1.scanProgress.completedSteps, \" of \", ctx_r1.scanProgress.totalSteps, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(30, 31, ctx_r1.animatedFilesScanned, \"1.0-0\"), \" / \", ctx_r1.scanProgress.totalFiles, \" files\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(39, 34, ctx_r1.animatedFilesScanned, \"1.0-0\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"of \", ctx_r1.scanProgress.totalFiles, \" total\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(51, 37, ctx_r1.animatedIssuesFound, \"1.0-0\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(56, 40, ctx_r1.animatedIssuesFound / ctx_r1.Math.max(ctx_r1.animatedFilesScanned, 1) * 100, \"1.1-1\"), \"% of files\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getElapsedTime());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"running\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(74, 43, ctx_r1.animatedFilesScanned / ctx_r1.Math.max(ctx_r1.getElapsedTimeInMinutes(), 1), \"1.1-1\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showRealTimeUpdates && ctx_r1.progressHistory.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"running\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"completed\" || ctx_r1.scanProgress.status === \"failed\");\n  }\n}\nfunction ScanProgressChartComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"mat-icon\", 73);\n    i0.ɵɵtext(2, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Active Scan\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start a new security scan to see real-time progress here.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 70)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Start New Scan \");\n    i0.ɵɵelementEnd()();\n  }\n}\n// Register Chart.js components\nChart.register(...registerables);\nexport class ScanProgressChartComponent {\n  constructor() {\n    this.scanProgress = null;\n    this.showRealTimeUpdates = true;\n    this.updateInterval = 2000; // milliseconds\n    this.destroy$ = new Subject();\n    this.chart = null;\n    this.progressHistory = [];\n    // Make Math available in template\n    this.Math = Math;\n    // Animation properties\n    this.animatedProgress = 0;\n    this.animatedFilesScanned = 0;\n    this.animatedIssuesFound = 0;\n  }\n  ngOnInit() {\n    if (this.showRealTimeUpdates && this.scanProgress?.status === 'running') {\n      this.startRealTimeUpdates();\n    }\n  }\n  ngAfterViewInit() {\n    this.createProgressChart();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  }\n  startRealTimeUpdates() {\n    interval(this.updateInterval).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      if (this.scanProgress?.status === 'running') {\n        this.updateProgressData();\n        this.animateCounters();\n      }\n    });\n  }\n  updateProgressData() {\n    if (!this.scanProgress) return;\n    const dataPoint = {\n      timestamp: new Date(),\n      progress: this.scanProgress.progress,\n      filesScanned: this.scanProgress.filesScanned,\n      issuesFound: this.scanProgress.issuesFound\n    };\n    this.progressHistory.push(dataPoint);\n    // Keep only last 50 data points for performance\n    if (this.progressHistory.length > 50) {\n      this.progressHistory.shift();\n    }\n    this.updateChart();\n  }\n  animateCounters() {\n    if (!this.scanProgress) return;\n    // Animate progress\n    const progressDiff = this.scanProgress.progress - this.animatedProgress;\n    this.animatedProgress += progressDiff * 0.1;\n    // Animate files scanned\n    const filesDiff = this.scanProgress.filesScanned - this.animatedFilesScanned;\n    this.animatedFilesScanned += filesDiff * 0.1;\n    // Animate issues found\n    const issuesDiff = this.scanProgress.issuesFound - this.animatedIssuesFound;\n    this.animatedIssuesFound += issuesDiff * 0.1;\n  }\n  createProgressChart() {\n    if (!this.progressCanvas?.nativeElement) {\n      return;\n    }\n    const ctx = this.progressCanvas.nativeElement.getContext('2d');\n    if (!ctx) {\n      return;\n    }\n    // Destroy existing chart\n    if (this.chart) {\n      this.chart.destroy();\n    }\n    const config = {\n      type: 'line',\n      data: {\n        labels: [],\n        datasets: [{\n          label: 'Progress %',\n          data: [],\n          borderColor: '#3b82f6',\n          backgroundColor: 'rgba(59, 130, 246, 0.1)',\n          borderWidth: 2,\n          fill: true,\n          tension: 0.4,\n          yAxisID: 'y'\n        }, {\n          label: 'Issues Found',\n          data: [],\n          borderColor: '#ef4444',\n          backgroundColor: 'rgba(239, 68, 68, 0.1)',\n          borderWidth: 2,\n          fill: false,\n          tension: 0.4,\n          yAxisID: 'y1'\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          mode: 'index',\n          intersect: false\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              usePointStyle: true,\n              font: {\n                family: 'Inter, sans-serif',\n                size: 12,\n                weight: 500\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8,\n            callbacks: {\n              title: context => {\n                const timestamp = new Date(context[0].label);\n                return timestamp.toLocaleTimeString();\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Time',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            ticks: {\n              callback: function (value, index) {\n                const timestamp = this.getLabelForValue(value);\n                return new Date(timestamp).toLocaleTimeString([], {\n                  hour: '2-digit',\n                  minute: '2-digit'\n                });\n              },\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          },\n          y: {\n            type: 'linear',\n            display: true,\n            position: 'left',\n            title: {\n              display: true,\n              text: 'Progress (%)',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            min: 0,\n            max: 100,\n            ticks: {\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          },\n          y1: {\n            type: 'linear',\n            display: true,\n            position: 'right',\n            title: {\n              display: true,\n              text: 'Issues Found',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            min: 0,\n            grid: {\n              drawOnChartArea: false\n            },\n            ticks: {\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          }\n        },\n        animation: {\n          duration: 750,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n    this.chart = new Chart(ctx, config);\n    this.updateChart();\n  }\n  updateChart() {\n    if (!this.chart || this.progressHistory.length === 0) {\n      return;\n    }\n    const labels = this.progressHistory.map(point => point.timestamp.toISOString());\n    const progressData = this.progressHistory.map(point => point.progress);\n    const issuesData = this.progressHistory.map(point => point.issuesFound);\n    this.chart.data.labels = labels;\n    this.chart.data.datasets[0].data = progressData;\n    this.chart.data.datasets[1].data = issuesData;\n    this.chart.update('none');\n  }\n  getStatusIcon() {\n    if (!this.scanProgress) return 'help';\n    switch (this.scanProgress.status) {\n      case 'pending':\n        return 'schedule';\n      case 'running':\n        return 'hourglass_empty';\n      case 'completed':\n        return 'check_circle';\n      case 'failed':\n        return 'error';\n      default:\n        return 'help';\n    }\n  }\n  getStatusColor() {\n    if (!this.scanProgress) return 'var(--spt-text-secondary)';\n    switch (this.scanProgress.status) {\n      case 'pending':\n        return 'var(--spt-warning-600)';\n      case 'running':\n        return 'var(--spt-info-600)';\n      case 'completed':\n        return 'var(--spt-success-600)';\n      case 'failed':\n        return 'var(--spt-error-600)';\n      default:\n        return 'var(--spt-text-secondary)';\n    }\n  }\n  getEstimatedTimeRemaining() {\n    if (!this.scanProgress || !this.scanProgress.estimatedCompletion) {\n      return 'Calculating...';\n    }\n    const now = new Date();\n    const remaining = this.scanProgress.estimatedCompletion.getTime() - now.getTime();\n    if (remaining <= 0) {\n      return 'Almost done';\n    }\n    const minutes = Math.floor(remaining / 60000);\n    const seconds = Math.floor(remaining % 60000 / 1000);\n    if (minutes > 0) {\n      return `${minutes}m ${seconds}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  }\n  getElapsedTime() {\n    if (!this.scanProgress) return '0s';\n    const now = new Date();\n    const elapsed = now.getTime() - this.scanProgress.startTime.getTime();\n    const minutes = Math.floor(elapsed / 60000);\n    const seconds = Math.floor(elapsed % 60000 / 1000);\n    if (minutes > 0) {\n      return `${minutes}m ${seconds}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  }\n  cancelScan() {\n    // Emit cancel event or call service\n    console.log('Cancel scan requested for:', this.scanProgress?.scanId);\n  }\n  pauseScan() {\n    // Emit pause event or call service\n    console.log('Pause scan requested for:', this.scanProgress?.scanId);\n  }\n  getElapsedTimeInMinutes() {\n    if (!this.scanProgress) return 0;\n    const now = new Date();\n    const elapsed = now.getTime() - this.scanProgress.startTime.getTime();\n    return elapsed / 60000; // Convert to minutes\n  }\n  getScanSteps() {\n    return [{\n      name: 'Initialize',\n      description: 'Setting up scan environment'\n    }, {\n      name: 'File Discovery',\n      description: 'Finding smart contract files'\n    }, {\n      name: 'Static Analysis',\n      description: 'Analyzing code structure'\n    }, {\n      name: 'Security Checks',\n      description: 'Running security rules'\n    }, {\n      name: 'Vulnerability Detection',\n      description: 'Identifying security issues'\n    }, {\n      name: 'Report Generation',\n      description: 'Compiling results'\n    }];\n  }\n  static {\n    this.ɵfac = function ScanProgressChartComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScanProgressChartComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ScanProgressChartComponent,\n      selectors: [[\"app-scan-progress-chart\"]],\n      viewQuery: function ScanProgressChartComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progressCanvas = _t.first);\n        }\n      },\n      inputs: {\n        scanProgress: \"scanProgress\",\n        showRealTimeUpdates: \"showRealTimeUpdates\",\n        updateInterval: \"updateInterval\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"progressCanvas\", \"\"], [\"class\", \"progress-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"progress-container\"], [1, \"progress-header\"], [1, \"scan-info\"], [1, \"scan-title\"], [1, \"status-icon\"], [1, \"scan-id\"], [1, \"scan-status\"], [1, \"status-text\"], [1, \"current-step\"], [\"class\", \"progress-actions\", 4, \"ngIf\"], [1, \"main-progress\"], [1, \"progress-info\"], [1, \"progress-label\"], [1, \"progress-percentage\"], [\"mode\", \"determinate\", 1, \"main-progress-bar\", 3, \"value\"], [1, \"progress-details\"], [1, \"progress-stats\"], [1, \"stat-card\", \"files\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"stat-detail\"], [1, \"stat-card\", \"issues\"], [1, \"stat-card\", \"time\"], [\"class\", \"stat-detail\", 4, \"ngIf\"], [1, \"stat-card\", \"speed\"], [\"class\", \"progress-chart\", 4, \"ngIf\"], [\"class\", \"step-progress\", 4, \"ngIf\"], [\"class\", \"completion-summary\", 4, \"ngIf\"], [1, \"progress-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Pause Scan\", 1, \"action-btn\", \"pause\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Cancel Scan\", 1, \"action-btn\", \"cancel\", 3, \"click\"], [1, \"progress-chart\"], [1, \"chart-header\"], [1, \"chart-legend\"], [1, \"legend-item\"], [1, \"legend-color\", \"progress\"], [1, \"legend-color\", \"issues\"], [1, \"chart-content\"], [1, \"progress-canvas\"], [1, \"step-progress\"], [1, \"steps-list\"], [\"class\", \"step-item\", 3, \"completed\", \"current\", \"pending\", 4, \"ngFor\", \"ngForOf\"], [1, \"step-item\"], [1, \"step-indicator\"], [4, \"ngIf\"], [\"class\", \"spinning\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"step-name\"], [1, \"step-description\"], [1, \"step-status\"], [\"class\", \"completed-icon\", 4, \"ngIf\"], [\"mode\", \"indeterminate\", \"class\", \"step-progress-bar\", 4, \"ngIf\"], [1, \"spinning\"], [1, \"completed-icon\"], [\"mode\", \"indeterminate\", 1, \"step-progress-bar\"], [1, \"completion-summary\"], [1, \"summary-header\"], [1, \"summary-icon\"], [1, \"summary-stats\"], [1, \"summary-item\"], [1, \"label\"], [1, \"value\"], [1, \"summary-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"], [\"mat-stroked-button\", \"\"], [1, \"empty-state\"], [1, \"empty-icon\"]],\n      template: function ScanProgressChartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ScanProgressChartComponent_div_0_Template, 82, 46, \"div\", 1)(1, ScanProgressChartComponent_div_1_Template, 11, 0, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.scanProgress);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.scanProgress);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.DecimalPipe, i1.TitleCasePipe, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatProgressBarModule, i4.MatProgressBar],\n      styles: [\"\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  background: var(--spt-surface);\\n  border-radius: var(--spt-radius-xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n  overflow: hidden;\\n}\\n\\n\\n\\n.progress-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: var(--spt-space-6);\\n  border-bottom: 1px solid var(--spt-border-light);\\n  background: var(--spt-bg-secondary);\\n}\\n\\n.scan-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.scan-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  margin-bottom: var(--spt-space-2);\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.scan-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.scan-id[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-tertiary);\\n  background: var(--spt-gray-100);\\n  padding: var(--spt-space-1) var(--spt-space-2);\\n  border-radius: var(--spt-radius-md);\\n  font-family: var(--spt-font-mono);\\n}\\n\\n.scan-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-1);\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-semibold);\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n\\n.scan-status.status-pending[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n}\\n\\n.scan-status.status-running[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: var(--spt-info-600);\\n}\\n\\n.scan-status.status-completed[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n}\\n\\n.scan-status.status-failed[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.current-step[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.progress-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-2);\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: var(--spt-radius-lg);\\n  transition: all 0.2s ease;\\n}\\n\\n.action-btn.pause[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n}\\n\\n.action-btn.pause[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-warning-50);\\n  color: var(--spt-warning-700);\\n}\\n\\n.action-btn.cancel[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.action-btn.cancel[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-error-50);\\n  color: var(--spt-error-700);\\n}\\n\\n\\n\\n.main-progress[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n  border-bottom: 1px solid var(--spt-border-light);\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: var(--spt-space-3);\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.progress-percentage[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-2xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-primary-600);\\n}\\n\\n.main-progress-bar[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: var(--spt-radius-lg);\\n  margin-bottom: var(--spt-space-3);\\n}\\n.main-progress-bar[_ngcontent-%COMP%]     .mat-mdc-progress-bar-fill::after {\\n  border-radius: var(--spt-radius-lg);\\n}\\n.main-progress-bar[_ngcontent-%COMP%]     .mat-mdc-progress-bar-buffer {\\n  border-radius: var(--spt-radius-lg);\\n}\\n\\n.progress-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n}\\n\\n\\n\\n.progress-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: var(--spt-space-4);\\n  padding: var(--spt-space-6);\\n  border-bottom: 1px solid var(--spt-border-light);\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  padding: var(--spt-space-4);\\n  background: var(--spt-bg-secondary);\\n  border-radius: var(--spt-radius-lg);\\n  border: 1px solid var(--spt-border-light);\\n  transition: all 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: var(--spt-shadow-sm);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: var(--spt-radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.stat-card.files[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-info-100);\\n  color: var(--spt-info-600);\\n}\\n\\n.stat-card.issues[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-error-100);\\n  color: var(--spt-error-600);\\n}\\n\\n.stat-card.time[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-warning-100);\\n  color: var(--spt-warning-600);\\n}\\n\\n.stat-card.speed[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-success-100);\\n  color: var(--spt-success-600);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-text-primary);\\n  line-height: var(--spt-leading-tight);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n  margin: var(--spt-space-1) 0;\\n}\\n\\n.stat-detail[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-secondary);\\n}\\n\\n\\n\\n.progress-chart[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n  border-bottom: 1px solid var(--spt-border-light);\\n}\\n\\n.chart-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.chart-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.chart-legend[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-4);\\n}\\n\\n.legend-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.legend-color[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: var(--spt-radius-sm);\\n}\\n\\n.legend-color.progress[_ngcontent-%COMP%] {\\n  background: #3b82f6;\\n}\\n\\n.legend-color.issues[_ngcontent-%COMP%] {\\n  background: #ef4444;\\n}\\n\\n.chart-content[_ngcontent-%COMP%] {\\n  height: 200px;\\n  position: relative;\\n}\\n\\n.progress-canvas[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n}\\n\\n\\n\\n.step-progress[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n  border-bottom: 1px solid var(--spt-border-light);\\n}\\n\\n.step-progress[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-4) 0;\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.steps-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-3);\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-4);\\n  padding: var(--spt-space-3);\\n  border-radius: var(--spt-radius-lg);\\n  transition: all 0.2s ease;\\n}\\n\\n.step-item.completed[_ngcontent-%COMP%] {\\n  background: var(--spt-success-50);\\n  border: 1px solid var(--spt-success-200);\\n}\\n\\n.step-item.current[_ngcontent-%COMP%] {\\n  background: var(--spt-info-50);\\n  border: 1px solid var(--spt-info-200);\\n}\\n\\n.step-item.pending[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-50);\\n  border: 1px solid var(--spt-border-light);\\n}\\n\\n.step-indicator[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: var(--spt-radius-full);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: var(--spt-font-semibold);\\n  font-size: var(--spt-text-sm);\\n  flex-shrink: 0;\\n}\\n\\n.step-item.completed[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%] {\\n  background: var(--spt-success-600);\\n  color: white;\\n}\\n\\n.step-item.current[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%] {\\n  background: var(--spt-info-600);\\n  color: white;\\n}\\n\\n.step-item.pending[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-200);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.step-name[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n  margin-bottom: var(--spt-space-1);\\n}\\n\\n.step-description[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.step-status[_ngcontent-%COMP%] {\\n  width: 100px;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.completed-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.step-progress-bar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 4px;\\n}\\n\\n\\n\\n.completion-summary[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.summary-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.summary-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: var(--spt-space-4);\\n  margin-bottom: var(--spt-space-6);\\n}\\n\\n.summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spt-space-3);\\n  background: var(--spt-bg-secondary);\\n  border-radius: var(--spt-radius-lg);\\n  border: 1px solid var(--spt-border-light);\\n}\\n\\n.summary-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.summary-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.summary-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-3);\\n  justify-content: center;\\n}\\n\\n\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spt-space-12);\\n  text-align: center;\\n  color: var(--spt-text-secondary);\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: var(--spt-text-tertiary);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-2) 0;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-6) 0;\\n  font-size: var(--spt-text-sm);\\n  max-width: 300px;\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n\\n\\n.spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .progress-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spt-space-4);\\n    align-items: stretch;\\n  }\\n  .progress-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .chart-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spt-space-2);\\n    align-items: stretch;\\n  }\\n  .summary-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .summary-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressBarModule", "Chart", "registerables", "Subject", "interval", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "ScanProgressChartComponent_div_0_div_16_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "pauseScan", "ɵɵtext", "ɵɵelementEnd", "ScanProgressChartComponent_div_0_div_16_Template_button_click_4_listener", "cancelScan", "ɵɵadvance", "ɵɵtextInterpolate1", "getEstimatedTimeRemaining", "ɵɵelement", "ɵɵtextInterpolate", "i_r3", "ɵɵtemplate", "ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_2_Template", "ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_3_Template", "ScanProgressChartComponent_div_0_div_80_div_4_span_4_Template", "ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_11_Template", "ScanProgressChartComponent_div_0_div_80_div_4_mat_progress_bar_12_Template", "ɵɵclassProp", "scanProgress", "completedSteps", "ɵɵproperty", "step_r4", "name", "description", "ScanProgressChartComponent_div_0_div_80_div_4_Template", "getScanSteps", "ScanProgressChartComponent_div_0_div_81_button_24_Template", "ScanProgressChartComponent_div_0_div_81_button_25_Template", "ɵɵstyleProp", "getStatusColor", "getStatusIcon", "ɵɵpipeBind1", "status", "filesScanned", "issuesFound", "getElapsedTime", "ScanProgressChartComponent_div_0_div_16_Template", "ScanProgressChartComponent_div_0_div_66_Template", "ScanProgressChartComponent_div_0_div_79_Template", "ScanProgressChartComponent_div_0_div_80_Template", "ScanProgressChartComponent_div_0_div_81_Template", "projectName", "scanId", "ɵɵclassMap", "currentStep", "ɵɵpipeBind2", "animatedProgress", "ɵɵtextInterpolate2", "totalSteps", "animatedFilesScanned", "totalFiles", "animatedIssuesFound", "Math", "max", "getElapsedTimeInMinutes", "showRealTimeUpdates", "progressHistory", "length", "register", "ScanProgressChartComponent", "constructor", "updateInterval", "destroy$", "chart", "ngOnInit", "startRealTimeUpdates", "ngAfterViewInit", "createProgressChart", "ngOnDestroy", "next", "complete", "destroy", "pipe", "subscribe", "updateProgressData", "animateCounters", "dataPoint", "timestamp", "Date", "progress", "push", "shift", "updateChart", "progressDiff", "filesDiff", "issuesDiff", "progressCanvas", "nativeElement", "ctx", "getContext", "config", "type", "data", "labels", "datasets", "label", "borderColor", "backgroundColor", "borderWidth", "fill", "tension", "yAxisID", "options", "responsive", "maintainAspectRatio", "interaction", "mode", "intersect", "plugins", "legend", "display", "position", "usePointStyle", "font", "family", "size", "weight", "tooltip", "titleColor", "bodyColor", "cornerRadius", "callbacks", "title", "context", "toLocaleTimeString", "scales", "x", "text", "ticks", "callback", "value", "index", "getLabelForValue", "hour", "minute", "y", "min", "y1", "grid", "drawOnChartArea", "animation", "duration", "easing", "map", "point", "toISOString", "progressData", "issuesData", "update", "estimatedCompletion", "now", "remaining", "getTime", "minutes", "floor", "seconds", "elapsed", "startTime", "console", "log", "selectors", "viewQuery", "ScanProgressChartComponent_Query", "rf", "ScanProgressChartComponent_div_0_Template", "ScanProgressChartComponent_div_1_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "TitleCasePipe", "i2", "MatButton", "MatIconButton", "i3", "MatIcon", "i4", "MatProgressBar", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\shared\\charts\\scan-progress-chart\\scan-progress-chart.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\shared\\charts\\scan-progress-chart\\scan-progress-chart.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { Chart, ChartConfiguration, registerables } from 'chart.js';\nimport { Subject, interval, takeUntil } from 'rxjs';\n\n// Register Chart.js components\nChart.register(...registerables);\n\nexport interface ScanProgress {\n  scanId: string;\n  projectName: string;\n  status: 'pending' | 'running' | 'completed' | 'failed';\n  progress: number; // 0-100\n  currentStep: string;\n  totalSteps: number;\n  completedSteps: number;\n  startTime: Date;\n  estimatedCompletion?: Date;\n  filesScanned: number;\n  totalFiles: number;\n  issuesFound: number;\n}\n\nexport interface ProgressDataPoint {\n  timestamp: Date;\n  progress: number;\n  filesScanned: number;\n  issuesFound: number;\n}\n\n@Component({\n  selector: 'app-scan-progress-chart',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressBarModule\n  ],\n  templateUrl: './scan-progress-chart.component.html',\n  styleUrls: ['./scan-progress-chart.component.scss']\n})\nexport class ScanProgressChartComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('progressCanvas', { static: true }) progressCanvas!: ElementRef<HTMLCanvasElement>;\n  \n  @Input() scanProgress: ScanProgress | null = null;\n  @Input() showRealTimeUpdates: boolean = true;\n  @Input() updateInterval: number = 2000; // milliseconds\n\n  private destroy$ = new Subject<void>();\n  private chart: Chart | null = null;\n  progressHistory: ProgressDataPoint[] = [];\n\n  // Make Math available in template\n  Math = Math;\n\n  // Animation properties\n  animatedProgress: number = 0;\n  animatedFilesScanned: number = 0;\n  animatedIssuesFound: number = 0;\n\n  ngOnInit(): void {\n    if (this.showRealTimeUpdates && this.scanProgress?.status === 'running') {\n      this.startRealTimeUpdates();\n    }\n  }\n\n  ngAfterViewInit(): void {\n    this.createProgressChart();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    \n    if (this.chart) {\n      this.chart.destroy();\n    }\n  }\n\n  private startRealTimeUpdates(): void {\n    interval(this.updateInterval)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        if (this.scanProgress?.status === 'running') {\n          this.updateProgressData();\n          this.animateCounters();\n        }\n      });\n  }\n\n  private updateProgressData(): void {\n    if (!this.scanProgress) return;\n\n    const dataPoint: ProgressDataPoint = {\n      timestamp: new Date(),\n      progress: this.scanProgress.progress,\n      filesScanned: this.scanProgress.filesScanned,\n      issuesFound: this.scanProgress.issuesFound\n    };\n\n    this.progressHistory.push(dataPoint);\n\n    // Keep only last 50 data points for performance\n    if (this.progressHistory.length > 50) {\n      this.progressHistory.shift();\n    }\n\n    this.updateChart();\n  }\n\n  private animateCounters(): void {\n    if (!this.scanProgress) return;\n\n    // Animate progress\n    const progressDiff = this.scanProgress.progress - this.animatedProgress;\n    this.animatedProgress += progressDiff * 0.1;\n\n    // Animate files scanned\n    const filesDiff = this.scanProgress.filesScanned - this.animatedFilesScanned;\n    this.animatedFilesScanned += filesDiff * 0.1;\n\n    // Animate issues found\n    const issuesDiff = this.scanProgress.issuesFound - this.animatedIssuesFound;\n    this.animatedIssuesFound += issuesDiff * 0.1;\n  }\n\n  private createProgressChart(): void {\n    if (!this.progressCanvas?.nativeElement) {\n      return;\n    }\n\n    const ctx = this.progressCanvas.nativeElement.getContext('2d');\n    if (!ctx) {\n      return;\n    }\n\n    // Destroy existing chart\n    if (this.chart) {\n      this.chart.destroy();\n    }\n\n    const config: ChartConfiguration = {\n      type: 'line',\n      data: {\n        labels: [],\n        datasets: [\n          {\n            label: 'Progress %',\n            data: [],\n            borderColor: '#3b82f6',\n            backgroundColor: 'rgba(59, 130, 246, 0.1)',\n            borderWidth: 2,\n            fill: true,\n            tension: 0.4,\n            yAxisID: 'y'\n          },\n          {\n            label: 'Issues Found',\n            data: [],\n            borderColor: '#ef4444',\n            backgroundColor: 'rgba(239, 68, 68, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4,\n            yAxisID: 'y1'\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          mode: 'index',\n          intersect: false,\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              usePointStyle: true,\n              font: {\n                family: 'Inter, sans-serif',\n                size: 12,\n                weight: 500\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: '#374151',\n            borderWidth: 1,\n            cornerRadius: 8,\n            callbacks: {\n              title: (context) => {\n                const timestamp = new Date(context[0].label);\n                return timestamp.toLocaleTimeString();\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Time',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            ticks: {\n              callback: function(value, index) {\n                const timestamp = this.getLabelForValue(value as number);\n                return new Date(timestamp).toLocaleTimeString([], { \n                  hour: '2-digit', \n                  minute: '2-digit' \n                });\n              },\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          },\n          y: {\n            type: 'linear',\n            display: true,\n            position: 'left',\n            title: {\n              display: true,\n              text: 'Progress (%)',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            min: 0,\n            max: 100,\n            ticks: {\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          },\n          y1: {\n            type: 'linear',\n            display: true,\n            position: 'right',\n            title: {\n              display: true,\n              text: 'Issues Found',\n              font: {\n                family: 'Inter, sans-serif',\n                weight: 500\n              }\n            },\n            min: 0,\n            grid: {\n              drawOnChartArea: false,\n            },\n            ticks: {\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          }\n        },\n        animation: {\n          duration: 750,\n          easing: 'easeInOutQuart'\n        }\n      }\n    };\n\n    this.chart = new Chart(ctx, config);\n    this.updateChart();\n  }\n\n  private updateChart(): void {\n    if (!this.chart || this.progressHistory.length === 0) {\n      return;\n    }\n\n    const labels = this.progressHistory.map(point => point.timestamp.toISOString());\n    const progressData = this.progressHistory.map(point => point.progress);\n    const issuesData = this.progressHistory.map(point => point.issuesFound);\n\n    this.chart.data.labels = labels;\n    this.chart.data.datasets[0].data = progressData;\n    this.chart.data.datasets[1].data = issuesData;\n\n    this.chart.update('none');\n  }\n\n  getStatusIcon(): string {\n    if (!this.scanProgress) return 'help';\n    \n    switch (this.scanProgress.status) {\n      case 'pending': return 'schedule';\n      case 'running': return 'hourglass_empty';\n      case 'completed': return 'check_circle';\n      case 'failed': return 'error';\n      default: return 'help';\n    }\n  }\n\n  getStatusColor(): string {\n    if (!this.scanProgress) return 'var(--spt-text-secondary)';\n    \n    switch (this.scanProgress.status) {\n      case 'pending': return 'var(--spt-warning-600)';\n      case 'running': return 'var(--spt-info-600)';\n      case 'completed': return 'var(--spt-success-600)';\n      case 'failed': return 'var(--spt-error-600)';\n      default: return 'var(--spt-text-secondary)';\n    }\n  }\n\n  getEstimatedTimeRemaining(): string {\n    if (!this.scanProgress || !this.scanProgress.estimatedCompletion) {\n      return 'Calculating...';\n    }\n\n    const now = new Date();\n    const remaining = this.scanProgress.estimatedCompletion.getTime() - now.getTime();\n    \n    if (remaining <= 0) {\n      return 'Almost done';\n    }\n\n    const minutes = Math.floor(remaining / 60000);\n    const seconds = Math.floor((remaining % 60000) / 1000);\n\n    if (minutes > 0) {\n      return `${minutes}m ${seconds}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  }\n\n  getElapsedTime(): string {\n    if (!this.scanProgress) return '0s';\n\n    const now = new Date();\n    const elapsed = now.getTime() - this.scanProgress.startTime.getTime();\n    \n    const minutes = Math.floor(elapsed / 60000);\n    const seconds = Math.floor((elapsed % 60000) / 1000);\n\n    if (minutes > 0) {\n      return `${minutes}m ${seconds}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  }\n\n  cancelScan(): void {\n    // Emit cancel event or call service\n    console.log('Cancel scan requested for:', this.scanProgress?.scanId);\n  }\n\n  pauseScan(): void {\n    // Emit pause event or call service\n    console.log('Pause scan requested for:', this.scanProgress?.scanId);\n  }\n\n  getElapsedTimeInMinutes(): number {\n    if (!this.scanProgress) return 0;\n\n    const now = new Date();\n    const elapsed = now.getTime() - this.scanProgress.startTime.getTime();\n    return elapsed / 60000; // Convert to minutes\n  }\n\n  getScanSteps(): Array<{name: string, description: string}> {\n    return [\n      { name: 'Initialize', description: 'Setting up scan environment' },\n      { name: 'File Discovery', description: 'Finding smart contract files' },\n      { name: 'Static Analysis', description: 'Analyzing code structure' },\n      { name: 'Security Checks', description: 'Running security rules' },\n      { name: 'Vulnerability Detection', description: 'Identifying security issues' },\n      { name: 'Report Generation', description: 'Compiling results' }\n    ];\n  }\n}\n", "<div class=\"progress-container\" *ngIf=\"scanProgress\">\n  <!-- Progress Header -->\n  <div class=\"progress-header\">\n    <div class=\"scan-info\">\n      <div class=\"scan-title\">\n        <mat-icon [style.color]=\"getStatusColor()\" class=\"status-icon\">\n          {{ getStatusIcon() }}\n        </mat-icon>\n        <h3>{{ scanProgress.projectName }}</h3>\n        <span class=\"scan-id\">ID: {{ scanProgress.scanId }}</span>\n      </div>\n      \n      <div class=\"scan-status\" [class]=\"'status-' + scanProgress.status\">\n        <span class=\"status-text\">{{ scanProgress.status | titlecase }}</span>\n        <span class=\"current-step\">{{ scanProgress.currentStep }}</span>\n      </div>\n    </div>\n\n    <div class=\"progress-actions\" *ngIf=\"scanProgress.status === 'running'\">\n      <button \n        mat-icon-button \n        matTooltip=\"Pause Scan\"\n        (click)=\"pauseScan()\"\n        class=\"action-btn pause\">\n        <mat-icon>pause</mat-icon>\n      </button>\n      \n      <button \n        mat-icon-button \n        matTooltip=\"Cancel Scan\"\n        (click)=\"cancelScan()\"\n        class=\"action-btn cancel\">\n        <mat-icon>stop</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Main Progress Bar -->\n  <div class=\"main-progress\">\n    <div class=\"progress-info\">\n      <span class=\"progress-label\">Overall Progress</span>\n      <span class=\"progress-percentage\">{{ animatedProgress | number:'1.1-1' }}%</span>\n    </div>\n    \n    <mat-progress-bar \n      mode=\"determinate\" \n      [value]=\"animatedProgress\"\n      class=\"main-progress-bar\">\n    </mat-progress-bar>\n    \n    <div class=\"progress-details\">\n      <span>Step {{ scanProgress.completedSteps }} of {{ scanProgress.totalSteps }}</span>\n      <span>{{ animatedFilesScanned | number:'1.0-0' }} / {{ scanProgress.totalFiles }} files</span>\n    </div>\n  </div>\n\n  <!-- Progress Stats Grid -->\n  <div class=\"progress-stats\">\n    <div class=\"stat-card files\">\n      <div class=\"stat-icon\">\n        <mat-icon>description</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ animatedFilesScanned | number:'1.0-0' }}</div>\n        <div class=\"stat-label\">Files Scanned</div>\n        <div class=\"stat-detail\">of {{ scanProgress.totalFiles }} total</div>\n      </div>\n    </div>\n\n    <div class=\"stat-card issues\">\n      <div class=\"stat-icon\">\n        <mat-icon>bug_report</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ animatedIssuesFound | number:'1.0-0' }}</div>\n        <div class=\"stat-label\">Issues Found</div>\n        <div class=\"stat-detail\">{{ (animatedIssuesFound / Math.max(animatedFilesScanned, 1) * 100) | number:'1.1-1' }}% of files</div>\n      </div>\n    </div>\n\n    <div class=\"stat-card time\">\n      <div class=\"stat-icon\">\n        <mat-icon>schedule</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ getElapsedTime() }}</div>\n        <div class=\"stat-label\">Elapsed Time</div>\n        <div class=\"stat-detail\" *ngIf=\"scanProgress.status === 'running'\">\n          {{ getEstimatedTimeRemaining() }} remaining\n        </div>\n      </div>\n    </div>\n\n    <div class=\"stat-card speed\">\n      <div class=\"stat-icon\">\n        <mat-icon>speed</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ (animatedFilesScanned / Math.max(getElapsedTimeInMinutes(), 1)) | number:'1.1-1' }}</div>\n        <div class=\"stat-label\">Files/Min</div>\n        <div class=\"stat-detail\">Scan Speed</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Real-time Progress Chart -->\n  <div class=\"progress-chart\" *ngIf=\"showRealTimeUpdates && progressHistory.length > 1\">\n    <div class=\"chart-header\">\n      <h4>Real-time Progress</h4>\n      <div class=\"chart-legend\">\n        <div class=\"legend-item\">\n          <div class=\"legend-color progress\"></div>\n          <span>Progress</span>\n        </div>\n        <div class=\"legend-item\">\n          <div class=\"legend-color issues\"></div>\n          <span>Issues Found</span>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"chart-content\">\n      <canvas \n        #progressCanvas\n        class=\"progress-canvas\"\n        [attr.aria-label]=\"'Real-time progress chart for ' + scanProgress.projectName\">\n      </canvas>\n    </div>\n  </div>\n\n  <!-- Step Progress -->\n  <div class=\"step-progress\" *ngIf=\"scanProgress.status === 'running'\">\n    <h4>Scan Steps</h4>\n    <div class=\"steps-list\">\n      <div \n        *ngFor=\"let step of getScanSteps(); let i = index\"\n        class=\"step-item\"\n        [class.completed]=\"i < scanProgress.completedSteps\"\n        [class.current]=\"i === scanProgress.completedSteps\"\n        [class.pending]=\"i > scanProgress.completedSteps\">\n        \n        <div class=\"step-indicator\">\n          <mat-icon *ngIf=\"i < scanProgress.completedSteps\">check</mat-icon>\n          <mat-icon *ngIf=\"i === scanProgress.completedSteps\" class=\"spinning\">hourglass_empty</mat-icon>\n          <span *ngIf=\"i > scanProgress.completedSteps\">{{ i + 1 }}</span>\n        </div>\n        \n        <div class=\"step-content\">\n          <div class=\"step-name\">{{ step.name }}</div>\n          <div class=\"step-description\">{{ step.description }}</div>\n        </div>\n        \n        <div class=\"step-status\">\n          <mat-icon *ngIf=\"i < scanProgress.completedSteps\" class=\"completed-icon\">check_circle</mat-icon>\n          <mat-progress-bar \n            *ngIf=\"i === scanProgress.completedSteps\"\n            mode=\"indeterminate\"\n            class=\"step-progress-bar\">\n          </mat-progress-bar>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Completion Summary -->\n  <div class=\"completion-summary\" *ngIf=\"scanProgress.status === 'completed' || scanProgress.status === 'failed'\">\n    <div class=\"summary-header\">\n      <mat-icon [style.color]=\"getStatusColor()\" class=\"summary-icon\">\n        {{ getStatusIcon() }}\n      </mat-icon>\n      <h4>Scan {{ scanProgress.status | titlecase }}</h4>\n    </div>\n    \n    <div class=\"summary-stats\">\n      <div class=\"summary-item\">\n        <span class=\"label\">Total Files Scanned:</span>\n        <span class=\"value\">{{ scanProgress.filesScanned }}</span>\n      </div>\n      <div class=\"summary-item\">\n        <span class=\"label\">Issues Found:</span>\n        <span class=\"value\">{{ scanProgress.issuesFound }}</span>\n      </div>\n      <div class=\"summary-item\">\n        <span class=\"label\">Total Time:</span>\n        <span class=\"value\">{{ getElapsedTime() }}</span>\n      </div>\n    </div>\n    \n    <div class=\"summary-actions\">\n      <button mat-raised-button color=\"primary\" *ngIf=\"scanProgress.status === 'completed'\">\n        <mat-icon>visibility</mat-icon>\n        View Report\n      </button>\n      <button mat-stroked-button *ngIf=\"scanProgress.status === 'failed'\">\n        <mat-icon>refresh</mat-icon>\n        Retry Scan\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Empty State -->\n<div class=\"empty-state\" *ngIf=\"!scanProgress\">\n  <mat-icon class=\"empty-icon\">hourglass_empty</mat-icon>\n  <h4>No Active Scan</h4>\n  <p>Start a new security scan to see real-time progress here.</p>\n  <button mat-raised-button color=\"primary\">\n    <mat-icon>security</mat-icon>\n    Start New Scan\n  </button>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,KAAK,EAAsBC,aAAa,QAAQ,UAAU;AACnE,SAASC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;ICY7CC,EADF,CAAAC,cAAA,cAAwE,iBAK3C;IADzBD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAErBT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IACjBV,EADiB,CAAAW,YAAA,EAAW,EACnB;IAETX,EAAA,CAAAC,cAAA,iBAI4B;IAD1BD,EAAA,CAAAE,UAAA,mBAAAU,yEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,UAAA,EAAY;IAAA,EAAC;IAEtBb,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAElBV,EAFkB,CAAAW,YAAA,EAAW,EAClB,EACL;;;;;IAqDFX,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAT,MAAA,CAAAU,yBAAA,kBACF;;;;;IAmBFhB,EAFJ,CAAAC,cAAA,cAAsF,cAC1D,SACpB;IAAAD,EAAA,CAAAU,MAAA,yBAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEzBX,EADF,CAAAC,cAAA,cAA0B,cACC;IACvBD,EAAA,CAAAiB,SAAA,cAAyC;IACzCjB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAChBV,EADgB,CAAAW,YAAA,EAAO,EACjB;IACNX,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAiB,SAAA,eAAuC;IACvCjB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAGxBV,EAHwB,CAAAW,YAAA,EAAO,EACrB,EACF,EACF;IAENX,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAiB,SAAA,qBAIS;IAEbjB,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAHAX,EAAA,CAAAc,SAAA,IAA8E;;;;;;IAiB5Ed,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAClEX,EAAA,CAAAC,cAAA,mBAAqE;IAAAD,EAAA,CAAAU,MAAA,sBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAC/FX,EAAA,CAAAC,cAAA,WAA8C;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAlBX,EAAA,CAAAc,SAAA,EAAW;IAAXd,EAAA,CAAAkB,iBAAA,CAAAC,IAAA,KAAW;;;;;IASzDnB,EAAA,CAAAC,cAAA,mBAAyE;IAAAD,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAChGX,EAAA,CAAAiB,SAAA,2BAImB;;;;;IAjBrBjB,EAPF,CAAAC,cAAA,cAKoD,cAEtB;IAG1BD,EAFA,CAAAoB,UAAA,IAAAC,iEAAA,uBAAkD,IAAAC,iEAAA,uBACmB,IAAAC,6DAAA,mBACvB;IAChDvB,EAAA,CAAAW,YAAA,EAAM;IAGJX,EADF,CAAAC,cAAA,cAA0B,cACD;IAAAD,EAAA,CAAAU,MAAA,GAAe;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC5CX,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAsB;IACtDV,EADsD,CAAAW,YAAA,EAAM,EACtD;IAENX,EAAA,CAAAC,cAAA,eAAyB;IAEvBD,EADA,CAAAoB,UAAA,KAAAI,kEAAA,uBAAyE,KAAAC,0EAAA,+BAI7C;IAGhCzB,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;IArBJX,EAFA,CAAA0B,WAAA,cAAAP,IAAA,GAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CAAmD,YAAAT,IAAA,KAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CACA,YAAAT,IAAA,GAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CACF;IAGpC5B,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAA6B,UAAA,SAAAV,IAAA,GAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CAAqC;IACrC5B,EAAA,CAAAc,SAAA,EAAuC;IAAvCd,EAAA,CAAA6B,UAAA,SAAAV,IAAA,KAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CAAuC;IAC3C5B,EAAA,CAAAc,SAAA,EAAqC;IAArCd,EAAA,CAAA6B,UAAA,SAAAV,IAAA,GAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CAAqC;IAIrB5B,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAAkB,iBAAA,CAAAY,OAAA,CAAAC,IAAA,CAAe;IACR/B,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAkB,iBAAA,CAAAY,OAAA,CAAAE,WAAA,CAAsB;IAIzChC,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAA6B,UAAA,SAAAV,IAAA,GAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CAAqC;IAE7C5B,EAAA,CAAAc,SAAA,EAAuC;IAAvCd,EAAA,CAAA6B,UAAA,SAAAV,IAAA,KAAAb,MAAA,CAAAqB,YAAA,CAAAC,cAAA,CAAuC;;;;;IAvBhD5B,EADF,CAAAC,cAAA,cAAqE,SAC/D;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnBX,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAoB,UAAA,IAAAa,sDAAA,oBAKoD;IAuBxDjC,EADE,CAAAW,YAAA,EAAM,EACF;;;;IA3BiBX,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAA6B,UAAA,YAAAvB,MAAA,CAAA4B,YAAA,GAAmB;;;;;IAuDpClC,EADF,CAAAC,cAAA,iBAAsF,eAC1E;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC/BX,EAAA,CAAAU,MAAA,oBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAEPX,EADF,CAAAC,cAAA,iBAAoE,eACxD;IAAAD,EAAA,CAAAU,MAAA,cAAO;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC5BX,EAAA,CAAAU,MAAA,mBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IA7BTX,EAFJ,CAAAC,cAAA,cAAgH,cAClF,mBACsC;IAC9DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACXX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAA0C;;IAChDV,EADgD,CAAAW,YAAA,EAAK,EAC/C;IAIFX,EAFJ,CAAAC,cAAA,cAA2B,cACC,eACJ;IAAAD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC/CX,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAU,MAAA,IAA+B;IACrDV,EADqD,CAAAW,YAAA,EAAO,EACtD;IAEJX,EADF,CAAAC,cAAA,eAA0B,gBACJ;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACxCX,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAU,MAAA,IAA8B;IACpDV,EADoD,CAAAW,YAAA,EAAO,EACrD;IAEJX,EADF,CAAAC,cAAA,eAA0B,gBACJ;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACtCX,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAU,MAAA,IAAsB;IAE9CV,EAF8C,CAAAW,YAAA,EAAO,EAC7C,EACF;IAENX,EAAA,CAAAC,cAAA,eAA6B;IAK3BD,EAJA,CAAAoB,UAAA,KAAAe,0DAAA,qBAAsF,KAAAC,0DAAA,qBAIlB;IAKxEpC,EADE,CAAAW,YAAA,EAAM,EACF;;;;IA/BQX,EAAA,CAAAc,SAAA,GAAgC;IAAhCd,EAAA,CAAAqC,WAAA,UAAA/B,MAAA,CAAAgC,cAAA,GAAgC;IACxCtC,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAT,MAAA,CAAAiC,aAAA,QACF;IACIvC,EAAA,CAAAc,SAAA,GAA0C;IAA1Cd,EAAA,CAAAe,kBAAA,UAAAf,EAAA,CAAAwC,WAAA,OAAAlC,MAAA,CAAAqB,YAAA,CAAAc,MAAA,MAA0C;IAMxBzC,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAqB,YAAA,CAAAe,YAAA,CAA+B;IAI/B1C,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAqB,YAAA,CAAAgB,WAAA,CAA8B;IAI9B3C,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAsC,cAAA,GAAsB;IAKD5C,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAqB,YAAA,CAAAc,MAAA,iBAAyC;IAIxDzC,EAAA,CAAAc,SAAA,EAAsC;IAAtCd,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAqB,YAAA,CAAAc,MAAA,cAAsC;;;;;IA5LhEzC,EALR,CAAAC,cAAA,aAAqD,aAEtB,aACJ,aACG,kBACyC;IAC7DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACXX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACvCX,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAU,MAAA,GAA6B;IACrDV,EADqD,CAAAW,YAAA,EAAO,EACtD;IAGJX,EADF,CAAAC,cAAA,cAAmE,gBACvC;IAAAD,EAAA,CAAAU,MAAA,IAAqC;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACtEX,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,IAA8B;IAE7DV,EAF6D,CAAAW,YAAA,EAAO,EAC5D,EACF;IAENX,EAAA,CAAAoB,UAAA,KAAAyB,gDAAA,kBAAwE;IAiB1E7C,EAAA,CAAAW,YAAA,EAAM;IAKFX,EAFJ,CAAAC,cAAA,eAA2B,eACE,gBACI;IAAAD,EAAA,CAAAU,MAAA,wBAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACpDX,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAU,MAAA,IAAwC;;IAC5EV,EAD4E,CAAAW,YAAA,EAAO,EAC7E;IAENX,EAAA,CAAAiB,SAAA,4BAImB;IAGjBjB,EADF,CAAAC,cAAA,eAA8B,YACtB;IAAAD,EAAA,CAAAU,MAAA,IAAuE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACpFX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAiF;;IAE3FV,EAF2F,CAAAW,YAAA,EAAO,EAC1F,EACF;IAMAX,EAHN,CAAAC,cAAA,eAA4B,eACG,eACJ,gBACX;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IACvBV,EADuB,CAAAW,YAAA,EAAW,EAC5B;IAEJX,EADF,CAAAC,cAAA,eAA0B,eACA;IAAAD,EAAA,CAAAU,MAAA,IAA2C;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACzEX,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC3CX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAsC;IAEnEV,EAFmE,CAAAW,YAAA,EAAM,EACjE,EACF;IAIFX,EAFJ,CAAAC,cAAA,eAA8B,eACL,gBACX;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IACtBV,EADsB,CAAAW,YAAA,EAAW,EAC3B;IAEJX,EADF,CAAAC,cAAA,eAA0B,eACA;IAAAD,EAAA,CAAAU,MAAA,IAA0C;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACxEX,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC1CX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAgG;;IAE7HV,EAF6H,CAAAW,YAAA,EAAM,EAC3H,EACF;IAIFX,EAFJ,CAAAC,cAAA,eAA4B,eACH,gBACX;IAAAD,EAAA,CAAAU,MAAA,gBAAQ;IACpBV,EADoB,CAAAW,YAAA,EAAW,EACzB;IAEJX,EADF,CAAAC,cAAA,eAA0B,eACA;IAAAD,EAAA,CAAAU,MAAA,IAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACpDX,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC1CX,EAAA,CAAAoB,UAAA,KAAA0B,gDAAA,kBAAmE;IAIvE9C,EADE,CAAAW,YAAA,EAAM,EACF;IAIFX,EAFJ,CAAAC,cAAA,eAA6B,eACJ,gBACX;IAAAD,EAAA,CAAAU,MAAA,aAAK;IACjBV,EADiB,CAAAW,YAAA,EAAW,EACtB;IAEJX,EADF,CAAAC,cAAA,eAA0B,eACA;IAAAD,EAAA,CAAAU,MAAA,IAAsF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACpHX,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAU,MAAA,iBAAS;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACvCX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAGzCV,EAHyC,CAAAW,YAAA,EAAM,EACrC,EACF,EACF;IA8DNX,EA3DA,CAAAoB,UAAA,KAAA2B,gDAAA,mBAAsF,KAAAC,gDAAA,kBAyBjB,KAAAC,gDAAA,oBAkC2C;IAkClHjD,EAAA,CAAAW,YAAA,EAAM;;;;IAlMYX,EAAA,CAAAc,SAAA,GAAgC;IAAhCd,EAAA,CAAAqC,WAAA,UAAA/B,MAAA,CAAAgC,cAAA,GAAgC;IACxCtC,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAT,MAAA,CAAAiC,aAAA,QACF;IACIvC,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAqB,YAAA,CAAAuB,WAAA,CAA8B;IACZlD,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAe,kBAAA,SAAAT,MAAA,CAAAqB,YAAA,CAAAwB,MAAA,KAA6B;IAG5BnD,EAAA,CAAAc,SAAA,EAAyC;IAAzCd,EAAA,CAAAoD,UAAA,aAAA9C,MAAA,CAAAqB,YAAA,CAAAc,MAAA,CAAyC;IACtCzC,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAwC,WAAA,SAAAlC,MAAA,CAAAqB,YAAA,CAAAc,MAAA,EAAqC;IACpCzC,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAqB,YAAA,CAAA0B,WAAA,CAA8B;IAI9BrD,EAAA,CAAAc,SAAA,EAAuC;IAAvCd,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAqB,YAAA,CAAAc,MAAA,eAAuC;IAuBlCzC,EAAA,CAAAc,SAAA,GAAwC;IAAxCd,EAAA,CAAAe,kBAAA,KAAAf,EAAA,CAAAsD,WAAA,SAAAhD,MAAA,CAAAiD,gBAAA,gBAAwC;IAK1EvD,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAA6B,UAAA,UAAAvB,MAAA,CAAAiD,gBAAA,CAA0B;IAKpBvD,EAAA,CAAAc,SAAA,GAAuE;IAAvEd,EAAA,CAAAwD,kBAAA,UAAAlD,MAAA,CAAAqB,YAAA,CAAAC,cAAA,UAAAtB,MAAA,CAAAqB,YAAA,CAAA8B,UAAA,KAAuE;IACvEzD,EAAA,CAAAc,SAAA,GAAiF;IAAjFd,EAAA,CAAAwD,kBAAA,KAAAxD,EAAA,CAAAsD,WAAA,SAAAhD,MAAA,CAAAoD,oBAAA,mBAAApD,MAAA,CAAAqB,YAAA,CAAAgC,UAAA,WAAiF;IAW7D3D,EAAA,CAAAc,SAAA,GAA2C;IAA3Cd,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAsD,WAAA,SAAAhD,MAAA,CAAAoD,oBAAA,WAA2C;IAE1C1D,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAe,kBAAA,QAAAT,MAAA,CAAAqB,YAAA,CAAAgC,UAAA,WAAsC;IASvC3D,EAAA,CAAAc,SAAA,GAA0C;IAA1Cd,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAsD,WAAA,SAAAhD,MAAA,CAAAsD,mBAAA,WAA0C;IAEzC5D,EAAA,CAAAc,SAAA,GAAgG;IAAhGd,EAAA,CAAAe,kBAAA,KAAAf,EAAA,CAAAsD,WAAA,SAAAhD,MAAA,CAAAsD,mBAAA,GAAAtD,MAAA,CAAAuD,IAAA,CAAAC,GAAA,CAAAxD,MAAA,CAAAoD,oBAAA,mCAAgG;IASjG1D,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAsC,cAAA,GAAsB;IAEpB5C,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAqB,YAAA,CAAAc,MAAA,eAAuC;IAWzCzC,EAAA,CAAAc,SAAA,GAAsF;IAAtFd,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAsD,WAAA,SAAAhD,MAAA,CAAAoD,oBAAA,GAAApD,MAAA,CAAAuD,IAAA,CAAAC,GAAA,CAAAxD,MAAA,CAAAyD,uBAAA,iBAAsF;IAQvF/D,EAAA,CAAAc,SAAA,GAAuD;IAAvDd,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAA0D,mBAAA,IAAA1D,MAAA,CAAA2D,eAAA,CAAAC,MAAA,KAAuD;IAyBxDlE,EAAA,CAAAc,SAAA,EAAuC;IAAvCd,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAqB,YAAA,CAAAc,MAAA,eAAuC;IAkClCzC,EAAA,CAAAc,SAAA,EAA6E;IAA7Ed,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAqB,YAAA,CAAAc,MAAA,oBAAAnC,MAAA,CAAAqB,YAAA,CAAAc,MAAA,cAA6E;;;;;IAsC9GzC,EADF,CAAAC,cAAA,cAA+C,mBAChB;IAAAD,EAAA,CAAAU,MAAA,sBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACvDX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACvBX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,gEAAyD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAE9DX,EADF,CAAAC,cAAA,iBAA0C,eAC9B;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC7BX,EAAA,CAAAU,MAAA,wBACF;IACFV,EADE,CAAAW,YAAA,EAAS,EACL;;;ADzMN;AACAhB,KAAK,CAACwE,QAAQ,CAAC,GAAGvE,aAAa,CAAC;AAqChC,OAAM,MAAOwE,0BAA0B;EAbvCC,YAAA;IAgBW,KAAA1C,YAAY,GAAwB,IAAI;IACxC,KAAAqC,mBAAmB,GAAY,IAAI;IACnC,KAAAM,cAAc,GAAW,IAAI,CAAC,CAAC;IAEhC,KAAAC,QAAQ,GAAG,IAAI1E,OAAO,EAAQ;IAC9B,KAAA2E,KAAK,GAAiB,IAAI;IAClC,KAAAP,eAAe,GAAwB,EAAE;IAEzC;IACA,KAAAJ,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAN,gBAAgB,GAAW,CAAC;IAC5B,KAAAG,oBAAoB,GAAW,CAAC;IAChC,KAAAE,mBAAmB,GAAW,CAAC;;EAE/Ba,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,mBAAmB,IAAI,IAAI,CAACrC,YAAY,EAAEc,MAAM,KAAK,SAAS,EAAE;MACvE,IAAI,CAACiC,oBAAoB,EAAE;IAC7B;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,QAAQ,CAACO,IAAI,EAAE;IACpB,IAAI,CAACP,QAAQ,CAACQ,QAAQ,EAAE;IAExB,IAAI,IAAI,CAACP,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACQ,OAAO,EAAE;IACtB;EACF;EAEQN,oBAAoBA,CAAA;IAC1B5E,QAAQ,CAAC,IAAI,CAACwE,cAAc,CAAC,CAC1BW,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACwE,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAAC,MAAK;MACd,IAAI,IAAI,CAACvD,YAAY,EAAEc,MAAM,KAAK,SAAS,EAAE;QAC3C,IAAI,CAAC0C,kBAAkB,EAAE;QACzB,IAAI,CAACC,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACN;EAEQD,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACxD,YAAY,EAAE;IAExB,MAAM0D,SAAS,GAAsB;MACnCC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,QAAQ,EAAE,IAAI,CAAC7D,YAAY,CAAC6D,QAAQ;MACpC9C,YAAY,EAAE,IAAI,CAACf,YAAY,CAACe,YAAY;MAC5CC,WAAW,EAAE,IAAI,CAAChB,YAAY,CAACgB;KAChC;IAED,IAAI,CAACsB,eAAe,CAACwB,IAAI,CAACJ,SAAS,CAAC;IAEpC;IACA,IAAI,IAAI,CAACpB,eAAe,CAACC,MAAM,GAAG,EAAE,EAAE;MACpC,IAAI,CAACD,eAAe,CAACyB,KAAK,EAAE;IAC9B;IAEA,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQP,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACzD,YAAY,EAAE;IAExB;IACA,MAAMiE,YAAY,GAAG,IAAI,CAACjE,YAAY,CAAC6D,QAAQ,GAAG,IAAI,CAACjC,gBAAgB;IACvE,IAAI,CAACA,gBAAgB,IAAIqC,YAAY,GAAG,GAAG;IAE3C;IACA,MAAMC,SAAS,GAAG,IAAI,CAAClE,YAAY,CAACe,YAAY,GAAG,IAAI,CAACgB,oBAAoB;IAC5E,IAAI,CAACA,oBAAoB,IAAImC,SAAS,GAAG,GAAG;IAE5C;IACA,MAAMC,UAAU,GAAG,IAAI,CAACnE,YAAY,CAACgB,WAAW,GAAG,IAAI,CAACiB,mBAAmB;IAC3E,IAAI,CAACA,mBAAmB,IAAIkC,UAAU,GAAG,GAAG;EAC9C;EAEQlB,mBAAmBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACmB,cAAc,EAAEC,aAAa,EAAE;MACvC;IACF;IAEA,MAAMC,GAAG,GAAG,IAAI,CAACF,cAAc,CAACC,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACD,GAAG,EAAE;MACR;IACF;IAEA;IACA,IAAI,IAAI,CAACzB,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACQ,OAAO,EAAE;IACtB;IAEA,MAAMmB,MAAM,GAAuB;MACjCC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE;QACJC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,YAAY;UACnBH,IAAI,EAAE,EAAE;UACRI,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,GAAG;UACZC,OAAO,EAAE;SACV,EACD;UACEN,KAAK,EAAE,cAAc;UACrBH,IAAI,EAAE,EAAE;UACRI,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,wBAAwB;UACzCC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,GAAG;UACZC,OAAO,EAAE;SACV;OAEJ;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,WAAW,EAAE;UACXC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE;SACZ;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,KAAK;YACflB,MAAM,EAAE;cACNmB,aAAa,EAAE,IAAI;cACnBC,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BC,IAAI,EAAE,EAAE;gBACRC,MAAM,EAAE;;;WAGb;UACDC,OAAO,EAAE;YACPpB,eAAe,EAAE,oBAAoB;YACrCqB,UAAU,EAAE,SAAS;YACrBC,SAAS,EAAE,SAAS;YACpBvB,WAAW,EAAE,SAAS;YACtBE,WAAW,EAAE,CAAC;YACdsB,YAAY,EAAE,CAAC;YACfC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAI;gBACjB,MAAM9C,SAAS,GAAG,IAAIC,IAAI,CAAC6C,OAAO,CAAC,CAAC,CAAC,CAAC5B,KAAK,CAAC;gBAC5C,OAAOlB,SAAS,CAAC+C,kBAAkB,EAAE;cACvC;;;SAGL;QACDC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDhB,OAAO,EAAE,IAAI;YACbY,KAAK,EAAE;cACLZ,OAAO,EAAE,IAAI;cACbiB,IAAI,EAAE,MAAM;cACZd,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDY,KAAK,EAAE;cACLC,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAEC,KAAK;gBAC7B,MAAMtD,SAAS,GAAG,IAAI,CAACuD,gBAAgB,CAACF,KAAe,CAAC;gBACxD,OAAO,IAAIpD,IAAI,CAACD,SAAS,CAAC,CAAC+C,kBAAkB,CAAC,EAAE,EAAE;kBAChDS,IAAI,EAAE,SAAS;kBACfC,MAAM,EAAE;iBACT,CAAC;cACJ,CAAC;cACDrB,IAAI,EAAE;gBACJC,MAAM,EAAE;;;WAGb;UACDqB,CAAC,EAAE;YACD5C,IAAI,EAAE,QAAQ;YACdmB,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,MAAM;YAChBW,KAAK,EAAE;cACLZ,OAAO,EAAE,IAAI;cACbiB,IAAI,EAAE,cAAc;cACpBd,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDoB,GAAG,EAAE,CAAC;YACNnF,GAAG,EAAE,GAAG;YACR2E,KAAK,EAAE;cACLf,IAAI,EAAE;gBACJC,MAAM,EAAE;;;WAGb;UACDuB,EAAE,EAAE;YACF9C,IAAI,EAAE,QAAQ;YACdmB,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,OAAO;YACjBW,KAAK,EAAE;cACLZ,OAAO,EAAE,IAAI;cACbiB,IAAI,EAAE,cAAc;cACpBd,IAAI,EAAE;gBACJC,MAAM,EAAE,mBAAmB;gBAC3BE,MAAM,EAAE;;aAEX;YACDoB,GAAG,EAAE,CAAC;YACNE,IAAI,EAAE;cACJC,eAAe,EAAE;aAClB;YACDX,KAAK,EAAE;cACLf,IAAI,EAAE;gBACJC,MAAM,EAAE;;;;SAIf;QACD0B,SAAS,EAAE;UACTC,QAAQ,EAAE,GAAG;UACbC,MAAM,EAAE;;;KAGb;IAED,IAAI,CAAC/E,KAAK,GAAG,IAAI7E,KAAK,CAACsG,GAAG,EAAEE,MAAM,CAAC;IACnC,IAAI,CAACR,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACnB,KAAK,IAAI,IAAI,CAACP,eAAe,CAACC,MAAM,KAAK,CAAC,EAAE;MACpD;IACF;IAEA,MAAMoC,MAAM,GAAG,IAAI,CAACrC,eAAe,CAACuF,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACnE,SAAS,CAACoE,WAAW,EAAE,CAAC;IAC/E,MAAMC,YAAY,GAAG,IAAI,CAAC1F,eAAe,CAACuF,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACjE,QAAQ,CAAC;IACtE,MAAMoE,UAAU,GAAG,IAAI,CAAC3F,eAAe,CAACuF,GAAG,CAACC,KAAK,IAAIA,KAAK,CAAC9G,WAAW,CAAC;IAEvE,IAAI,CAAC6B,KAAK,CAAC6B,IAAI,CAACC,MAAM,GAAGA,MAAM;IAC/B,IAAI,CAAC9B,KAAK,CAAC6B,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACF,IAAI,GAAGsD,YAAY;IAC/C,IAAI,CAACnF,KAAK,CAAC6B,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACF,IAAI,GAAGuD,UAAU;IAE7C,IAAI,CAACpF,KAAK,CAACqF,MAAM,CAAC,MAAM,CAAC;EAC3B;EAEAtH,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE,OAAO,MAAM;IAErC,QAAQ,IAAI,CAACA,YAAY,CAACc,MAAM;MAC9B,KAAK,SAAS;QAAE,OAAO,UAAU;MACjC,KAAK,SAAS;QAAE,OAAO,iBAAiB;MACxC,KAAK,WAAW;QAAE,OAAO,cAAc;MACvC,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B;QAAS,OAAO,MAAM;IACxB;EACF;EAEAH,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACX,YAAY,EAAE,OAAO,2BAA2B;IAE1D,QAAQ,IAAI,CAACA,YAAY,CAACc,MAAM;MAC9B,KAAK,SAAS;QAAE,OAAO,wBAAwB;MAC/C,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C,KAAK,WAAW;QAAE,OAAO,wBAAwB;MACjD,KAAK,QAAQ;QAAE,OAAO,sBAAsB;MAC5C;QAAS,OAAO,2BAA2B;IAC7C;EACF;EAEAzB,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACW,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACmI,mBAAmB,EAAE;MAChE,OAAO,gBAAgB;IACzB;IAEA,MAAMC,GAAG,GAAG,IAAIxE,IAAI,EAAE;IACtB,MAAMyE,SAAS,GAAG,IAAI,CAACrI,YAAY,CAACmI,mBAAmB,CAACG,OAAO,EAAE,GAAGF,GAAG,CAACE,OAAO,EAAE;IAEjF,IAAID,SAAS,IAAI,CAAC,EAAE;MAClB,OAAO,aAAa;IACtB;IAEA,MAAME,OAAO,GAAGrG,IAAI,CAACsG,KAAK,CAACH,SAAS,GAAG,KAAK,CAAC;IAC7C,MAAMI,OAAO,GAAGvG,IAAI,CAACsG,KAAK,CAAEH,SAAS,GAAG,KAAK,GAAI,IAAI,CAAC;IAEtD,IAAIE,OAAO,GAAG,CAAC,EAAE;MACf,OAAO,GAAGA,OAAO,KAAKE,OAAO,GAAG;IAClC,CAAC,MAAM;MACL,OAAO,GAAGA,OAAO,GAAG;IACtB;EACF;EAEAxH,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjB,YAAY,EAAE,OAAO,IAAI;IAEnC,MAAMoI,GAAG,GAAG,IAAIxE,IAAI,EAAE;IACtB,MAAM8E,OAAO,GAAGN,GAAG,CAACE,OAAO,EAAE,GAAG,IAAI,CAACtI,YAAY,CAAC2I,SAAS,CAACL,OAAO,EAAE;IAErE,MAAMC,OAAO,GAAGrG,IAAI,CAACsG,KAAK,CAACE,OAAO,GAAG,KAAK,CAAC;IAC3C,MAAMD,OAAO,GAAGvG,IAAI,CAACsG,KAAK,CAAEE,OAAO,GAAG,KAAK,GAAI,IAAI,CAAC;IAEpD,IAAIH,OAAO,GAAG,CAAC,EAAE;MACf,OAAO,GAAGA,OAAO,KAAKE,OAAO,GAAG;IAClC,CAAC,MAAM;MACL,OAAO,GAAGA,OAAO,GAAG;IACtB;EACF;EAEAvJ,UAAUA,CAAA;IACR;IACA0J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC7I,YAAY,EAAEwB,MAAM,CAAC;EACtE;EAEA1C,SAASA,CAAA;IACP;IACA8J,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC7I,YAAY,EAAEwB,MAAM,CAAC;EACrE;EAEAY,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACpC,YAAY,EAAE,OAAO,CAAC;IAEhC,MAAMoI,GAAG,GAAG,IAAIxE,IAAI,EAAE;IACtB,MAAM8E,OAAO,GAAGN,GAAG,CAACE,OAAO,EAAE,GAAG,IAAI,CAACtI,YAAY,CAAC2I,SAAS,CAACL,OAAO,EAAE;IACrE,OAAOI,OAAO,GAAG,KAAK,CAAC,CAAC;EAC1B;EAEAnI,YAAYA,CAAA;IACV,OAAO,CACL;MAAEH,IAAI,EAAE,YAAY;MAAEC,WAAW,EAAE;IAA6B,CAAE,EAClE;MAAED,IAAI,EAAE,gBAAgB;MAAEC,WAAW,EAAE;IAA8B,CAAE,EACvE;MAAED,IAAI,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAA0B,CAAE,EACpE;MAAED,IAAI,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAAwB,CAAE,EAClE;MAAED,IAAI,EAAE,yBAAyB;MAAEC,WAAW,EAAE;IAA6B,CAAE,EAC/E;MAAED,IAAI,EAAE,mBAAmB;MAAEC,WAAW,EAAE;IAAmB,CAAE,CAChE;EACH;;;uCAzVWoC,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAqG,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAA3E,GAAA;QAAA,IAAA2E,EAAA;;;;;;;;;;;;;;;;;;UC2JvC5K,EA1MA,CAAAoB,UAAA,IAAAyJ,yCAAA,mBAAqD,IAAAC,yCAAA,kBA0MN;;;UA1Md9K,EAAA,CAAA6B,UAAA,SAAAoE,GAAA,CAAAtE,YAAA,CAAkB;UA0MzB3B,EAAA,CAAAc,SAAA,EAAmB;UAAnBd,EAAA,CAAA6B,UAAA,UAAAoE,GAAA,CAAAtE,YAAA,CAAmB;;;qBDpKzCrC,YAAY,EAAAyL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,aAAA,EACZ5L,aAAa,EACbC,eAAe,EAAA4L,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf7L,aAAa,EAAA8L,EAAA,CAAAC,OAAA,EACb9L,oBAAoB,EAAA+L,EAAA,CAAAC,cAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}