{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/expansion\";\nimport * as i6 from \"@angular/material/chips\";\nimport * as i7 from \"@angular/material/table\";\nfunction CliGuideComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", feature_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r1.description);\n  }\n}\nfunction CliGuideComponent_mat_card_28_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_card_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 27)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 21)(10, \"div\", 22)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Commands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"pre\")(16, \"code\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, CliGuideComponent_mat_card_28_div_18_Template, 5, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(method_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.description);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(method_r2.commands);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Flag\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r3.flag);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r4.type);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r5.description);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Default\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"code\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48);\n    i0.ɵɵtemplate(1, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template, 2, 1, \"code\", 49)(2, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", option_r6.default);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 50);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 51);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"h4\");\n    i0.ɵɵtext(2, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"table\", 38);\n    i0.ɵɵelementContainerStart(4, 39);\n    i0.ɵɵtemplate(5, CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template, 2, 0, \"th\", 40)(6, CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template, 3, 1, \"td\", 41);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 42);\n    i0.ɵɵtemplate(8, CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template, 2, 0, \"th\", 40)(9, CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template, 3, 1, \"td\", 41);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 43);\n    i0.ɵɵtemplate(11, CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template, 2, 0, \"th\", 40)(12, CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template, 2, 1, \"td\", 41);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 44);\n    i0.ɵɵtemplate(14, CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template, 2, 0, \"th\", 40)(15, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template, 3, 2, \"td\", 41);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(16, CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template, 1, 0, \"tr\", 45)(17, CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template, 1, 0, \"tr\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dataSource\", command_r7.options);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r7.optionColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r7.optionColumns);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 22)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Output\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const example_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"p\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 21)(4, \"div\", 22)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Command\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"pre\")(10, \"code\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template, 9, 1, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const example_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(example_r9.description);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(example_r9.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"h4\");\n    i0.ɵɵtext(2, \"Examples\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template, 13, 3, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", command_r7.examples);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h4\");\n    i0.ɵɵtext(2, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 59)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 30)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"code\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-panel-description\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"div\", 33)(9, \"h4\");\n    i0.ɵɵtext(10, \"Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 21)(12, \"pre\")(13, \"code\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, CliGuideComponent_mat_expansion_panel_36_div_15_Template, 18, 3, \"div\", 34)(16, CliGuideComponent_mat_expansion_panel_36_div_16_Template, 4, 1, \"div\", 35)(17, CliGuideComponent_mat_expansion_panel_36_div_17_Template, 8, 1, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"spt \", command_r7.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", command_r7.description, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(command_r7.usage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.options.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.examples.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_card_44_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(integration_r10.notes);\n  }\n}\nfunction CliGuideComponent_mat_card_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 60)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 21)(10, \"div\", 22)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"pre\")(16, \"code\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, CliGuideComponent_mat_card_44_div_18_Template, 5, 1, \"div\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r10 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(integration_r10.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(integration_r10.platform);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(integration_r10.description);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(integration_r10.filename);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(integration_r10.config);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", integration_r10.notes);\n  }\n}\nfunction CliGuideComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r11.key);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r11.description);\n  }\n}\nexport let CliGuideComponent = /*#__PURE__*/(() => {\n  class CliGuideComponent {\n    constructor() {\n      this.optionColumns = ['flag', 'type', 'description', 'default'];\n      this.cliFeatures = [{\n        title: 'Security Scanning',\n        description: 'Comprehensive security analysis for blockchain applications',\n        icon: 'security',\n        color: '#1976d2'\n      }, {\n        title: 'Multiple Formats',\n        description: 'Output results in JSON, YAML, CSV, or human-readable formats',\n        icon: 'description',\n        color: '#4caf50'\n      }, {\n        title: 'CI/CD Integration',\n        description: 'Easy integration with continuous integration pipelines',\n        icon: 'integration_instructions',\n        color: '#ff9800'\n      }, {\n        title: 'Configurable',\n        description: 'Flexible configuration options for different environments',\n        icon: 'tune',\n        color: '#9c27b0'\n      }];\n      this.installationMethods = [{\n        title: 'From Source',\n        description: 'Build from source code',\n        icon: 'code',\n        commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\ngo build -o spt cmd/main.go`,\n        notes: 'Requires Go 1.21+ to be installed'\n      }, {\n        title: 'Using Make',\n        description: 'Build using Makefile',\n        icon: 'build',\n        commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\nmake cli`,\n        notes: 'Binary will be created in build/ directory'\n      }, {\n        title: 'Go Install',\n        description: 'Install directly with Go',\n        icon: 'download',\n        commands: `go install github.com/blockchain-spt/cmd/spt@latest`,\n        notes: 'Installs to $GOPATH/bin'\n      }];\n      this.cliCommands = [{\n        name: 'scan',\n        description: 'Perform security scan on files or directories',\n        usage: 'spt scan [flags] [path]',\n        options: [{\n          flag: '--chain',\n          type: 'string',\n          description: 'Blockchain chain to analyze',\n          default: 'all'\n        }, {\n          flag: '--format',\n          type: 'string',\n          description: 'Output format (json, yaml, csv, table)',\n          default: 'table'\n        }, {\n          flag: '--output',\n          type: 'string',\n          description: 'Output file path'\n        }, {\n          flag: '--severity',\n          type: 'string',\n          description: 'Minimum severity level',\n          default: 'medium'\n        }, {\n          flag: '--recursive',\n          type: 'boolean',\n          description: 'Scan directories recursively',\n          default: 'true'\n        }],\n        examples: [{\n          command: 'spt scan ./contracts',\n          description: 'Scan all files in contracts directory',\n          output: `Scanning ./contracts...\nFound 3 issues:\n  HIGH: Potential reentrancy in contract.sol:42\n  MEDIUM: Unchecked return value in token.sol:15\n  MEDIUM: Gas optimization opportunity in utils.sol:8`\n        }, {\n          command: 'spt scan --chain ethereum --format json ./src',\n          description: 'Scan for Ethereum-specific issues and output as JSON'\n        }],\n        notes: 'Use --help flag with any command to see detailed usage information'\n      }, {\n        name: 'audit',\n        description: 'Perform comprehensive security audit',\n        usage: 'spt audit [flags] [path]',\n        options: [{\n          flag: '--generate-report',\n          type: 'boolean',\n          description: 'Generate detailed report',\n          default: 'false'\n        }, {\n          flag: '--report-path',\n          type: 'string',\n          description: 'Report output path',\n          default: './audit-report.html'\n        }, {\n          flag: '--template',\n          type: 'string',\n          description: 'Report template',\n          default: 'standard'\n        }],\n        examples: [{\n          command: 'spt audit --generate-report ./project',\n          description: 'Perform audit and generate HTML report'\n        }]\n      }, {\n        name: 'check',\n        description: 'Run specific security checks',\n        usage: 'spt check [subcommand] [flags] [path]',\n        options: [{\n          flag: '--fix',\n          type: 'boolean',\n          description: 'Attempt to fix issues automatically',\n          default: 'false'\n        }],\n        examples: [{\n          command: 'spt check deps --fix',\n          description: 'Check dependencies and fix known vulnerabilities'\n        }, {\n          command: 'spt check env',\n          description: 'Check environment configuration for security issues'\n        }]\n      }];\n      this.integrationExamples = [{\n        platform: 'GitHub Actions',\n        description: 'Integrate SPT into GitHub Actions workflow',\n        icon: 'integration_instructions',\n        filename: '.github/workflows/security.yml',\n        config: `name: Security Scan\non: [push, pull_request]\n\njobs:\n  security:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - uses: actions/setup-go@v3\n        with:\n          go-version: '1.21'\n      - name: Install SPT\n        run: go install github.com/blockchain-spt/cmd/spt@latest\n      - name: Run Security Scan\n        run: spt scan --format json --output security-report.json ./\n      - name: Upload Results\n        uses: actions/upload-artifact@v3\n        with:\n          name: security-report\n          path: security-report.json`,\n        notes: 'Add GITHUB_TOKEN to secrets for private repositories'\n      }, {\n        platform: 'GitLab CI',\n        description: 'Integrate SPT into GitLab CI/CD pipeline',\n        icon: 'integration_instructions',\n        filename: '.gitlab-ci.yml',\n        config: `security_scan:\n  stage: test\n  image: golang:1.21\n  script:\n    - go install github.com/blockchain-spt/cmd/spt@latest\n    - spt scan --format json --output security-report.json ./\n    - spt audit --generate-report --report-path audit-report.html ./\n  artifacts:\n    reports:\n      junit: security-report.json\n    paths:\n      - audit-report.html\n    expire_in: 1 week\n  only:\n    - merge_requests\n    - main`,\n        notes: 'Configure artifact storage for report persistence'\n      }];\n      this.configExample = `{\n  \"scanning\": {\n    \"chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n    \"severity_threshold\": \"medium\",\n    \"max_file_size\": \"10MB\",\n    \"timeout\": \"5m\",\n    \"parallel_scans\": 4\n  },\n  \"output\": {\n    \"format\": \"table\",\n    \"colors\": true,\n    \"verbose\": false\n  },\n  \"rules\": {\n    \"ethereum\": {\n      \"check_reentrancy\": true,\n      \"check_overflow\": true,\n      \"check_access_control\": true\n    },\n    \"bitcoin\": {\n      \"check_key_management\": true,\n      \"check_transaction_validation\": true\n    }\n  },\n  \"integrations\": {\n    \"vscode\": {\n      \"enabled\": true,\n      \"server_url\": \"http://localhost:8080\"\n    }\n  }\n}`;\n      this.configOptions = [{\n        key: 'scanning.chains',\n        description: 'Array of blockchain chains to analyze'\n      }, {\n        key: 'scanning.severity_threshold',\n        description: 'Minimum severity level to report'\n      }, {\n        key: 'output.format',\n        description: 'Default output format for scan results'\n      }, {\n        key: 'rules.ethereum',\n        description: 'Ethereum-specific security rules configuration'\n      }, {\n        key: 'rules.bitcoin',\n        description: 'Bitcoin-specific security rules configuration'\n      }, {\n        key: 'integrations',\n        description: 'Configuration for IDE and tool integrations'\n      }];\n    }\n    static {\n      this.ɵfac = function CliGuideComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CliGuideComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CliGuideComponent,\n        selectors: [[\"app-cli-guide\"]],\n        decls: 74,\n        vars: 6,\n        consts: [[1, \"cli-guide-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [1, \"cli-overview\"], [1, \"overview-card\"], [\"mat-card-avatar\", \"\"], [1, \"cli-features\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [\"animationDuration\", \"300ms\", 1, \"cli-tabs\"], [\"label\", \"Installation\"], [1, \"tab-content\"], [1, \"installation-methods\"], [\"class\", \"method-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Commands\"], [1, \"commands-list\"], [\"class\", \"command-panel\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"CI/CD Integration\"], [1, \"integration-examples\"], [\"class\", \"integration-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-card\"], [1, \"code-block\"], [1, \"code-header\"], [1, \"config-description\"], [1, \"config-options\"], [\"class\", \"config-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature\"], [1, \"method-card\"], [\"class\", \"method-notes\", 4, \"ngIf\"], [1, \"method-notes\"], [1, \"command-panel\"], [1, \"command-name\"], [1, \"command-details\"], [1, \"usage-section\"], [\"class\", \"options-section\", 4, \"ngIf\"], [\"class\", \"examples-section\", 4, \"ngIf\"], [\"class\", \"notes-section\", 4, \"ngIf\"], [1, \"options-section\"], [\"mat-table\", \"\", 1, \"options-table\", 3, \"dataSource\"], [\"matColumnDef\", \"flag\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"default\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [4, \"ngIf\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"examples-section\"], [\"class\", \"example\", 4, \"ngFor\", \"ngForOf\"], [1, \"example\"], [1, \"example-description\"], [\"class\", \"output-block\", 4, \"ngIf\"], [1, \"output-block\"], [1, \"notes-section\"], [1, \"notes-content\"], [1, \"integration-card\"], [\"class\", \"integration-notes\", 4, \"ngIf\"], [1, \"integration-notes\"], [1, \"config-option\"]],\n        template: function CliGuideComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"terminal\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(5, \" CLI Guide \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 2);\n            i0.ɵɵtext(7, \" Complete guide to the SPT command-line interface \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-header\")(11, \"mat-icon\", 5);\n            i0.ɵɵtext(12, \"info\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-title\");\n            i0.ɵɵtext(14, \"SPT CLI Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"mat-card-subtitle\");\n            i0.ɵɵtext(16, \"Powerful command-line security scanning tool\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"mat-card-content\")(18, \"p\");\n            i0.ɵɵtext(19, \"The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 6);\n            i0.ɵɵtemplate(21, CliGuideComponent_div_21_Template, 8, 5, \"div\", 7);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(22, \"mat-tab-group\", 8)(23, \"mat-tab\", 9)(24, \"div\", 10)(25, \"h2\");\n            i0.ɵɵtext(26, \"Installation Methods\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 11);\n            i0.ɵɵtemplate(28, CliGuideComponent_mat_card_28_Template, 19, 5, \"mat-card\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"mat-tab\", 13)(30, \"div\", 10)(31, \"h2\");\n            i0.ɵɵtext(32, \"Available Commands\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p\");\n            i0.ɵɵtext(34, \"Complete reference for all SPT CLI commands and their options.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 14);\n            i0.ɵɵtemplate(36, CliGuideComponent_mat_expansion_panel_36_Template, 18, 6, \"mat-expansion-panel\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"mat-tab\", 16)(38, \"div\", 10)(39, \"h2\");\n            i0.ɵɵtext(40, \"CI/CD Integration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"p\");\n            i0.ɵɵtext(42, \"Integrate SPT CLI into your continuous integration and deployment pipelines.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 17);\n            i0.ɵɵtemplate(44, CliGuideComponent_mat_card_44_Template, 19, 6, \"mat-card\", 18);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"mat-tab\", 19)(46, \"div\", 10)(47, \"h2\");\n            i0.ɵɵtext(48, \"Configuration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"p\");\n            i0.ɵɵtext(50, \"Configure SPT CLI for your development environment and preferences.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"mat-card\", 20)(52, \"mat-card-header\")(53, \"mat-icon\", 5);\n            i0.ɵɵtext(54, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"mat-card-title\");\n            i0.ɵɵtext(56, \"Configuration File\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"mat-card-subtitle\");\n            i0.ɵɵtext(58, \"spt.config.json\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"mat-card-content\")(60, \"div\", 21)(61, \"div\", 22)(62, \"mat-icon\");\n            i0.ɵɵtext(63, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"span\");\n            i0.ɵɵtext(65, \"JSON Configuration\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"pre\")(67, \"code\");\n            i0.ɵɵtext(68);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(69, \"div\", 23)(70, \"h4\");\n            i0.ɵɵtext(71, \"Configuration Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"div\", 24);\n            i0.ɵɵtemplate(73, CliGuideComponent_div_73_Template, 5, 2, \"div\", 25);\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngForOf\", ctx.cliFeatures);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngForOf\", ctx.installationMethods);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.cliCommands);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.integrationExamples);\n            i0.ɵɵadvance(24);\n            i0.ɵɵtextInterpolate(ctx.configExample);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.configOptions);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatExpansionModule, i5.MatExpansionPanel, i5.MatExpansionPanelHeader, i5.MatExpansionPanelTitle, i5.MatExpansionPanelDescription, MatChipsModule, i6.MatChip, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow],\n        styles: [\".cli-guide-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.page-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;color:#1976d2;margin:0 0 8px}.page-subtitle[_ngcontent-%COMP%]{color:#666;font-size:1.1em;margin:0}.cli-overview[_ngcontent-%COMP%]{margin-bottom:32px}.overview-card[_ngcontent-%COMP%]{border:1px solid #e0e0e0}.cli-features[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:16px;margin-top:16px}.feature[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;padding:12px;background:#f5f5f5;border-radius:8px}.feature[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:2px}.feature[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:4px}.feature[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.9em}.cli-tabs[_ngcontent-%COMP%]{margin-bottom:32px}.tab-content[_ngcontent-%COMP%]{padding:24px 0}.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:8px}.installation-methods[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:24px;margin-top:24px}.method-card[_ngcontent-%COMP%]{height:fit-content}.method-notes[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-top:12px;padding:8px 12px;background:#e3f2fd;border-radius:4px;color:#1976d2}.commands-list[_ngcontent-%COMP%]{margin-top:24px}.command-panel[_ngcontent-%COMP%]{margin-bottom:8px;border:1px solid #e0e0e0;border-radius:8px}.command-name[_ngcontent-%COMP%]{background:#f5f5f5;padding:4px 8px;border-radius:4px;font-family:Courier New,monospace;font-weight:500}.command-details[_ngcontent-%COMP%]{padding:16px 0}.usage-section[_ngcontent-%COMP%], .options-section[_ngcontent-%COMP%], .examples-section[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]{margin-bottom:24px}.usage-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .options-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .examples-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#1976d2}.code-block[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;overflow:hidden;margin-bottom:16px}.code-header[_ngcontent-%COMP%]{background:#f5f5f5;padding:8px 16px;display:flex;align-items:center;gap:8px;font-weight:500;border-bottom:1px solid #e0e0e0}.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{margin:0;padding:16px;background:#fafafa;overflow-x:auto}.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:.9em}.output-block[_ngcontent-%COMP%]{border:1px solid #4caf50;border-radius:8px;overflow:hidden;margin-top:8px}.output-block[_ngcontent-%COMP%]   .code-header[_ngcontent-%COMP%]{background:#e8f5e8;border-bottom-color:#4caf50}.output-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background:#f1f8e9}.options-table[_ngcontent-%COMP%]{width:100%;border:1px solid #e0e0e0;border-radius:8px}.options-table[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:#f5f5f5;padding:2px 6px;border-radius:4px;font-size:.9em}.example[_ngcontent-%COMP%]{margin-bottom:24px;padding:16px;background:#f9f9f9;border-radius:8px}.example-description[_ngcontent-%COMP%]{margin:0 0 12px;font-weight:500;color:#1976d2}.notes-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:8px;padding:12px;background:#e3f2fd;border-radius:8px;color:#1976d2}.notes-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.integration-examples[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(500px,1fr));gap:24px;margin-top:24px}.integration-card[_ngcontent-%COMP%]{height:fit-content}.integration-notes[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-top:12px;padding:8px 12px;background:#fff3e0;border-radius:4px;color:#f57c00}.config-card[_ngcontent-%COMP%], .config-description[_ngcontent-%COMP%]{margin-top:24px}.config-description[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;color:#1976d2}.config-options[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:16px}.config-option[_ngcontent-%COMP%]{padding:12px;background:#f5f5f5;border-radius:8px}.config-option[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:4px;color:#1976d2}.config-option[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.9em}@media (max-width: 768px){.cli-features[_ngcontent-%COMP%], .installation-methods[_ngcontent-%COMP%], .integration-examples[_ngcontent-%COMP%], .config-options[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return CliGuideComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}