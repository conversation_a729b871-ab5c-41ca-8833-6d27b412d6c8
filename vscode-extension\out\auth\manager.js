"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationManager = void 0;
const vscode = __importStar(require("vscode"));
const crypto = __importStar(require("crypto"));
class AuthenticationManager {
    constructor(context, configManager) {
        this.context = context;
        this.configManager = configManager;
        this.authState = { isAuthenticated: false };
        this.authChangeEmitter = new vscode.EventEmitter();
        this.onAuthStateChanged = this.authChangeEmitter.event;
        this.loadAuthState();
    }
    /**
     * Check if user is currently authenticated
     */
    isAuthenticated() {
        // Check if token exists and is not expired
        if (!this.authState.token || !this.authState.expiresAt) {
            return false;
        }
        // Check if token is expired
        if (Date.now() >= this.authState.expiresAt) {
            this.clearAuthState();
            return false;
        }
        return this.authState.isAuthenticated;
    }
    /**
     * Get current authentication state
     */
    getAuthState() {
        return { ...this.authState };
    }
    /**
     * Get current JWT token
     */
    getToken() {
        if (this.isAuthenticated()) {
            return this.authState.token;
        }
        return undefined;
    }
    /**
     * Initiate authentication flow - redirect to web portal
     */
    async authenticate() {
        try {
            // Generate a unique state parameter for security
            const state = crypto.randomBytes(32).toString('hex');
            const serverUrl = this.configManager.getServerUrl();
            // Store pending auth state
            await this.context.globalState.update(AuthenticationManager.PENDING_AUTH_KEY, {
                state,
                timestamp: Date.now()
            });
            // Construct authentication URL
            const authUrl = `${serverUrl}/auth/vscode?state=${state}&redirect_uri=vscode://blockchain-security-protocol.spt/auth/callback`;
            // Show information message to user
            const result = await vscode.window.showInformationMessage('Authentication required. You will be redirected to the SPT web portal to sign in.', 'Open Browser', 'Cancel');
            if (result === 'Open Browser') {
                // Open the authentication URL in the default browser
                await vscode.env.openExternal(vscode.Uri.parse(authUrl));
                // Show progress while waiting for authentication
                return await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: 'Waiting for authentication...',
                    cancellable: true
                }, async (progress, token) => {
                    return new Promise((resolve) => {
                        // Set up timeout (5 minutes)
                        const timeout = setTimeout(() => {
                            resolve(false);
                        }, 5 * 60 * 1000);
                        // Listen for authentication completion
                        const disposable = this.onAuthStateChanged((authState) => {
                            if (authState.isAuthenticated) {
                                clearTimeout(timeout);
                                disposable.dispose();
                                resolve(true);
                            }
                        });
                        // Handle cancellation
                        token.onCancellationRequested(() => {
                            clearTimeout(timeout);
                            disposable.dispose();
                            resolve(false);
                        });
                    });
                });
            }
            return false;
        }
        catch (error) {
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
            return false;
        }
    }
    /**
     * Handle authentication callback from web portal
     */
    async handleAuthCallback(token, user, expiresIn) {
        try {
            // Calculate expiration time
            const expiresAt = Date.now() + (expiresIn * 1000);
            // Update auth state
            this.authState = {
                isAuthenticated: true,
                token,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role
                },
                expiresAt
            };
            // Save to persistent storage
            await this.saveAuthState();
            // Clear pending auth
            await this.context.globalState.update(AuthenticationManager.PENDING_AUTH_KEY, undefined);
            // Notify listeners
            this.authChangeEmitter.fire(this.authState);
            vscode.window.showInformationMessage(`Successfully authenticated as ${user.username}`);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to complete authentication: ${error}`);
        }
    }
    /**
     * Sign out the user
     */
    async signOut() {
        this.clearAuthState();
        await this.saveAuthState();
        this.authChangeEmitter.fire(this.authState);
        vscode.window.showInformationMessage('Successfully signed out');
    }
    /**
     * Validate pending authentication state
     */
    async validatePendingAuth(state) {
        const pendingAuth = this.context.globalState.get(AuthenticationManager.PENDING_AUTH_KEY);
        if (!pendingAuth || pendingAuth.state !== state) {
            return false;
        }
        // Check if the auth request is not too old (10 minutes)
        const maxAge = 10 * 60 * 1000;
        if (Date.now() - pendingAuth.timestamp > maxAge) {
            await this.context.globalState.update(AuthenticationManager.PENDING_AUTH_KEY, undefined);
            return false;
        }
        return true;
    }
    /**
     * Load authentication state from storage
     */
    loadAuthState() {
        const savedState = this.context.globalState.get(AuthenticationManager.AUTH_STATE_KEY);
        if (savedState) {
            this.authState = savedState;
            // Check if token is expired
            if (!this.isAuthenticated()) {
                this.clearAuthState();
            }
        }
    }
    /**
     * Save authentication state to storage
     */
    async saveAuthState() {
        await this.context.globalState.update(AuthenticationManager.AUTH_STATE_KEY, this.authState);
    }
    /**
     * Clear authentication state
     */
    clearAuthState() {
        this.authState = { isAuthenticated: false };
    }
    /**
     * Dispose resources
     */
    dispose() {
        this.authChangeEmitter.dispose();
    }
}
exports.AuthenticationManager = AuthenticationManager;
AuthenticationManager.AUTH_STATE_KEY = 'spt.authState';
AuthenticationManager.PENDING_AUTH_KEY = 'spt.pendingAuth';
//# sourceMappingURL=manager.js.map