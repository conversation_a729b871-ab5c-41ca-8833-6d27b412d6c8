import { Routes } from '@angular/router';
import { AuthGuard, GuestGuard, RoleGuard } from './guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  // Authentication routes
  {
    path: 'login',
    loadComponent: () => import('./components/auth/login.component').then(m => m.LoginComponent),
    canActivate: [GuestGuard]
  },
  {
    path: 'register',
    loadComponent: () => import('./components/auth/register.component').then(m => m.RegisterComponent),
    canActivate: [GuestGuard]
  },
  {
    path: 'auth/vscode',
    redirectTo: '/login',
    pathMatch: 'full'
  },
  // Protected routes
  {
    path: 'dashboard',
    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'scan',
    loadComponent: () => import('./components/scan/scan.component').then(m => m.ScanComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'scan/:id',
    loadComponent: () => import('./components/scan-details/scan-details.component').then(m => m.ScanDetailsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'checklist',
    loadComponent: () => import('./components/checklist/checklist.component').then(m => m.ChecklistComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'reports',
    loadComponent: () => import('./components/reports/reports.component').then(m => m.ReportsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'settings',
    loadComponent: () => import('./components/settings/settings.component').then(m => m.SettingsComponent),
    canActivate: [AuthGuard]
  },
  // Projects management (admin only)
  {
    path: 'projects',
    loadComponent: () => import('./components/projects/projects.component').then(m => m.ProjectsComponent),
    canActivate: [AuthGuard, RoleGuard],
    data: { roles: ['admin', 'manager'] }
  },
  // Documentation routes (no authentication required)
  {
    path: 'doc',
    loadChildren: () => import('./modules/documentation/documentation.module').then(m => m.DocumentationModule)
  },
  // Test route (no authentication required)
  {
    path: 'test',
    loadComponent: () => import('./components/test/test.component').then(m => m.TestComponent)
  },
  // Error pages
  {
    path: 'unauthorized',
    loadComponent: () => import('./components/error/unauthorized.component').then(m => m.UnauthorizedComponent)
  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
