{"version": 3, "file": "uriHandler.js", "sourceRoot": "", "sources": ["../../src/auth/uriHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,MAAa,cAAc;IACvB,YAAoB,WAAkC;QAAlC,gBAAW,GAAX,WAAW,CAAuB;IAAG,CAAC;IAE1D,KAAK,CAAC,SAAS,CAAC,GAAe;QAC3B,IAAI;YACA,gBAAgB;YAChB,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAE7C,IAAI,IAAI,KAAK,gBAAgB,EAAE;gBAC3B,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACxC;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;aACjE;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;SACpE;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAsB;QACnD,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACjC,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACjC,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACjC,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE1C,oBAAoB;QACpB,IAAI,KAAK,EAAE;YACP,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO;SACV;QAED,+BAA+B;QAC/B,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4CAA4C,CAAC,CAAC;YAC7E,OAAO;SACV;QAED,2BAA2B;QAC3B,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iDAAiD,CAAC,CAAC;YAClF,OAAO;SACV;QAED,IAAI;YACA,kBAAkB;YAClB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;YACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAEjD,0BAA0B;YAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;SAC5E;QAAC,OAAO,UAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wCAAwC,UAAU,EAAE,CAAC,CAAC;SACxF;IACL,CAAC;CACJ;AAvDD,wCAuDC"}