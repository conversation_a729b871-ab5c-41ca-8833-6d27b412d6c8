{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/api.service\";\nimport * as i3 from \"../../services/websocket.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/button\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"@angular/material/progress-bar\";\nfunction ScanComponent_mat_card_3_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r0.currentScanProgress.currentFile, \" \");\n  }\n}\nfunction ScanComponent_mat_card_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 32)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Scan in Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 33)(8, \"div\", 34)(9, \"div\", 35)(10, \"span\", 36);\n    i0.ɵɵtext(11, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 37);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 35)(16, \"span\", 36);\n    i0.ɵɵtext(17, \"Files:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 37);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 35)(21, \"span\", 36);\n    i0.ɵɵtext(22, \"Issues Found:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 37);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(25, \"mat-progress-bar\", 38);\n    i0.ɵɵelementStart(26, \"div\", 39);\n    i0.ɵɵtext(27);\n    i0.ɵɵtemplate(28, ScanComponent_mat_card_3_span_28_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.currentScanProgress.message);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 8, ctx_r0.currentScanProgress.status));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.currentScanProgress.filesScanned, \" / \", ctx_r0.currentScanProgress.totalFiles, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.currentScanProgress.issuesFound);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r0.currentScanProgress.progress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.currentScanProgress.progress, \"% Complete \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentScanProgress.currentFile);\n  }\n}\nfunction ScanComponent_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Project path is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanComponent_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" At least one chain must be selected \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanComponent_mat_card_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 32)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Scan in Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵelement(7, \"mat-progress-bar\", 42);\n    i0.ɵɵelementStart(8, \"div\", 43)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.scanStatus);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Scanning project: \", ctx_r0.currentScanPath, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Chains: \", ctx_r0.currentScanChains.join(\", \"), \"\");\n  }\n}\nfunction ScanComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function ScanComponent_div_99_Template_div_click_0_listener() {\n      const scan_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewScanDetails(scan_r3.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 45)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"span\", 47);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 48);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getProjectName(scan_r3.project_path));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", scan_r3.chains.join(\", \"), \" \\u2022 \", ctx_r0.formatDate(scan_r3.created_at), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"status-\" + scan_r3.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(scan_r3.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", (scan_r3.issues == null ? null : scan_r3.issues.length) || 0, \" issues\");\n  }\n}\nexport let ScanComponent = /*#__PURE__*/(() => {\n  class ScanComponent {\n    constructor(fb, apiService, webSocketService, router, snackBar) {\n      this.fb = fb;\n      this.apiService = apiService;\n      this.webSocketService = webSocketService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.isScanning = false;\n      this.scanStatus = '';\n      this.currentScanPath = '';\n      this.currentScanChains = [];\n      this.recentScans = [];\n      this.currentScanProgress = null;\n      this.subscriptions = [];\n      this.scanForm = this.fb.group({\n        projectPath: ['', Validators.required],\n        chains: [['ethereum'], Validators.required],\n        scanType: ['full'],\n        checkKeys: [true],\n        checkContracts: [true],\n        checkDependencies: [true],\n        checkEnvironment: [true]\n      });\n    }\n    ngOnInit() {\n      this.loadRecentScans();\n      this.subscribeToScanStatus();\n      this.subscribeToScanProgress();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    onSubmit() {\n      if (this.scanForm.valid) {\n        const formValue = this.scanForm.value;\n        const scanRequest = {\n          project_path: formValue.projectPath,\n          chains: formValue.chains,\n          scan_type: formValue.scanType,\n          options: {\n            check_keys: formValue.checkKeys,\n            check_contracts: formValue.checkContracts,\n            check_dependencies: formValue.checkDependencies,\n            check_environment: formValue.checkEnvironment\n          }\n        };\n        this.startScan(scanRequest);\n      }\n    }\n    startScan(request) {\n      this.isScanning = true;\n      this.currentScanPath = request.project_path;\n      this.currentScanChains = request.chains;\n      this.scanStatus = 'Initializing scan...';\n      this.apiService.startScan(request).subscribe({\n        next: response => {\n          this.snackBar.open('Scan started successfully!', 'Close', {\n            duration: 3000\n          });\n          this.scanStatus = 'Scan in progress...';\n          // Subscribe to real-time progress updates for this scan\n          this.webSocketService.subscribeScanProgress(response.scan_id);\n          // For development, simulate progress if WebSocket is not available\n          if (this.webSocketService.getCurrentConnectionState() !== 'connected') {\n            this.webSocketService.mockScanProgress(response.scan_id);\n          }\n        },\n        error: error => {\n          console.error('Scan failed:', error);\n          this.isScanning = false;\n          this.snackBar.open('Scan failed to start', 'Close', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    onReset() {\n      this.scanForm.reset({\n        chains: ['ethereum'],\n        scanType: 'full',\n        checkKeys: true,\n        checkContracts: true,\n        checkDependencies: true,\n        checkEnvironment: true\n      });\n    }\n    quickScanKeys() {\n      const request = {\n        project_path: this.scanForm.get('projectPath')?.value || '.',\n        chains: ['general'],\n        scan_type: 'quick',\n        options: {\n          check_keys: true\n        }\n      };\n      this.startScan(request);\n    }\n    quickScanContracts() {\n      const request = {\n        project_path: this.scanForm.get('projectPath')?.value || '.',\n        chains: this.scanForm.get('chains')?.value || ['ethereum'],\n        scan_type: 'quick',\n        options: {\n          check_contracts: true\n        }\n      };\n      this.startScan(request);\n    }\n    quickScanEnvironment() {\n      const request = {\n        project_path: this.scanForm.get('projectPath')?.value || '.',\n        chains: ['general'],\n        scan_type: 'quick',\n        options: {\n          check_environment: true\n        }\n      };\n      this.startScan(request);\n    }\n    loadRecentScans() {\n      this.apiService.getScanHistory().subscribe({\n        next: response => {\n          this.recentScans = response.scans.slice(0, 3);\n        },\n        error: error => {\n          console.error('Error loading recent scans:', error);\n          // Use mock data for development\n          this.recentScans = [this.apiService.generateMockScanResult()];\n        }\n      });\n    }\n    subscribeToScanStatus() {\n      const statusSub = this.apiService.scanStatus$.subscribe(status => {\n        if (status !== 'idle') {\n          this.scanStatus = status;\n        }\n      });\n      this.subscriptions.push(statusSub);\n    }\n    subscribeToScanProgress() {\n      const progressSub = this.webSocketService.scanProgress$.subscribe(progress => {\n        this.currentScanProgress = progress;\n        if (progress) {\n          this.isScanning = progress.status === 'starting' || progress.status === 'scanning' || progress.status === 'analyzing';\n          if (progress.status === 'completed') {\n            this.snackBar.open('Scan completed successfully!', 'View Results', {\n              duration: 5000\n            }).onAction().subscribe(() => {\n              this.router.navigate(['/scan', progress.scanId]);\n            });\n            this.loadRecentScans(); // Refresh the recent scans list\n          } else if (progress.status === 'failed') {\n            this.snackBar.open('Scan failed: ' + (progress.message || 'Unknown error'), 'Close', {\n              duration: 5000\n            });\n          }\n        }\n      });\n      this.subscriptions.push(progressSub);\n    }\n    viewScanDetails(scanId) {\n      this.router.navigate(['/scan', scanId]);\n    }\n    getProjectName(path) {\n      return path.split('/').pop() || path;\n    }\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString();\n    }\n    static {\n      this.ɵfac = function ScanComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ScanComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ApiService), i0.ɵɵdirectiveInject(i3.WebSocketService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ScanComponent,\n        selectors: [[\"app-scan\"]],\n        decls: 100,\n        vars: 11,\n        consts: [[1, \"scan-container\"], [\"class\", \"progress-card\", 4, \"ngIf\"], [1, \"scan-form-card\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"projectPath\", \"placeholder\", \"/path/to/your/project\", 3, \"disabled\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"formControlName\", \"chains\", \"multiple\", \"\"], [\"value\", \"ethereum\"], [\"value\", \"bitcoin\"], [\"value\", \"general\"], [\"formControlName\", \"scanType\"], [\"value\", \"full\"], [\"value\", \"quick\"], [\"value\", \"custom\"], [1, \"security-options\"], [1, \"options-grid\"], [\"formControlName\", \"checkKeys\"], [\"formControlName\", \"checkContracts\"], [\"formControlName\", \"checkDependencies\"], [\"formControlName\", \"checkEnvironment\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [1, \"quick-scan-card\"], [1, \"quick-actions\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [1, \"recent-scans-card\"], [1, \"recent-scans-list\"], [\"class\", \"scan-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"progress-card\"], [1, \"progress-info\"], [1, \"progress-stats\"], [1, \"stat\"], [1, \"label\"], [1, \"value\"], [\"mode\", \"determinate\", 1, \"progress-bar\", 3, \"value\"], [1, \"progress-text\"], [\"class\", \"current-file\", 4, \"ngIf\"], [1, \"current-file\"], [\"mode\", \"indeterminate\", \"color\", \"primary\"], [1, \"progress-details\"], [1, \"scan-item\", 3, \"click\"], [1, \"scan-info\"], [1, \"scan-status\"], [1, \"status-badge\"], [1, \"issues-count\"]],\n        template: function ScanComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n            i0.ɵɵtext(2, \"\\uD83D\\uDD0D Security Scan\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(3, ScanComponent_mat_card_3_Template, 29, 10, \"mat-card\", 1);\n            i0.ɵɵelementStart(4, \"mat-card\", 2)(5, \"mat-card-header\")(6, \"mat-card-title\");\n            i0.ɵɵtext(7, \"Configure Security Scan\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n            i0.ɵɵtext(9, \"Set up a comprehensive security scan for your blockchain project\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 3);\n            i0.ɵɵlistener(\"ngSubmit\", function ScanComponent_Template_form_ngSubmit_11_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(12, \"div\", 4)(13, \"mat-form-field\", 5)(14, \"mat-label\");\n            i0.ɵɵtext(15, \"Project Path\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(16, \"input\", 6);\n            i0.ɵɵelementStart(17, \"mat-icon\", 7);\n            i0.ɵɵtext(18, \"folder\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, ScanComponent_mat_error_19_Template, 2, 0, \"mat-error\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 4)(21, \"mat-form-field\", 5)(22, \"mat-label\");\n            i0.ɵɵtext(23, \"Blockchain Chains\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"mat-select\", 9)(25, \"mat-option\", 10);\n            i0.ɵɵtext(26, \"Ethereum\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"mat-option\", 11);\n            i0.ɵɵtext(28, \"Bitcoin\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"mat-option\", 12);\n            i0.ɵɵtext(30, \"General Security\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(31, ScanComponent_mat_error_31_Template, 2, 0, \"mat-error\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 4)(33, \"mat-form-field\", 5)(34, \"mat-label\");\n            i0.ɵɵtext(35, \"Scan Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"mat-select\", 13)(37, \"mat-option\", 14);\n            i0.ɵɵtext(38, \"Full Scan\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"mat-option\", 15);\n            i0.ɵɵtext(40, \"Quick Scan\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"mat-option\", 16);\n            i0.ɵɵtext(42, \"Custom Scan\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(43, \"div\", 17)(44, \"h3\");\n            i0.ɵɵtext(45, \"Security Check Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"div\", 18)(47, \"mat-checkbox\", 19)(48, \"mat-icon\");\n            i0.ɵɵtext(49, \"vpn_key\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(50, \" Check for exposed keys \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"mat-checkbox\", 20)(52, \"mat-icon\");\n            i0.ɵɵtext(53, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(54, \" Smart contract audit \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"mat-checkbox\", 21)(56, \"mat-icon\");\n            i0.ɵɵtext(57, \"extension\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(58, \" Dependency scanning \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"mat-checkbox\", 22)(60, \"mat-icon\");\n            i0.ɵɵtext(61, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(62, \" Environment security \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(63, \"div\", 23)(64, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function ScanComponent_Template_button_click_64_listener() {\n              return ctx.onReset();\n            });\n            i0.ɵɵelementStart(65, \"mat-icon\");\n            i0.ɵɵtext(66, \"refresh\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(67, \" Reset \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"button\", 25)(69, \"mat-icon\");\n            i0.ɵɵtext(70, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(71);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(72, ScanComponent_mat_card_72_Template, 13, 3, \"mat-card\", 1);\n            i0.ɵɵelementStart(73, \"mat-card\", 26)(74, \"mat-card-header\")(75, \"mat-card-title\");\n            i0.ɵɵtext(76, \"Quick Scan Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"mat-card-subtitle\");\n            i0.ɵɵtext(78, \"Run specific security checks quickly\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(79, \"mat-card-content\")(80, \"div\", 27)(81, \"button\", 28);\n            i0.ɵɵlistener(\"click\", function ScanComponent_Template_button_click_81_listener() {\n              return ctx.quickScanKeys();\n            });\n            i0.ɵɵelementStart(82, \"mat-icon\");\n            i0.ɵɵtext(83, \"vpn_key\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(84, \" Check Keys Only \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"button\", 28);\n            i0.ɵɵlistener(\"click\", function ScanComponent_Template_button_click_85_listener() {\n              return ctx.quickScanContracts();\n            });\n            i0.ɵɵelementStart(86, \"mat-icon\");\n            i0.ɵɵtext(87, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(88, \" Audit Contracts \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"button\", 28);\n            i0.ɵɵlistener(\"click\", function ScanComponent_Template_button_click_89_listener() {\n              return ctx.quickScanEnvironment();\n            });\n            i0.ɵɵelementStart(90, \"mat-icon\");\n            i0.ɵɵtext(91, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(92, \" Environment Check \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(93, \"mat-card\", 29)(94, \"mat-card-header\")(95, \"mat-card-title\");\n            i0.ɵɵtext(96, \"Recent Scans\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(97, \"mat-card-content\")(98, \"div\", 30);\n            i0.ɵɵtemplate(99, ScanComponent_div_99_Template, 11, 7, \"div\", 31);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_4_0;\n            let tmp_5_0;\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentScanProgress);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"disabled\", ctx.isScanning);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.scanForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.isScanning);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.scanForm.get(\"projectPath\")) == null ? null : tmp_4_0.hasError(\"required\"));\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.scanForm.get(\"chains\")) == null ? null : tmp_5_0.hasError(\"required\"));\n            i0.ɵɵadvance(37);\n            i0.ɵɵproperty(\"disabled\", ctx.scanForm.invalid || ctx.isScanning);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isScanning ? \"Scanning...\" : \"Start Scan\", \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isScanning);\n            i0.ɵɵadvance(27);\n            i0.ɵɵproperty(\"ngForOf\", ctx.recentScans);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, i6.TitleCasePipe, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, MatFormFieldModule, i8.MatFormField, i8.MatLabel, i8.MatError, i8.MatSuffix, MatInputModule, i9.MatInput, MatSelectModule, i10.MatSelect, i10.MatOption, MatCheckboxModule, i11.MatCheckbox, MatButtonModule, i12.MatButton, MatIconModule, i13.MatIcon, MatProgressBarModule, i14.MatProgressBar, MatSnackBarModule, MatStepperModule],\n        styles: [\".scan-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}h1[_ngcontent-%COMP%]{margin-bottom:30px;color:#333}.scan-form-card[_ngcontent-%COMP%], .progress-card[_ngcontent-%COMP%], .quick-scan-card[_ngcontent-%COMP%], .recent-scans-card[_ngcontent-%COMP%]{margin-bottom:30px}.form-row[_ngcontent-%COMP%]{margin-bottom:20px}.full-width[_ngcontent-%COMP%]{width:100%}.security-options[_ngcontent-%COMP%]{margin:30px 0}.security-options[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:15px;color:#333}.options-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px}.options-grid[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:15px;margin-top:30px}.progress-card[_ngcontent-%COMP%]{border-left:4px solid #2196f3}.progress-details[_ngcontent-%COMP%]{margin-top:15px}.progress-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:5px 0;color:#666}.quick-actions[_ngcontent-%COMP%]{display:flex;gap:15px;flex-wrap:wrap}.quick-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.recent-scans-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px}.scan-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px;border:1px solid #e0e0e0;border-radius:8px;cursor:pointer;transition:background-color .2s}.scan-item[_ngcontent-%COMP%]:hover{background-color:#f5f5f5}.scan-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px;color:#333}.scan-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:14px}.scan-status[_ngcontent-%COMP%]{text-align:right}.status-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:700;text-transform:uppercase}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.status-running[_ngcontent-%COMP%]{background-color:#2196f3;color:#fff}.status-failed[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.status-pending[_ngcontent-%COMP%]{background-color:#ff9800;color:#fff}.issues-count[_ngcontent-%COMP%]{display:block;margin-top:5px;font-size:12px;color:#666}\"]\n      });\n    }\n  }\n  return ScanComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}