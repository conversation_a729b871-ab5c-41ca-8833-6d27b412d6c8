{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'http://localhost:8080/api/v1/auth';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.tokenSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.token$ = this.tokenSubject.asObservable();\n    // Initialize authentication state from localStorage\n    this.initializeAuthState();\n  }\n  initializeAuthState() {\n    const token = localStorage.getItem('auth_token');\n    const user = localStorage.getItem('current_user');\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        // Validate token before setting state\n        if (this.isTokenValid(token)) {\n          this.tokenSubject.next(token);\n          this.currentUserSubject.next(parsedUser);\n        } else {\n          // Token is invalid, clear storage\n          this.clearAuthData();\n        }\n      } catch (error) {\n        // Invalid user data, clear storage\n        this.clearAuthData();\n      }\n    }\n  }\n  isTokenValid(token) {\n    if (!token) return false;\n    // Check if it's a JWT token (has 3 parts separated by dots)\n    if (token.includes('.') && token.split('.').length === 3) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        return payload.exp > currentTime;\n      } catch {\n        return false;\n      }\n    }\n    return false;\n  }\n  // Authentication methods\n  login(credentials) {\n    return this.http.post(`${this.baseUrl}/login`, credentials).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(this.handleError));\n  }\n  register(userData) {\n    return this.http.post(`${this.baseUrl}/register`, userData).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(this.handleError));\n  }\n  logout() {\n    // Call logout endpoint to invalidate token on server\n    this.http.post(`${this.baseUrl}/logout`, {}).subscribe({\n      complete: () => {\n        this.clearAuthData();\n      },\n      error: () => {\n        // Clear local data even if server call fails\n        this.clearAuthData();\n      }\n    });\n  }\n  refreshToken() {\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n    return this.http.post(`${this.baseUrl}/refresh`, {\n      refresh_token: refreshToken\n    }).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(error => {\n      this.clearAuthData();\n      return throwError(() => error);\n    }));\n  }\n  // Utility methods\n  isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    if (!token || !user) {\n      return false;\n    }\n    return this.isTokenValid(token);\n  }\n  getToken() {\n    return localStorage.getItem('auth_token');\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  hasRole(role) {\n    const user = this.getCurrentUser();\n    return user ? user.role === role : false;\n  }\n  hasAnyRole(roles) {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n  // HTTP headers with authentication\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n  // Private methods\n  setAuthData(response) {\n    localStorage.setItem('auth_token', response.token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    this.tokenSubject.next(response.token);\n    this.currentUserSubject.next(response.user);\n    // Set token expiration timer\n    if (response.expires_in) {\n      setTimeout(() => {\n        this.refreshToken().subscribe({\n          error: () => this.logout()\n        });\n      }, (response.expires_in - 300) * 1000); // Refresh 5 minutes before expiry\n    }\n  }\n  clearAuthData() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('current_user');\n    localStorage.removeItem('refresh_token');\n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  // Mock authentication for development\n  mockLogin(username = 'admin', role = 'admin') {\n    const mockUser = {\n      id: '1',\n      username: username,\n      email: `${username}@spt.local`,\n      role: role,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    const mockToken = this.generateMockToken(mockUser);\n    const mockResponse = {\n      token: mockToken,\n      user: mockUser,\n      expires_in: 3600\n    };\n    this.setAuthData(mockResponse);\n  }\n  generateMockToken(user) {\n    const header = btoa(JSON.stringify({\n      alg: 'HS256',\n      typ: 'JWT'\n    }));\n    const payload = btoa(JSON.stringify({\n      sub: user.id,\n      username: user.username,\n      role: user.role,\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + 3600\n    }));\n    const signature = btoa('mock-signature');\n    return `${header}.${payload}.${signature}`;\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "map", "catchError", "AuthService", "constructor", "http", "router", "baseUrl", "currentUserSubject", "tokenSubject", "currentUser$", "asObservable", "token$", "initializeAuthState", "token", "localStorage", "getItem", "user", "parsedUser", "JSON", "parse", "isTokenValid", "next", "clearAuthData", "error", "includes", "split", "length", "payload", "atob", "currentTime", "Math", "floor", "Date", "now", "exp", "login", "credentials", "post", "pipe", "response", "setAuthData", "handleError", "register", "userData", "logout", "subscribe", "complete", "refreshToken", "Error", "refresh_token", "isAuthenticated", "getToken", "getCurrentUser", "value", "hasRole", "role", "hasAnyRole", "roles", "getAuthHeaders", "setItem", "stringify", "expires_in", "setTimeout", "removeItem", "navigate", "errorMessage", "message", "console", "mockLogin", "username", "mockUser", "id", "email", "created_at", "toISOString", "last_login", "mockToken", "generateMockToken", "mockResponse", "header", "btoa", "alg", "typ", "sub", "iat", "signature", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { Router } from '@angular/router';\n\nexport interface User {\n  id: string;\n  username: string;\n  email: string;\n  role: string;\n  created_at: string;\n  last_login?: string;\n}\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n}\n\nexport interface AuthResponse {\n  token: string;\n  user: User;\n  expires_in: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly baseUrl = 'http://localhost:8080/api/v1/auth';\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private tokenSubject = new BehaviorSubject<string | null>(null);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public token$ = this.tokenSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    // Initialize authentication state from localStorage\n    this.initializeAuthState();\n  }\n\n  private initializeAuthState(): void {\n    const token = localStorage.getItem('auth_token');\n    const user = localStorage.getItem('current_user');\n\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        // Validate token before setting state\n        if (this.isTokenValid(token)) {\n          this.tokenSubject.next(token);\n          this.currentUserSubject.next(parsedUser);\n        } else {\n          // Token is invalid, clear storage\n          this.clearAuthData();\n        }\n      } catch (error) {\n        // Invalid user data, clear storage\n        this.clearAuthData();\n      }\n    }\n  }\n\n  private isTokenValid(token: string): boolean {\n    if (!token) return false;\n\n    // Check if it's a JWT token (has 3 parts separated by dots)\n    if (token.includes('.') && token.split('.').length === 3) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        return payload.exp > currentTime;\n      } catch {\n        return false;\n      }\n    }\n\n    return false;\n  }\n\n  // Authentication methods\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.baseUrl}/login`, credentials)\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.baseUrl}/register`, userData)\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  logout(): void {\n    // Call logout endpoint to invalidate token on server\n    this.http.post(`${this.baseUrl}/logout`, {}).subscribe({\n      complete: () => {\n        this.clearAuthData();\n      },\n      error: () => {\n        // Clear local data even if server call fails\n        this.clearAuthData();\n      }\n    });\n  }\n\n  refreshToken(): Observable<AuthResponse> {\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n\n    return this.http.post<AuthResponse>(`${this.baseUrl}/refresh`, { refresh_token: refreshToken })\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(error => {\n          this.clearAuthData();\n          return throwError(() => error);\n        })\n      );\n  }\n\n  // Utility methods\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n\n    if (!token || !user) {\n      return false;\n    }\n\n    return this.isTokenValid(token);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  hasRole(role: string): boolean {\n    const user = this.getCurrentUser();\n    return user ? user.role === role : false;\n  }\n\n  hasAnyRole(roles: string[]): boolean {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n\n  // HTTP headers with authentication\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n\n  // Private methods\n  private setAuthData(response: AuthResponse): void {\n    localStorage.setItem('auth_token', response.token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    \n    this.tokenSubject.next(response.token);\n    this.currentUserSubject.next(response.user);\n\n    // Set token expiration timer\n    if (response.expires_in) {\n      setTimeout(() => {\n        this.refreshToken().subscribe({\n          error: () => this.logout()\n        });\n      }, (response.expires_in - 300) * 1000); // Refresh 5 minutes before expiry\n    }\n  }\n\n  private clearAuthData(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('current_user');\n    localStorage.removeItem('refresh_token');\n    \n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    \n    this.router.navigate(['/login']);\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An error occurred';\n    \n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    \n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n\n  // Mock authentication for development\n  mockLogin(username: string = 'admin', role: string = 'admin'): void {\n    const mockUser: User = {\n      id: '1',\n      username: username,\n      email: `${username}@spt.local`,\n      role: role,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n\n    const mockToken = this.generateMockToken(mockUser);\n    \n    const mockResponse: AuthResponse = {\n      token: mockToken,\n      user: mockUser,\n      expires_in: 3600\n    };\n\n    this.setAuthData(mockResponse);\n  }\n\n  private generateMockToken(user: User): string {\n    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));\n    const payload = btoa(JSON.stringify({\n      sub: user.id,\n      username: user.username,\n      role: user.role,\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + 3600\n    }));\n    const signature = btoa('mock-signature');\n    \n    return `${header}.${payload}.${signature}`;\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;;AAiChD,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAG,mCAAmC;IACtD,KAAAC,kBAAkB,GAAG,IAAIT,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAU,YAAY,GAAG,IAAIV,eAAe,CAAgB,IAAI,CAAC;IAExD,KAAAW,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,MAAM,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAM9C;IACA,IAAI,CAACE,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAEjD,IAAIF,KAAK,IAAIG,IAAI,EAAE;MACjB,IAAI;QACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;QACnC;QACA,IAAI,IAAI,CAACI,YAAY,CAACP,KAAK,CAAC,EAAE;UAC5B,IAAI,CAACL,YAAY,CAACa,IAAI,CAACR,KAAK,CAAC;UAC7B,IAAI,CAACN,kBAAkB,CAACc,IAAI,CAACJ,UAAU,CAAC;QAC1C,CAAC,MAAM;UACL;UACA,IAAI,CAACK,aAAa,EAAE;QACtB;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd;QACA,IAAI,CAACD,aAAa,EAAE;MACtB;IACF;EACF;EAEQF,YAAYA,CAACP,KAAa;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,KAAK;IAExB;IACA,IAAIA,KAAK,CAACW,QAAQ,CAAC,GAAG,CAAC,IAAIX,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI;QACF,MAAMC,OAAO,GAAGT,IAAI,CAACC,KAAK,CAACS,IAAI,CAACf,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,MAAMI,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;QACjD,OAAON,OAAO,CAACO,GAAG,GAAGL,WAAW;MAClC,CAAC,CAAC,MAAM;QACN,OAAO,KAAK;MACd;IACF;IAEA,OAAO,KAAK;EACd;EAEA;EACAM,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAAChC,IAAI,CAACiC,IAAI,CAAe,GAAG,IAAI,CAAC/B,OAAO,QAAQ,EAAE8B,WAAW,CAAC,CACtEE,IAAI,CACHtC,GAAG,CAACuC,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtC,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACL;EAEAC,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAACvC,IAAI,CAACiC,IAAI,CAAe,GAAG,IAAI,CAAC/B,OAAO,WAAW,EAAEqC,QAAQ,CAAC,CACtEL,IAAI,CACHtC,GAAG,CAACuC,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtC,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACL;EAEAG,MAAMA,CAAA;IACJ;IACA,IAAI,CAACxC,IAAI,CAACiC,IAAI,CAAC,GAAG,IAAI,CAAC/B,OAAO,SAAS,EAAE,EAAE,CAAC,CAACuC,SAAS,CAAC;MACrDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACxB,aAAa,EAAE;MACtB,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACD,aAAa,EAAE;MACtB;KACD,CAAC;EACJ;EAEAyB,YAAYA,CAAA;IACV,MAAMA,YAAY,GAAGjC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC1D,IAAI,CAACgC,YAAY,EAAE;MACjB,OAAOhD,UAAU,CAAC,MAAM,IAAIiD,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE;IAEA,OAAO,IAAI,CAAC5C,IAAI,CAACiC,IAAI,CAAe,GAAG,IAAI,CAAC/B,OAAO,UAAU,EAAE;MAAE2C,aAAa,EAAEF;IAAY,CAAE,CAAC,CAC5FT,IAAI,CACHtC,GAAG,CAACuC,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtC,UAAU,CAACsB,KAAK,IAAG;MACjB,IAAI,CAACD,aAAa,EAAE;MACpB,OAAOvB,UAAU,CAAC,MAAMwB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;EACA2B,eAAeA,CAAA;IACb,MAAMrC,KAAK,GAAG,IAAI,CAACsC,QAAQ,EAAE;IAC7B,MAAMnC,IAAI,GAAG,IAAI,CAACoC,cAAc,EAAE;IAElC,IAAI,CAACvC,KAAK,IAAI,CAACG,IAAI,EAAE;MACnB,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAACI,YAAY,CAACP,KAAK,CAAC;EACjC;EAEAsC,QAAQA,CAAA;IACN,OAAOrC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAC3C;EAEAqC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC7C,kBAAkB,CAAC8C,KAAK;EACtC;EAEAC,OAAOA,CAACC,IAAY;IAClB,MAAMvC,IAAI,GAAG,IAAI,CAACoC,cAAc,EAAE;IAClC,OAAOpC,IAAI,GAAGA,IAAI,CAACuC,IAAI,KAAKA,IAAI,GAAG,KAAK;EAC1C;EAEAC,UAAUA,CAACC,KAAe;IACxB,MAAMzC,IAAI,GAAG,IAAI,CAACoC,cAAc,EAAE;IAClC,OAAOpC,IAAI,GAAGyC,KAAK,CAACjC,QAAQ,CAACR,IAAI,CAACuC,IAAI,CAAC,GAAG,KAAK;EACjD;EAEA;EACAG,cAAcA,CAAA;IACZ,MAAM7C,KAAK,GAAG,IAAI,CAACsC,QAAQ,EAAE;IAC7B,OAAO,IAAItD,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAEgB,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;KAC9C,CAAC;EACJ;EAEA;EACQ2B,WAAWA,CAACD,QAAsB;IACxCzB,YAAY,CAAC6C,OAAO,CAAC,YAAY,EAAEpB,QAAQ,CAAC1B,KAAK,CAAC;IAClDC,YAAY,CAAC6C,OAAO,CAAC,cAAc,EAAEzC,IAAI,CAAC0C,SAAS,CAACrB,QAAQ,CAACvB,IAAI,CAAC,CAAC;IAEnE,IAAI,CAACR,YAAY,CAACa,IAAI,CAACkB,QAAQ,CAAC1B,KAAK,CAAC;IACtC,IAAI,CAACN,kBAAkB,CAACc,IAAI,CAACkB,QAAQ,CAACvB,IAAI,CAAC;IAE3C;IACA,IAAIuB,QAAQ,CAACsB,UAAU,EAAE;MACvBC,UAAU,CAAC,MAAK;QACd,IAAI,CAACf,YAAY,EAAE,CAACF,SAAS,CAAC;UAC5BtB,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACqB,MAAM;SACzB,CAAC;MACJ,CAAC,EAAE,CAACL,QAAQ,CAACsB,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IAC1C;EACF;EAEQvC,aAAaA,CAAA;IACnBR,YAAY,CAACiD,UAAU,CAAC,YAAY,CAAC;IACrCjD,YAAY,CAACiD,UAAU,CAAC,cAAc,CAAC;IACvCjD,YAAY,CAACiD,UAAU,CAAC,eAAe,CAAC;IAExC,IAAI,CAACvD,YAAY,CAACa,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACd,kBAAkB,CAACc,IAAI,CAAC,IAAI,CAAC;IAElC,IAAI,CAAChB,MAAM,CAAC2D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEQvB,WAAWA,CAAClB,KAAU;IAC5B,IAAI0C,YAAY,GAAG,mBAAmB;IAEtC,IAAI1C,KAAK,CAACA,KAAK,EAAE2C,OAAO,EAAE;MACxBD,YAAY,GAAG1C,KAAK,CAACA,KAAK,CAAC2C,OAAO;IACpC,CAAC,MAAM,IAAI3C,KAAK,CAAC2C,OAAO,EAAE;MACxBD,YAAY,GAAG1C,KAAK,CAAC2C,OAAO;IAC9B;IAEAC,OAAO,CAAC5C,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,OAAOxB,UAAU,CAAC,MAAM,IAAIiD,KAAK,CAACiB,YAAY,CAAC,CAAC;EAClD;EAEA;EACAG,SAASA,CAACC,QAAA,GAAmB,OAAO,EAAEd,IAAA,GAAe,OAAO;IAC1D,MAAMe,QAAQ,GAAS;MACrBC,EAAE,EAAE,GAAG;MACPF,QAAQ,EAAEA,QAAQ;MAClBG,KAAK,EAAE,GAAGH,QAAQ,YAAY;MAC9Bd,IAAI,EAAEA,IAAI;MACVkB,UAAU,EAAE,IAAIzC,IAAI,EAAE,CAAC0C,WAAW,EAAE;MACpCC,UAAU,EAAE,IAAI3C,IAAI,EAAE,CAAC0C,WAAW;KACnC;IAED,MAAME,SAAS,GAAG,IAAI,CAACC,iBAAiB,CAACP,QAAQ,CAAC;IAElD,MAAMQ,YAAY,GAAiB;MACjCjE,KAAK,EAAE+D,SAAS;MAChB5D,IAAI,EAAEsD,QAAQ;MACdT,UAAU,EAAE;KACb;IAED,IAAI,CAACrB,WAAW,CAACsC,YAAY,CAAC;EAChC;EAEQD,iBAAiBA,CAAC7D,IAAU;IAClC,MAAM+D,MAAM,GAAGC,IAAI,CAAC9D,IAAI,CAAC0C,SAAS,CAAC;MAAEqB,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAK,CAAE,CAAC,CAAC;IACjE,MAAMvD,OAAO,GAAGqD,IAAI,CAAC9D,IAAI,CAAC0C,SAAS,CAAC;MAClCuB,GAAG,EAAEnE,IAAI,CAACuD,EAAE;MACZF,QAAQ,EAAErD,IAAI,CAACqD,QAAQ;MACvBd,IAAI,EAAEvC,IAAI,CAACuC,IAAI;MACf6B,GAAG,EAAEtD,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MAClCC,GAAG,EAAEJ,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG;KACtC,CAAC,CAAC;IACH,MAAMoD,SAAS,GAAGL,IAAI,CAAC,gBAAgB,CAAC;IAExC,OAAO,GAAGD,MAAM,IAAIpD,OAAO,IAAI0D,SAAS,EAAE;EAC5C;;;uCAjOWnF,WAAW,EAAAoF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXzF,WAAW;MAAA0F,OAAA,EAAX1F,WAAW,CAAA2F,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}