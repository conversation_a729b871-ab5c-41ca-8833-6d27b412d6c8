.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card-wrapper {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  max-width: 1000px;
  width: 100%;
}

.register-card {
  flex: 0 0 450px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.logo h1 {
  margin: 0;
  color: #1a202c;
  font-weight: 700;
  font-size: 28px;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
}

.full-width {
  width: 100%;
}

.form-options {
  margin-top: 15px;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.form-actions {
  margin-top: 25px;
}

.register-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.register-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.card-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.card-actions p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

.login-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}

.benefits-info {
  flex: 1;
  color: white;
  padding: 20px;
}

.benefits-info h3 {
  font-size: 24px;
  margin-bottom: 20px;
  font-weight: 700;
}

.benefits-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefits-info li {
  padding: 10px 0;
  font-size: 16px;
  line-height: 1.5;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .register-card-wrapper {
    flex-direction: column;
    gap: 20px;
  }

  .register-card {
    flex: none;
    max-width: 100%;
    padding: 30px 20px;
  }

  .benefits-info {
    order: -1;
    text-align: center;
  }

  .logo h1 {
    font-size: 24px;
  }
}

mat-form-field {
  margin-bottom: 15px;
}

mat-spinner {
  margin-right: 10px;
}

mat-error {
  font-size: 12px;
  margin-top: 5px;
}
