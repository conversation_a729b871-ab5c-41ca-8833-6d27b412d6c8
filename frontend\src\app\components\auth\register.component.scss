.register-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-x: hidden;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
  pointer-events: none;
}

.app-header {
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 600;
  font-size: 1.5rem;
}

.brand-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.header-link {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
  }
}

.register-content {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  gap: 3rem;
  align-items: flex-start;
}

.register-card-wrapper {
  flex: 0 0 400px;
  max-width: 400px;
}

.register-card {
  width: 100%;
  padding: 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.register-header {
  text-align: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
}

.logo-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #667eea;
}

.logo-text {
  text-align: left;
}

.logo-text h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
}

.logo-text .subtitle {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 0.9rem;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.full-width {
  width: 100%;
}

.form-options {
  margin-top: 15px;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.form-actions {
  margin-top: 25px;
}

.register-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.register-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.card-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.card-actions p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

.login-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}

.features-section {
  flex: 1;
  color: white;
  padding: 20px;
}

.features-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 30px;

  .features-icon {
    font-size: 2rem;
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.9);
  }

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
  }
}

.feature-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }

  .feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    flex-shrink: 0;

    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      color: white;
    }
  }

  .feature-content {
    flex: 1;

    h3 {
      margin: 0 0 8px 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: white;
    }

    p {
      margin: 0;
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.4;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .register-content {
    flex-direction: column;
    gap: 30px;
    padding: 1rem;
  }

  .register-card-wrapper {
    flex: none;
    max-width: 100%;
  }

  .register-card {
    padding: 30px 20px;
  }

  .features-section {
    order: -1;
    text-align: center;
  }

  .logo {
    flex-direction: column;
    text-align: center;
  }

  .logo-text {
    text-align: center;
  }

  .logo-text h1 {
    font-size: 1.5rem;
  }

  .header-content {
    padding: 1rem;
  }

  .brand {
    font-size: 1.2rem;
  }
}

mat-form-field {
  margin-bottom: 15px;
}

mat-spinner {
  margin-right: 10px;
}

mat-error {
  font-size: 12px;
  margin-top: 5px;
}
