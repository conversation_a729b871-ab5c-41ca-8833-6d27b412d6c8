{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/chips\";\nimport * as i7 from \"@angular/material/expansion\";\nimport * as i8 from \"@angular/material/table\";\nfunction VscodeExtensionComponent_div_29_div_9_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 55)(1, \"mat-icon\", 56);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const benefit_r1 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(benefit_r1);\n  }\n}\nfunction VscodeExtensionComponent_div_29_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"ul\", 53);\n    i0.ɵɵtemplate(2, VscodeExtensionComponent_div_29_div_9_li_2_Template, 5, 1, \"li\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", feature_r2.benefits);\n  }\n}\nfunction VscodeExtensionComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"h3\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, VscodeExtensionComponent_div_29_div_9_Template, 3, 1, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", feature_r2.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", feature_r2.benefits.length > 0);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_38_div_9_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const benefit_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(benefit_r3);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_38_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"h4\");\n    i0.ɵɵtext(2, \"Benefits:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_mat_card_38_div_9_li_4_Template, 2, 1, \"li\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", feature_r4.benefits);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 57)(1, \"mat-card-header\")(2, \"mat-icon\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, VscodeExtensionComponent_mat_card_38_div_9_Template, 5, 1, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r4.getFeatureColor(feature_r4.icon));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", feature_r4.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r4.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r4.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", feature_r4.benefits.length > 0);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_46_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Command\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(step_r6.command);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_46_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(step_r6.notes);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 61)(1, \"mat-card-header\")(2, \"div\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 63);\n    i0.ɵɵtemplate(12, VscodeExtensionComponent_mat_card_46_div_12_Template, 9, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, VscodeExtensionComponent_mat_card_46_div_13_Template, 5, 1, \"div\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r7 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r6.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r6.subtitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r6.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r6.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r6.notes);\n  }\n}\nfunction VscodeExtensionComponent_th_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 67);\n    i0.ɵɵtext(1, \"Setting\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 68)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r8.key);\n  }\n}\nfunction VscodeExtensionComponent_th_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 67);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 68)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r9.type);\n  }\n}\nfunction VscodeExtensionComponent_th_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 67);\n    i0.ɵɵtext(1, \"Default\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 68)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r10.default);\n  }\n}\nfunction VscodeExtensionComponent_th_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 67);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(setting_r11.description);\n  }\n}\nfunction VscodeExtensionComponent_tr_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 69);\n  }\n}\nfunction VscodeExtensionComponent_tr_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 70);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_124_div_11_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r12);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_124_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"h4\");\n    i0.ɵɵtext(2, \"Steps:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ol\");\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_mat_expansion_panel_124_div_11_li_4_Template, 2, 1, \"li\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r13.steps);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_124_div_12_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r14 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r14);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_124_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"h4\");\n    i0.ɵɵtext(2, \"Tips:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 77);\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_mat_expansion_panel_124_div_12_li_4_Template, 5, 1, \"li\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r13.tips);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 71)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 72)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, VscodeExtensionComponent_mat_expansion_panel_124_div_11_Template, 5, 1, \"div\", 73)(12, VscodeExtensionComponent_mat_expansion_panel_124_div_12_Template, 5, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"expanded\", usage_r13.expanded);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(usage_r13.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", usage_r13.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", usage_r13.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(usage_r13.details);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r13.steps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r13.tips);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_132_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r15);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_132_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"h4\");\n    i0.ɵɵtext(2, \"Prevention:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(issue_r16.prevention);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 78)(1, \"mat-card-header\")(2, \"mat-icon\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 79)(10, \"h4\");\n    i0.ɵɵtext(11, \"Solution:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"ol\");\n    i0.ɵɵtemplate(13, VscodeExtensionComponent_mat_card_132_li_13_Template, 2, 1, \"li\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, VscodeExtensionComponent_mat_card_132_div_14_Template, 5, 1, \"div\", 80);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", issue_r16.severity === \"high\" ? \"#f44336\" : \"#ff9800\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", issue_r16.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r16.problem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r16.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", issue_r16.solution);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", issue_r16.prevention);\n  }\n}\nexport class VscodeExtensionComponent {\n  constructor() {\n    this.settingsColumns = ['key', 'type', 'default', 'description'];\n    this.configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n    this.extensionFeatures = [{\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities.',\n      icon: 'security',\n      benefits: ['Immediate vulnerability detection', 'Reduced security debt', 'Faster development cycles', 'Proactive security measures']\n    }, {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues and suggestions.',\n      icon: 'visibility',\n      benefits: ['Clear visual feedback', 'Context-aware highlighting', 'Severity-based color coding', 'Non-intrusive indicators']\n    }, {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and fixes.',\n      icon: 'bug_report',\n      benefits: ['Centralized issue tracking', 'Detailed error descriptions', 'Quick navigation to issues', 'Integration with existing workflow']\n    }, {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code.',\n      icon: 'lens',\n      benefits: ['Contextual security metrics', 'One-click security actions', 'Code quality insights', 'Performance recommendations']\n    }, {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements.',\n      icon: 'info',\n      benefits: ['Instant security documentation', 'Best practice suggestions', 'Vulnerability explanations', 'Quick reference access']\n    }, {\n      title: 'Multi-chain Support',\n      description: 'Support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      benefits: ['Comprehensive blockchain coverage', 'Chain-specific security rules', 'Unified security approach', 'Extensible architecture']\n    }];\n    this.installationSteps = [{\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    }, {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    }, {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }];\n    this.configSettings = [{\n      key: 'spt.enabled',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable/disable SPT security analysis'\n    }, {\n      key: 'spt.serverUrl',\n      type: 'string',\n      default: 'http://localhost:8080',\n      description: 'SPT backend server URL'\n    }, {\n      key: 'spt.apiKey',\n      type: 'string',\n      default: '\"\"',\n      description: 'API key for authentication'\n    }, {\n      key: 'spt.autoScan',\n      type: 'boolean',\n      default: 'true',\n      description: 'Automatically scan files on save'\n    }, {\n      key: 'spt.scanOnOpen',\n      type: 'boolean',\n      default: 'false',\n      description: 'Automatically scan files when opened'\n    }, {\n      key: 'spt.chains',\n      type: 'array',\n      default: '[\"ethereum\", \"bitcoin\", \"general\"]',\n      description: 'Blockchain chains to analyze'\n    }, {\n      key: 'spt.severity',\n      type: 'string',\n      default: '\"medium\"',\n      description: 'Minimum severity level to show'\n    }, {\n      key: 'spt.showInlineDecorations',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show inline security decorations'\n    }, {\n      key: 'spt.showProblems',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show security issues in Problems panel'\n    }, {\n      key: 'spt.enableCodeLens',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security-related CodeLens'\n    }, {\n      key: 'spt.enableHover',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security information on hover'\n    }];\n    this.usageExamples = [{\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: ['Open a blockchain project in VS Code', 'Save a file to trigger automatic scanning', 'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"', 'View results in Problems panel or inline decorations'],\n      tips: ['Enable auto-scan for continuous security monitoring', 'Use the Problems panel to navigate between issues', 'Check the status bar for scan progress']\n    }, {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: ['Hover over highlighted code to see issue details', 'Click on issues in Problems panel for more information', 'Use CodeLens actions for quick fixes', 'Follow the recommended solutions'],\n      tips: ['Start with critical and high severity issues', 'Use hover information for quick context', 'Check references for additional learning']\n    }, {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: ['Open VS Code settings (Ctrl+,)', 'Search for \"SPT\" to find extension settings', 'Adjust chains, severity, and scan triggers', 'Save settings and restart if needed'],\n      tips: ['Use workspace settings for project-specific configuration', 'Adjust severity threshold based on project maturity', 'Enable scan-on-open for comprehensive coverage']\n    }];\n    this.troubleshootingIssues = [{\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: ['Verify SPT backend server is running on configured port', 'Check spt.serverUrl setting in VS Code preferences', 'Ensure firewall is not blocking the connection', 'Try restarting VS Code and the SPT server'],\n      prevention: 'Always start the SPT backend server before using the extension'\n    }, {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: ['Check if the file type is supported (Solidity, JavaScript, etc.)', 'Verify the correct blockchain chains are selected', 'Lower the severity threshold in settings', 'Ensure the file contains actual security-relevant code'],\n      prevention: 'Review supported file types and ensure proper project structure'\n    }, {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: ['Disable auto-scan and use manual scanning', 'Increase scan timeout in settings', 'Exclude large files or directories from scanning', 'Reduce the number of enabled blockchain chains'],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }];\n  }\n  getFeatureColor(icon) {\n    const colors = {\n      'security': '#1976d2',\n      'visibility': '#4caf50',\n      'bug_report': '#f44336',\n      'lens': '#ff9800',\n      'info': '#9c27b0',\n      'link': '#00bcd4'\n    };\n    return colors[icon] || '#1976d2';\n  }\n  static {\n    this.ɵfac = function VscodeExtensionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VscodeExtensionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VscodeExtensionComponent,\n      selectors: [[\"app-vscode-extension\"]],\n      decls: 133,\n      vars: 9,\n      consts: [[1, \"spt-vscode-container\"], [1, \"spt-hero-section\"], [1, \"spt-hero-content\"], [1, \"spt-hero-icon\"], [1, \"spt-hero-text\"], [1, \"spt-hero-title\"], [1, \"spt-hero-subtitle\"], [1, \"spt-hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"spt-primary-btn\"], [\"mat-stroked-button\", \"\", 1, \"spt-secondary-btn\"], [1, \"spt-features-overview\"], [1, \"spt-section-header\"], [1, \"spt-section-title\"], [1, \"spt-section-subtitle\"], [1, \"spt-features-grid\"], [\"class\", \"spt-feature-card spt-feature-primary\", 4, \"ngFor\", \"ngForOf\"], [\"animationDuration\", \"300ms\", 1, \"extension-tabs\"], [\"label\", \"Features\"], [1, \"tab-content\"], [1, \"features-grid\"], [\"class\", \"feature-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Installation\"], [1, \"installation-steps\"], [\"class\", \"step-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-sections\"], [1, \"config-section\"], [\"mat-card-avatar\", \"\"], [1, \"code-block\"], [1, \"code-header\"], [\"mat-table\", \"\", 1, \"settings-table\", 3, \"dataSource\"], [\"matColumnDef\", \"key\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"default\"], [\"matColumnDef\", \"description\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"label\", \"Usage\"], [1, \"usage-sections\"], [1, \"usage-panels\"], [3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Troubleshooting\"], [1, \"troubleshooting-sections\"], [\"class\", \"troubleshooting-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-feature-card\", \"spt-feature-primary\"], [1, \"spt-feature-icon\"], [1, \"spt-feature-content\"], [1, \"spt-feature-title\"], [1, \"spt-feature-description\"], [\"class\", \"spt-feature-benefits\", 4, \"ngIf\"], [1, \"spt-feature-benefits\"], [1, \"spt-benefits-list\"], [\"class\", \"spt-benefit-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-benefit-item\"], [1, \"spt-benefit-icon\"], [1, \"feature-card\"], [\"class\", \"benefits-list\", 4, \"ngIf\"], [1, \"benefits-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"step-card\"], [\"mat-card-avatar\", \"\", 1, \"step-number\"], [1, \"step-content\"], [\"class\", \"code-block\", 4, \"ngIf\"], [\"class\", \"step-notes\", 4, \"ngIf\"], [1, \"step-notes\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [3, \"expanded\"], [1, \"usage-content\"], [\"class\", \"usage-steps\", 4, \"ngIf\"], [\"class\", \"usage-tips\", 4, \"ngIf\"], [1, \"usage-steps\"], [1, \"usage-tips\"], [1, \"tips-list\"], [1, \"troubleshooting-card\"], [1, \"solution-steps\"], [\"class\", \"prevention-tips\", 4, \"ngIf\"], [1, \"prevention-tips\"]],\n      template: function VscodeExtensionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"extension\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"h1\", 5);\n          i0.ɵɵtext(8, \"VS Code Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 6);\n          i0.ɵɵtext(10, \" Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"button\", 8)(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Install Extension\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"button\", 9)(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\");\n          i0.ɵɵtext(21, \"View Source\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"section\", 10)(23, \"div\", 11)(24, \"h2\", 12);\n          i0.ɵɵtext(25, \"Key Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 13);\n          i0.ɵɵtext(27, \" Discover the powerful features that make SPT extension essential for secure blockchain development. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 14);\n          i0.ɵɵtemplate(29, VscodeExtensionComponent_div_29_Template, 10, 6, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-tab-group\", 16)(31, \"mat-tab\", 17)(32, \"div\", 18)(33, \"h2\");\n          i0.ɵɵtext(34, \"Key Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\");\n          i0.ɵɵtext(36, \"Discover the powerful features that make SPT extension essential for secure blockchain development.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 19);\n          i0.ɵɵtemplate(38, VscodeExtensionComponent_mat_card_38_Template, 10, 6, \"mat-card\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"mat-tab\", 21)(40, \"div\", 18)(41, \"h2\");\n          i0.ɵɵtext(42, \"Installation Guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Get the SPT extension installed and configured in VS Code.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 22);\n          i0.ɵɵtemplate(46, VscodeExtensionComponent_mat_card_46_Template, 14, 6, \"mat-card\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"mat-tab\", 24)(48, \"div\", 18)(49, \"h2\");\n          i0.ɵɵtext(50, \"Extension Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\");\n          i0.ɵɵtext(52, \"Customize the SPT extension to fit your development workflow and preferences.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 25)(54, \"mat-card\", 26)(55, \"mat-card-header\")(56, \"mat-icon\", 27);\n          i0.ɵɵtext(57, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"mat-card-title\");\n          i0.ɵɵtext(59, \"Settings Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"mat-card-subtitle\");\n          i0.ɵɵtext(61, \"Configure extension behavior\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"mat-card-content\")(63, \"p\");\n          i0.ɵɵtext(64, \"Access extension settings through VS Code preferences:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 28)(66, \"div\", 29)(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"keyboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"span\");\n          i0.ɵɵtext(70, \"Keyboard Shortcut\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"pre\")(72, \"code\");\n          i0.ɵɵtext(73, \"Ctrl+Shift+P \\u2192 \\\"Preferences: Open Settings (JSON)\\\"\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(74, \"mat-card\", 26)(75, \"mat-card-header\")(76, \"mat-icon\", 27);\n          i0.ɵɵtext(77, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"mat-card-title\");\n          i0.ɵɵtext(79, \"Configuration Example\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"mat-card-subtitle\");\n          i0.ɵɵtext(81, \"settings.json\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"mat-card-content\")(83, \"div\", 28)(84, \"div\", 29)(85, \"mat-icon\");\n          i0.ɵɵtext(86, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"span\");\n          i0.ɵɵtext(88, \"JSON Configuration\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"pre\")(90, \"code\");\n          i0.ɵɵtext(91);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(92, \"mat-card\", 26)(93, \"mat-card-header\")(94, \"mat-icon\", 27);\n          i0.ɵɵtext(95, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"mat-card-title\");\n          i0.ɵɵtext(97, \"Available Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"mat-card-subtitle\");\n          i0.ɵɵtext(99, \"Complete settings reference\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"mat-card-content\")(101, \"table\", 30);\n          i0.ɵɵelementContainerStart(102, 31);\n          i0.ɵɵtemplate(103, VscodeExtensionComponent_th_103_Template, 2, 0, \"th\", 32)(104, VscodeExtensionComponent_td_104_Template, 3, 1, \"td\", 33);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(105, 34);\n          i0.ɵɵtemplate(106, VscodeExtensionComponent_th_106_Template, 2, 0, \"th\", 32)(107, VscodeExtensionComponent_td_107_Template, 3, 1, \"td\", 33);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(108, 35);\n          i0.ɵɵtemplate(109, VscodeExtensionComponent_th_109_Template, 2, 0, \"th\", 32)(110, VscodeExtensionComponent_td_110_Template, 3, 1, \"td\", 33);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(111, 36);\n          i0.ɵɵtemplate(112, VscodeExtensionComponent_th_112_Template, 2, 0, \"th\", 32)(113, VscodeExtensionComponent_td_113_Template, 2, 1, \"td\", 33);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(114, VscodeExtensionComponent_tr_114_Template, 1, 0, \"tr\", 37)(115, VscodeExtensionComponent_tr_115_Template, 1, 0, \"tr\", 38);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(116, \"mat-tab\", 39)(117, \"div\", 18)(118, \"h2\");\n          i0.ɵɵtext(119, \"Using the Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"p\");\n          i0.ɵɵtext(121, \"Learn how to effectively use SPT extension features in your daily development workflow.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"div\", 40)(123, \"mat-accordion\", 41);\n          i0.ɵɵtemplate(124, VscodeExtensionComponent_mat_expansion_panel_124_Template, 13, 7, \"mat-expansion-panel\", 42);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(125, \"mat-tab\", 43)(126, \"div\", 18)(127, \"h2\");\n          i0.ɵɵtext(128, \"Troubleshooting\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"p\");\n          i0.ɵɵtext(130, \"Common issues and solutions for the SPT VS Code extension.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"div\", 44);\n          i0.ɵɵtemplate(132, VscodeExtensionComponent_mat_card_132_Template, 15, 7, \"mat-card\", 45);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngForOf\", ctx.primaryFeatures);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.extensionFeatures);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.installationSteps);\n          i0.ɵɵadvance(45);\n          i0.ɵɵtextInterpolate(ctx.configExample);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"dataSource\", ctx.configSettings);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.settingsColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.settingsColumns);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.usageExamples);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.troubleshootingIssues);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, MatChipsModule, i6.MatChip, MatExpansionModule, i7.MatAccordion, i7.MatExpansionPanel, i7.MatExpansionPanelHeader, i7.MatExpansionPanelTitle, i7.MatExpansionPanelDescription, MatTableModule, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow],\n      styles: [\".vscode-extension-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  color: #1976d2;\\n  margin: 0 0 8px 0;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1em;\\n  margin: 0;\\n}\\n\\n.extension-overview[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.overview-card[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.quick-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 16px;\\n  margin-top: 16px;\\n}\\n\\n.stat[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n}\\n\\n.extension-tabs[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 24px 0;\\n}\\n\\n.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  margin-bottom: 8px;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n  margin-top: 24px;\\n}\\n\\n.benefits-list[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.installation-steps[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.step-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.code-block[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin: 16px 0;\\n}\\n\\n.code-header[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 8px 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px;\\n  background: #fafafa;\\n  overflow-x: auto;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.9em;\\n}\\n\\n.step-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 12px;\\n  padding: 8px 12px;\\n  background: #e3f2fd;\\n  border-radius: 4px;\\n  color: #1976d2;\\n}\\n\\n.config-sections[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.config-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.settings-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.settings-table[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 0.9em;\\n}\\n\\n.usage-sections[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.usage-panels[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.usage-content[_ngcontent-%COMP%] {\\n  padding: 16px 0;\\n}\\n\\n.usage-steps[_ngcontent-%COMP%], \\n.usage-tips[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.usage-steps[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.usage-tips[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #1976d2;\\n}\\n\\n.tips-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.tips-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n  padding: 8px;\\n  background: #fff3e0;\\n  border-radius: 4px;\\n}\\n\\n.tips-list[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #f57c00;\\n  margin-top: 2px;\\n}\\n\\n.troubleshooting-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 24px;\\n  margin-top: 24px;\\n}\\n\\n.troubleshooting-card[_ngcontent-%COMP%] {\\n  height: fit-content;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%], \\n.prevention-tips[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.prevention-tips[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #1976d2;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  color: #666;\\n}\\n\\n.prevention-tips[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .quick-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .troubleshooting-sections[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatTabsModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatChipsModule", "MatExpansionModule", "MatTableModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "benefit_r1", "ɵɵtemplate", "VscodeExtensionComponent_div_29_div_9_li_2_Template", "ɵɵproperty", "feature_r2", "benefits", "VscodeExtensionComponent_div_29_div_9_Template", "ɵɵstyleProp", "color", "icon", "title", "description", "length", "benefit_r3", "VscodeExtensionComponent_mat_card_38_div_9_li_4_Template", "feature_r4", "VscodeExtensionComponent_mat_card_38_div_9_Template", "ctx_r4", "getFeatureColor", "ɵɵtextInterpolate1", "step_r6", "command", "notes", "VscodeExtensionComponent_mat_card_46_div_12_Template", "VscodeExtensionComponent_mat_card_46_div_13_Template", "i_r7", "subtitle", "setting_r8", "key", "setting_r9", "type", "setting_r10", "default", "setting_r11", "ɵɵelement", "step_r12", "VscodeExtensionComponent_mat_expansion_panel_124_div_11_li_4_Template", "usage_r13", "steps", "tip_r14", "VscodeExtensionComponent_mat_expansion_panel_124_div_12_li_4_Template", "tips", "VscodeExtensionComponent_mat_expansion_panel_124_div_11_Template", "VscodeExtensionComponent_mat_expansion_panel_124_div_12_Template", "expanded", "details", "step_r15", "issue_r16", "prevention", "VscodeExtensionComponent_mat_card_132_li_13_Template", "VscodeExtensionComponent_mat_card_132_div_14_Template", "severity", "problem", "solution", "VscodeExtensionComponent", "constructor", "settingsColumns", "config<PERSON><PERSON><PERSON>", "extensionFeatures", "installationSteps", "configSettings", "usageExamples", "troubleshootingIssues", "colors", "selectors", "decls", "vars", "consts", "template", "VscodeExtensionComponent_Template", "rf", "ctx", "VscodeExtensionComponent_div_29_Template", "VscodeExtensionComponent_mat_card_38_Template", "VscodeExtensionComponent_mat_card_46_Template", "ɵɵelementContainerStart", "VscodeExtensionComponent_th_103_Template", "VscodeExtensionComponent_td_104_Template", "VscodeExtensionComponent_th_106_Template", "VscodeExtensionComponent_td_107_Template", "VscodeExtensionComponent_th_109_Template", "VscodeExtensionComponent_td_110_Template", "VscodeExtensionComponent_th_112_Template", "VscodeExtensionComponent_td_113_Template", "VscodeExtensionComponent_tr_114_Template", "VscodeExtensionComponent_tr_115_Template", "VscodeExtensionComponent_mat_expansion_panel_124_Template", "VscodeExtensionComponent_mat_card_132_Template", "primaryFeatures", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "Mat<PERSON><PERSON>", "MatTabGroup", "i3", "MatCard", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i4", "MatIcon", "i5", "MatButton", "i6", "MatChip", "i7", "Mat<PERSON><PERSON>rdi<PERSON>", "MatExpansionPanel", "MatExpansionPanelHeader", "MatExpansionPanelTitle", "MatExpansionPanelDescription", "i8", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\vscode-extension\\vscode-extension.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\n\ninterface ExtensionFeature {\n  title: string;\n  description: string;\n  icon: string;\n  screenshot?: string;\n  benefits: string[];\n}\n\ninterface ConfigSetting {\n  key: string;\n  type: string;\n  default: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-vscode-extension',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTabsModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatChipsModule,\n    MatExpansionModule,\n    MatTableModule\n  ],\n  template: `\n    <div class=\"spt-vscode-container\">\n      <!-- Hero Section -->\n      <header class=\"spt-hero-section\">\n        <div class=\"spt-hero-content\">\n          <div class=\"spt-hero-icon\">\n            <mat-icon>extension</mat-icon>\n          </div>\n          <div class=\"spt-hero-text\">\n            <h1 class=\"spt-hero-title\">VS Code Extension</h1>\n            <p class=\"spt-hero-subtitle\">\n              Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration.\n            </p>\n          </div>\n        </div>\n        <div class=\"spt-hero-actions\">\n          <button mat-raised-button color=\"primary\" class=\"spt-primary-btn\">\n            <mat-icon>download</mat-icon>\n            <span>Install Extension</span>\n          </button>\n          <button mat-stroked-button class=\"spt-secondary-btn\">\n            <mat-icon>code</mat-icon>\n            <span>View Source</span>\n          </button>\n        </div>\n      </header>\n\n      <!-- Key Features Overview -->\n      <section class=\"spt-features-overview\">\n        <div class=\"spt-section-header\">\n          <h2 class=\"spt-section-title\">Key Features</h2>\n          <p class=\"spt-section-subtitle\">\n            Discover the powerful features that make SPT extension essential for secure blockchain development.\n          </p>\n        </div>\n\n        <div class=\"spt-features-grid\">\n          <div class=\"spt-feature-card spt-feature-primary\" *ngFor=\"let feature of primaryFeatures\">\n            <div class=\"spt-feature-icon\" [style.background]=\"feature.color\">\n              <mat-icon>{{ feature.icon }}</mat-icon>\n            </div>\n            <div class=\"spt-feature-content\">\n              <h3 class=\"spt-feature-title\">{{ feature.title }}</h3>\n              <p class=\"spt-feature-description\">{{ feature.description }}</p>\n              <div class=\"spt-feature-benefits\" *ngIf=\"feature.benefits.length > 0\">\n                <ul class=\"spt-benefits-list\">\n                  <li *ngFor=\"let benefit of feature.benefits\" class=\"spt-benefit-item\">\n                    <mat-icon class=\"spt-benefit-icon\">check_circle</mat-icon>\n                    <span>{{ benefit }}</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <mat-tab-group class=\"extension-tabs\" animationDuration=\"300ms\">\n        <!-- Features Tab -->\n        <mat-tab label=\"Features\">\n          <div class=\"tab-content\">\n            <h2>Key Features</h2>\n            <p>Discover the powerful features that make SPT extension essential for secure blockchain development.</p>\n            \n            <div class=\"features-grid\">\n              <mat-card class=\"feature-card\" *ngFor=\"let feature of extensionFeatures\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar [style.background-color]=\"getFeatureColor(feature.icon)\">\n                    {{ feature.icon }}\n                  </mat-icon>\n                  <mat-card-title>{{ feature.title }}</mat-card-title>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>{{ feature.description }}</p>\n                  <div class=\"benefits-list\" *ngIf=\"feature.benefits.length > 0\">\n                    <h4>Benefits:</h4>\n                    <ul>\n                      <li *ngFor=\"let benefit of feature.benefits\">{{ benefit }}</li>\n                    </ul>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Installation Tab -->\n        <mat-tab label=\"Installation\">\n          <div class=\"tab-content\">\n            <h2>Installation Guide</h2>\n            <p>Get the SPT extension installed and configured in VS Code.</p>\n            \n            <div class=\"installation-steps\">\n              <mat-card class=\"step-card\" *ngFor=\"let step of installationSteps; let i = index\">\n                <mat-card-header>\n                  <div mat-card-avatar class=\"step-number\">{{ i + 1 }}</div>\n                  <mat-card-title>{{ step.title }}</mat-card-title>\n                  <mat-card-subtitle>{{ step.subtitle }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>{{ step.description }}</p>\n                  <div class=\"step-content\">\n                    <div class=\"code-block\" *ngIf=\"step.command\">\n                      <div class=\"code-header\">\n                        <mat-icon>terminal</mat-icon>\n                        <span>Command</span>\n                      </div>\n                      <pre><code>{{ step.command }}</code></pre>\n                    </div>\n\n                  </div>\n                  <div class=\"step-notes\" *ngIf=\"step.notes\">\n                    <mat-icon>info</mat-icon>\n                    <span>{{ step.notes }}</span>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Configuration Tab -->\n        <mat-tab label=\"Configuration\">\n          <div class=\"tab-content\">\n            <h2>Extension Configuration</h2>\n            <p>Customize the SPT extension to fit your development workflow and preferences.</p>\n            \n            <div class=\"config-sections\">\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>settings</mat-icon>\n                  <mat-card-title>Settings Overview</mat-card-title>\n                  <mat-card-subtitle>Configure extension behavior</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>Access extension settings through VS Code preferences:</p>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>keyboard</mat-icon>\n                      <span>Keyboard Shortcut</span>\n                    </div>\n                    <pre><code>Ctrl+Shift+P → \"Preferences: Open Settings (JSON)\"</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>code</mat-icon>\n                  <mat-card-title>Configuration Example</mat-card-title>\n                  <mat-card-subtitle>settings.json</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>code</mat-icon>\n                      <span>JSON Configuration</span>\n                    </div>\n                    <pre><code>{{ configExample }}</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>list</mat-icon>\n                  <mat-card-title>Available Settings</mat-card-title>\n                  <mat-card-subtitle>Complete settings reference</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <table mat-table [dataSource]=\"configSettings\" class=\"settings-table\">\n                    <ng-container matColumnDef=\"key\">\n                      <th mat-header-cell *matHeaderCellDef>Setting</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.key }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"type\">\n                      <th mat-header-cell *matHeaderCellDef>Type</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <mat-chip>{{ setting.type }}</mat-chip>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"default\">\n                      <th mat-header-cell *matHeaderCellDef>Default</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.default }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"description\">\n                      <th mat-header-cell *matHeaderCellDef>Description</th>\n                      <td mat-cell *matCellDef=\"let setting\">{{ setting.description }}</td>\n                    </ng-container>\n                    <tr mat-header-row *matHeaderRowDef=\"settingsColumns\"></tr>\n                    <tr mat-row *matRowDef=\"let row; columns: settingsColumns;\"></tr>\n                  </table>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Usage Tab -->\n        <mat-tab label=\"Usage\">\n          <div class=\"tab-content\">\n            <h2>Using the Extension</h2>\n            <p>Learn how to effectively use SPT extension features in your daily development workflow.</p>\n            \n            <div class=\"usage-sections\">\n              <mat-accordion class=\"usage-panels\">\n                <mat-expansion-panel *ngFor=\"let usage of usageExamples\" [expanded]=\"usage.expanded\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <mat-icon>{{ usage.icon }}</mat-icon>\n                      {{ usage.title }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      {{ usage.description }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n                  \n                  <div class=\"usage-content\">\n                    <p>{{ usage.details }}</p>\n                    <div class=\"usage-steps\" *ngIf=\"usage.steps\">\n                      <h4>Steps:</h4>\n                      <ol>\n                        <li *ngFor=\"let step of usage.steps\">{{ step }}</li>\n                      </ol>\n                    </div>\n                    <div class=\"usage-tips\" *ngIf=\"usage.tips\">\n                      <h4>Tips:</h4>\n                      <ul class=\"tips-list\">\n                        <li *ngFor=\"let tip of usage.tips\">\n                          <mat-icon>lightbulb</mat-icon>\n                          <span>{{ tip }}</span>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Troubleshooting Tab -->\n        <mat-tab label=\"Troubleshooting\">\n          <div class=\"tab-content\">\n            <h2>Troubleshooting</h2>\n            <p>Common issues and solutions for the SPT VS Code extension.</p>\n            \n            <div class=\"troubleshooting-sections\">\n              <mat-card class=\"troubleshooting-card\" *ngFor=\"let issue of troubleshootingIssues\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar [style.background-color]=\"issue.severity === 'high' ? '#f44336' : '#ff9800'\">\n                    {{ issue.icon }}\n                  </mat-icon>\n                  <mat-card-title>{{ issue.problem }}</mat-card-title>\n                  <mat-card-subtitle>{{ issue.description }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"solution-steps\">\n                    <h4>Solution:</h4>\n                    <ol>\n                      <li *ngFor=\"let step of issue.solution\">{{ step }}</li>\n                    </ol>\n                  </div>\n                  <div class=\"prevention-tips\" *ngIf=\"issue.prevention\">\n                    <h4>Prevention:</h4>\n                    <p>{{ issue.prevention }}</p>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styles: [`\n    .vscode-extension-container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .page-header {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .page-header h1 {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      color: #1976d2;\n      margin: 0 0 8px 0;\n    }\n\n    .page-subtitle {\n      color: #666;\n      font-size: 1.1em;\n      margin: 0;\n    }\n\n    .extension-overview {\n      margin-bottom: 32px;\n    }\n\n    .overview-card {\n      border: 1px solid #e0e0e0;\n    }\n\n    .quick-stats {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 16px;\n      margin-top: 16px;\n    }\n\n    .stat {\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .extension-tabs {\n      margin-bottom: 32px;\n    }\n\n    .tab-content {\n      padding: 24px 0;\n    }\n\n    .tab-content h2 {\n      color: #1976d2;\n      margin-bottom: 8px;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .benefits-list {\n      margin-top: 16px;\n    }\n\n    .installation-steps {\n      margin-top: 24px;\n    }\n\n    .step-card {\n      margin-bottom: 24px;\n    }\n\n    .step-number {\n      background: #1976d2;\n      color: white;\n      border-radius: 50%;\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n    }\n\n    .step-content {\n      margin-top: 16px;\n    }\n\n    .code-block {\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      overflow: hidden;\n      margin: 16px 0;\n    }\n\n    .code-header {\n      background: #f5f5f5;\n      padding: 8px 16px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-weight: 500;\n      border-bottom: 1px solid #e0e0e0;\n    }\n\n    .code-block pre {\n      margin: 0;\n      padding: 16px;\n      background: #fafafa;\n      overflow-x: auto;\n    }\n\n    .code-block code {\n      font-family: 'Courier New', monospace;\n      font-size: 0.9em;\n    }\n\n\n\n    .step-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #e3f2fd;\n      border-radius: 4px;\n      color: #1976d2;\n    }\n\n    .config-sections {\n      margin-top: 24px;\n    }\n\n    .config-section {\n      margin-bottom: 24px;\n    }\n\n    .settings-table {\n      width: 100%;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .settings-table code {\n      background: #f5f5f5;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-size: 0.9em;\n    }\n\n    .usage-sections {\n      margin-top: 24px;\n    }\n\n    .usage-panels {\n      margin-top: 16px;\n    }\n\n    .usage-content {\n      padding: 16px 0;\n    }\n\n    .usage-steps,\n    .usage-tips {\n      margin-top: 16px;\n    }\n\n    .usage-steps h4,\n    .usage-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .tips-list {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .tips-list li {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n      margin-bottom: 8px;\n      padding: 8px;\n      background: #fff3e0;\n      border-radius: 4px;\n    }\n\n    .tips-list mat-icon {\n      color: #f57c00;\n      margin-top: 2px;\n    }\n\n    .troubleshooting-sections {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .troubleshooting-card {\n      height: fit-content;\n    }\n\n    .solution-steps,\n    .prevention-tips {\n      margin-top: 16px;\n    }\n\n    .solution-steps h4,\n    .prevention-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .solution-steps ol {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .solution-steps li {\n      margin-bottom: 8px;\n      color: #666;\n    }\n\n    .prevention-tips p {\n      margin: 0;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .quick-stats {\n        grid-template-columns: 1fr;\n      }\n      \n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .troubleshooting-sections {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class VscodeExtensionComponent {\n  settingsColumns: string[] = ['key', 'type', 'default', 'description'];\n\n  configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n\n  extensionFeatures: ExtensionFeature[] = [\n    {\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities.',\n      icon: 'security',\n      benefits: [\n        'Immediate vulnerability detection',\n        'Reduced security debt',\n        'Faster development cycles',\n        'Proactive security measures'\n      ]\n    },\n    {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues and suggestions.',\n      icon: 'visibility',\n      benefits: [\n        'Clear visual feedback',\n        'Context-aware highlighting',\n        'Severity-based color coding',\n        'Non-intrusive indicators'\n      ]\n    },\n    {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and fixes.',\n      icon: 'bug_report',\n      benefits: [\n        'Centralized issue tracking',\n        'Detailed error descriptions',\n        'Quick navigation to issues',\n        'Integration with existing workflow'\n      ]\n    },\n    {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code.',\n      icon: 'lens',\n      benefits: [\n        'Contextual security metrics',\n        'One-click security actions',\n        'Code quality insights',\n        'Performance recommendations'\n      ]\n    },\n    {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements.',\n      icon: 'info',\n      benefits: [\n        'Instant security documentation',\n        'Best practice suggestions',\n        'Vulnerability explanations',\n        'Quick reference access'\n      ]\n    },\n    {\n      title: 'Multi-chain Support',\n      description: 'Support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      benefits: [\n        'Comprehensive blockchain coverage',\n        'Chain-specific security rules',\n        'Unified security approach',\n        'Extensible architecture'\n      ]\n    }\n  ];\n\n  installationSteps = [\n    {\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    },\n    {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    },\n    {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }\n  ];\n\n  configSettings: ConfigSetting[] = [\n    { key: 'spt.enabled', type: 'boolean', default: 'true', description: 'Enable/disable SPT security analysis' },\n    { key: 'spt.serverUrl', type: 'string', default: 'http://localhost:8080', description: 'SPT backend server URL' },\n    { key: 'spt.apiKey', type: 'string', default: '\"\"', description: 'API key for authentication' },\n    { key: 'spt.autoScan', type: 'boolean', default: 'true', description: 'Automatically scan files on save' },\n    { key: 'spt.scanOnOpen', type: 'boolean', default: 'false', description: 'Automatically scan files when opened' },\n    { key: 'spt.chains', type: 'array', default: '[\"ethereum\", \"bitcoin\", \"general\"]', description: 'Blockchain chains to analyze' },\n    { key: 'spt.severity', type: 'string', default: '\"medium\"', description: 'Minimum severity level to show' },\n    { key: 'spt.showInlineDecorations', type: 'boolean', default: 'true', description: 'Show inline security decorations' },\n    { key: 'spt.showProblems', type: 'boolean', default: 'true', description: 'Show security issues in Problems panel' },\n    { key: 'spt.enableCodeLens', type: 'boolean', default: 'true', description: 'Enable security-related CodeLens' },\n    { key: 'spt.enableHover', type: 'boolean', default: 'true', description: 'Enable security information on hover' }\n  ];\n\n  usageExamples = [\n    {\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: [\n        'Open a blockchain project in VS Code',\n        'Save a file to trigger automatic scanning',\n        'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"',\n        'View results in Problems panel or inline decorations'\n      ],\n      tips: [\n        'Enable auto-scan for continuous security monitoring',\n        'Use the Problems panel to navigate between issues',\n        'Check the status bar for scan progress'\n      ]\n    },\n    {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: [\n        'Hover over highlighted code to see issue details',\n        'Click on issues in Problems panel for more information',\n        'Use CodeLens actions for quick fixes',\n        'Follow the recommended solutions'\n      ],\n      tips: [\n        'Start with critical and high severity issues',\n        'Use hover information for quick context',\n        'Check references for additional learning'\n      ]\n    },\n    {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: [\n        'Open VS Code settings (Ctrl+,)',\n        'Search for \"SPT\" to find extension settings',\n        'Adjust chains, severity, and scan triggers',\n        'Save settings and restart if needed'\n      ],\n      tips: [\n        'Use workspace settings for project-specific configuration',\n        'Adjust severity threshold based on project maturity',\n        'Enable scan-on-open for comprehensive coverage'\n      ]\n    }\n  ];\n\n  troubleshootingIssues = [\n    {\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: [\n        'Verify SPT backend server is running on configured port',\n        'Check spt.serverUrl setting in VS Code preferences',\n        'Ensure firewall is not blocking the connection',\n        'Try restarting VS Code and the SPT server'\n      ],\n      prevention: 'Always start the SPT backend server before using the extension'\n    },\n    {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: [\n        'Check if the file type is supported (Solidity, JavaScript, etc.)',\n        'Verify the correct blockchain chains are selected',\n        'Lower the severity threshold in settings',\n        'Ensure the file contains actual security-relevant code'\n      ],\n      prevention: 'Review supported file types and ensure proper project structure'\n    },\n    {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: [\n        'Disable auto-scan and use manual scanning',\n        'Increase scan timeout in settings',\n        'Exclude large files or directories from scanning',\n        'Reduce the number of enabled blockchain chains'\n      ],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }\n  ];\n\n  getFeatureColor(icon: string): string {\n    const colors: { [key: string]: string } = {\n      'security': '#1976d2',\n      'visibility': '#4caf50',\n      'bug_report': '#f44336',\n      'lens': '#ff9800',\n      'info': '#9c27b0',\n      'link': '#00bcd4'\n    };\n    return colors[icon] || '#1976d2';\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;;IA6EpCC,EADF,CAAAC,cAAA,aAAsE,mBACjC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1DH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAa;IACrBF,EADqB,CAAAG,YAAA,EAAO,EACvB;;;;IADGH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAa;;;;;IAHvBN,EADF,CAAAC,cAAA,cAAsE,aACtC;IAC5BD,EAAA,CAAAO,UAAA,IAAAC,mDAAA,iBAAsE;IAK1ER,EADE,CAAAG,YAAA,EAAK,EACD;;;;IALsBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,YAAAC,UAAA,CAAAC,QAAA,CAAmB;;;;;IAP/CX,EAFJ,CAAAC,cAAA,cAA0F,cACvB,eACrD;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACnC;IAEJH,EADF,CAAAC,cAAA,cAAiC,aACD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChEH,EAAA,CAAAO,UAAA,IAAAK,8CAAA,kBAAsE;IAS1EZ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAf0BH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAa,WAAA,eAAAH,UAAA,CAAAI,KAAA,CAAkC;IACpDd,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAK,IAAA,CAAkB;IAGEf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAM,KAAA,CAAmB;IACdhB,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAO,WAAA,CAAyB;IACzBjB,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAC,QAAA,CAAAO,MAAA,KAAiC;;;;;IAiC5DlB,EAAA,CAAAC,cAAA,SAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAc,UAAA,CAAa;;;;;IAF5DnB,EADF,CAAAC,cAAA,cAA+D,SACzD;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAAa,wDAAA,iBAA6C;IAEjDpB,EADE,CAAAG,YAAA,EAAK,EACD;;;;IAFsBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,YAAAY,UAAA,CAAAV,QAAA,CAAmB;;;;;IAV/CX,EAFJ,CAAAC,cAAA,mBAAyE,sBACtD,mBACoE;IACjFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChCH,EAAA,CAAAO,UAAA,IAAAe,mDAAA,kBAA+D;IAOnEtB,EADE,CAAAG,YAAA,EAAmB,EACV;;;;;IAdmBH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAa,WAAA,qBAAAU,MAAA,CAAAC,eAAA,CAAAH,UAAA,CAAAN,IAAA,EAAwD;IAChFf,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAyB,kBAAA,MAAAJ,UAAA,CAAAN,IAAA,MACF;IACgBf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAgB,UAAA,CAAAL,KAAA,CAAmB;IAGhChB,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAgB,UAAA,CAAAJ,WAAA,CAAyB;IACAjB,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAS,UAAA,SAAAY,UAAA,CAAAV,QAAA,CAAAO,MAAA,KAAiC;;;;;IA8BvDlB,EAFJ,CAAAC,cAAA,cAA6C,cAClB,eACb;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,cAAO;IACfF,EADe,CAAAG,YAAA,EAAO,EAChB;IACDH,EAAL,CAAAC,cAAA,UAAK,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAAM,EACtC;;;;IADOH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAC,OAAA,CAAkB;;;;;IAK/B3B,EADF,CAAAC,cAAA,cAA2C,eAC/B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EACzB;;;;IADEH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAE,KAAA,CAAgB;;;;;IAlBxB5B,EAFJ,CAAAC,cAAA,mBAAkF,sBAC/D,cAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACxCF,EADwC,CAAAG,YAAA,EAAoB,EAC1C;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAO,UAAA,KAAAsB,oDAAA,kBAA6C;IAQ/C7B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAO,UAAA,KAAAuB,oDAAA,kBAA2C;IAK/C9B,EADE,CAAAG,YAAA,EAAmB,EACV;;;;;IArBkCH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAA0B,IAAA,KAAW;IACpC/B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAV,KAAA,CAAgB;IACbhB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAM,QAAA,CAAmB;IAGnChC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAT,WAAA,CAAsB;IAEEjB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAS,UAAA,SAAAiB,OAAA,CAAAC,OAAA,CAAkB;IASpB3B,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,SAAAiB,OAAA,CAAAE,KAAA,CAAgB;;;;;IA6DrC5B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAuC,WAC/B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC3B;;;;IADGH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA4B,UAAA,CAAAC,GAAA,CAAiB;;;;;IAIzBlC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAuC,eAC3B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACpC;;;;IADOH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA8B,UAAA,CAAAC,IAAA,CAAkB;;;;;IAI9BpC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAuC,WAC/B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC7BF,EAD6B,CAAAG,YAAA,EAAO,EAC/B;;;;IADGH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAgC,WAAA,CAAAC,OAAA,CAAqB;;;;;IAI7BtC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACtDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAkC,WAAA,CAAAtB,WAAA,CAAyB;;;;;IAElEjB,EAAA,CAAAwC,SAAA,aAA2D;;;;;IAC3DxC,EAAA,CAAAwC,SAAA,aAAiE;;;;;IAgC7DxC,EAAA,CAAAC,cAAA,SAAqC;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAoC,QAAA,CAAU;;;;;IAFjDzC,EADF,CAAAC,cAAA,cAA6C,SACvC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAAmC,qEAAA,iBAAqC;IAEzC1C,EADE,CAAAG,YAAA,EAAK,EACD;;;;IAFmBH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAS,UAAA,YAAAkC,SAAA,CAAAC,KAAA,CAAc;;;;;IAOjC5C,EADF,CAAAC,cAAA,SAAmC,eACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EACnB;;;;IADGH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAwC,OAAA,CAAS;;;;;IAJnB7C,EADF,CAAAC,cAAA,cAA2C,SACrC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAO,UAAA,IAAAuC,qEAAA,iBAAmC;IAKvC9C,EADE,CAAAG,YAAA,EAAK,EACD;;;;IALkBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAS,UAAA,YAAAkC,SAAA,CAAAI,IAAA,CAAa;;;;;IAnBnC/C,EAHN,CAAAC,cAAA,8BAAqF,iCACvD,sBACT,eACL;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrCH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAG3BH,EADF,CAAAC,cAAA,cAA2B,QACtB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAO1BH,EANA,CAAAO,UAAA,KAAAyC,gEAAA,kBAA6C,KAAAC,gEAAA,kBAMF;IAU/CjD,EADE,CAAAG,YAAA,EAAM,EACc;;;;IA7BmCH,EAAA,CAAAS,UAAA,aAAAkC,SAAA,CAAAO,QAAA,CAA2B;IAGpElD,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAsC,SAAA,CAAA5B,IAAA,CAAgB;IAC1Bf,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAyB,kBAAA,MAAAkB,SAAA,CAAA3B,KAAA,MACF;IAEEhB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAyB,kBAAA,MAAAkB,SAAA,CAAA1B,WAAA,MACF;IAIGjB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAsC,SAAA,CAAAQ,OAAA,CAAmB;IACInD,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAS,UAAA,SAAAkC,SAAA,CAAAC,KAAA,CAAiB;IAMlB5C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,SAAAkC,SAAA,CAAAI,IAAA,CAAgB;;;;;IAmCvC/C,EAAA,CAAAC,cAAA,SAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAA+C,QAAA,CAAU;;;;;IAIpDpD,EADF,CAAAC,cAAA,cAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;;IADDH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAgD,SAAA,CAAAC,UAAA,CAAsB;;;;;IAf3BtD,EAFJ,CAAAC,cAAA,mBAAmF,sBAChE,mBACwF;IACrGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACpDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAC5CF,EAD4C,CAAAG,YAAA,EAAoB,EAC9C;IAGdH,EAFJ,CAAAC,cAAA,uBAAkB,cACY,UACtB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAO,UAAA,KAAAgD,oDAAA,iBAAwC;IAE5CvD,EADE,CAAAG,YAAA,EAAK,EACD;IACNH,EAAA,CAAAO,UAAA,KAAAiD,qDAAA,kBAAsD;IAK1DxD,EADE,CAAAG,YAAA,EAAmB,EACV;;;;IAlBmBH,EAAA,CAAAI,SAAA,GAA4E;IAA5EJ,EAAA,CAAAa,WAAA,qBAAAwC,SAAA,CAAAI,QAAA,oCAA4E;IACpGzD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAyB,kBAAA,MAAA4B,SAAA,CAAAtC,IAAA,MACF;IACgBf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAgD,SAAA,CAAAK,OAAA,CAAmB;IAChB1D,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAgD,SAAA,CAAApC,WAAA,CAAuB;IAMjBjB,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAS,UAAA,YAAA4C,SAAA,CAAAM,QAAA,CAAiB;IAGZ3D,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAS,UAAA,SAAA4C,SAAA,CAAAC,UAAA,CAAsB;;;AA0QtE,OAAM,MAAOM,wBAAwB;EAliBrCC,YAAA;IAmiBE,KAAAC,eAAe,GAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAErE,KAAAC,aAAa,GAAG;;;;;;;;;;;;EAYhB;IAEA,KAAAC,iBAAiB,GAAuB,CACtC;MACEhD,KAAK,EAAE,6BAA6B;MACpCC,WAAW,EAAE,8FAA8F;MAC3GF,IAAI,EAAE,UAAU;MAChBJ,QAAQ,EAAE,CACR,mCAAmC,EACnC,uBAAuB,EACvB,2BAA2B,EAC3B,6BAA6B;KAEhC,EACD;MACEK,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,uFAAuF;MACpGF,IAAI,EAAE,YAAY;MAClBJ,QAAQ,EAAE,CACR,uBAAuB,EACvB,4BAA4B,EAC5B,6BAA6B,EAC7B,0BAA0B;KAE7B,EACD;MACEK,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE,2FAA2F;MACxGF,IAAI,EAAE,YAAY;MAClBJ,QAAQ,EAAE,CACR,4BAA4B,EAC5B,6BAA6B,EAC7B,4BAA4B,EAC5B,oCAAoC;KAEvC,EACD;MACEK,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,iFAAiF;MAC9FF,IAAI,EAAE,MAAM;MACZJ,QAAQ,EAAE,CACR,6BAA6B,EAC7B,4BAA4B,EAC5B,uBAAuB,EACvB,6BAA6B;KAEhC,EACD;MACEK,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,gFAAgF;MAC7FF,IAAI,EAAE,MAAM;MACZJ,QAAQ,EAAE,CACR,gCAAgC,EAChC,2BAA2B,EAC3B,4BAA4B,EAC5B,wBAAwB;KAE3B,EACD;MACEK,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,0EAA0E;MACvFF,IAAI,EAAE,MAAM;MACZJ,QAAQ,EAAE,CACR,mCAAmC,EACnC,+BAA+B,EAC/B,2BAA2B,EAC3B,yBAAyB;KAE5B,CACF;IAED,KAAAsD,iBAAiB,GAAG,CAClB;MACEjD,KAAK,EAAE,kCAAkC;MACzCgB,QAAQ,EAAE,oBAAoB;MAC9Bf,WAAW,EAAE,8EAA8E;MAC3FU,OAAO,EAAE,uCAAuC;MAChDC,KAAK,EAAE;KACR,EACD;MACEZ,KAAK,EAAE,8BAA8B;MACrCgB,QAAQ,EAAE,uBAAuB;MACjCf,WAAW,EAAE,gEAAgE;MAC7EU,OAAO,EAAE,6CAA6C;MACtDC,KAAK,EAAE;KACR,EACD;MACEZ,KAAK,EAAE,qBAAqB;MAC5BgB,QAAQ,EAAE,qBAAqB;MAC/Bf,WAAW,EAAE,yEAAyE;MACtFW,KAAK,EAAE;KACR,CACF;IAED,KAAAsC,cAAc,GAAoB,CAChC;MAAEhC,GAAG,EAAE,aAAa;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAsC,CAAE,EAC7G;MAAEiB,GAAG,EAAE,eAAe;MAAEE,IAAI,EAAE,QAAQ;MAAEE,OAAO,EAAE,uBAAuB;MAAErB,WAAW,EAAE;IAAwB,CAAE,EACjH;MAAEiB,GAAG,EAAE,YAAY;MAAEE,IAAI,EAAE,QAAQ;MAAEE,OAAO,EAAE,IAAI;MAAErB,WAAW,EAAE;IAA4B,CAAE,EAC/F;MAAEiB,GAAG,EAAE,cAAc;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAkC,CAAE,EAC1G;MAAEiB,GAAG,EAAE,gBAAgB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,OAAO;MAAErB,WAAW,EAAE;IAAsC,CAAE,EACjH;MAAEiB,GAAG,EAAE,YAAY;MAAEE,IAAI,EAAE,OAAO;MAAEE,OAAO,EAAE,oCAAoC;MAAErB,WAAW,EAAE;IAA8B,CAAE,EAChI;MAAEiB,GAAG,EAAE,cAAc;MAAEE,IAAI,EAAE,QAAQ;MAAEE,OAAO,EAAE,UAAU;MAAErB,WAAW,EAAE;IAAgC,CAAE,EAC3G;MAAEiB,GAAG,EAAE,2BAA2B;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAkC,CAAE,EACvH;MAAEiB,GAAG,EAAE,kBAAkB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAwC,CAAE,EACpH;MAAEiB,GAAG,EAAE,oBAAoB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAkC,CAAE,EAChH;MAAEiB,GAAG,EAAE,iBAAiB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAsC,CAAE,CAClH;IAED,KAAAkD,aAAa,GAAG,CACd;MACEnD,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE,uCAAuC;MACpDF,IAAI,EAAE,SAAS;MACfmC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,yGAAyG;MAClHP,KAAK,EAAE,CACL,sCAAsC,EACtC,2CAA2C,EAC3C,gDAAgD,EAChD,sDAAsD,CACvD;MACDG,IAAI,EAAE,CACJ,qDAAqD,EACrD,mDAAmD,EACnD,wCAAwC;KAE3C,EACD;MACE/B,KAAK,EAAE,+BAA+B;MACtCC,WAAW,EAAE,gDAAgD;MAC7DF,IAAI,EAAE,MAAM;MACZoC,OAAO,EAAE,mHAAmH;MAC5HP,KAAK,EAAE,CACL,kDAAkD,EAClD,wDAAwD,EACxD,sCAAsC,EACtC,kCAAkC,CACnC;MACDG,IAAI,EAAE,CACJ,8CAA8C,EAC9C,yCAAyC,EACzC,0CAA0C;KAE7C,EACD;MACE/B,KAAK,EAAE,2BAA2B;MAClCC,WAAW,EAAE,8CAA8C;MAC3DF,IAAI,EAAE,MAAM;MACZoC,OAAO,EAAE,mFAAmF;MAC5FP,KAAK,EAAE,CACL,gCAAgC,EAChC,6CAA6C,EAC7C,4CAA4C,EAC5C,qCAAqC,CACtC;MACDG,IAAI,EAAE,CACJ,2DAA2D,EAC3D,qDAAqD,EACrD,gDAAgD;KAEnD,CACF;IAED,KAAAqB,qBAAqB,GAAG,CACtB;MACEV,OAAO,EAAE,oCAAoC;MAC7CzC,WAAW,EAAE,oDAAoD;MACjEF,IAAI,EAAE,WAAW;MACjB0C,QAAQ,EAAE,MAAM;MAChBE,QAAQ,EAAE,CACR,yDAAyD,EACzD,oDAAoD,EACpD,gDAAgD,EAChD,2CAA2C,CAC5C;MACDL,UAAU,EAAE;KACb,EACD;MACEI,OAAO,EAAE,6BAA6B;MACtCzC,WAAW,EAAE,0DAA0D;MACvEF,IAAI,EAAE,YAAY;MAClB0C,QAAQ,EAAE,QAAQ;MAClBE,QAAQ,EAAE,CACR,kEAAkE,EAClE,mDAAmD,EACnD,0CAA0C,EAC1C,wDAAwD,CACzD;MACDL,UAAU,EAAE;KACb,EACD;MACEI,OAAO,EAAE,oBAAoB;MAC7BzC,WAAW,EAAE,2CAA2C;MACxDF,IAAI,EAAE,OAAO;MACb0C,QAAQ,EAAE,QAAQ;MAClBE,QAAQ,EAAE,CACR,2CAA2C,EAC3C,mCAAmC,EACnC,kDAAkD,EAClD,gDAAgD,CACjD;MACDL,UAAU,EAAE;KACb,CACF;;EAED9B,eAAeA,CAACT,IAAY;IAC1B,MAAMsD,MAAM,GAA8B;MACxC,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,YAAY,EAAE,SAAS;MACvB,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE;KACT;IACD,OAAOA,MAAM,CAACtD,IAAI,CAAC,IAAI,SAAS;EAClC;;;uCAtOW6C,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAU,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/gBzB5E,EALR,CAAAC,cAAA,aAAkC,gBAEC,aACD,aACD,eACf;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EAC1B;UAEJH,EADF,CAAAC,cAAA,aAA2B,YACE;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjDH,EAAA,CAAAC,cAAA,WAA6B;UAC3BD,EAAA,CAAAE,MAAA,kIACF;UAEJF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAA8B,iBACsC,gBACtD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzBF,EADyB,CAAAG,YAAA,EAAO,EACvB;UAEPH,EADF,CAAAC,cAAA,iBAAqD,gBACzC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAGvBF,EAHuB,CAAAG,YAAA,EAAO,EACjB,EACL,EACC;UAKLH,EAFJ,CAAAC,cAAA,mBAAuC,eACL,cACA;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAC,cAAA,aAAgC;UAC9BD,EAAA,CAAAE,MAAA,6GACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAENH,EAAA,CAAAC,cAAA,eAA+B;UAC7BD,EAAA,CAAAO,UAAA,KAAAuE,wCAAA,mBAA0F;UAkB9F9E,EADE,CAAAG,YAAA,EAAM,EACE;UAMJH,EAJN,CAAAC,cAAA,yBAAgE,mBAEpC,eACC,UACnB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2GAAmG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE1GH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAO,UAAA,KAAAwE,6CAAA,wBAAyE;UAmB/E/E,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA8B,eACH,UACnB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kEAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjEH,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAO,UAAA,KAAAyE,6CAAA,wBAAkF;UA0BxFhF,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA+B,eACJ,UACnB;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qFAA6E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK9EH,EAHN,CAAAC,cAAA,eAA6B,oBACM,uBACd,oBACW;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAClDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UACjDF,EADiD,CAAAG,YAAA,EAAoB,EACnD;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAE,MAAA,8DAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGzDH,EAFJ,CAAAC,cAAA,eAAwB,eACG,gBACb;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;UACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;UAAAD,EAAA,CAAAE,MAAA,iEAAkD;UAGnEF,EAHmE,CAAAG,YAAA,EAAO,EAAM,EACtE,EACW,EACV;UAIPH,EAFJ,CAAAC,cAAA,oBAAiC,uBACd,oBACW;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzCH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACtDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAClCF,EADkC,CAAAG,YAAA,EAAoB,EACpC;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACQ,eACG,gBACb;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;UACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAGpCF,EAHoC,CAAAG,YAAA,EAAO,EAAM,EACvC,EACW,EACV;UAIPH,EAFJ,CAAAC,cAAA,oBAAiC,uBACd,oBACW;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzCH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACnDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,mCAA2B;UAChDF,EADgD,CAAAG,YAAA,EAAoB,EAClD;UAEhBH,EADF,CAAAC,cAAA,yBAAkB,kBACsD;UACpED,EAAA,CAAAiF,uBAAA,SAAiC;UAE/BjF,EADA,CAAAO,UAAA,MAAA2E,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAIzCnF,EAAA,CAAAiF,uBAAA,SAAkC;UAEhCjF,EADA,CAAAO,UAAA,MAAA6E,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAIzCrF,EAAA,CAAAiF,uBAAA,SAAqC;UAEnCjF,EADA,CAAAO,UAAA,MAAA+E,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAIzCvF,EAAA,CAAAiF,uBAAA,SAAyC;UAEvCjF,EADA,CAAAO,UAAA,MAAAiF,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAGzCzF,EADA,CAAAO,UAAA,MAAAmF,wCAAA,iBAAsD,MAAAC,wCAAA,iBACM;UAMxE3F,EALU,CAAAG,YAAA,EAAQ,EACS,EACV,EACP,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,oBAAuB,gBACI,WACnB;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,gGAAuF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG5FH,EADF,CAAAC,cAAA,gBAA4B,0BACU;UAClCD,EAAA,CAAAO,UAAA,MAAAqF,yDAAA,mCAAqF;UAiC7F5F,EAHM,CAAAG,YAAA,EAAgB,EACZ,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,oBAAiC,gBACN,WACnB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mEAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjEH,EAAA,CAAAC,cAAA,gBAAsC;UACpCD,EAAA,CAAAO,UAAA,MAAAsF,8CAAA,wBAAmF;UAyB7F7F,EAJQ,CAAAG,YAAA,EAAM,EACF,EACE,EACI,EACZ;;;UAhPsEH,EAAA,CAAAI,SAAA,IAAkB;UAAlBJ,EAAA,CAAAS,UAAA,YAAAoE,GAAA,CAAAiB,eAAA,CAAkB;UA4BjC9F,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAS,UAAA,YAAAoE,GAAA,CAAAb,iBAAA,CAAoB;UA4B1BhE,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAS,UAAA,YAAAoE,GAAA,CAAAZ,iBAAA,CAAsB;UAiElDjE,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAK,iBAAA,CAAAwE,GAAA,CAAAd,aAAA,CAAmB;UAYf/D,EAAA,CAAAI,SAAA,IAA6B;UAA7BJ,EAAA,CAAAS,UAAA,eAAAoE,GAAA,CAAAX,cAAA,CAA6B;UAuBxBlE,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAS,UAAA,oBAAAoE,GAAA,CAAAf,eAAA,CAAgC;UACnB9D,EAAA,CAAAI,SAAA,EAAyB;UAAzBJ,EAAA,CAAAS,UAAA,qBAAAoE,GAAA,CAAAf,eAAA,CAAyB;UAgBvB9D,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAS,UAAA,YAAAoE,GAAA,CAAAV,aAAA,CAAgB;UA0CAnE,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAS,UAAA,YAAAoE,GAAA,CAAAT,qBAAA,CAAwB;;;qBArQ3F5E,YAAY,EAAAuG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZxG,aAAa,EAAAyG,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACb1G,aAAa,EAAA2G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACbhH,aAAa,EAAAiH,EAAA,CAAAC,OAAA,EACbjH,eAAe,EAAAkH,EAAA,CAAAC,SAAA,EACflH,cAAc,EAAAmH,EAAA,CAAAC,OAAA,EACdnH,kBAAkB,EAAAoH,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,sBAAA,EAAAJ,EAAA,CAAAK,4BAAA,EAClBxH,cAAc,EAAAyH,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}