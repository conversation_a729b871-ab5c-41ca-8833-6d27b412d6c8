"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthUriHandler = void 0;
const vscode = __importStar(require("vscode"));
class AuthUriHandler {
    constructor(authManager) {
        this.authManager = authManager;
    }
    async handleUri(uri) {
        try {
            // Parse the URI
            const path = uri.path;
            const query = new URLSearchParams(uri.query);
            if (path === '/auth/callback') {
                await this.handleAuthCallback(query);
            }
            else {
                vscode.window.showWarningMessage(`Unknown URI path: ${path}`);
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to handle URI: ${error}`);
        }
    }
    async handleAuthCallback(query) {
        const token = query.get('token');
        const state = query.get('state');
        const error = query.get('error');
        const userParam = query.get('user');
        const expiresIn = query.get('expires_in');
        // Handle error case
        if (error) {
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
            return;
        }
        // Validate required parameters
        if (!token || !state || !userParam || !expiresIn) {
            vscode.window.showErrorMessage('Invalid authentication callback parameters');
            return;
        }
        // Validate state parameter
        if (!(await this.authManager.validatePendingAuth(state))) {
            vscode.window.showErrorMessage('Invalid authentication state. Please try again.');
            return;
        }
        try {
            // Parse user data
            const user = JSON.parse(decodeURIComponent(userParam));
            const expiresInSeconds = parseInt(expiresIn, 10);
            // Complete authentication
            await this.authManager.handleAuthCallback(token, user, expiresInSeconds);
        }
        catch (parseError) {
            vscode.window.showErrorMessage(`Failed to parse authentication data: ${parseError}`);
        }
    }
}
exports.AuthUriHandler = AuthUriHandler;
//# sourceMappingURL=uriHandler.js.map