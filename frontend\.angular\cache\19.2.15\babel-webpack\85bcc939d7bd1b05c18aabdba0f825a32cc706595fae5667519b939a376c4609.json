{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ProjectDialogComponent } from './project-dialog/project-dialog.component';\nimport { ConfirmDialogComponent } from '../../shared/confirm-dialog/confirm-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/chips\";\nfunction ProjectsComponent_th_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"div\", 23)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r1.description);\n  }\n}\nfunction ProjectsComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"type-\" + project_r2.type);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, project_r2.type), \" \");\n  }\n}\nfunction ProjectsComponent_th_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + project_r3.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, project_r3.status), \" \");\n  }\n}\nfunction ProjectsComponent_th_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Scans\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(project_r4.scan_count);\n  }\n}\nfunction ProjectsComponent_th_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r5 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r5.getIssueClass(project_r5.issue_count));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r5.issue_count, \" \");\n  }\n}\nfunction ProjectsComponent_th_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Last Scan\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r7.last_scan ? i0.ɵɵpipeBind2(2, 1, project_r7.last_scan, \"short\") : \"Never\", \" \");\n  }\n}\nfunction ProjectsComponent_th_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_1_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.scanProject(project_r9));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_4_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.viewProject(project_r9));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_7_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.editProject(project_r9));\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_10_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.deleteProject(project_r9));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectsComponent_tr_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 28);\n  }\n}\nfunction ProjectsComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 29);\n  }\n}\nexport class ProjectsComponent {\n  constructor(dialog, snackBar, router) {\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.router = router;\n    this.projects = [];\n    this.displayedColumns = ['name', 'type', 'status', 'scans', 'issues', 'lastScan', 'actions'];\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.loadProjects();\n  }\n  loadProjects() {\n    this.isLoading = true;\n    // Mock data for development\n    setTimeout(() => {\n      this.projects = this.generateMockProjects();\n      this.isLoading = false;\n    }, 1000);\n  }\n  getActiveProjects() {\n    return this.projects.filter(p => p.status === 'active').length;\n  }\n  getTotalScans() {\n    return this.projects.reduce((total, p) => total + (p.scan_count || 0), 0);\n  }\n  getTotalIssues() {\n    // Since issue_count is not in the Project model, we'll return 0 for now\n    return 0;\n  }\n  getIssueClass(count) {\n    if (count === 0) return 'issue-none';\n    if (count <= 5) return 'issue-low';\n    if (count <= 15) return 'issue-medium';\n    return 'issue-high';\n  }\n  openCreateDialog() {\n    const dialogRef = this.dialog.open(ProjectDialogComponent, {\n      width: '600px',\n      data: {\n        mode: 'create'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // Add the new project to the list\n        this.projects.unshift(result);\n        this.snackBar.open('Project created successfully!', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  scanProject(project) {\n    this.router.navigate(['/scan'], {\n      queryParams: {\n        project: project.id\n      }\n    });\n  }\n  viewProject(project) {\n    this.router.navigate(['/projects', project.id]);\n  }\n  editProject(project) {\n    const dialogRef = this.dialog.open(ProjectDialogComponent, {\n      width: '600px',\n      data: {\n        mode: 'edit',\n        project: project\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // Update the project in the list\n        const index = this.projects.findIndex(p => p.id === project.id);\n        if (index !== -1) {\n          this.projects[index] = result;\n          this.snackBar.open('Project updated successfully!', 'Close', {\n            duration: 3000\n          });\n        }\n      }\n    });\n  }\n  deleteProject(project) {\n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      width: '450px',\n      data: {\n        title: 'Delete Project',\n        message: `Are you sure you want to delete \"${project.name}\"? This action cannot be undone.`,\n        confirmText: 'Delete',\n        cancelText: 'Cancel',\n        type: 'danger'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // Remove the project from the list\n        const index = this.projects.findIndex(p => p.id === project.id);\n        if (index !== -1) {\n          this.projects.splice(index, 1);\n          this.snackBar.open('Project deleted successfully!', 'Close', {\n            duration: 3000\n          });\n        }\n      }\n    });\n  }\n  generateMockProjects() {\n    return [{\n      id: '1',\n      name: 'DeFi Protocol',\n      description: 'Decentralized finance smart contracts',\n      type: 'ethereum',\n      status: 'active',\n      repository_url: 'https://github.com/example/defi-protocol',\n      created_at: '2024-01-15T10:00:00Z',\n      updated_at: '2024-07-20T15:30:00Z',\n      last_scan: '2024-07-28T09:15:00Z',\n      scan_count: 15,\n      issue_count: 8,\n      owner: 'admin',\n      collaborators: ['dev1', 'dev2']\n    }, {\n      id: '2',\n      name: 'Bitcoin Wallet',\n      description: 'Multi-signature Bitcoin wallet implementation',\n      type: 'bitcoin',\n      status: 'active',\n      local_path: '/projects/bitcoin-wallet',\n      created_at: '2024-02-01T14:00:00Z',\n      updated_at: '2024-07-25T11:20:00Z',\n      last_scan: '2024-07-27T16:45:00Z',\n      scan_count: 8,\n      issue_count: 3,\n      owner: 'admin',\n      collaborators: ['security-team']\n    }, {\n      id: '3',\n      name: 'Cross-Chain Bridge',\n      description: 'Multi-chain asset bridge protocol',\n      type: 'multi-chain',\n      status: 'inactive',\n      repository_url: 'https://github.com/example/cross-chain-bridge',\n      created_at: '2024-03-10T09:30:00Z',\n      updated_at: '2024-06-15T13:45:00Z',\n      scan_count: 22,\n      issue_count: 12,\n      owner: 'admin',\n      collaborators: ['bridge-team', 'auditors']\n    }];\n  }\n  static {\n    this.ɵfac = function ProjectsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProjectsComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.MatSnackBar), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectsComponent,\n      selectors: [[\"app-projects\"]],\n      decls: 63,\n      vars: 7,\n      consts: [[1, \"projects-container\"], [1, \"header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"projects-table-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"projects-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"scans\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"lastScan\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"project-name\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Start Scan\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Details\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Edit Project\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete Project\", \"color\", \"warn\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function ProjectsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"\\uD83D\\uDDC2\\uFE0F Project Management\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function ProjectsComponent_Template_button_click_4_listener() {\n            return ctx.openCreateDialog();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" New Project \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-content\")(11, \"div\", 5);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 6);\n          i0.ɵɵtext(14, \"Total Projects\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"mat-card\", 4)(16, \"mat-card-content\")(17, \"div\", 5);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 6);\n          i0.ɵɵtext(20, \"Active Projects\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"mat-card\", 4)(22, \"mat-card-content\")(23, \"div\", 5);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 6);\n          i0.ɵɵtext(26, \"Total Scans\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"mat-card\", 4)(28, \"mat-card-content\")(29, \"div\", 5);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 6);\n          i0.ɵɵtext(32, \"Total Issues\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(33, \"mat-card\", 7)(34, \"mat-card-header\")(35, \"mat-card-title\");\n          i0.ɵɵtext(36, \"Projects\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-card-content\")(38, \"div\", 8)(39, \"table\", 9);\n          i0.ɵɵelementContainerStart(40, 10);\n          i0.ɵɵtemplate(41, ProjectsComponent_th_41_Template, 2, 0, \"th\", 11)(42, ProjectsComponent_td_42_Template, 6, 2, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(43, 13);\n          i0.ɵɵtemplate(44, ProjectsComponent_th_44_Template, 2, 0, \"th\", 11)(45, ProjectsComponent_td_45_Template, 4, 5, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(46, 14);\n          i0.ɵɵtemplate(47, ProjectsComponent_th_47_Template, 2, 0, \"th\", 11)(48, ProjectsComponent_td_48_Template, 4, 5, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(49, 15);\n          i0.ɵɵtemplate(50, ProjectsComponent_th_50_Template, 2, 0, \"th\", 11)(51, ProjectsComponent_td_51_Template, 2, 1, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(52, 16);\n          i0.ɵɵtemplate(53, ProjectsComponent_th_53_Template, 2, 0, \"th\", 11)(54, ProjectsComponent_td_54_Template, 3, 3, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(55, 17);\n          i0.ɵɵtemplate(56, ProjectsComponent_th_56_Template, 2, 0, \"th\", 11)(57, ProjectsComponent_td_57_Template, 3, 4, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(58, 18);\n          i0.ɵɵtemplate(59, ProjectsComponent_th_59_Template, 2, 0, \"th\", 11)(60, ProjectsComponent_td_60_Template, 13, 0, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(61, ProjectsComponent_tr_61_Template, 1, 0, \"tr\", 19)(62, ProjectsComponent_tr_62_Template, 1, 0, \"tr\", 20);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.projects.length);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getActiveProjects());\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getTotalScans());\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getTotalIssues());\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"dataSource\", ctx.projects);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n        }\n      },\n      dependencies: [CommonModule, i4.TitleCasePipe, i4.DatePipe, ReactiveFormsModule, MatCardModule, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, MatButtonModule, i6.MatButton, i6.MatIconButton, MatIconModule, i7.MatIcon, MatTableModule, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow, MatDialogModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatChipsModule, i9.MatChip, MatSnackBarModule, MatProgressSpinnerModule],\n      styles: [\".projects-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n}\\n\\n.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  font-weight: bold;\\n  color: #667eea;\\n  margin-bottom: 8px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.projects-table-card[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.projects-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.project-name[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 16px;\\n  margin-bottom: 4px;\\n}\\n\\n.project-name[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.type-ethereum[_ngcontent-%COMP%] {\\n  background-color: #627eea;\\n  color: white;\\n}\\n\\n.type-bitcoin[_ngcontent-%COMP%] {\\n  background-color: #f7931a;\\n  color: white;\\n}\\n\\n.type-multi-chain[_ngcontent-%COMP%] {\\n  background-color: #9c27b0;\\n  color: white;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n  color: white;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n}\\n\\n.status-archived[_ngcontent-%COMP%] {\\n  background-color: #757575;\\n  color: white;\\n}\\n\\n.issue-high[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  font-weight: bold;\\n}\\n\\n.issue-medium[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-weight: bold;\\n}\\n\\n.issue-low[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.issue-none[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\nmat-chip[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  min-height: 24px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatTableModule", "MatDialogModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatChipsModule", "MatSnackBarModule", "MatProgressSpinnerModule", "ProjectDialogComponent", "ConfirmDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "project_r1", "name", "description", "ɵɵclassMap", "project_r2", "type", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "project_r3", "status", "project_r4", "scan_count", "ctx_r5", "getIssueClass", "project_r5", "issue_count", "project_r7", "last_scan", "ɵɵpipeBind2", "ɵɵlistener", "ProjectsComponent_td_60_Template_button_click_1_listener", "project_r9", "ɵɵrestoreView", "_r8", "$implicit", "ɵɵnextContext", "ɵɵresetView", "scanProject", "ProjectsComponent_td_60_Template_button_click_4_listener", "viewProject", "ProjectsComponent_td_60_Template_button_click_7_listener", "editProject", "ProjectsComponent_td_60_Template_button_click_10_listener", "deleteProject", "ɵɵelement", "ProjectsComponent", "constructor", "dialog", "snackBar", "router", "projects", "displayedColumns", "isLoading", "ngOnInit", "loadProjects", "setTimeout", "generateMockProjects", "getActiveProjects", "filter", "p", "length", "getTotalScans", "reduce", "total", "getTotalIssues", "count", "openCreateDialog", "dialogRef", "open", "width", "data", "mode", "afterClosed", "subscribe", "result", "unshift", "duration", "project", "navigate", "queryParams", "id", "index", "findIndex", "title", "message", "confirmText", "cancelText", "splice", "repository_url", "created_at", "updated_at", "owner", "collaborators", "local_path", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "MatSnackBar", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "ProjectsComponent_Template", "rf", "ctx", "ProjectsComponent_Template_button_click_4_listener", "ɵɵelementContainerStart", "ɵɵtemplate", "ProjectsComponent_th_41_Template", "ProjectsComponent_td_42_Template", "ProjectsComponent_th_44_Template", "ProjectsComponent_td_45_Template", "ProjectsComponent_th_47_Template", "ProjectsComponent_td_48_Template", "ProjectsComponent_th_50_Template", "ProjectsComponent_td_51_Template", "ProjectsComponent_th_53_Template", "ProjectsComponent_td_54_Template", "ProjectsComponent_th_56_Template", "ProjectsComponent_td_57_Template", "ProjectsComponent_th_59_Template", "ProjectsComponent_td_60_Template", "ProjectsComponent_tr_61_Template", "ProjectsComponent_tr_62_Template", "ɵɵproperty", "i4", "TitleCasePipe", "DatePipe", "i5", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i6", "MatButton", "MatIconButton", "i7", "MatIcon", "i8", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i9", "MatChip", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\projects\\projects.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Router } from '@angular/router';\nimport { ProjectDialogComponent, ProjectDialogData } from './project-dialog/project-dialog.component';\nimport { ConfirmDialogComponent, ConfirmDialogData } from '../../shared/confirm-dialog/confirm-dialog.component';\nimport { Project } from '../../models/project.model';\n\n@Component({\n  selector: 'app-projects',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTableModule,\n    MatDialogModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatChipsModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ],\n  template: `\n    <div class=\"projects-container\">\n      <div class=\"header\">\n        <h1>🗂️ Project Management</h1>\n        <button mat-raised-button color=\"primary\" (click)=\"openCreateDialog()\">\n          <mat-icon>add</mat-icon>\n          New Project\n        </button>\n      </div>\n\n      <!-- Project Stats -->\n      <div class=\"stats-grid\">\n        <mat-card class=\"stat-card\">\n          <mat-card-content>\n            <div class=\"stat-number\">{{ projects.length }}</div>\n            <div class=\"stat-label\">Total Projects</div>\n          </mat-card-content>\n        </mat-card>\n        <mat-card class=\"stat-card\">\n          <mat-card-content>\n            <div class=\"stat-number\">{{ getActiveProjects() }}</div>\n            <div class=\"stat-label\">Active Projects</div>\n          </mat-card-content>\n        </mat-card>\n        <mat-card class=\"stat-card\">\n          <mat-card-content>\n            <div class=\"stat-number\">{{ getTotalScans() }}</div>\n            <div class=\"stat-label\">Total Scans</div>\n          </mat-card-content>\n        </mat-card>\n        <mat-card class=\"stat-card\">\n          <mat-card-content>\n            <div class=\"stat-number\">{{ getTotalIssues() }}</div>\n            <div class=\"stat-label\">Total Issues</div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Projects Table -->\n      <mat-card class=\"projects-table-card\">\n        <mat-card-header>\n          <mat-card-title>Projects</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"table-container\">\n            <table mat-table [dataSource]=\"projects\" class=\"projects-table\">\n              <ng-container matColumnDef=\"name\">\n                <th mat-header-cell *matHeaderCellDef>Name</th>\n                <td mat-cell *matCellDef=\"let project\">\n                  <div class=\"project-name\">\n                    <strong>{{ project.name }}</strong>\n                    <small>{{ project.description }}</small>\n                  </div>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"type\">\n                <th mat-header-cell *matHeaderCellDef>Type</th>\n                <td mat-cell *matCellDef=\"let project\">\n                  <mat-chip [class]=\"'type-' + project.type\">\n                    {{ project.type | titlecase }}\n                  </mat-chip>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"status\">\n                <th mat-header-cell *matHeaderCellDef>Status</th>\n                <td mat-cell *matCellDef=\"let project\">\n                  <mat-chip [class]=\"'status-' + project.status\">\n                    {{ project.status | titlecase }}\n                  </mat-chip>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"scans\">\n                <th mat-header-cell *matHeaderCellDef>Scans</th>\n                <td mat-cell *matCellDef=\"let project\">{{ project.scan_count }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"issues\">\n                <th mat-header-cell *matHeaderCellDef>Issues</th>\n                <td mat-cell *matCellDef=\"let project\">\n                  <span [class]=\"getIssueClass(project.issue_count)\">\n                    {{ project.issue_count }}\n                  </span>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"lastScan\">\n                <th mat-header-cell *matHeaderCellDef>Last Scan</th>\n                <td mat-cell *matCellDef=\"let project\">\n                  {{ project.last_scan ? (project.last_scan | date:'short') : 'Never' }}\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let project\">\n                  <button mat-icon-button (click)=\"scanProject(project)\" \n                          matTooltip=\"Start Scan\">\n                    <mat-icon>security</mat-icon>\n                  </button>\n                  <button mat-icon-button (click)=\"viewProject(project)\" \n                          matTooltip=\"View Details\">\n                    <mat-icon>visibility</mat-icon>\n                  </button>\n                  <button mat-icon-button (click)=\"editProject(project)\" \n                          matTooltip=\"Edit Project\">\n                    <mat-icon>edit</mat-icon>\n                  </button>\n                  <button mat-icon-button (click)=\"deleteProject(project)\" \n                          matTooltip=\"Delete Project\" \n                          color=\"warn\">\n                    <mat-icon>delete</mat-icon>\n                  </button>\n                </td>\n              </ng-container>\n\n              <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n            </table>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .projects-container {\n      padding: 20px;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    .header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 30px;\n    }\n\n    .header h1 {\n      margin: 0;\n      color: #333;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n\n    .stat-card {\n      text-align: center;\n      padding: 20px;\n    }\n\n    .stat-number {\n      font-size: 36px;\n      font-weight: bold;\n      color: #667eea;\n      margin-bottom: 8px;\n    }\n\n    .stat-label {\n      font-size: 14px;\n      color: #666;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    .projects-table-card {\n      margin-bottom: 30px;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .projects-table {\n      width: 100%;\n    }\n\n    .project-name strong {\n      display: block;\n      font-size: 16px;\n      margin-bottom: 4px;\n    }\n\n    .project-name small {\n      color: #666;\n      font-size: 12px;\n    }\n\n    .type-ethereum {\n      background-color: #627eea;\n      color: white;\n    }\n\n    .type-bitcoin {\n      background-color: #f7931a;\n      color: white;\n    }\n\n    .type-multi-chain {\n      background-color: #9c27b0;\n      color: white;\n    }\n\n    .status-active {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-inactive {\n      background-color: #ff9800;\n      color: white;\n    }\n\n    .status-archived {\n      background-color: #757575;\n      color: white;\n    }\n\n    .issue-high {\n      color: #f44336;\n      font-weight: bold;\n    }\n\n    .issue-medium {\n      color: #ff9800;\n      font-weight: bold;\n    }\n\n    .issue-low {\n      color: #4caf50;\n    }\n\n    .issue-none {\n      color: #666;\n    }\n\n    mat-chip {\n      font-size: 12px;\n      min-height: 24px;\n    }\n  `]\n})\nexport class ProjectsComponent implements OnInit {\n  projects: Project[] = [];\n  displayedColumns: string[] = ['name', 'type', 'status', 'scans', 'issues', 'lastScan', 'actions'];\n  isLoading = false;\n\n  constructor(\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadProjects();\n  }\n\n  loadProjects(): void {\n    this.isLoading = true;\n    // Mock data for development\n    setTimeout(() => {\n      this.projects = this.generateMockProjects();\n      this.isLoading = false;\n    }, 1000);\n  }\n\n  getActiveProjects(): number {\n    return this.projects.filter(p => p.status === 'active').length;\n  }\n\n  getTotalScans(): number {\n    return this.projects.reduce((total, p) => total + (p.scan_count || 0), 0);\n  }\n\n  getTotalIssues(): number {\n    // Since issue_count is not in the Project model, we'll return 0 for now\n    return 0;\n  }\n\n  getIssueClass(count: number): string {\n    if (count === 0) return 'issue-none';\n    if (count <= 5) return 'issue-low';\n    if (count <= 15) return 'issue-medium';\n    return 'issue-high';\n  }\n\n  openCreateDialog(): void {\n    const dialogRef = this.dialog.open(ProjectDialogComponent, {\n      width: '600px',\n      data: { mode: 'create' } as ProjectDialogData\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // Add the new project to the list\n        this.projects.unshift(result as Project);\n        this.snackBar.open('Project created successfully!', 'Close', { duration: 3000 });\n      }\n    });\n  }\n\n  scanProject(project: Project): void {\n    this.router.navigate(['/scan'], { queryParams: { project: project.id } });\n  }\n\n  viewProject(project: Project): void {\n    this.router.navigate(['/projects', project.id]);\n  }\n\n  editProject(project: Project): void {\n    const dialogRef = this.dialog.open(ProjectDialogComponent, {\n      width: '600px',\n      data: { mode: 'edit', project: project } as ProjectDialogData\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // Update the project in the list\n        const index = this.projects.findIndex(p => p.id === project.id);\n        if (index !== -1) {\n          this.projects[index] = result as Project;\n          this.snackBar.open('Project updated successfully!', 'Close', { duration: 3000 });\n        }\n      }\n    });\n  }\n\n  deleteProject(project: Project): void {\n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      width: '450px',\n      data: {\n        title: 'Delete Project',\n        message: `Are you sure you want to delete \"${project.name}\"? This action cannot be undone.`,\n        confirmText: 'Delete',\n        cancelText: 'Cancel',\n        type: 'danger'\n      } as ConfirmDialogData\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // Remove the project from the list\n        const index = this.projects.findIndex(p => p.id === project.id);\n        if (index !== -1) {\n          this.projects.splice(index, 1);\n          this.snackBar.open('Project deleted successfully!', 'Close', { duration: 3000 });\n        }\n      }\n    });\n  }\n\n  private generateMockProjects(): Project[] {\n    return [\n      {\n        id: '1',\n        name: 'DeFi Protocol',\n        description: 'Decentralized finance smart contracts',\n        type: 'ethereum',\n        status: 'active',\n        repository_url: 'https://github.com/example/defi-protocol',\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-07-20T15:30:00Z',\n        last_scan: '2024-07-28T09:15:00Z',\n        scan_count: 15,\n        issue_count: 8,\n        owner: 'admin',\n        collaborators: ['dev1', 'dev2']\n      },\n      {\n        id: '2',\n        name: 'Bitcoin Wallet',\n        description: 'Multi-signature Bitcoin wallet implementation',\n        type: 'bitcoin',\n        status: 'active',\n        local_path: '/projects/bitcoin-wallet',\n        created_at: '2024-02-01T14:00:00Z',\n        updated_at: '2024-07-25T11:20:00Z',\n        last_scan: '2024-07-27T16:45:00Z',\n        scan_count: 8,\n        issue_count: 3,\n        owner: 'admin',\n        collaborators: ['security-team']\n      },\n      {\n        id: '3',\n        name: 'Cross-Chain Bridge',\n        description: 'Multi-chain asset bridge protocol',\n        type: 'multi-chain',\n        status: 'inactive',\n        repository_url: 'https://github.com/example/cross-chain-bridge',\n        created_at: '2024-03-10T09:30:00Z',\n        updated_at: '2024-06-15T13:45:00Z',\n        scan_count: 22,\n        issue_count: 12,\n        owner: 'admin',\n        collaborators: ['bridge-team', 'auditors']\n      }\n    ];\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAA4C,gBAAgB;AACxF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,sBAAsB,QAA2B,2CAA2C;AACrG,SAASC,sBAAsB,QAA2B,sDAAsD;;;;;;;;;;;;;IAoEhGC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG3CH,EAFJ,CAAAC,cAAA,aAAuC,cACX,aAChB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnCH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAEpCF,EAFoC,CAAAG,YAAA,EAAQ,EACpC,EACH;;;;IAHOH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAAC,IAAA,CAAkB;IACnBP,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAAE,WAAA,CAAyB;;;;;IAMpCR,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAuC,eACM;IACzCD,EAAA,CAAAE,MAAA,GACF;;IACFF,EADE,CAAAG,YAAA,EAAW,EACR;;;;IAHOH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAS,UAAA,WAAAC,UAAA,CAAAC,IAAA,CAAgC;IACxCX,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAZ,EAAA,CAAAa,WAAA,OAAAH,UAAA,CAAAC,IAAA,OACF;;;;;IAKFX,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAuC,eACU;IAC7CD,EAAA,CAAAE,MAAA,GACF;;IACFF,EADE,CAAAG,YAAA,EAAW,EACR;;;;IAHOH,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAS,UAAA,aAAAK,UAAA,CAAAC,MAAA,CAAoC;IAC5Cf,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAZ,EAAA,CAAAa,WAAA,OAAAC,UAAA,CAAAC,MAAA,OACF;;;;;IAKFf,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAChDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAW,UAAA,CAAAC,UAAA,CAAwB;;;;;IAI/DjB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAuC,WACc;IACjDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;;IAHGH,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAS,UAAA,CAAAS,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAC,WAAA,EAA4C;IAChDrB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAQ,UAAA,CAAAC,WAAA,MACF;;;;;IAKFrB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACpDH,EAAA,CAAAC,cAAA,aAAuC;IACrCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAU,UAAA,CAAAC,SAAA,GAAAvB,EAAA,CAAAwB,WAAA,OAAAF,UAAA,CAAAC,SAAA,0BACF;;;;;IAIAvB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAuC,iBAEL;IADRD,EAAA,CAAAyB,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,UAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAlB,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASd,MAAA,CAAAe,WAAA,CAAAN,UAAA,CAAoB;IAAA,EAAC;IAEpD3B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IACpBF,EADoB,CAAAG,YAAA,EAAW,EACtB;IACTH,EAAA,CAAAC,cAAA,iBACkC;IADVD,EAAA,CAAAyB,UAAA,mBAAAS,yDAAA;MAAA,MAAAP,UAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAlB,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASd,MAAA,CAAAiB,WAAA,CAAAR,UAAA,CAAoB;IAAA,EAAC;IAEpD3B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,iBACkC;IADVD,EAAA,CAAAyB,UAAA,mBAAAW,yDAAA;MAAA,MAAAT,UAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAlB,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASd,MAAA,CAAAmB,WAAA,CAAAV,UAAA,CAAoB;IAAA,EAAC;IAEpD3B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,kBAEqB;IAFGD,EAAA,CAAAyB,UAAA,mBAAAa,0DAAA;MAAA,MAAAX,UAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAlB,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASd,MAAA,CAAAqB,aAAA,CAAAZ,UAAA,CAAsB;IAAA,EAAC;IAGtD3B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAEpBF,EAFoB,CAAAG,YAAA,EAAW,EACpB,EACN;;;;;IAGPH,EAAA,CAAAwC,SAAA,aAA4D;;;;;IAC5DxC,EAAA,CAAAwC,SAAA,aAAkE;;;AAiIhF,OAAM,MAAOC,iBAAiB;EAK5BC,YACUC,MAAiB,EACjBC,QAAqB,EACrBC,MAAc;IAFd,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;IACjG,KAAAC,SAAS,GAAG,KAAK;EAMd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACF,SAAS,GAAG,IAAI;IACrB;IACAG,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACM,oBAAoB,EAAE;MAC3C,IAAI,CAACJ,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAK,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACP,QAAQ,CAACQ,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,QAAQ,CAAC,CAACyC,MAAM;EAChE;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACX,QAAQ,CAACY,MAAM,CAAC,CAACC,KAAK,EAAEJ,CAAC,KAAKI,KAAK,IAAIJ,CAAC,CAACtC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA2C,cAAcA,CAAA;IACZ;IACA,OAAO,CAAC;EACV;EAEAzC,aAAaA,CAAC0C,KAAa;IACzB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,YAAY;IACpC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,WAAW;IAClC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,cAAc;IACtC,OAAO,YAAY;EACrB;EAEAC,gBAAgBA,CAAA;IACd,MAAMC,SAAS,GAAG,IAAI,CAACpB,MAAM,CAACqB,IAAI,CAAClE,sBAAsB,EAAE;MACzDmE,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QAAEC,IAAI,EAAE;MAAQ;KACvB,CAAC;IAEFJ,SAAS,CAACK,WAAW,EAAE,CAACC,SAAS,CAACC,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV;QACA,IAAI,CAACxB,QAAQ,CAACyB,OAAO,CAACD,MAAiB,CAAC;QACxC,IAAI,CAAC1B,QAAQ,CAACoB,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAAEQ,QAAQ,EAAE;QAAI,CAAE,CAAC;MAClF;IACF,CAAC,CAAC;EACJ;EAEAvC,WAAWA,CAACwC,OAAgB;IAC1B,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEC,WAAW,EAAE;QAAEF,OAAO,EAAEA,OAAO,CAACG;MAAE;IAAE,CAAE,CAAC;EAC3E;EAEAzC,WAAWA,CAACsC,OAAgB;IAC1B,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,WAAW,EAAED,OAAO,CAACG,EAAE,CAAC,CAAC;EACjD;EAEAvC,WAAWA,CAACoC,OAAgB;IAC1B,MAAMV,SAAS,GAAG,IAAI,CAACpB,MAAM,CAACqB,IAAI,CAAClE,sBAAsB,EAAE;MACzDmE,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEM,OAAO,EAAEA;MAAO;KACvC,CAAC;IAEFV,SAAS,CAACK,WAAW,EAAE,CAACC,SAAS,CAACC,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV;QACA,MAAMO,KAAK,GAAG,IAAI,CAAC/B,QAAQ,CAACgC,SAAS,CAACvB,CAAC,IAAIA,CAAC,CAACqB,EAAE,KAAKH,OAAO,CAACG,EAAE,CAAC;QAC/D,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC/B,QAAQ,CAAC+B,KAAK,CAAC,GAAGP,MAAiB;UACxC,IAAI,CAAC1B,QAAQ,CAACoB,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;YAAEQ,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;MACF;IACF,CAAC,CAAC;EACJ;EAEAjC,aAAaA,CAACkC,OAAgB;IAC5B,MAAMV,SAAS,GAAG,IAAI,CAACpB,MAAM,CAACqB,IAAI,CAACjE,sBAAsB,EAAE;MACzDkE,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QACJa,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE,oCAAoCP,OAAO,CAAClE,IAAI,kCAAkC;QAC3F0E,WAAW,EAAE,QAAQ;QACrBC,UAAU,EAAE,QAAQ;QACpBvE,IAAI,EAAE;;KAET,CAAC;IAEFoD,SAAS,CAACK,WAAW,EAAE,CAACC,SAAS,CAACC,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV;QACA,MAAMO,KAAK,GAAG,IAAI,CAAC/B,QAAQ,CAACgC,SAAS,CAACvB,CAAC,IAAIA,CAAC,CAACqB,EAAE,KAAKH,OAAO,CAACG,EAAE,CAAC;QAC/D,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC/B,QAAQ,CAACqC,MAAM,CAACN,KAAK,EAAE,CAAC,CAAC;UAC9B,IAAI,CAACjC,QAAQ,CAACoB,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;YAAEQ,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;MACF;IACF,CAAC,CAAC;EACJ;EAEQpB,oBAAoBA,CAAA;IAC1B,OAAO,CACL;MACEwB,EAAE,EAAE,GAAG;MACPrE,IAAI,EAAE,eAAe;MACrBC,WAAW,EAAE,uCAAuC;MACpDG,IAAI,EAAE,UAAU;MAChBI,MAAM,EAAE,QAAQ;MAChBqE,cAAc,EAAE,0CAA0C;MAC1DC,UAAU,EAAE,sBAAsB;MAClCC,UAAU,EAAE,sBAAsB;MAClC/D,SAAS,EAAE,sBAAsB;MACjCN,UAAU,EAAE,EAAE;MACdI,WAAW,EAAE,CAAC;MACdkE,KAAK,EAAE,OAAO;MACdC,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM;KAC/B,EACD;MACEZ,EAAE,EAAE,GAAG;MACPrE,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,+CAA+C;MAC5DG,IAAI,EAAE,SAAS;MACfI,MAAM,EAAE,QAAQ;MAChB0E,UAAU,EAAE,0BAA0B;MACtCJ,UAAU,EAAE,sBAAsB;MAClCC,UAAU,EAAE,sBAAsB;MAClC/D,SAAS,EAAE,sBAAsB;MACjCN,UAAU,EAAE,CAAC;MACbI,WAAW,EAAE,CAAC;MACdkE,KAAK,EAAE,OAAO;MACdC,aAAa,EAAE,CAAC,eAAe;KAChC,EACD;MACEZ,EAAE,EAAE,GAAG;MACPrE,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,mCAAmC;MAChDG,IAAI,EAAE,aAAa;MACnBI,MAAM,EAAE,UAAU;MAClBqE,cAAc,EAAE,+CAA+C;MAC/DC,UAAU,EAAE,sBAAsB;MAClCC,UAAU,EAAE,sBAAsB;MAClCrE,UAAU,EAAE,EAAE;MACdI,WAAW,EAAE,EAAE;MACfkE,KAAK,EAAE,OAAO;MACdC,aAAa,EAAE,CAAC,aAAa,EAAE,UAAU;KAC1C,CACF;EACH;;;uCA5JW/C,iBAAiB,EAAAzC,EAAA,CAAA0F,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA5F,EAAA,CAAA0F,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9F,EAAA,CAAA0F,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjBvD,iBAAiB;MAAAwD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArPtBvG,EAFJ,CAAAC,cAAA,aAAgC,aACV,SACd;UAAAD,EAAA,CAAAE,MAAA,4CAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,gBAAuE;UAA7BD,EAAA,CAAAyB,UAAA,mBAAAgF,mDAAA;YAAA,OAASD,GAAA,CAAA1C,gBAAA,EAAkB;UAAA,EAAC;UACpE9D,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,UAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,oBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACL;UAMAH,EAHN,CAAAC,cAAA,aAAwB,kBACM,wBACR,cACS;UAAAD,EAAA,CAAAE,MAAA,IAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpDH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAE1CF,EAF0C,CAAAG,YAAA,EAAM,EAC3B,EACV;UAGPH,EAFJ,CAAAC,cAAA,mBAA4B,wBACR,cACS;UAAAD,EAAA,CAAAE,MAAA,IAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxDH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAE3CF,EAF2C,CAAAG,YAAA,EAAM,EAC5B,EACV;UAGPH,EAFJ,CAAAC,cAAA,mBAA4B,wBACR,cACS;UAAAD,EAAA,CAAAE,MAAA,IAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpDH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAEvCF,EAFuC,CAAAG,YAAA,EAAM,EACxB,EACV;UAGPH,EAFJ,CAAAC,cAAA,mBAA4B,wBACR,cACS;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACzB,EACV,EACP;UAKFH,EAFJ,CAAAC,cAAA,mBAAsC,uBACnB,sBACC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAC1BF,EAD0B,CAAAG,YAAA,EAAiB,EACzB;UAGdH,EAFJ,CAAAC,cAAA,wBAAkB,cACa,gBACqC;UAC9DD,EAAA,CAAA0G,uBAAA,QAAkC;UAEhC1G,EADA,CAAA2G,UAAA,KAAAC,gCAAA,iBAAsC,KAAAC,gCAAA,iBACC;;UAQzC7G,EAAA,CAAA0G,uBAAA,QAAkC;UAEhC1G,EADA,CAAA2G,UAAA,KAAAG,gCAAA,iBAAsC,KAAAC,gCAAA,iBACC;;UAOzC/G,EAAA,CAAA0G,uBAAA,QAAoC;UAElC1G,EADA,CAAA2G,UAAA,KAAAK,gCAAA,iBAAsC,KAAAC,gCAAA,iBACC;;UAOzCjH,EAAA,CAAA0G,uBAAA,QAAmC;UAEjC1G,EADA,CAAA2G,UAAA,KAAAO,gCAAA,iBAAsC,KAAAC,gCAAA,iBACC;;UAGzCnH,EAAA,CAAA0G,uBAAA,QAAoC;UAElC1G,EADA,CAAA2G,UAAA,KAAAS,gCAAA,iBAAsC,KAAAC,gCAAA,iBACC;;UAOzCrH,EAAA,CAAA0G,uBAAA,QAAsC;UAEpC1G,EADA,CAAA2G,UAAA,KAAAW,gCAAA,iBAAsC,KAAAC,gCAAA,iBACC;;UAKzCvH,EAAA,CAAA0G,uBAAA,QAAqC;UAEnC1G,EADA,CAAA2G,UAAA,KAAAa,gCAAA,iBAAsC,KAAAC,gCAAA,kBACC;;UAsBzCzH,EADA,CAAA2G,UAAA,KAAAe,gCAAA,iBAAuD,KAAAC,gCAAA,iBACM;UAKvE3H,EAJQ,CAAAG,YAAA,EAAQ,EACJ,EACW,EACV,EACP;;;UA9G2BH,EAAA,CAAAI,SAAA,IAAqB;UAArBJ,EAAA,CAAAK,iBAAA,CAAAmG,GAAA,CAAA1D,QAAA,CAAAU,MAAA,CAAqB;UAMrBxD,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAK,iBAAA,CAAAmG,GAAA,CAAAnD,iBAAA,GAAyB;UAMzBrD,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAK,iBAAA,CAAAmG,GAAA,CAAA/C,aAAA,GAAqB;UAMrBzD,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAK,iBAAA,CAAAmG,GAAA,CAAA5C,cAAA,GAAsB;UAa9B5D,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAA4H,UAAA,eAAApB,GAAA,CAAA1D,QAAA,CAAuB;UAyElB9C,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAA4H,UAAA,oBAAApB,GAAA,CAAAzD,gBAAA,CAAiC;UACpB/C,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAA4H,UAAA,qBAAApB,GAAA,CAAAzD,gBAAA,CAA0B;;;qBArIrE9D,YAAY,EAAA4I,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,QAAA,EACZ7I,mBAAmB,EACnBC,aAAa,EAAA6I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbhJ,eAAe,EAAAiJ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACflJ,aAAa,EAAAmJ,EAAA,CAAAC,OAAA,EACbnJ,cAAc,EAAAoJ,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACd7J,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,cAAc,EAAA0J,EAAA,CAAAC,OAAA,EACd1J,iBAAiB,EACjBC,wBAAwB;MAAA0J,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}