{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { DocumentationNavComponent } from '../documentation-nav/documentation-nav.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/toolbar\";\nimport * as i3 from \"@angular/material/sidenav\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nexport class DocumentationLayoutComponent {\n  static {\n    this.ɵfac = function DocumentationLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DocumentationLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DocumentationLayoutComponent,\n      selectors: [[\"app-documentation-layout\"]],\n      decls: 23,\n      vars: 0,\n      consts: [[1, \"documentation-container\"], [1, \"doc-toolbar\"], [1, \"toolbar-brand\"], [1, \"brand-icon\"], [1, \"brand-text\"], [1, \"brand-title\"], [1, \"brand-subtitle\"], [1, \"spacer\"], [1, \"toolbar-actions\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/dashboard\", 1, \"back-to-app-btn\"], [1, \"doc-sidenav-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"doc-sidenav\"], [1, \"doc-content\"], [1, \"content-wrapper\"]],\n      template: function DocumentationLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"shield\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"span\", 5);\n          i0.ɵɵtext(8, \"SPT Documentation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\", 6);\n          i0.ɵɵtext(10, \"Security Protocol Tool\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(11, \"span\", 7);\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Back to App \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"mat-sidenav-container\", 10)(18, \"mat-sidenav\", 11);\n          i0.ɵɵelement(19, \"app-documentation-nav\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"mat-sidenav-content\", 12)(21, \"div\", 13);\n          i0.ɵɵelement(22, \"router-outlet\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [CommonModule, RouterModule, i1.RouterOutlet, i1.RouterLink, MatToolbarModule, i2.MatToolbar, MatSidenavModule, i3.MatSidenav, i3.MatSidenavContainer, i3.MatSidenavContent, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, MatTooltipModule, DocumentationNavComponent],\n      styles: [\".documentation-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: #fafbfc;\\n}\\n\\n.doc-toolbar[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  color: #1a202c;\\n  z-index: 2;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);\\n  padding: 0 32px;\\n  min-height: 64px;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.toolbar-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  padding: 10px;\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n\\n.brand-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n\\n.brand-title[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n  font-weight: 700;\\n  letter-spacing: -0.5px;\\n  color: #1a202c;\\n}\\n\\n.brand-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875em;\\n  color: #64748b;\\n  font-weight: 500;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.toolbar-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.back-to-app-btn[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  border: 1px solid #667eea;\\n  color: white;\\n  border-radius: 8px;\\n  padding: 10px 20px;\\n  font-weight: 600;\\n  font-size: 0.875em;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);\\n}\\n\\n.back-to-app-btn[_ngcontent-%COMP%]:hover {\\n  background: #5a67d8;\\n  border-color: #5a67d8;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n\\n.back-to-app-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.doc-sidenav-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.doc-sidenav[_ngcontent-%COMP%] {\\n  width: 320px;\\n  border-right: none;\\n  background: linear-gradient(180deg, #ffffff 0%, #f8f9ff 100%);\\n  box-shadow: 4px 0 20px rgba(102, 126, 234, 0.1);\\n}\\n\\n.doc-content[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  padding: 32px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  min-height: calc(100vh - 72px);\\n}\\n\\n@media (max-width: 1024px) {\\n  .doc-sidenav[_ngcontent-%COMP%] {\\n    width: 280px;\\n  }\\n  .content-wrapper[_ngcontent-%COMP%] {\\n    padding: 24px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .doc-toolbar[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    min-height: 64px;\\n  }\\n  .brand-title[_ngcontent-%COMP%] {\\n    font-size: 1.2em;\\n  }\\n  .brand-subtitle[_ngcontent-%COMP%] {\\n    font-size: 0.8em;\\n  }\\n  .doc-sidenav[_ngcontent-%COMP%] {\\n    width: 260px;\\n  }\\n  .content-wrapper[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .back-to-app-btn[_ngcontent-%COMP%] {\\n    padding: 6px 16px;\\n    font-size: 0.9em;\\n  }\\n  .back-to-app-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .toolbar-brand[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .brand-icon[_ngcontent-%COMP%] {\\n    padding: 6px;\\n  }\\n  .brand-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n    width: 24px;\\n    height: 24px;\\n  }\\n  .back-to-app-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .back-to-app-btn[_ngcontent-%COMP%] {\\n    min-width: 40px;\\n    padding: 8px;\\n    border-radius: 50%;\\n  }\\n  .back-to-app-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatToolbarModule", "MatSidenavModule", "MatIconModule", "MatButtonModule", "MatTooltipModule", "DocumentationNavComponent", "DocumentationLayoutComponent", "selectors", "decls", "vars", "consts", "template", "DocumentationLayoutComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "i1", "RouterOutlet", "RouterLink", "i2", "MatToolbar", "i3", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i4", "MatIcon", "i5", "MatButton", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\documentation-layout\\documentation-layout.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { DocumentationNavComponent } from '../documentation-nav/documentation-nav.component';\n\n@Component({\n  selector: 'app-documentation-layout',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatToolbarModule,\n    MatSidenavModule,\n    MatIconModule,\n    MatButtonModule,\n    MatTooltipModule,\n    DocumentationNavComponent\n  ],\n  template: `\n    <div class=\"documentation-container\">\n      <mat-toolbar class=\"doc-toolbar\">\n        <div class=\"toolbar-brand\">\n          <div class=\"brand-icon\">\n            <mat-icon>shield</mat-icon>\n          </div>\n          <div class=\"brand-text\">\n            <span class=\"brand-title\">SPT Documentation</span>\n            <span class=\"brand-subtitle\">Security Protocol Tool</span>\n          </div>\n        </div>\n        <span class=\"spacer\"></span>\n        <div class=\"toolbar-actions\">\n          <button mat-stroked-button routerLink=\"/dashboard\" class=\"back-to-app-btn\">\n            <mat-icon>arrow_back</mat-icon>\n            Back to App\n          </button>\n        </div>\n      </mat-toolbar>\n\n      <mat-sidenav-container class=\"doc-sidenav-container\">\n        <mat-sidenav mode=\"side\" opened=\"true\" class=\"doc-sidenav\">\n          <app-documentation-nav></app-documentation-nav>\n        </mat-sidenav>\n\n        <mat-sidenav-content class=\"doc-content\">\n          <div class=\"content-wrapper\">\n            <router-outlet></router-outlet>\n          </div>\n        </mat-sidenav-content>\n      </mat-sidenav-container>\n    </div>\n  `,\n  styles: [`\n    .documentation-container {\n      height: 100vh;\n      display: flex;\n      flex-direction: column;\n      background: #fafbfc;\n    }\n\n    .doc-toolbar {\n      background: #ffffff;\n      color: #1a202c;\n      z-index: 2;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);\n      padding: 0 32px;\n      min-height: 64px;\n      border-bottom: 1px solid #e2e8f0;\n    }\n\n    .toolbar-brand {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n    }\n\n    .brand-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 12px;\n      padding: 10px;\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .brand-icon mat-icon {\n      color: #ffffff;\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .brand-text {\n      display: flex;\n      flex-direction: column;\n      gap: 2px;\n    }\n\n    .brand-title {\n      font-size: 1.5em;\n      font-weight: 700;\n      letter-spacing: -0.5px;\n      color: #1a202c;\n    }\n\n    .brand-subtitle {\n      font-size: 0.875em;\n      color: #64748b;\n      font-weight: 500;\n    }\n\n    .spacer {\n      flex: 1 1 auto;\n    }\n\n    .toolbar-actions {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .back-to-app-btn {\n      background: #667eea;\n      border: 1px solid #667eea;\n      color: white;\n      border-radius: 8px;\n      padding: 10px 20px;\n      font-weight: 600;\n      font-size: 0.875em;\n      transition: all 0.2s ease;\n      box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);\n    }\n\n    .back-to-app-btn:hover {\n      background: #5a67d8;\n      border-color: #5a67d8;\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .back-to-app-btn mat-icon {\n      margin-right: 8px;\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .doc-sidenav-container {\n      flex: 1;\n    }\n\n    .doc-sidenav {\n      width: 320px;\n      border-right: none;\n      background: linear-gradient(180deg, #ffffff 0%, #f8f9ff 100%);\n      box-shadow: 4px 0 20px rgba(102, 126, 234, 0.1);\n    }\n\n    .doc-content {\n      background: transparent;\n    }\n\n    .content-wrapper {\n      padding: 32px;\n      max-width: 1200px;\n      margin: 0 auto;\n      min-height: calc(100vh - 72px);\n    }\n\n    @media (max-width: 1024px) {\n      .doc-sidenav {\n        width: 280px;\n      }\n\n      .content-wrapper {\n        padding: 24px;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .doc-toolbar {\n        padding: 0 16px;\n        min-height: 64px;\n      }\n\n      .brand-title {\n        font-size: 1.2em;\n      }\n\n      .brand-subtitle {\n        font-size: 0.8em;\n      }\n\n      .doc-sidenav {\n        width: 260px;\n      }\n\n      .content-wrapper {\n        padding: 16px;\n      }\n\n      .back-to-app-btn {\n        padding: 6px 16px;\n        font-size: 0.9em;\n      }\n\n      .back-to-app-btn mat-icon {\n        font-size: 16px;\n        width: 16px;\n        height: 16px;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .toolbar-brand {\n        gap: 12px;\n      }\n\n      .brand-icon {\n        padding: 6px;\n      }\n\n      .brand-icon mat-icon {\n        font-size: 24px;\n        width: 24px;\n        height: 24px;\n      }\n\n      .back-to-app-btn span {\n        display: none;\n      }\n\n      .back-to-app-btn {\n        min-width: 40px;\n        padding: 8px;\n        border-radius: 50%;\n      }\n\n      .back-to-app-btn mat-icon {\n        margin: 0;\n      }\n    }\n  `]\n})\nexport class DocumentationLayoutComponent { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,yBAAyB,QAAQ,kDAAkD;;;;;;;AA+O5F,OAAM,MAAOC,4BAA4B;;;uCAA5BA,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3N7BE,EAJR,CAAAC,cAAA,aAAqC,qBACF,aACJ,aACD,eACZ;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;UAEJH,EADF,CAAAC,cAAA,aAAwB,cACI;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClDH,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAEvDF,EAFuD,CAAAG,YAAA,EAAO,EACtD,EACF;UACNH,EAAA,CAAAI,SAAA,eAA4B;UAGxBJ,EAFJ,CAAAC,cAAA,cAA6B,iBACgD,gBAC/D;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,qBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACM;UAGZH,EADF,CAAAC,cAAA,iCAAqD,uBACQ;UACzDD,EAAA,CAAAI,SAAA,6BAA+C;UACjDJ,EAAA,CAAAG,YAAA,EAAc;UAGZH,EADF,CAAAC,cAAA,+BAAyC,eACV;UAC3BD,EAAA,CAAAI,SAAA,qBAA+B;UAIvCJ,EAHM,CAAAG,YAAA,EAAM,EACc,EACA,EACpB;;;qBAzCNpB,YAAY,EACZC,YAAY,EAAAqB,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,UAAA,EACZtB,gBAAgB,EAAAuB,EAAA,CAAAC,UAAA,EAChBvB,gBAAgB,EAAAwB,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAChB1B,aAAa,EAAA2B,EAAA,CAAAC,OAAA,EACb3B,eAAe,EAAA4B,EAAA,CAAAC,SAAA,EACf5B,gBAAgB,EAChBC,yBAAyB;MAAA4B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}