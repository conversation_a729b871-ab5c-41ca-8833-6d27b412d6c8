<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <div class="logo">
        <mat-icon>security</mat-icon>
        <span>SPT</span>
      </div>
      <h1>Welcome Back</h1>
      <p>Sign in to your security dashboard</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
      <mat-form-field appearance="outline">
        <mat-label>Username</mat-label>
        <input matInput formControlName="username" autocomplete="username">
        <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
          Username is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Password</mat-label>
        <input matInput
               [type]="hidePassword ? 'password' : 'text'"
               formControlName="password"
               autocomplete="current-password">
        <button mat-icon-button matSuffix
                (click)="hidePassword = !hidePassword"
                type="button">
          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
          Password is required
        </mat-error>
      </mat-form-field>

      <div class="form-options">
        <mat-checkbox formControlName="rememberMe">
          Remember me
        </mat-checkbox>
        <a href="#" class="forgot-password">Forgot password?</a>
      </div>

      <button mat-raised-button
              color="primary"
              type="submit"
              class="auth-button"
              [disabled]="loginForm.invalid || isLoading">
        <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
        <span *ngIf="!isLoading">Sign In</span>
      </button>

      <button mat-button
              type="button"
              class="demo-button"
              (click)="loginWithDemo()"
              [disabled]="isLoading">
        Demo Login
      </button>
    </form>

    <div class="auth-footer">
      <p>Don't have an account? <a routerLink="/register">Sign up</a></p>
    </div>
  </div>


