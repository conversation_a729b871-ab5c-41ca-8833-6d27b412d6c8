<div class="login-container">
  <!-- Background Pattern -->
  <div class="background-pattern"></div>

  <!-- Header -->
  <header class="app-header">
    <div class="header-content">
      <div class="brand">
        <mat-icon class="brand-icon">shield</mat-icon>
        <span class="brand-text">SPT</span>
      </div>
      <div class="header-actions">
        <button mat-button routerLink="/register" class="header-link">
          Create Account
        </button>
      </div>
    </div>
  </header>

  <div class="login-content">
    <div class="login-card-wrapper">
      <mat-card class="login-card">
        <mat-card-header class="login-header">
          <div class="logo">
            <div class="logo-icon-wrapper">
              <mat-icon class="logo-icon">security</mat-icon>
            </div>
            <div class="logo-text">
              <h1>Welcome Back</h1>
              <p class="subtitle">Sign in to your security dashboard</p>
            </div>
          </div>
        </mat-card-header>

      <mat-card-content>
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <div class="form-fields">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Username</mat-label>
              <input matInput formControlName="username" autocomplete="username">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
                Username is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Password</mat-label>
              <input matInput 
                     [type]="hidePassword ? 'password' : 'text'" 
                     formControlName="password"
                     autocomplete="current-password">
              <button mat-icon-button matSuffix 
                      (click)="hidePassword = !hidePassword" 
                      [attr.aria-label]="'Hide password'" 
                      [attr.aria-pressed]="hidePassword"
                      type="button">
                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
            </mat-form-field>

            <div class="form-options">
              <mat-checkbox formControlName="rememberMe">
                Remember me
              </mat-checkbox>
              <a href="#" class="forgot-password">Forgot password?</a>
            </div>
          </div>

          <div class="form-actions">
            <button mat-raised-button 
                    color="primary" 
                    type="submit" 
                    class="login-button"
                    [disabled]="loginForm.invalid || isLoading">
              <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
              <span *ngIf="!isLoading">Sign In</span>
            </button>

            <button mat-button 
                    type="button" 
                    class="demo-button"
                    (click)="loginWithDemo()"
                    [disabled]="isLoading">
              Demo Login
            </button>
          </div>
        </form>
      </mat-card-content>

      <mat-card-actions class="card-actions">
        <p>Don't have an account? 
          <a routerLink="/register" class="register-link">Sign up</a>
        </p>
      </mat-card-actions>
    </mat-card>

    <div class="features-info">
      <div class="features-header">
        <mat-icon class="features-icon">verified_user</mat-icon>
        <h3>Security Features</h3>
      </div>
      <div class="features-grid">
        <div class="feature-item">
          <mat-icon>code</mat-icon>
          <div>
            <h4>Smart Contract Analysis</h4>
            <p>Advanced static analysis for Solidity contracts</p>
          </div>
        </div>
        <div class="feature-item">
          <mat-icon>bug_report</mat-icon>
          <div>
            <h4>Vulnerability Detection</h4>
            <p>Identify security flaws and potential exploits</p>
          </div>
        </div>
        <div class="feature-item">
          <mat-icon>monitor</mat-icon>
          <div>
            <h4>Real-time Monitoring</h4>
            <p>Continuous security monitoring and alerts</p>
          </div>
        </div>
        <div class="feature-item">
          <mat-icon>assessment</mat-icon>
          <div>
            <h4>Comprehensive Reports</h4>
            <p>Detailed security reports and recommendations</p>
          </div>
        </div>
      </div>

      <div class="stats">
        <div class="stat-item">
          <span class="stat-number">10K+</span>
          <span class="stat-label">Contracts Analyzed</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">500+</span>
          <span class="stat-label">Vulnerabilities Found</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">99.9%</span>
          <span class="stat-label">Accuracy Rate</span>
        </div>
      </div>
    </div>
  </div>
  </div>
</div>
