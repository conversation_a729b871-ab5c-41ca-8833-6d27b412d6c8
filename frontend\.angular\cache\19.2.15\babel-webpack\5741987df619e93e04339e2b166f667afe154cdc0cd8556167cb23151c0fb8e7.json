{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/chips\";\nfunction ArchitectureComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const principle_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", principle_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(principle_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(principle_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(principle_r1.description);\n  }\n}\nfunction ArchitectureComponent_mat_card_116_div_11_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const responsibility_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(responsibility_r2);\n  }\n}\nfunction ArchitectureComponent_mat_card_116_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"h4\");\n    i0.ɵɵtext(2, \"Responsibilities:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, ArchitectureComponent_mat_card_116_div_11_li_4_Template, 2, 1, \"li\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const component_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", component_r3.responsibilities);\n  }\n}\nfunction ArchitectureComponent_mat_card_116_div_12_mat_chip_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const api_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(api_r4);\n  }\n}\nfunction ArchitectureComponent_mat_card_116_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"h4\");\n    i0.ɵɵtext(2, \"APIs:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-chip-listbox\", 53);\n    i0.ɵɵtemplate(4, ArchitectureComponent_mat_card_116_div_12_mat_chip_4_Template, 2, 1, \"mat-chip\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const component_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", component_r3.apis);\n  }\n}\nfunction ArchitectureComponent_mat_card_116_div_13_mat_chip_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dep_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dep_r5);\n  }\n}\nfunction ArchitectureComponent_mat_card_116_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"h4\");\n    i0.ɵɵtext(2, \"Dependencies:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-chip-listbox\", 55);\n    i0.ɵɵtemplate(4, ArchitectureComponent_mat_card_116_div_13_mat_chip_4_Template, 2, 1, \"mat-chip\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const component_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", component_r3.dependencies);\n  }\n}\nfunction ArchitectureComponent_mat_card_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 46)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ArchitectureComponent_mat_card_116_div_11_Template, 5, 1, \"div\", 47)(12, ArchitectureComponent_mat_card_116_div_12_Template, 5, 1, \"div\", 48)(13, ArchitectureComponent_mat_card_116_div_13_Template, 5, 1, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const component_r3 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r5.getComponentColor(component_r3.technology));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getComponentIcon(component_r3.technology), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(component_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(component_r3.technology);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(component_r3.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", component_r3.responsibilities.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", component_r3.apis && component_r3.apis.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", component_r3.dependencies && component_r3.dependencies.length > 0);\n  }\n}\nfunction ArchitectureComponent_div_134_div_8_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(detail_r7);\n  }\n}\nfunction ArchitectureComponent_div_134_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"mat-chip-listbox\");\n    i0.ɵɵtemplate(2, ArchitectureComponent_div_134_div_8_mat_chip_2_Template, 2, 1, \"mat-chip\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", step_r8.details);\n  }\n}\nfunction ArchitectureComponent_div_134_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_downward\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArchitectureComponent_div_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 58)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ArchitectureComponent_div_134_div_8_Template, 3, 1, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ArchitectureComponent_div_134_div_9_Template, 3, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r8.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r8.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r8.details);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r9 < ctx_r5.scanFlowSteps.length - 1);\n  }\n}\nfunction ArchitectureComponent_div_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 65)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 66);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 67)(12, \"mat-icon\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 68)(17, \"p\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flow_r10 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getFlowIcon(flow_r10.from));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flow_r10.from);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(flow_r10.protocol);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getFlowIcon(flow_r10.to));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flow_r10.to);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flow_r10.description);\n  }\n}\nfunction ArchitectureComponent_mat_card_153_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const measure_r11 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(measure_r11);\n  }\n}\nfunction ArchitectureComponent_mat_card_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 69)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 70);\n    i0.ɵɵtemplate(10, ArchitectureComponent_mat_card_153_div_10_Template, 5, 1, \"div\", 71);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const section_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", section_r12.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", section_r12.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r12.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", section_r12.measures);\n  }\n}\nfunction ArchitectureComponent_mat_card_161_li_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pro_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(pro_r13);\n  }\n}\nfunction ArchitectureComponent_mat_card_161_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const con_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(con_r14);\n  }\n}\nfunction ArchitectureComponent_mat_card_161_div_21_mat_chip_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const useCase_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(useCase_r15);\n  }\n}\nfunction ArchitectureComponent_mat_card_161_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"h4\");\n    i0.ɵɵtext(2, \"Best for:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-chip-listbox\");\n    i0.ɵɵtemplate(4, ArchitectureComponent_mat_card_161_div_21_mat_chip_4_Template, 2, 1, \"mat-chip\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", option_r16.useCases);\n  }\n}\nfunction ArchitectureComponent_mat_card_161_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 73)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 74)(10, \"div\", 75)(11, \"div\", 76)(12, \"h4\");\n    i0.ɵɵtext(13, \"Advantages:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ul\");\n    i0.ɵɵtemplate(15, ArchitectureComponent_mat_card_161_li_15_Template, 2, 1, \"li\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 77)(17, \"h4\");\n    i0.ɵɵtext(18, \"Considerations:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ul\");\n    i0.ɵɵtemplate(20, ArchitectureComponent_mat_card_161_li_20_Template, 2, 1, \"li\", 51);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, ArchitectureComponent_mat_card_161_div_21_Template, 5, 1, \"div\", 78);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const option_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", option_r16.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r16.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r16.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r16.description);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", option_r16.pros);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", option_r16.cons);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", option_r16.useCases);\n  }\n}\nexport let ArchitectureComponent = /*#__PURE__*/(() => {\n  class ArchitectureComponent {\n    constructor() {\n      this.architecturePrinciples = [{\n        title: 'Modularity',\n        description: 'Clear separation of concerns with independent, reusable components',\n        icon: 'view_module',\n        color: '#1976d2'\n      }, {\n        title: 'Scalability',\n        description: 'Horizontal and vertical scaling capabilities for growing workloads',\n        icon: 'trending_up',\n        color: '#4caf50'\n      }, {\n        title: 'Security',\n        description: 'Security-first design with multiple layers of protection',\n        icon: 'security',\n        color: '#f44336'\n      }, {\n        title: 'Extensibility',\n        description: 'Plugin architecture for adding new blockchain support and features',\n        icon: 'extension',\n        color: '#ff9800'\n      }];\n      this.systemComponents = [{\n        name: 'Angular Dashboard',\n        description: 'Modern web interface for security scanning, project management, and report generation',\n        technology: 'Angular',\n        responsibilities: ['User interface and experience', 'Real-time scan monitoring', 'Report visualization', 'Project management', 'User authentication'],\n        apis: ['REST API', 'WebSocket'],\n        dependencies: ['Angular Material', 'RxJS', 'Chart.js']\n      }, {\n        name: 'REST API Server',\n        description: 'RESTful API providing all backend functionality and data access',\n        technology: 'Go',\n        responsibilities: ['HTTP request handling', 'Authentication and authorization', 'Data validation', 'Business logic coordination', 'Response formatting'],\n        apis: ['HTTP REST', 'WebSocket'],\n        dependencies: ['Gin Framework', 'JWT', 'CORS']\n      }, {\n        name: 'Scanner Engine',\n        description: 'Core security analysis engine for blockchain applications',\n        technology: 'Go',\n        responsibilities: ['File parsing and analysis', 'Security rule execution', 'Vulnerability detection', 'Performance optimization', 'Result aggregation'],\n        dependencies: ['AST Parsers', 'Security Rules', 'Pattern Matching']\n      }, {\n        name: 'VS Code Extension',\n        description: 'IDE integration providing real-time security feedback',\n        technology: 'TypeScript',\n        responsibilities: ['Real-time code analysis', 'Inline security decorations', 'Problems panel integration', 'CodeLens functionality', 'Settings management'],\n        apis: ['VS Code API', 'SPT REST API'],\n        dependencies: ['VS Code SDK', 'WebSocket Client']\n      }, {\n        name: 'CLI Tool',\n        description: 'Command-line interface for automated security scanning',\n        technology: 'Go',\n        responsibilities: ['Command parsing', 'File system operations', 'Batch processing', 'Output formatting', 'CI/CD integration'],\n        dependencies: ['Cobra CLI', 'File Watchers', 'Output Formatters']\n      }, {\n        name: 'Database Layer',\n        description: 'Persistent storage for scan results, configurations, and user data',\n        technology: 'SQLite/PostgreSQL',\n        responsibilities: ['Data persistence', 'Query optimization', 'Transaction management', 'Data integrity', 'Backup and recovery'],\n        dependencies: ['GORM', 'Database Drivers', 'Migration Tools']\n      }];\n      this.scanFlowSteps = [{\n        title: 'Scan Request',\n        description: 'User initiates scan through web dashboard, CLI, or VS Code extension',\n        details: ['File path validation', 'Permission checks', 'Queue management']\n      }, {\n        title: 'File Discovery',\n        description: 'System discovers and categorizes files for analysis',\n        details: ['Recursive directory traversal', 'File type detection', 'Exclusion filtering']\n      }, {\n        title: 'Security Analysis',\n        description: 'Scanner engine analyzes files using blockchain-specific rules',\n        details: ['AST parsing', 'Pattern matching', 'Vulnerability detection', 'Performance analysis']\n      }, {\n        title: 'Result Processing',\n        description: 'Analysis results are processed and stored',\n        details: ['Result aggregation', 'Severity classification', 'Database storage', 'Cache updates']\n      }, {\n        title: 'Notification',\n        description: 'Results are delivered to requesting clients',\n        details: ['WebSocket notifications', 'REST API responses', 'Real-time updates']\n      }];\n      this.realtimeFlows = [{\n        from: 'Scanner Engine',\n        to: 'WebSocket Manager',\n        description: 'Scan progress and results are pushed to WebSocket manager',\n        protocol: 'Internal API'\n      }, {\n        from: 'WebSocket Manager',\n        to: 'Angular Dashboard',\n        description: 'Real-time scan updates are broadcast to web clients',\n        protocol: 'WebSocket'\n      }, {\n        from: 'WebSocket Manager',\n        to: 'VS Code Extension',\n        description: 'Live security feedback is sent to IDE clients',\n        protocol: 'WebSocket'\n      }];\n      this.securitySections = [{\n        title: 'Authentication & Authorization',\n        description: 'User identity and access control',\n        icon: 'person',\n        color: '#1976d2',\n        measures: ['JWT-based authentication', 'Role-based access control', 'Session management', 'API key authentication', 'Secure password policies']\n      }, {\n        title: 'Data Protection',\n        description: 'Data security and privacy measures',\n        icon: 'shield',\n        color: '#4caf50',\n        measures: ['Data encryption at rest', 'TLS/HTTPS for data in transit', 'Input validation and sanitization', 'SQL injection prevention', 'XSS protection']\n      }, {\n        title: 'Infrastructure Security',\n        description: 'System and network security',\n        icon: 'cloud_circle',\n        color: '#ff9800',\n        measures: ['CORS configuration', 'Rate limiting', 'Firewall rules', 'Security headers', 'Audit logging']\n      }];\n      this.deploymentOptions = [{\n        title: 'Local Development',\n        description: 'Single-machine deployment for development and testing',\n        icon: 'computer',\n        color: '#1976d2',\n        pros: ['Easy setup and configuration', 'Fast development cycles', 'Full control over environment', 'No network dependencies'],\n        cons: ['Limited scalability', 'Single point of failure', 'Resource constraints', 'Not suitable for production'],\n        useCases: ['Development', 'Testing', 'Proof of Concept']\n      }, {\n        title: 'Docker Containers',\n        description: 'Containerized deployment for consistency and portability',\n        icon: 'inventory_2',\n        color: '#4caf50',\n        pros: ['Environment consistency', 'Easy deployment', 'Resource isolation', 'Scalable architecture'],\n        cons: ['Container orchestration complexity', 'Resource overhead', 'Learning curve', 'Storage persistence challenges'],\n        useCases: ['Production', 'CI/CD', 'Multi-environment']\n      }, {\n        title: 'Cloud Deployment',\n        description: 'Cloud-native deployment with managed services',\n        icon: 'cloud',\n        color: '#ff9800',\n        pros: ['High availability', 'Auto-scaling', 'Managed infrastructure', 'Global distribution'],\n        cons: ['Vendor lock-in', 'Cost considerations', 'Network latency', 'Compliance requirements'],\n        useCases: ['Enterprise', 'High Availability', 'Global Teams']\n      }];\n    }\n    getComponentColor(technology) {\n      const colors = {\n        'Angular': '#dd0031',\n        'Go': '#00add8',\n        'TypeScript': '#3178c6',\n        'SQLite/PostgreSQL': '#336791'\n      };\n      return colors[technology] || '#1976d2';\n    }\n    getComponentIcon(technology) {\n      const icons = {\n        'Angular': 'web',\n        'Go': 'code',\n        'TypeScript': 'extension',\n        'SQLite/PostgreSQL': 'storage'\n      };\n      return icons[technology] || 'code';\n    }\n    getFlowIcon(component) {\n      const icons = {\n        'Scanner Engine': 'security',\n        'WebSocket Manager': 'swap_horiz',\n        'Angular Dashboard': 'web',\n        'VS Code Extension': 'extension'\n      };\n      return icons[component] || 'device_unknown';\n    }\n    static {\n      this.ɵfac = function ArchitectureComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ArchitectureComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ArchitectureComponent,\n        selectors: [[\"app-architecture\"]],\n        decls: 162,\n        vars: 6,\n        consts: [[1, \"architecture-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [1, \"architecture-overview\"], [1, \"overview-card\"], [\"mat-card-avatar\", \"\"], [1, \"architecture-principles\"], [\"class\", \"principle\", 4, \"ngFor\", \"ngForOf\"], [\"animationDuration\", \"300ms\", 1, \"architecture-tabs\"], [\"label\", \"System Overview\"], [1, \"tab-content\"], [1, \"architecture-diagram\"], [1, \"diagram-card\"], [1, \"diagram-content\"], [1, \"component-layer\", \"frontend-layer\"], [1, \"components\"], [1, \"component\", \"frontend\"], [1, \"component\", \"vscode\"], [1, \"component\", \"cli\"], [1, \"connection-layer\"], [1, \"connection\"], [1, \"component-layer\", \"backend-layer\"], [1, \"component\", \"api\"], [1, \"component\", \"scanner\"], [1, \"component\", \"reports\"], [1, \"component-layer\", \"data-layer\"], [1, \"component\", \"database\"], [1, \"component\", \"cache\"], [1, \"component\", \"files\"], [\"label\", \"Components\"], [1, \"components-grid\"], [\"class\", \"component-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Data Flow\"], [1, \"data-flow-sections\"], [1, \"flow-card\"], [1, \"flow-diagram\"], [\"class\", \"flow-step\", 4, \"ngFor\", \"ngForOf\"], [1, \"realtime-flow\"], [\"class\", \"flow-item\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Security\"], [1, \"security-sections\"], [\"class\", \"security-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Deployment\"], [1, \"deployment-options\"], [\"class\", \"deployment-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"principle\"], [1, \"component-card\"], [\"class\", \"responsibilities\", 4, \"ngIf\"], [\"class\", \"apis\", 4, \"ngIf\"], [\"class\", \"dependencies\", 4, \"ngIf\"], [1, \"responsibilities\"], [4, \"ngFor\", \"ngForOf\"], [1, \"apis\"], [1, \"api-chips\"], [1, \"dependencies\"], [1, \"dependency-chips\"], [1, \"flow-step\"], [1, \"step-number\"], [1, \"step-content\"], [\"class\", \"step-details\", 4, \"ngIf\"], [\"class\", \"flow-arrow\", 4, \"ngIf\"], [1, \"step-details\"], [1, \"flow-arrow\"], [1, \"flow-item\"], [1, \"flow-source\"], [1, \"flow-connection\"], [1, \"protocol\"], [1, \"flow-target\"], [1, \"flow-description\"], [1, \"security-card\"], [1, \"security-measures\"], [\"class\", \"measure\", 4, \"ngFor\", \"ngForOf\"], [1, \"measure\"], [1, \"deployment-card\"], [1, \"deployment-details\"], [1, \"pros-cons\"], [1, \"pros\"], [1, \"cons\"], [\"class\", \"use-cases\", 4, \"ngIf\"], [1, \"use-cases\"]],\n        template: function ArchitectureComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"account_tree\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(5, \" System Architecture \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 2);\n            i0.ɵɵtext(7, \" Comprehensive overview of SPT system architecture and components \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-header\")(11, \"mat-icon\", 5);\n            i0.ɵɵtext(12, \"info\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-title\");\n            i0.ɵɵtext(14, \"Architecture Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"mat-card-subtitle\");\n            i0.ɵɵtext(16, \"Modular, scalable, and secure design\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"mat-card-content\")(18, \"p\");\n            i0.ɵɵtext(19, \"SPT follows a microservices architecture with clear separation of concerns, enabling scalability, maintainability, and extensibility.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 6);\n            i0.ɵɵtemplate(21, ArchitectureComponent_div_21_Template, 8, 5, \"div\", 7);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(22, \"mat-tab-group\", 8)(23, \"mat-tab\", 9)(24, \"div\", 10)(25, \"h2\");\n            i0.ɵɵtext(26, \"High-Level Architecture\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"p\");\n            i0.ɵɵtext(28, \"SPT consists of multiple interconnected components working together to provide comprehensive security analysis.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 11)(30, \"mat-card\", 12)(31, \"mat-card-header\")(32, \"mat-icon\", 5);\n            i0.ɵɵtext(33, \"architecture\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"mat-card-title\");\n            i0.ɵɵtext(35, \"System Components\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"mat-card-subtitle\");\n            i0.ɵɵtext(37, \"Main architectural components and their relationships\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"mat-card-content\")(39, \"div\", 13)(40, \"div\", 14)(41, \"h4\");\n            i0.ɵɵtext(42, \"Frontend Layer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 15)(44, \"div\", 16)(45, \"mat-icon\");\n            i0.ɵɵtext(46, \"web\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"span\");\n            i0.ɵɵtext(48, \"Angular Dashboard\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 17)(50, \"mat-icon\");\n            i0.ɵɵtext(51, \"extension\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"span\");\n            i0.ɵɵtext(53, \"VS Code Extension\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"div\", 18)(55, \"mat-icon\");\n            i0.ɵɵtext(56, \"terminal\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"span\");\n            i0.ɵɵtext(58, \"CLI Tool\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(59, \"div\", 19)(60, \"div\", 20)(61, \"mat-icon\");\n            i0.ɵɵtext(62, \"swap_vert\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"span\");\n            i0.ɵɵtext(64, \"HTTP/WebSocket\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(65, \"div\", 21)(66, \"h4\");\n            i0.ɵɵtext(67, \"Backend Layer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"div\", 15)(69, \"div\", 22)(70, \"mat-icon\");\n            i0.ɵɵtext(71, \"api\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"span\");\n            i0.ɵɵtext(73, \"REST API\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"div\", 23)(75, \"mat-icon\");\n            i0.ɵɵtext(76, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"span\");\n            i0.ɵɵtext(78, \"Scanner Engine\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(79, \"div\", 24)(80, \"mat-icon\");\n            i0.ɵɵtext(81, \"assessment\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"span\");\n            i0.ɵɵtext(83, \"Report Generator\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(84, \"div\", 19)(85, \"div\", 20)(86, \"mat-icon\");\n            i0.ɵɵtext(87, \"swap_vert\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"span\");\n            i0.ɵɵtext(89, \"Database Queries\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(90, \"div\", 25)(91, \"h4\");\n            i0.ɵɵtext(92, \"Data Layer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"div\", 15)(94, \"div\", 26)(95, \"mat-icon\");\n            i0.ɵɵtext(96, \"storage\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"span\");\n            i0.ɵɵtext(98, \"SQLite/PostgreSQL\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(99, \"div\", 27)(100, \"mat-icon\");\n            i0.ɵɵtext(101, \"memory\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"span\");\n            i0.ɵɵtext(103, \"In-Memory Cache\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(104, \"div\", 28)(105, \"mat-icon\");\n            i0.ɵɵtext(106, \"folder\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"span\");\n            i0.ɵɵtext(108, \"File System\");\n            i0.ɵɵelementEnd()()()()()()()()()();\n            i0.ɵɵelementStart(109, \"mat-tab\", 29)(110, \"div\", 10)(111, \"h2\");\n            i0.ɵɵtext(112, \"System Components\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(113, \"p\");\n            i0.ɵɵtext(114, \"Detailed breakdown of each component in the SPT architecture.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"div\", 30);\n            i0.ɵɵtemplate(116, ArchitectureComponent_mat_card_116_Template, 14, 9, \"mat-card\", 31);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(117, \"mat-tab\", 32)(118, \"div\", 10)(119, \"h2\");\n            i0.ɵɵtext(120, \"Data Flow Patterns\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(121, \"p\");\n            i0.ɵɵtext(122, \"How data flows through the SPT system during different operations.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(123, \"div\", 33)(124, \"mat-card\", 34)(125, \"mat-card-header\")(126, \"mat-icon\", 5);\n            i0.ɵɵtext(127, \"timeline\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(128, \"mat-card-title\");\n            i0.ɵɵtext(129, \"Security Scan Flow\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(130, \"mat-card-subtitle\");\n            i0.ɵɵtext(131, \"End-to-end scan process\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(132, \"mat-card-content\")(133, \"div\", 35);\n            i0.ɵɵtemplate(134, ArchitectureComponent_div_134_Template, 10, 5, \"div\", 36);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(135, \"mat-card\", 34)(136, \"mat-card-header\")(137, \"mat-icon\", 5);\n            i0.ɵɵtext(138, \"swap_horiz\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(139, \"mat-card-title\");\n            i0.ɵɵtext(140, \"Real-time Updates\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(141, \"mat-card-subtitle\");\n            i0.ɵɵtext(142, \"WebSocket communication flow\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(143, \"mat-card-content\")(144, \"div\", 37);\n            i0.ɵɵtemplate(145, ArchitectureComponent_div_145_Template, 19, 6, \"div\", 38);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(146, \"mat-tab\", 39)(147, \"div\", 10)(148, \"h2\");\n            i0.ɵɵtext(149, \"Security Architecture\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(150, \"p\");\n            i0.ɵɵtext(151, \"Security measures and patterns implemented throughout the SPT system.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(152, \"div\", 40);\n            i0.ɵɵtemplate(153, ArchitectureComponent_mat_card_153_Template, 11, 6, \"mat-card\", 41);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(154, \"mat-tab\", 42)(155, \"div\", 10)(156, \"h2\");\n            i0.ɵɵtext(157, \"Deployment Architecture\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(158, \"p\");\n            i0.ɵɵtext(159, \"Deployment patterns and infrastructure considerations for SPT.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(160, \"div\", 43);\n            i0.ɵɵtemplate(161, ArchitectureComponent_mat_card_161_Template, 22, 8, \"mat-card\", 44);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngForOf\", ctx.architecturePrinciples);\n            i0.ɵɵadvance(95);\n            i0.ɵɵproperty(\"ngForOf\", ctx.systemComponents);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngForOf\", ctx.scanFlowSteps);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.realtimeFlows);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.securitySections);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.deploymentOptions);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatChipsModule, i5.MatChip, i5.MatChipListbox, MatExpansionModule],\n        styles: [\".architecture-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}mat-icon[_ngcontent-%COMP%]{display:inline-flex!important;align-items:center!important;justify-content:center!important;vertical-align:middle!important;line-height:1!important}.page-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;color:#1976d2;margin:0 0 8px}.page-subtitle[_ngcontent-%COMP%]{color:#666;font-size:1.1em;margin:0}.architecture-overview[_ngcontent-%COMP%]{margin-bottom:32px}.overview-card[_ngcontent-%COMP%]{border:1px solid #e0e0e0}.architecture-principles[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:16px;margin-top:16px}.principle[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;padding:12px;background:#f5f5f5;border-radius:8px}.principle[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:2px}.principle[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:4px}.principle[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.9em}.architecture-tabs[_ngcontent-%COMP%]{margin-bottom:32px}.tab-content[_ngcontent-%COMP%]{padding:24px 0}.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:8px}.architecture-diagram[_ngcontent-%COMP%]{margin-top:24px}.diagram-card[_ngcontent-%COMP%]{border:1px solid #e0e0e0}.diagram-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;padding:16px}.component-layer[_ngcontent-%COMP%]{padding:16px;border-radius:8px;text-align:center}.frontend-layer[_ngcontent-%COMP%]{background:#e3f2fd}.backend-layer[_ngcontent-%COMP%]{background:#e8f5e8}.data-layer[_ngcontent-%COMP%]{background:#fff3e0}.component-layer[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;color:#1976d2}.components[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:16px;flex-wrap:wrap}.component[_ngcontent-%COMP%]{padding:12px;background:#fff;border-radius:8px;text-align:center;min-width:120px}.connection-layer[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.connection[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:4px;color:#666}.connection[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.8em}.components-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:24px;margin-top:24px}.component-card[_ngcontent-%COMP%]{height:100%}.responsibilities[_ngcontent-%COMP%], .apis[_ngcontent-%COMP%], .dependencies[_ngcontent-%COMP%]{margin-top:16px}.responsibilities[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .apis[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .dependencies[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#1976d2;font-size:.9em}.responsibilities[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px}.responsibilities[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:4px;color:#666;font-size:.9em}.api-chips[_ngcontent-%COMP%], .dependency-chips[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:4px}.data-flow-sections[_ngcontent-%COMP%]{margin-top:24px}.flow-card[_ngcontent-%COMP%]{margin-bottom:24px}.flow-diagram[_ngcontent-%COMP%]{padding:16px}.flow-step[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:16px;margin-bottom:24px}.step-number[_ngcontent-%COMP%]{background:#1976d2;color:#fff;border-radius:50%;width:32px;height:32px;display:flex;align-items:center;justify-content:center;font-weight:700;flex-shrink:0}.step-content[_ngcontent-%COMP%]{flex:1}.step-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#1976d2}.step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 8px;color:#666}.step-details[_ngcontent-%COMP%]{margin-top:8px}.flow-arrow[_ngcontent-%COMP%]{text-align:center;color:#1976d2;margin:8px 0}.realtime-flow[_ngcontent-%COMP%]{padding:16px}.flow-item[_ngcontent-%COMP%]{padding:16px;background:#f9f9f9;border-radius:8px;margin-bottom:24px}.security-sections[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:24px;margin-top:24px}.security-card[_ngcontent-%COMP%]{height:fit-content}.security-measures[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.measure[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px;background:#f5f5f5;border-radius:4px}.measure[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#4caf50;font-size:18px;width:18px;height:18px}.deployment-options[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:24px;margin-top:24px}.deployment-card[_ngcontent-%COMP%]{height:fit-content}.deployment-details[_ngcontent-%COMP%]{margin-top:16px}.pros-cons[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:16px;margin-bottom:16px}.pros[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .cons[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#1976d2;font-size:.9em}.pros[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .cons[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px}.pros[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .cons[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:4px;color:#666;font-size:.9em}.use-cases[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#1976d2;font-size:.9em}@media (max-width: 768px){.architecture-principles[_ngcontent-%COMP%]{grid-template-columns:1fr}.components[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.components-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.flow-item[_ngcontent-%COMP%]{grid-template-columns:1fr;text-align:center}.pros-cons[_ngcontent-%COMP%], .security-sections[_ngcontent-%COMP%], .deployment-options[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return ArchitectureComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}