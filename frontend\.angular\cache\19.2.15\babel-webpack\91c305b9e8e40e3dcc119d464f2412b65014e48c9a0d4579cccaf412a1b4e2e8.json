{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/checkbox\";\nfunction LoginComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 24);\n  }\n}\nfunction LoginComponent_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  loginWithDemo() {\n    this.isLoading = true;\n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', {\n        duration: 3000\n      });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 57,\n      vars: 11,\n      consts: [[1, \"login-container\"], [1, \"login-card-wrapper\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"logo-icon\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"form-options\"], [\"formControlName\", \"rememberMe\"], [\"href\", \"#\", 1, \"forgot-password\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"demo-button\", 3, \"click\", \"disabled\"], [1, \"card-actions\"], [\"routerLink\", \"/register\", 1, \"register-link\"], [1, \"features-info\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h1\");\n          i0.ɵɵtext(8, \"SPT Security\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n          i0.ɵɵtext(10, \"Blockchain Security Protocol Tool\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"mat-form-field\", 8)(15, \"mat-label\");\n          i0.ɵɵtext(16, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 9);\n          i0.ɵɵelementStart(18, \"mat-icon\", 10);\n          i0.ɵɵtext(19, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, LoginComponent_mat_error_20_Template, 2, 0, \"mat-error\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-form-field\", 8)(22, \"mat-label\");\n          i0.ɵɵtext(23, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 12);\n          i0.ɵɵelementStart(25, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_25_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(26, \"mat-icon\");\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(28, LoginComponent_mat_error_28_Template, 2, 0, \"mat-error\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 14)(30, \"mat-checkbox\", 15);\n          i0.ɵɵtext(31, \" Remember me \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"a\", 16);\n          i0.ɵɵtext(33, \"Forgot password?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 17)(35, \"button\", 18);\n          i0.ɵɵtemplate(36, LoginComponent_mat_spinner_36_Template, 1, 0, \"mat-spinner\", 19)(37, LoginComponent_span_37_Template, 2, 0, \"span\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_38_listener() {\n            return ctx.loginWithDemo();\n          });\n          i0.ɵɵtext(39, \" Demo Login \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"mat-card-actions\", 21)(41, \"p\");\n          i0.ɵɵtext(42, \"Don't have an account? \");\n          i0.ɵɵelementStart(43, \"a\", 22);\n          i0.ɵɵtext(44, \"Sign up\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"div\", 23)(46, \"h3\");\n          i0.ɵɵtext(47, \"\\uD83D\\uDEE1\\uFE0F Security Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"ul\")(49, \"li\");\n          i0.ɵɵtext(50, \"\\u2705 Smart Contract Analysis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"li\");\n          i0.ɵɵtext(52, \"\\u2705 Vulnerability Detection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"li\");\n          i0.ɵɵtext(54, \"\\u2705 Real-time Monitoring\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"li\");\n          i0.ɵɵtext(56, \"\\u2705 Comprehensive Reports\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.hasError(\"required\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i12.MatCheckbox],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.login-card-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 40px;\\n  align-items: center;\\n  max-width: 900px;\\n  width: 100%;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 400px;\\n  padding: 20px;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #667eea;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 300;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 14px;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.demo-button[_ngcontent-%COMP%] {\\n  height: 40px;\\n  color: #667eea;\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.register-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.features-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n  padding: 40px;\\n}\\n\\n.features-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 20px;\\n  font-weight: 300;\\n}\\n\\n.features-info[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n}\\n\\n.features-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n  font-size: 16px;\\n  opacity: 0.9;\\n}\\n\\n@media (max-width: 768px) {\\n  .login-card-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n  .features-info[_ngcontent-%COMP%] {\\n    order: -1;\\n    padding: 20px;\\n    text-align: center;\\n  }\\n  .login-card[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n}\\nmat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "hidePassword", "isLoading", "returnUrl", "loginForm", "group", "username", "required", "password", "rememberMe", "ngOnInit", "snapshot", "queryParams", "onSubmit", "valid", "credentials", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "message", "loginWithDemo", "setTimeout", "mockLogin", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_12_listener", "ɵɵtemplate", "LoginComponent_mat_error_20_Template", "LoginComponent_Template_button_click_25_listener", "LoginComponent_mat_error_28_Template", "LoginComponent_mat_spinner_36_Template", "LoginComponent_span_37_Template", "LoginComponent_Template_button_click_38_listener", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "tmp_6_0", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatButton", "MatIconButton", "i10", "MatIcon", "i11", "MatProgressSpinner", "i12", "MatCheckbox", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { AuthService, LoginRequest } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  template: `\n    <div class=\"login-container\">\n      <div class=\"login-card-wrapper\">\n        <mat-card class=\"login-card\">\n          <mat-card-header class=\"login-header\">\n            <div class=\"logo\">\n              <mat-icon class=\"logo-icon\">security</mat-icon>\n              <h1>SPT Security</h1>\n            </div>\n            <mat-card-subtitle>Blockchain Security Protocol Tool</mat-card-subtitle>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n              <div class=\"form-fields\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Username</mat-label>\n                  <input matInput formControlName=\"username\" autocomplete=\"username\">\n                  <mat-icon matSuffix>person</mat-icon>\n                  <mat-error *ngIf=\"loginForm.get('username')?.hasError('required')\">\n                    Username is required\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Password</mat-label>\n                  <input matInput \n                         [type]=\"hidePassword ? 'password' : 'text'\" \n                         formControlName=\"password\"\n                         autocomplete=\"current-password\">\n                  <button mat-icon-button matSuffix \n                          (click)=\"hidePassword = !hidePassword\" \n                          [attr.aria-label]=\"'Hide password'\" \n                          [attr.aria-pressed]=\"hidePassword\"\n                          type=\"button\">\n                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                  </button>\n                  <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                    Password is required\n                  </mat-error>\n                </mat-form-field>\n\n                <div class=\"form-options\">\n                  <mat-checkbox formControlName=\"rememberMe\">\n                    Remember me\n                  </mat-checkbox>\n                  <a href=\"#\" class=\"forgot-password\">Forgot password?</a>\n                </div>\n              </div>\n\n              <div class=\"form-actions\">\n                <button mat-raised-button \n                        color=\"primary\" \n                        type=\"submit\" \n                        class=\"login-button\"\n                        [disabled]=\"loginForm.invalid || isLoading\">\n                  <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n                  <span *ngIf=\"!isLoading\">Sign In</span>\n                </button>\n\n                <button mat-button \n                        type=\"button\" \n                        class=\"demo-button\"\n                        (click)=\"loginWithDemo()\"\n                        [disabled]=\"isLoading\">\n                  Demo Login\n                </button>\n              </div>\n            </form>\n          </mat-card-content>\n\n          <mat-card-actions class=\"card-actions\">\n            <p>Don't have an account? \n              <a routerLink=\"/register\" class=\"register-link\">Sign up</a>\n            </p>\n          </mat-card-actions>\n        </mat-card>\n\n        <div class=\"features-info\">\n          <h3>🛡️ Security Features</h3>\n          <ul>\n            <li>✅ Smart Contract Analysis</li>\n            <li>✅ Vulnerability Detection</li>\n            <li>✅ Real-time Monitoring</li>\n            <li>✅ Comprehensive Reports</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .login-container {\n      min-height: 100vh;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 20px;\n    }\n\n    .login-card-wrapper {\n      display: flex;\n      gap: 40px;\n      align-items: center;\n      max-width: 900px;\n      width: 100%;\n    }\n\n    .login-card {\n      flex: 1;\n      max-width: 400px;\n      padding: 20px;\n    }\n\n    .login-header {\n      text-align: center;\n      margin-bottom: 30px;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 10px;\n      margin-bottom: 10px;\n    }\n\n    .logo-icon {\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n      color: #667eea;\n    }\n\n    .logo h1 {\n      margin: 0;\n      color: #333;\n      font-weight: 300;\n    }\n\n    .form-fields {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n\n    .full-width {\n      width: 100%;\n    }\n\n    .form-options {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-top: 10px;\n    }\n\n    .forgot-password {\n      color: #667eea;\n      text-decoration: none;\n      font-size: 14px;\n    }\n\n    .forgot-password:hover {\n      text-decoration: underline;\n    }\n\n    .form-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 15px;\n    }\n\n    .login-button {\n      height: 48px;\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .demo-button {\n      height: 40px;\n      color: #667eea;\n    }\n\n    .card-actions {\n      text-align: center;\n      padding-top: 20px;\n      border-top: 1px solid #e0e0e0;\n    }\n\n    .register-link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .register-link:hover {\n      text-decoration: underline;\n    }\n\n    .features-info {\n      flex: 1;\n      color: white;\n      padding: 40px;\n    }\n\n    .features-info h3 {\n      font-size: 24px;\n      margin-bottom: 20px;\n      font-weight: 300;\n    }\n\n    .features-info ul {\n      list-style: none;\n      padding: 0;\n    }\n\n    .features-info li {\n      padding: 10px 0;\n      font-size: 16px;\n      opacity: 0.9;\n    }\n\n    @media (max-width: 768px) {\n      .login-card-wrapper {\n        flex-direction: column;\n        gap: 20px;\n      }\n\n      .features-info {\n        order: -1;\n        padding: 20px;\n        text-align: center;\n      }\n\n      .login-card {\n        max-width: 100%;\n      }\n    }\n\n    mat-spinner {\n      margin-right: 10px;\n    }\n  `]\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  hidePassword = true;\n  isLoading = false;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      \n      const credentials: LoginRequest = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n\n  loginWithDemo(): void {\n    this.isLoading = true;\n    \n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AAExF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;IAqC5CC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAiBZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AA6LzD,OAAM,MAAOE,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MAErB,MAAMa,WAAW,GAAiB;QAChCT,QAAQ,EAAE,IAAI,CAACF,SAAS,CAACY,KAAK,CAACV,QAAQ;QACvCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACY,KAAK,CAACR;OAChC;MAED,IAAI,CAACX,WAAW,CAACoB,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;QACxC,CAAC;QACDqB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;OACD,CAAC;IACJ;EACF;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACxB,SAAS,GAAG,IAAI;IAErB;IACAyB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9B,WAAW,CAAC+B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;MAC5C,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;IACxC,CAAC,EAAE,IAAI,CAAC;EACV;;;uCA1DWT,cAAc,EAAAL,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA/C,EAAA,CAAAwC,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd5C,cAAc;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhPbxD,EALV,CAAAC,cAAA,aAA6B,aACK,kBACD,yBACW,aAClB,kBACY;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/CH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAClBF,EADkB,CAAAG,YAAA,EAAK,EACjB;UACNH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,yCAAiC;UACtDF,EADsD,CAAAG,YAAA,EAAoB,EACxD;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,eACsC;UAAxBD,EAAA,CAAA0D,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAG/CxB,EAFJ,CAAAC,cAAA,cAAyB,yBACiC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAAmE;UACnEJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA4D,UAAA,KAAAC,oCAAA,wBAAmE;UAGrE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAGuC;UACvCJ,EAAA,CAAAC,cAAA,kBAIsB;UAHdD,EAAA,CAAA0D,UAAA,mBAAAI,iDAAA;YAAA,OAAAL,GAAA,CAAA7C,YAAA,IAAA6C,GAAA,CAAA7C,YAAA;UAAA,EAAsC;UAI5CZ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAA4D,UAAA,KAAAG,oCAAA,wBAAmE;UAGrE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,wBACmB;UACzCD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAExDF,EAFwD,CAAAG,YAAA,EAAI,EACpD,EACF;UAGJH,EADF,CAAAC,cAAA,eAA0B,kBAK4B;UAElDD,EADA,CAAA4D,UAAA,KAAAI,sCAAA,0BAA6C,KAAAC,+BAAA,mBACpB;UAC3BjE,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAI+B;UADvBD,EAAA,CAAA0D,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAE/BrC,EAAA,CAAAE,MAAA,oBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACU;UAGjBH,EADF,CAAAC,cAAA,4BAAuC,SAClC;UAAAD,EAAA,CAAAE,MAAA,+BACD;UAAAF,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAG7DF,EAH6D,CAAAG,YAAA,EAAI,EACzD,EACa,EACV;UAGTH,EADF,CAAAC,cAAA,eAA2B,UACrB;UAAAD,EAAA,CAAAE,MAAA,4CAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EADF,CAAAC,cAAA,UAAI,UACE;UAAAD,EAAA,CAAAE,MAAA,sCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,sCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,mCAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oCAAuB;UAInCF,EAJmC,CAAAG,YAAA,EAAK,EAC7B,EACD,EACF,EACF;;;;;UA3EQH,EAAA,CAAAmE,SAAA,IAAuB;UAAvBnE,EAAA,CAAAoE,UAAA,cAAAX,GAAA,CAAA1C,SAAA,CAAuB;UAMXf,EAAA,CAAAmE,SAAA,GAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAC,OAAA,GAAAZ,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAD,OAAA,CAAAE,QAAA,aAAqD;UAQ1DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA7C,YAAA,uBAA2C;UAK1CZ,EAAA,CAAAmE,SAAA,EAAmC;;UAG/BnE,EAAA,CAAAmE,SAAA,GAAkD;UAAlDnE,EAAA,CAAAwE,iBAAA,CAAAf,GAAA,CAAA7C,YAAA,mCAAkD;UAElDZ,EAAA,CAAAmE,SAAA,EAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAK,OAAA,GAAAhB,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAG,OAAA,CAAAF,QAAA,aAAqD;UAkB3DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA1C,SAAA,CAAA2D,OAAA,IAAAjB,GAAA,CAAA5C,SAAA,CAA2C;UACnCb,EAAA,CAAAmE,SAAA,EAAe;UAAfnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA5C,SAAA,CAAe;UACtBb,EAAA,CAAAmE,SAAA,EAAgB;UAAhBnE,EAAA,CAAAoE,UAAA,UAAAX,GAAA,CAAA5C,SAAA,CAAgB;UAOjBb,EAAA,CAAAmE,SAAA,EAAsB;UAAtBnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA5C,SAAA,CAAsB;;;qBA3E1CxB,YAAY,EAAAsF,EAAA,CAAAC,IAAA,EACZtF,mBAAmB,EAAAmD,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,kBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EACnB1F,aAAa,EAAA2F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EACb/F,kBAAkB,EAAAgG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBnG,cAAc,EAAAoG,EAAA,CAAAC,QAAA,EACdpG,eAAe,EAAAqG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACftG,aAAa,EAAAuG,GAAA,CAAAC,OAAA,EACbvG,wBAAwB,EAAAwG,GAAA,CAAAC,kBAAA,EACxBxG,iBAAiB,EACjBC,iBAAiB,EAAAwG,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}