{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { SEVERITY_COLORS } from '../../models/security.models';\nimport { SecurityMetricsChartComponent } from '../../shared/charts/security-metrics-chart/security-metrics-chart.component';\nimport { ScanProgressChartComponent } from '../../shared/charts/scan-progress-chart/scan-progress-chart.component';\nimport { SecurityTrendsDashboardComponent } from '../../shared/charts/security-trends-dashboard/security-trends-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/table\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/tooltip\";\nimport * as i9 from \"@angular/router\";\nconst _c0 = a0 => [\"/reports\", a0];\nfunction DashboardComponent_div_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"app-scan-progress-chart\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scanProgress\", ctx_r0.getActiveScan())(\"showRealTimeUpdates\", true)(\"updateInterval\", 2000);\n  }\n}\nfunction DashboardComponent_th_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", scan_r2.id.substring(0, 8), \"...\");\n  }\n}\nfunction DashboardComponent_th_142_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 69)(2, \"span\", 70);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 71);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getProjectName(scan_r3.project_path));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(scan_r3.project_path);\n  }\n}\nfunction DashboardComponent_th_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Chains\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_146_mat_chip_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chain_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(chain_r4);\n  }\n}\nfunction DashboardComponent_td_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 72)(2, \"mat-chip-set\");\n    i0.ɵɵtemplate(3, DashboardComponent_td_146_mat_chip_3_Template, 2, 1, \"mat-chip\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", scan_r5.chains);\n  }\n}\nfunction DashboardComponent_th_148_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 74)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + scan_r6.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getStatusIcon(scan_r6.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 4, scan_r6.status));\n  }\n}\nfunction DashboardComponent_th_151_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_152_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getIssueCount(scan_r7, \"critical\"), \" Critical \");\n  }\n}\nfunction DashboardComponent_td_152_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getIssueCount(scan_r7, \"high\"), \" High \");\n  }\n}\nfunction DashboardComponent_td_152_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getIssueCount(scan_r7, \"medium\"), \" Medium \");\n  }\n}\nfunction DashboardComponent_td_152_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83);\n    i0.ɵɵtext(1, \" No issues found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_152_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 75);\n    i0.ɵɵtemplate(2, DashboardComponent_td_152_span_2_Template, 2, 1, \"span\", 76)(3, DashboardComponent_td_152_span_3_Template, 2, 1, \"span\", 77)(4, DashboardComponent_td_152_span_4_Template, 2, 1, \"span\", 78)(5, DashboardComponent_td_152_span_5_Template, 2, 0, \"span\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getIssueCount(scan_r7, \"critical\") > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getIssueCount(scan_r7, \"high\") > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getIssueCount(scan_r7, \"medium\") > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !scan_r7.issues || scan_r7.issues.length === 0);\n  }\n}\nfunction DashboardComponent_th_154_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_155_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 84)(2, \"span\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 86);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(4, 2, scan_r8.created_at, \"MMM d, y\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 5, scan_r8.created_at, \"h:mm a\"));\n  }\n}\nfunction DashboardComponent_th_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_158_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 91)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"download\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_td_158_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 92)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"hourglass_empty\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_td_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 87)(2, \"button\", 88)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, DashboardComponent_td_158_button_5_Template, 3, 0, \"button\", 89)(6, DashboardComponent_td_158_button_6_Template, 3, 0, \"button\", 90);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, scan_r9.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", scan_r9.status === \"completed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", scan_r9.status === \"running\");\n  }\n}\nfunction DashboardComponent_tr_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 93);\n  }\n}\nfunction DashboardComponent_tr_160_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 94);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    // Additional properties for enhanced header\n    get totalScans() {\n      return this.stats.totalScans;\n    }\n    get activeScans() {\n      return this.recentScans.filter(scan => scan.status === 'running').length;\n    }\n    get criticalIssues() {\n      return this.stats.criticalIssues;\n    }\n    constructor(apiService) {\n      this.apiService = apiService;\n      this.stats = {\n        totalScans: 0,\n        criticalIssues: 0,\n        highIssues: 0,\n        mediumIssues: 0\n      };\n      this.recentScans = [];\n      this.topIssues = [];\n      this.displayedColumns = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n    }\n    ngOnInit() {\n      this.loadDashboardData();\n    }\n    loadDashboardData() {\n      // Load recent scans\n      this.apiService.getScanHistory().subscribe({\n        next: response => {\n          this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n          this.calculateStats(response.scans);\n        },\n        error: error => {\n          console.error('Error loading scan history:', error);\n          // Use mock data for development\n          this.loadMockData();\n        }\n      });\n    }\n    loadMockData() {\n      // Generate mock data for development\n      const mockScan = this.apiService.generateMockScanResult();\n      this.recentScans = [mockScan];\n      this.topIssues = mockScan.issues;\n      this.stats = {\n        totalScans: 1,\n        criticalIssues: mockScan.severity_counts['critical'] || 0,\n        highIssues: mockScan.severity_counts['high'] || 0,\n        mediumIssues: mockScan.severity_counts['medium'] || 0\n      };\n    }\n    calculateStats(scans) {\n      this.stats.totalScans = scans.length;\n      let criticalTotal = 0;\n      let highTotal = 0;\n      let mediumTotal = 0;\n      let allIssues = [];\n      scans.forEach(scan => {\n        criticalTotal += scan.severity_counts?.['critical'] || 0;\n        highTotal += scan.severity_counts?.['high'] || 0;\n        mediumTotal += scan.severity_counts?.['medium'] || 0;\n        allIssues = allIssues.concat(scan.issues || []);\n      });\n      this.stats.criticalIssues = criticalTotal;\n      this.stats.highIssues = highTotal;\n      this.stats.mediumIssues = mediumTotal;\n      // Get top 5 most severe issues\n      this.topIssues = allIssues.sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity)).slice(0, 5);\n    }\n    getSeverityWeight(severity) {\n      const weights = {\n        critical: 4,\n        high: 3,\n        medium: 2,\n        low: 1,\n        info: 0\n      };\n      return weights[severity] || 0;\n    }\n    getSeverityColor(severity) {\n      return SEVERITY_COLORS[severity] || '#666';\n    }\n    getProjectName(path) {\n      return path.split('/').pop() || path;\n    }\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString();\n    }\n    formatTime(dateString) {\n      return new Date(dateString).toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getStatusIcon(status) {\n      const icons = {\n        completed: 'check_circle',\n        running: 'hourglass_empty',\n        failed: 'error',\n        pending: 'schedule'\n      };\n      return icons[status] || 'help';\n    }\n    getIssueCount(scan, severity) {\n      return scan.severity_counts?.[severity] || 0;\n    }\n    // Chart data methods\n    getSecurityMetricsData() {\n      return [{\n        label: 'Critical',\n        value: this.stats.criticalIssues,\n        severity: 'critical',\n        trend: 15 // Mock trend data\n      }, {\n        label: 'High',\n        value: this.stats.highIssues,\n        severity: 'high',\n        trend: -5\n      }, {\n        label: 'Medium',\n        value: this.stats.mediumIssues,\n        severity: 'medium',\n        trend: 8\n      }, {\n        label: 'Low',\n        value: Math.max(0, this.stats.totalScans * 2 - this.stats.criticalIssues - this.stats.highIssues - this.stats.mediumIssues),\n        severity: 'low',\n        trend: -12\n      }];\n    }\n    getActiveScan() {\n      const activeScan = this.recentScans.find(scan => scan.status === 'running');\n      if (!activeScan) return null;\n      return {\n        scanId: activeScan.id,\n        projectName: this.getProjectName(activeScan.project_path),\n        status: 'running',\n        progress: 65,\n        // Mock progress\n        currentStep: 'Analyzing smart contracts',\n        totalSteps: 6,\n        completedSteps: 3,\n        startTime: new Date(activeScan.created_at),\n        estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000),\n        // 5 minutes from now\n        filesScanned: 45,\n        totalFiles: 78,\n        issuesFound: activeScan.issues?.length || 0\n      };\n    }\n    getSecurityTrendsData() {\n      // Generate mock trend data\n      const now = new Date();\n      const data = [];\n      for (let i = 29; i >= 0; i--) {\n        const date = new Date(now);\n        date.setDate(date.getDate() - i);\n        data.push({\n          date,\n          critical: Math.floor(Math.random() * 5) + (i < 10 ? 2 : 0),\n          high: Math.floor(Math.random() * 8) + 3,\n          medium: Math.floor(Math.random() * 12) + 5,\n          low: Math.floor(Math.random() * 15) + 8,\n          totalScans: Math.floor(Math.random() * 3) + 1,\n          averageScore: Math.floor(Math.random() * 30) + 60\n        });\n      }\n      return {\n        period: '30d',\n        data,\n        summary: {\n          totalIssues: this.stats.criticalIssues + this.stats.highIssues + this.stats.mediumIssues,\n          criticalTrend: 15,\n          // Mock trend\n          averageScore: 72,\n          scanFrequency: 1.2\n        }\n      };\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        decls: 183,\n        vars: 18,\n        consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-text\"], [1, \"dashboard-title\"], [1, \"title-icon\"], [1, \"dashboard-subtitle\"], [1, \"header-stats\"], [1, \"quick-stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\", 1, \"action-button\", \"primary\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/reports\", 1, \"action-button\", \"secondary\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-scans\", \"elevated\"], [1, \"stat-card-header\"], [1, \"stat-icon-wrapper\", \"success\"], [1, \"stat-icon\"], [1, \"stat-trend\"], [1, \"trend-icon\", \"positive\"], [1, \"trend-value\"], [1, \"stat-card-content\"], [1, \"stat-number\"], [1, \"stat-description\"], [1, \"stat-card-footer\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [1, \"stat-card\", \"critical-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"critical\"], [1, \"trend-icon\", \"negative\"], [1, \"stat-number\", \"critical\"], [\"mat-button\", \"\", \"color\", \"warn\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"high-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"warning\"], [1, \"trend-icon\", \"neutral\"], [1, \"stat-number\", \"warning\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"medium-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"info\"], [1, \"stat-number\", \"info\"], [\"mat-button\", \"\", \"routerLink\", \"/checklist\"], [1, \"chart-section\"], [\"title\", \"Security Issues Distribution\", 3, \"data\", \"chartType\", \"showTrends\", \"animated\"], [\"class\", \"chart-section\", 4, \"ngIf\"], [3, \"trendsData\", \"showComparison\", \"autoRefresh\"], [1, \"recent-scans-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"scans-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"project\"], [\"matColumnDef\", \"chains\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [1, \"quick-actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/checklist\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/reports\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/settings\"], [3, \"scanProgress\", \"showRealTimeUpdates\", \"updateInterval\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"scan-id\"], [1, \"project-info\"], [1, \"project-name\"], [1, \"project-path\"], [1, \"chains-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"status-badge\"], [1, \"issues-summary\"], [\"class\", \"issue-count critical\", 4, \"ngIf\"], [\"class\", \"issue-count high\", 4, \"ngIf\"], [\"class\", \"issue-count medium\", 4, \"ngIf\"], [\"class\", \"no-issues\", 4, \"ngIf\"], [1, \"issue-count\", \"critical\"], [1, \"issue-count\", \"high\"], [1, \"issue-count\", \"medium\"], [1, \"no-issues\"], [1, \"date-info\"], [1, \"date\"], [1, \"time\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Report\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Download Report\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Progress\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Download Report\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Progress\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4)(5, \"mat-icon\", 5);\n            i0.ɵɵtext(6, \"dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(7, \" Security Dashboard \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 6);\n            i0.ɵɵtext(9, \"Monitor your blockchain security posture in real-time\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"span\", 9);\n            i0.ɵɵtext(13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"span\", 10);\n            i0.ɵɵtext(15, \"Total Scans\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 8)(17, \"span\", 9);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"span\", 10);\n            i0.ɵɵtext(20, \"Active\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 8)(22, \"span\", 9);\n            i0.ɵɵtext(23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"span\", 10);\n            i0.ɵɵtext(25, \"Critical\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(26, \"div\", 11)(27, \"button\", 12)(28, \"mat-icon\");\n            i0.ɵɵtext(29, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30, \" Start New Scan \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"button\", 13)(32, \"mat-icon\");\n            i0.ɵɵtext(33, \"assessment\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(34, \" View Reports \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 14)(36, \"mat-card\", 15)(37, \"div\", 16)(38, \"div\", 17)(39, \"mat-icon\", 18);\n            i0.ɵɵtext(40, \"security\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 19)(42, \"mat-icon\", 20);\n            i0.ɵɵtext(43, \"trending_up\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"span\", 21);\n            i0.ɵɵtext(45, \"+12%\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(46, \"div\", 22)(47, \"div\", 23);\n            i0.ɵɵtext(48);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 10);\n            i0.ɵɵtext(50, \"Total Scans\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 24);\n            i0.ɵɵtext(52, \"Completed this month\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"div\", 25)(54, \"button\", 26)(55, \"mat-icon\");\n            i0.ɵɵtext(56, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(57, \" New Scan \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(58, \"mat-card\", 27)(59, \"div\", 16)(60, \"div\", 28)(61, \"mat-icon\", 18);\n            i0.ɵɵtext(62, \"error\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 19)(64, \"mat-icon\", 29);\n            i0.ɵɵtext(65, \"trending_up\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"span\", 21);\n            i0.ɵɵtext(67, \"+3\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(68, \"div\", 22)(69, \"div\", 30);\n            i0.ɵɵtext(70);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"div\", 10);\n            i0.ɵɵtext(72, \"Critical Issues\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"div\", 24);\n            i0.ɵɵtext(74, \"Require immediate attention\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(75, \"div\", 25)(76, \"button\", 31)(77, \"mat-icon\");\n            i0.ɵɵtext(78, \"priority_high\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(79, \" View Details \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(80, \"mat-card\", 32)(81, \"div\", 16)(82, \"div\", 33)(83, \"mat-icon\", 18);\n            i0.ɵɵtext(84, \"warning\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(85, \"div\", 19)(86, \"mat-icon\", 34);\n            i0.ɵɵtext(87, \"trending_flat\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"span\", 21);\n            i0.ɵɵtext(89, \"0\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(90, \"div\", 22)(91, \"div\", 35);\n            i0.ɵɵtext(92);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"div\", 10);\n            i0.ɵɵtext(94, \"High Priority\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"div\", 24);\n            i0.ɵɵtext(96, \"Should be addressed soon\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(97, \"div\", 25)(98, \"button\", 36)(99, \"mat-icon\");\n            i0.ɵɵtext(100, \"visibility\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(101, \" Review \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(102, \"mat-card\", 37)(103, \"div\", 16)(104, \"div\", 38)(105, \"mat-icon\", 18);\n            i0.ɵɵtext(106, \"info\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(107, \"div\", 19)(108, \"mat-icon\", 20);\n            i0.ɵɵtext(109, \"trending_down\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"span\", 21);\n            i0.ɵɵtext(111, \"-5\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(112, \"div\", 22)(113, \"div\", 39);\n            i0.ɵɵtext(114);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"div\", 10);\n            i0.ɵɵtext(116, \"Medium Priority\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(117, \"div\", 24);\n            i0.ɵɵtext(118, \"Monitor and plan fixes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(119, \"div\", 25)(120, \"button\", 40)(121, \"mat-icon\");\n            i0.ɵɵtext(122, \"checklist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(123, \" Checklist \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(124, \"div\", 41);\n            i0.ɵɵelement(125, \"app-security-metrics-chart\", 42);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(126, DashboardComponent_div_126_Template, 2, 3, \"div\", 43);\n            i0.ɵɵelementStart(127, \"div\", 41);\n            i0.ɵɵelement(128, \"app-security-trends-dashboard\", 44);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(129, \"mat-card\", 45)(130, \"mat-card-header\")(131, \"mat-card-title\");\n            i0.ɵɵtext(132, \"Recent Scans\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(133, \"mat-card-subtitle\");\n            i0.ɵɵtext(134, \"Latest security scan results\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(135, \"mat-card-content\")(136, \"div\", 46)(137, \"table\", 47);\n            i0.ɵɵelementContainerStart(138, 48);\n            i0.ɵɵtemplate(139, DashboardComponent_th_139_Template, 2, 0, \"th\", 49)(140, DashboardComponent_td_140_Template, 3, 1, \"td\", 50);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(141, 51);\n            i0.ɵɵtemplate(142, DashboardComponent_th_142_Template, 2, 0, \"th\", 49)(143, DashboardComponent_td_143_Template, 6, 2, \"td\", 50);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(144, 52);\n            i0.ɵɵtemplate(145, DashboardComponent_th_145_Template, 2, 0, \"th\", 49)(146, DashboardComponent_td_146_Template, 4, 1, \"td\", 50);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(147, 53);\n            i0.ɵɵtemplate(148, DashboardComponent_th_148_Template, 2, 0, \"th\", 49)(149, DashboardComponent_td_149_Template, 7, 6, \"td\", 50);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(150, 54);\n            i0.ɵɵtemplate(151, DashboardComponent_th_151_Template, 2, 0, \"th\", 49)(152, DashboardComponent_td_152_Template, 6, 4, \"td\", 50);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(153, 55);\n            i0.ɵɵtemplate(154, DashboardComponent_th_154_Template, 2, 0, \"th\", 49)(155, DashboardComponent_td_155_Template, 8, 8, \"td\", 50);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(156, 56);\n            i0.ɵɵtemplate(157, DashboardComponent_th_157_Template, 2, 0, \"th\", 49)(158, DashboardComponent_td_158_Template, 7, 5, \"td\", 50);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(159, DashboardComponent_tr_159_Template, 1, 0, \"tr\", 57)(160, DashboardComponent_tr_160_Template, 1, 0, \"tr\", 58);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(161, \"mat-card\", 59)(162, \"mat-card-header\")(163, \"mat-card-title\");\n            i0.ɵɵtext(164, \"Quick Actions\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(165, \"mat-card-content\")(166, \"div\", 60)(167, \"button\", 61)(168, \"mat-icon\");\n            i0.ɵɵtext(169, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(170, \" Start New Scan \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(171, \"button\", 62)(172, \"mat-icon\");\n            i0.ɵɵtext(173, \"checklist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(174, \" Security Checklist \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(175, \"button\", 63)(176, \"mat-icon\");\n            i0.ɵɵtext(177, \"assessment\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(178, \" Generate Report \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(179, \"button\", 64)(180, \"mat-icon\");\n            i0.ɵɵtext(181, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(182, \" Settings \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(13);\n            i0.ɵɵtextInterpolate(ctx.totalScans);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.activeScans);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.criticalIssues);\n            i0.ɵɵadvance(25);\n            i0.ɵɵtextInterpolate(ctx.stats.totalScans);\n            i0.ɵɵadvance(22);\n            i0.ɵɵtextInterpolate(ctx.stats.criticalIssues);\n            i0.ɵɵadvance(22);\n            i0.ɵɵtextInterpolate(ctx.stats.highIssues);\n            i0.ɵɵadvance(22);\n            i0.ɵɵtextInterpolate(ctx.stats.mediumIssues);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"data\", ctx.getSecurityMetricsData())(\"chartType\", \"doughnut\")(\"showTrends\", true)(\"animated\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getActiveScan());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"trendsData\", ctx.getSecurityTrendsData())(\"showComparison\", true)(\"autoRefresh\", false);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"dataSource\", ctx.recentScans);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, i2.DatePipe, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, i5.MatIconButton, MatTableModule, i6.MatTable, i6.MatHeaderCellDef, i6.MatHeaderRowDef, i6.MatColumnDef, i6.MatCellDef, i6.MatRowDef, i6.MatHeaderCell, i6.MatCell, i6.MatHeaderRow, i6.MatRow, MatChipsModule, i7.MatChip, i7.MatChipSet, MatProgressBarModule, MatTooltipModule, i8.MatTooltip, RouterModule, i9.RouterLink, SecurityMetricsChartComponent, ScanProgressChartComponent, SecurityTrendsDashboardComponent],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{padding:0;max-width:100%;margin:0;background:var(--spt-bg-secondary);min-height:100vh}.dashboard-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-primary-600) 0%,var(--spt-secondary-600) 100%);color:#fff;padding:var(--spt-space-12) var(--spt-space-8);display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:var(--spt-space-8);border-radius:0 0 var(--spt-radius-3xl) var(--spt-radius-3xl);position:relative;overflow:hidden}.dashboard-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');opacity:.3}.header-content[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-12);align-items:flex-start;position:relative;z-index:1}.header-text[_ngcontent-%COMP%]{flex:1}.dashboard-title[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-2) 0;font-size:var(--spt-text-4xl);font-weight:var(--spt-font-bold);display:flex;align-items:center;gap:var(--spt-space-3);line-height:var(--spt-leading-tight)}.title-icon[_ngcontent-%COMP%]{font-size:40px;width:40px;height:40px;background:#fff3;border-radius:var(--spt-radius-xl);display:flex;align-items:center;justify-content:center}.dashboard-subtitle[_ngcontent-%COMP%]{margin:0;opacity:.9;font-size:var(--spt-text-lg);font-weight:var(--spt-font-normal);line-height:var(--spt-leading-relaxed)}.header-stats[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-6);margin-top:var(--spt-space-2)}.quick-stat[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:var(--spt-space-3) var(--spt-space-4);background:#ffffff26;border-radius:var(--spt-radius-xl);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.stat-value[_ngcontent-%COMP%]{font-size:var(--spt-text-2xl);font-weight:var(--spt-font-bold);line-height:1}.stat-label[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);opacity:.8;text-transform:uppercase;letter-spacing:.05em;margin-top:var(--spt-space-1)}.header-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-3);position:relative;z-index:1}.action-button[_ngcontent-%COMP%]{height:48px;padding:0 var(--spt-space-6);border-radius:var(--spt-radius-xl);font-weight:var(--spt-font-semibold);transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.3)}.action-button.primary[_ngcontent-%COMP%]{background:#fff3;color:#fff}.action-button.primary[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:var(--spt-shadow-lg)}.action-button.secondary[_ngcontent-%COMP%]{background:#ffffff1a;color:#fff}.action-button.secondary[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-1px)}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(320px,1fr));gap:var(--spt-space-6);margin:0 var(--spt-space-8) var(--spt-space-12) var(--spt-space-8)}.stat-card[_ngcontent-%COMP%]{border-radius:var(--spt-radius-2xl);border:1px solid var(--spt-border);background:var(--spt-surface);box-shadow:var(--spt-shadow-sm);transition:all .3s cubic-bezier(.4,0,.2,1);overflow:hidden;position:relative}.stat-card.elevated[_ngcontent-%COMP%]{box-shadow:var(--spt-shadow-lg)}.stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:var(--spt-shadow-xl);border-color:var(--spt-primary-200)}.stat-card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:var(--spt-space-5) var(--spt-space-6) var(--spt-space-3) var(--spt-space-6)}.stat-card-content[_ngcontent-%COMP%]{padding:0 var(--spt-space-6) var(--spt-space-4) var(--spt-space-6)}.stat-card-footer[_ngcontent-%COMP%]{padding:var(--spt-space-3) var(--spt-space-6) var(--spt-space-5) var(--spt-space-6);border-top:1px solid var(--spt-border-light);background:var(--spt-bg-secondary)}.stat-icon-wrapper[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:var(--spt-radius-xl);display:flex;align-items:center;justify-content:center;position:relative;transition:all .3s ease}.stat-icon-wrapper.success[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-success-100) 0%,var(--spt-success-200) 100%);border:1px solid var(--spt-success-300)}.stat-icon-wrapper.critical[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-error-100) 0%,var(--spt-error-200) 100%);border:1px solid var(--spt-error-300)}.stat-icon-wrapper.warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-warning-100) 0%,var(--spt-warning-200) 100%);border:1px solid var(--spt-warning-300)}.stat-icon-wrapper.info[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-info-100) 0%,var(--spt-info-200) 100%);border:1px solid var(--spt-info-300)}.stat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;transition:transform .3s ease}.stat-card[_ngcontent-%COMP%]:hover   .stat-icon[_ngcontent-%COMP%]{transform:scale(1.1)}.stat-icon-wrapper.success[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-success-700)}.stat-icon-wrapper.critical[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-error-700)}.stat-icon-wrapper.warning[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-warning-700)}.stat-icon-wrapper.info[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-info-700)}.stat-trend[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-1);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-lg);font-size:var(--spt-text-xs);font-weight:var(--spt-font-semibold)}.trend-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.trend-icon.positive[_ngcontent-%COMP%]{color:var(--spt-success-600)}.trend-icon.negative[_ngcontent-%COMP%]{color:var(--spt-error-600)}.trend-icon.neutral[_ngcontent-%COMP%]{color:var(--spt-text-tertiary)}.trend-value[_ngcontent-%COMP%]{font-weight:var(--spt-font-bold)}.stat-number[_ngcontent-%COMP%]{font-size:var(--spt-text-4xl);font-weight:var(--spt-font-bold);color:var(--spt-text-primary);margin-bottom:var(--spt-space-1);line-height:var(--spt-leading-none);transition:color .3s ease}.stat-number.critical[_ngcontent-%COMP%]{color:var(--spt-error-600)}.stat-number.warning[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.stat-number.info[_ngcontent-%COMP%]{color:var(--spt-info-600)}.stat-label[_ngcontent-%COMP%]{font-size:var(--spt-text-base);color:var(--spt-text-primary);font-weight:var(--spt-font-semibold);margin-bottom:var(--spt-space-1);line-height:var(--spt-leading-tight)}.stat-description[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-text-secondary);font-weight:var(--spt-font-normal);line-height:var(--spt-leading-relaxed)}.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:flex-start;gap:var(--spt-space-2);padding:var(--spt-space-2) var(--spt-space-3);border-radius:var(--spt-radius-lg);font-weight:var(--spt-font-medium);transition:all .2s ease}.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translate(4px)}.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.chart-section[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-8)}.chart-section[_ngcontent-%COMP%]:last-of-type{margin-bottom:var(--spt-space-6)}.recent-scans-card[_ngcontent-%COMP%]{margin:0 var(--spt-space-8) var(--spt-space-8) var(--spt-space-8);border-radius:var(--spt-radius-2xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm);overflow:hidden}.table-container[_ngcontent-%COMP%]{overflow-x:auto;max-width:100%}.scans-table[_ngcontent-%COMP%]{width:100%;background:var(--spt-surface)}.scan-id[_ngcontent-%COMP%]{font-family:var(--spt-font-mono);font-size:var(--spt-text-sm);color:var(--spt-text-secondary);background:var(--spt-gray-100);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-md)}.project-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-1)}.project-name[_ngcontent-%COMP%]{font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.project-path[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-secondary);font-family:var(--spt-font-mono)}.chains-list[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:var(--spt-space-1)}.status-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);padding:var(--spt-space-2) var(--spt-space-3);border-radius:var(--spt-radius-lg);font-size:var(--spt-text-sm);font-weight:var(--spt-font-medium);text-transform:capitalize}.status-badge.status-completed[_ngcontent-%COMP%]{background:var(--spt-success-100);color:var(--spt-success-700)}.status-badge.status-running[_ngcontent-%COMP%]{background:var(--spt-info-100);color:var(--spt-info-700)}.status-badge.status-failed[_ngcontent-%COMP%]{background:var(--spt-error-100);color:var(--spt-error-700)}.status-badge.status-pending[_ngcontent-%COMP%]{background:var(--spt-warning-100);color:var(--spt-warning-700)}.issues-summary[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-1)}.issue-count[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);font-weight:var(--spt-font-semibold);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-md)}.issue-count.critical[_ngcontent-%COMP%]{background:var(--spt-error-100);color:var(--spt-error-700)}.issue-count.high[_ngcontent-%COMP%]{background:var(--spt-warning-100);color:var(--spt-warning-700)}.issue-count.medium[_ngcontent-%COMP%]{background:var(--spt-info-100);color:var(--spt-info-700)}.no-issues[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-secondary);font-style:italic}.date-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-1)}.date[_ngcontent-%COMP%]{font-weight:var(--spt-font-medium);color:var(--spt-text-primary)}.time[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-secondary)}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-1)}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:var(--spt-radius-lg);color:var(--spt-text-secondary);transition:all .2s ease}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:var(--spt-primary-50);color:var(--spt-primary-600);transform:scale(1.1)}.quick-actions-card[_ngcontent-%COMP%]{margin:0 var(--spt-space-8) var(--spt-space-8) var(--spt-space-8);border-radius:var(--spt-radius-2xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm)}.actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:var(--spt-space-4)}.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{height:64px;border-radius:var(--spt-radius-xl);font-weight:var(--spt-font-semibold);display:flex;align-items:center;justify-content:center;gap:var(--spt-space-3);transition:all .3s ease}.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:var(--spt-shadow-lg)}.actions-grid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}@media (max-width: 768px){.chart-section[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-6)}}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}