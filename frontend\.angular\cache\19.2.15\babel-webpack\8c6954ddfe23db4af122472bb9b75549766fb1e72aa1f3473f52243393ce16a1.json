{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/list\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nconst _c0 = a0 => [\"/doc\", a0];\nfunction DocumentationNavComponent_mat_list_item_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-list-item\", 16)(1, \"div\", 17)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, item_r1.route));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.description);\n  }\n}\nexport class DocumentationNavComponent {\n  constructor() {\n    this.navItems = [{\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    }, {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    }, {\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    }, {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    }, {\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    }, {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    }, {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }];\n  }\n  static {\n    this.ɵfac = function DocumentationNavComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DocumentationNavComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DocumentationNavComponent,\n      selectors: [[\"app-documentation-nav\"]],\n      decls: 29,\n      vars: 1,\n      consts: [[1, \"nav-container\"], [1, \"nav-header\"], [1, \"nav-header-icon\"], [1, \"nav-header-text\"], [1, \"nav-subtitle\"], [1, \"nav-list\"], [\"routerLinkActive\", \"active-nav-item\", \"class\", \"nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-divider\"], [1, \"nav-footer\"], [1, \"version-info\"], [1, \"version-icon\"], [1, \"version-text\"], [1, \"version-number\"], [1, \"version-status\"], [1, \"quick-links\"], [\"href\", \"https://github.com/blockchain-spt\", \"target\", \"_blank\", \"mat-button\", \"\", 1, \"github-link\"], [\"routerLinkActive\", \"active-nav-item\", 1, \"nav-item\", 3, \"routerLink\"], [\"matListItemIcon\", \"\", 1, \"nav-item-icon\"], [\"matListItemTitle\", \"\", 1, \"nav-item-title\"], [\"matListItemLine\", \"\", 1, \"nav-description\"]],\n      template: function DocumentationNavComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"menu_book\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"h3\");\n          i0.ɵɵtext(7, \"Documentation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 4);\n          i0.ɵɵtext(9, \"Comprehensive guide to SPT\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"mat-nav-list\", 5);\n          i0.ɵɵtemplate(11, DocumentationNavComponent_mat_list_item_11_Template, 8, 6, \"mat-list-item\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"div\", 7);\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"div\", 10)(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"verified\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 11)(19, \"span\", 12);\n          i0.ɵɵtext(20, \"SPT v1.0.0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\", 13);\n          i0.ɵɵtext(22, \"Stable Release\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"a\", 15)(25, \"mat-icon\");\n          i0.ɵɵtext(26, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\");\n          i0.ɵɵtext(28, \"GitHub\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.navItems);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, MatListModule, i3.MatNavList, i3.MatListItem, i3.MatListItemIcon, i3.MatListItemLine, i3.MatListItemTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatAnchor, MatDividerModule],\n      styles: [\".nav-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 32px 0;\\n}\\n\\n.nav-header[_ngcontent-%COMP%] {\\n  padding: 0 32px 32px;\\n  margin-bottom: 24px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  position: relative;\\n}\\n\\n.nav-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 32px;\\n  right: 32px;\\n  height: 1px;\\n  background: #e2e8f0;\\n}\\n\\n.nav-header-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  padding: 10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n\\n.nav-header-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.nav-header-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #1a202c;\\n  font-weight: 700;\\n  font-size: 1.25em;\\n  letter-spacing: -0.5px;\\n}\\n\\n.nav-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 0.875em;\\n  font-weight: 500;\\n}\\n\\n.nav-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0 12px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  margin: 6px 0;\\n  border-radius: 16px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\\n  border-left: 3px solid #667eea;\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\\n}\\n\\n.nav-item-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n  border-radius: 10px;\\n  padding: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 16px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-item-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\\n}\\n\\n.nav-item-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.nav-item-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.95em;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-item-title[_ngcontent-%COMP%] {\\n  color: #4c63d2;\\n}\\n\\n.nav-description[_ngcontent-%COMP%] {\\n  font-size: 0.8em;\\n  color: #8892b0;\\n  margin-top: 2px;\\n  line-height: 1.3;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-description[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.nav-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent 0%, #e8eaff 20%, #d1d9ff 50%, #e8eaff 80%, transparent 100%);\\n  margin: 20px 24px;\\n}\\n\\n.nav-footer[_ngcontent-%COMP%] {\\n  padding: 20px 24px 0;\\n}\\n\\n.version-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding: 12px;\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);\\n  border-radius: 12px;\\n  border: 1px solid #e8eaff;\\n}\\n\\n.version-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n  border-radius: 8px;\\n  padding: 6px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.version-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.version-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n\\n.version-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4c63d2;\\n  font-size: 0.9em;\\n}\\n\\n.version-status[_ngcontent-%COMP%] {\\n  font-size: 0.75em;\\n  color: #8892b0;\\n  font-weight: 500;\\n}\\n\\n.quick-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.github-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #24292e 0%, #1a1e22 100%);\\n  color: white;\\n  border-radius: 10px;\\n  padding: 8px 16px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  border: none;\\n  width: 100%;\\n  justify-content: flex-start;\\n}\\n\\n.github-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #2f363d 0%, #24292e 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(36, 41, 46, 0.3);\\n}\\n\\n.github-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  margin-right: 8px;\\n}\\n\\n.github-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.9em;\\n}\\n\\n@media (max-width: 768px) {\\n  .nav-container[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .nav-header[_ngcontent-%COMP%] {\\n    padding: 0 16px 16px;\\n    margin-bottom: 16px;\\n    gap: 12px;\\n  }\\n  .nav-header[_ngcontent-%COMP%]::after {\\n    left: 16px;\\n    right: 16px;\\n  }\\n  .nav-header-icon[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .nav-header-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    width: 20px;\\n    height: 20px;\\n  }\\n  .nav-list[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n  }\\n  .nav-divider[_ngcontent-%COMP%] {\\n    margin: 16px 16px;\\n  }\\n  .nav-footer[_ngcontent-%COMP%] {\\n    padding: 16px 16px 0;\\n  }\\n  .version-info[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9kb2N1bWVudGF0aW9uL2NvbXBvbmVudHMvZG9jdW1lbnRhdGlvbi1uYXYvZG9jdW1lbnRhdGlvbi1uYXYuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSTtFQUNFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0Esa0JBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLCtDQUFBO0FBQU47O0FBR0k7RUFDRSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esc0JBQUE7QUFBTjs7QUFHSTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsT0FBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGlEQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUtJO0VBQ0UsMEJBQUE7RUFDQSxnREFBQTtBQUZOOztBQUtJO0VBQ0UsOEZBQUE7RUFDQSw4QkFBQTtFQUNBLDBCQUFBO0VBQ0EsK0NBQUE7QUFGTjs7QUFLSTtFQUNFLDZEQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtBQUZOOztBQUtJO0VBQ0UsNkRBQUE7RUFDQSw4Q0FBQTtBQUZOOztBQUtJO0VBQ0UsWUFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtBQUZOOztBQUtJO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7QUFGTjs7QUFLSTtFQUNFLGNBQUE7QUFGTjs7QUFLSTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtBQUZOOztBQUtJO0VBQ0UsY0FBQTtBQUZOOztBQUtJO0VBQ0UsV0FBQTtFQUNBLDJHQUFBO0VBQ0EsaUJBQUE7QUFGTjs7QUFLSTtFQUNFLG9CQUFBO0FBRk47O0FBS0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0FBRk47O0FBS0k7RUFDRSw2REFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBRk47O0FBS0k7RUFDRSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBRk47O0FBS0k7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxRQUFBO0FBRk47O0FBS0k7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQUZOOztBQUtJO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUFGTjs7QUFLSTtFQUNFLGFBQUE7RUFDQSxRQUFBO0FBRk47O0FBS0k7RUFDRSw2REFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0EsMkJBQUE7QUFGTjs7QUFLSTtFQUNFLDZEQUFBO0VBQ0EsMkJBQUE7RUFDQSw0Q0FBQTtBQUZOOztBQUtJO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7QUFGTjs7QUFLSTtFQUNFLGdCQUFBO0FBRk47O0FBS0k7RUFDRTtJQUNFLGVBQUE7RUFGTjtFQUtJO0lBQ0Usb0JBQUE7SUFDQSxtQkFBQTtJQUNBLFNBQUE7RUFITjtFQU1JO0lBQ0UsVUFBQTtJQUNBLFdBQUE7RUFKTjtFQU9JO0lBQ0UsWUFBQTtFQUxOO0VBUUk7SUFDRSxlQUFBO0lBQ0EsV0FBQTtJQUNBLFlBQUE7RUFOTjtFQVNJO0lBQ0UsY0FBQTtFQVBOO0VBVUk7SUFDRSxpQkFBQTtFQVJOO0VBV0k7SUFDRSxvQkFBQTtFQVROO0VBWUk7SUFDRSxhQUFBO0VBVk47QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5uYXYtY29udGFpbmVyIHtcbiAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgcGFkZGluZzogMzJweCAwO1xuICAgIH1cblxuICAgIC5uYXYtaGVhZGVyIHtcbiAgICAgIHBhZGRpbmc6IDAgMzJweCAzMnB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIH1cblxuICAgIC5uYXYtaGVhZGVyOjphZnRlciB7XG4gICAgICBjb250ZW50OiAnJztcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIGJvdHRvbTogMDtcbiAgICAgIGxlZnQ6IDMycHg7XG4gICAgICByaWdodDogMzJweDtcbiAgICAgIGhlaWdodDogMXB4O1xuICAgICAgYmFja2dyb3VuZDogI2UyZThmMDtcbiAgICB9XG5cbiAgICAubmF2LWhlYWRlci1pY29uIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgcGFkZGluZzogMTBweDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKTtcbiAgICB9XG5cbiAgICAubmF2LWhlYWRlci1pY29uIG1hdC1pY29uIHtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICAgIHdpZHRoOiAyNHB4O1xuICAgICAgaGVpZ2h0OiAyNHB4O1xuICAgIH1cblxuICAgIC5uYXYtaGVhZGVyLXRleHQgaDMge1xuICAgICAgbWFyZ2luOiAwIDAgNHB4IDA7XG4gICAgICBjb2xvcjogIzFhMjAyYztcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICBmb250LXNpemU6IDEuMjVlbTtcbiAgICAgIGxldHRlci1zcGFjaW5nOiAtMC41cHg7XG4gICAgfVxuXG4gICAgLm5hdi1zdWJ0aXRsZSB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBjb2xvcjogIzY0NzQ4YjtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuXG4gICAgLm5hdi1saXN0IHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBwYWRkaW5nOiAwIDEycHg7XG4gICAgfVxuXG4gICAgLm5hdi1pdGVtIHtcbiAgICAgIG1hcmdpbjogNnB4IDA7XG4gICAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgfVxuXG5cblxuICAgIC5uYXYtaXRlbTpob3ZlciB7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoNHB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjE1KTtcbiAgICB9XG5cbiAgICAubmF2LWl0ZW0uYWN0aXZlLW5hdi1pdGVtIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKSAwJSwgcmdiYSgxMTgsIDc1LCAxNjIsIDAuMSkgMTAwJSk7XG4gICAgICBib3JkZXItbGVmdDogM3B4IHNvbGlkICM2NjdlZWE7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoNHB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjIpO1xuICAgIH1cblxuICAgIC5uYXYtaXRlbS1pY29uIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMDkzZmIgMCUsICNmNTU3NmMgMTAwJSk7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgICAgcGFkZGluZzogOHB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIG1hcmdpbi1yaWdodDogMTZweDtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgIHotaW5kZXg6IDE7XG4gICAgfVxuXG4gICAgLm5hdi1pdGVtLmFjdGl2ZS1uYXYtaXRlbSAubmF2LWl0ZW0taWNvbiB7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpO1xuICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKTtcbiAgICB9XG5cbiAgICAubmF2LWl0ZW0taWNvbiBtYXQtaWNvbiB7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBmb250LXNpemU6IDIwcHg7XG4gICAgICB3aWR0aDogMjBweDtcbiAgICAgIGhlaWdodDogMjBweDtcbiAgICB9XG5cbiAgICAubmF2LWl0ZW0tdGl0bGUge1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGNvbG9yOiAjMmQzNzQ4O1xuICAgICAgZm9udC1zaXplOiAwLjk1ZW07XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICB6LWluZGV4OiAxO1xuICAgIH1cblxuICAgIC5uYXYtaXRlbS5hY3RpdmUtbmF2LWl0ZW0gLm5hdi1pdGVtLXRpdGxlIHtcbiAgICAgIGNvbG9yOiAjNGM2M2QyO1xuICAgIH1cblxuICAgIC5uYXYtZGVzY3JpcHRpb24ge1xuICAgICAgZm9udC1zaXplOiAwLjhlbTtcbiAgICAgIGNvbG9yOiAjODg5MmIwO1xuICAgICAgbWFyZ2luLXRvcDogMnB4O1xuICAgICAgbGluZS1oZWlnaHQ6IDEuMztcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgIHotaW5kZXg6IDE7XG4gICAgfVxuXG4gICAgLm5hdi1pdGVtLmFjdGl2ZS1uYXYtaXRlbSAubmF2LWRlc2NyaXB0aW9uIHtcbiAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgIH1cblxuICAgIC5uYXYtZGl2aWRlciB7XG4gICAgICBoZWlnaHQ6IDFweDtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQgMCUsICNlOGVhZmYgMjAlLCAjZDFkOWZmIDUwJSwgI2U4ZWFmZiA4MCUsIHRyYW5zcGFyZW50IDEwMCUpO1xuICAgICAgbWFyZ2luOiAyMHB4IDI0cHg7XG4gICAgfVxuXG4gICAgLm5hdi1mb290ZXIge1xuICAgICAgcGFkZGluZzogMjBweCAyNHB4IDA7XG4gICAgfVxuXG4gICAgLnZlcnNpb24taW5mbyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTJweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gICAgICBwYWRkaW5nOiAxMnB4O1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmZiAwJSwgI2U4ZWFmZiAxMDAlKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZThlYWZmO1xuICAgIH1cblxuICAgIC52ZXJzaW9uLWljb24ge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRmYWNmZSAwJSwgIzAwZjJmZSAxMDAlKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIHBhZGRpbmc6IDZweDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgfVxuXG4gICAgLnZlcnNpb24taWNvbiBtYXQtaWNvbiB7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICB3aWR0aDogMTZweDtcbiAgICAgIGhlaWdodDogMTZweDtcbiAgICB9XG5cbiAgICAudmVyc2lvbi10ZXh0IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgZ2FwOiAycHg7XG4gICAgfVxuXG4gICAgLnZlcnNpb24tbnVtYmVyIHtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjb2xvcjogIzRjNjNkMjtcbiAgICAgIGZvbnQtc2l6ZTogMC45ZW07XG4gICAgfVxuXG4gICAgLnZlcnNpb24tc3RhdHVzIHtcbiAgICAgIGZvbnQtc2l6ZTogMC43NWVtO1xuICAgICAgY29sb3I6ICM4ODkyYjA7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgIH1cblxuICAgIC5xdWljay1saW5rcyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiA4cHg7XG4gICAgfVxuXG4gICAgLmdpdGh1Yi1saW5rIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyNDI5MmUgMCUsICMxYTFlMjIgMTAwJSk7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgICAgcGFkZGluZzogOHB4IDE2cHg7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0O1xuICAgIH1cblxuICAgIC5naXRodWItbGluazpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMmYzNjNkIDAlLCAjMjQyOTJlIDEwMCUpO1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDM2LCA0MSwgNDYsIDAuMyk7XG4gICAgfVxuXG4gICAgLmdpdGh1Yi1saW5rIG1hdC1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgIHdpZHRoOiAxNnB4O1xuICAgICAgaGVpZ2h0OiAxNnB4O1xuICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gICAgfVxuXG4gICAgLmdpdGh1Yi1saW5rIHNwYW4ge1xuICAgICAgZm9udC1zaXplOiAwLjllbTtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIC5uYXYtY29udGFpbmVyIHtcbiAgICAgICAgcGFkZGluZzogMTZweCAwO1xuICAgICAgfVxuXG4gICAgICAubmF2LWhlYWRlciB7XG4gICAgICAgIHBhZGRpbmc6IDAgMTZweCAxNnB4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICAgICAgICBnYXA6IDEycHg7XG4gICAgICB9XG5cbiAgICAgIC5uYXYtaGVhZGVyOjphZnRlciB7XG4gICAgICAgIGxlZnQ6IDE2cHg7XG4gICAgICAgIHJpZ2h0OiAxNnB4O1xuICAgICAgfVxuXG4gICAgICAubmF2LWhlYWRlci1pY29uIHtcbiAgICAgICAgcGFkZGluZzogOHB4O1xuICAgICAgfVxuXG4gICAgICAubmF2LWhlYWRlci1pY29uIG1hdC1pY29uIHtcbiAgICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgICB3aWR0aDogMjBweDtcbiAgICAgICAgaGVpZ2h0OiAyMHB4O1xuICAgICAgfVxuXG4gICAgICAubmF2LWxpc3Qge1xuICAgICAgICBwYWRkaW5nOiAwIDhweDtcbiAgICAgIH1cblxuICAgICAgLm5hdi1kaXZpZGVyIHtcbiAgICAgICAgbWFyZ2luOiAxNnB4IDE2cHg7XG4gICAgICB9XG5cbiAgICAgIC5uYXYtZm9vdGVyIHtcbiAgICAgICAgcGFkZGluZzogMTZweCAxNnB4IDA7XG4gICAgICB9XG5cbiAgICAgIC52ZXJzaW9uLWluZm8ge1xuICAgICAgICBwYWRkaW5nOiAxMHB4O1xuICAgICAgfVxuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatListModule", "MatIconModule", "MatButtonModule", "MatDividerModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "item_r1", "route", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "label", "description", "DocumentationNavComponent", "constructor", "navItems", "selectors", "decls", "vars", "consts", "template", "DocumentationNavComponent_Template", "rf", "ctx", "ɵɵtemplate", "DocumentationNavComponent_mat_list_item_11_Template", "ɵɵelement", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "RouterLink", "RouterLinkActive", "i3", "MatNavList", "MatListItem", "MatListItemIcon", "MatListItemLine", "MatListItemTitle", "i4", "MatIcon", "i5", "<PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\documentation-nav\\documentation-nav.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDividerModule } from '@angular/material/divider';\n\ninterface NavItem {\n  label: string;\n  route: string;\n  icon: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-documentation-nav',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatDividerModule\n  ],\n  template: `\n    <div class=\"nav-container\">\n      <div class=\"nav-header\">\n        <div class=\"nav-header-icon\">\n          <mat-icon>menu_book</mat-icon>\n        </div>\n        <div class=\"nav-header-text\">\n          <h3>Documentation</h3>\n          <p class=\"nav-subtitle\">Comprehensive guide to SPT</p>\n        </div>\n      </div>\n\n      <mat-nav-list class=\"nav-list\">\n        <mat-list-item\n          *ngFor=\"let item of navItems\"\n          [routerLink]=\"['/doc', item.route]\"\n          routerLinkActive=\"active-nav-item\"\n          class=\"nav-item\">\n          <div class=\"nav-item-icon\" matListItemIcon>\n            <mat-icon>{{ item.icon }}</mat-icon>\n          </div>\n          <div matListItemTitle class=\"nav-item-title\">{{ item.label }}</div>\n          <div matListItemLine class=\"nav-description\">{{ item.description }}</div>\n        </mat-list-item>\n      </mat-nav-list>\n\n      <div class=\"nav-divider\"></div>\n\n      <div class=\"nav-footer\">\n        <div class=\"version-info\">\n          <div class=\"version-icon\">\n            <mat-icon>verified</mat-icon>\n          </div>\n          <div class=\"version-text\">\n            <span class=\"version-number\">SPT v1.0.0</span>\n            <span class=\"version-status\">Stable Release</span>\n          </div>\n        </div>\n        <div class=\"quick-links\">\n          <a href=\"https://github.com/blockchain-spt\" target=\"_blank\" mat-button class=\"github-link\">\n            <mat-icon>code</mat-icon>\n            <span>GitHub</span>\n          </a>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .nav-container {\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      padding: 32px 0;\n    }\n\n    .nav-header {\n      padding: 0 32px 32px;\n      margin-bottom: 24px;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      position: relative;\n    }\n\n    .nav-header::after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 32px;\n      right: 32px;\n      height: 1px;\n      background: #e2e8f0;\n    }\n\n    .nav-header-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 12px;\n      padding: 10px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .nav-header-icon mat-icon {\n      color: white;\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .nav-header-text h3 {\n      margin: 0 0 4px 0;\n      color: #1a202c;\n      font-weight: 700;\n      font-size: 1.25em;\n      letter-spacing: -0.5px;\n    }\n\n    .nav-subtitle {\n      margin: 0;\n      color: #64748b;\n      font-size: 0.875em;\n      font-weight: 500;\n    }\n\n    .nav-list {\n      flex: 1;\n      padding: 0 12px;\n    }\n\n    .nav-item {\n      margin: 6px 0;\n      border-radius: 16px;\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      position: relative;\n      overflow: hidden;\n    }\n\n\n\n    .nav-item:hover {\n      transform: translateX(4px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);\n    }\n\n    .nav-item.active-nav-item {\n      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\n      border-left: 3px solid #667eea;\n      transform: translateX(4px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n    }\n\n    .nav-item-icon {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n      border-radius: 10px;\n      padding: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 16px;\n      position: relative;\n      z-index: 1;\n    }\n\n    .nav-item.active-nav-item .nav-item-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n    }\n\n    .nav-item-icon mat-icon {\n      color: white;\n      font-size: 20px;\n      width: 20px;\n      height: 20px;\n    }\n\n    .nav-item-title {\n      font-weight: 600;\n      color: #2d3748;\n      font-size: 0.95em;\n      position: relative;\n      z-index: 1;\n    }\n\n    .nav-item.active-nav-item .nav-item-title {\n      color: #4c63d2;\n    }\n\n    .nav-description {\n      font-size: 0.8em;\n      color: #8892b0;\n      margin-top: 2px;\n      line-height: 1.3;\n      position: relative;\n      z-index: 1;\n    }\n\n    .nav-item.active-nav-item .nav-description {\n      color: #667eea;\n    }\n\n    .nav-divider {\n      height: 1px;\n      background: linear-gradient(90deg, transparent 0%, #e8eaff 20%, #d1d9ff 50%, #e8eaff 80%, transparent 100%);\n      margin: 20px 24px;\n    }\n\n    .nav-footer {\n      padding: 20px 24px 0;\n    }\n\n    .version-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      margin-bottom: 16px;\n      padding: 12px;\n      background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);\n      border-radius: 12px;\n      border: 1px solid #e8eaff;\n    }\n\n    .version-icon {\n      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n      border-radius: 8px;\n      padding: 6px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .version-icon mat-icon {\n      color: white;\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    .version-text {\n      display: flex;\n      flex-direction: column;\n      gap: 2px;\n    }\n\n    .version-number {\n      font-weight: 600;\n      color: #4c63d2;\n      font-size: 0.9em;\n    }\n\n    .version-status {\n      font-size: 0.75em;\n      color: #8892b0;\n      font-weight: 500;\n    }\n\n    .quick-links {\n      display: flex;\n      gap: 8px;\n    }\n\n    .github-link {\n      background: linear-gradient(135deg, #24292e 0%, #1a1e22 100%);\n      color: white;\n      border-radius: 10px;\n      padding: 8px 16px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n      border: none;\n      width: 100%;\n      justify-content: flex-start;\n    }\n\n    .github-link:hover {\n      background: linear-gradient(135deg, #2f363d 0%, #24292e 100%);\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(36, 41, 46, 0.3);\n    }\n\n    .github-link mat-icon {\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n      margin-right: 8px;\n    }\n\n    .github-link span {\n      font-size: 0.9em;\n    }\n\n    @media (max-width: 768px) {\n      .nav-container {\n        padding: 16px 0;\n      }\n\n      .nav-header {\n        padding: 0 16px 16px;\n        margin-bottom: 16px;\n        gap: 12px;\n      }\n\n      .nav-header::after {\n        left: 16px;\n        right: 16px;\n      }\n\n      .nav-header-icon {\n        padding: 8px;\n      }\n\n      .nav-header-icon mat-icon {\n        font-size: 20px;\n        width: 20px;\n        height: 20px;\n      }\n\n      .nav-list {\n        padding: 0 8px;\n      }\n\n      .nav-divider {\n        margin: 16px 16px;\n      }\n\n      .nav-footer {\n        padding: 16px 16px 0;\n      }\n\n      .version-info {\n        padding: 10px;\n      }\n    }\n  `]\n})\nexport class DocumentationNavComponent {\n  navItems: NavItem[] = [\n    {\n      label: 'Overview',\n      route: 'overview',\n      icon: 'home',\n      description: 'Introduction to SPT'\n    },\n    {\n      label: 'Getting Started',\n      route: 'getting-started',\n      icon: 'play_arrow',\n      description: 'Installation and setup'\n    },\n    {\n      label: 'API Reference',\n      route: 'api-reference',\n      icon: 'api',\n      description: 'REST API endpoints'\n    },\n    {\n      label: 'Security Practices',\n      route: 'security-practices',\n      icon: 'security',\n      description: 'Best practices guide'\n    },\n    {\n      label: 'CLI Guide',\n      route: 'cli-guide',\n      icon: 'terminal',\n      description: 'Command line interface'\n    },\n    {\n      label: 'VS Code Extension',\n      route: 'vscode-extension',\n      icon: 'extension',\n      description: 'IDE integration'\n    },\n    {\n      label: 'Architecture',\n      route: 'architecture',\n      icon: 'account_tree',\n      description: 'System architecture'\n    }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;IAuChDC,EANJ,CAAAC,cAAA,wBAImB,cAC0B,eAC/B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IACNH,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnEH,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACrEF,EADqE,CAAAG,YAAA,EAAM,EAC3D;;;;IARdH,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,KAAA,EAAmC;IAIvBR,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAEkBX,EAAA,CAAAS,SAAA,GAAgB;IAAhBT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;IAChBZ,EAAA,CAAAS,SAAA,GAAsB;IAAtBT,EAAA,CAAAU,iBAAA,CAAAH,OAAA,CAAAM,WAAA,CAAsB;;;AAqS7E,OAAM,MAAOC,yBAAyB;EAtUtCC,YAAA;IAuUE,KAAAC,QAAQ,GAAc,CACpB;MACEJ,KAAK,EAAE,UAAU;MACjBJ,KAAK,EAAE,UAAU;MACjBG,IAAI,EAAE,MAAM;MACZE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBJ,KAAK,EAAE,iBAAiB;MACxBG,IAAI,EAAE,YAAY;MAClBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,eAAe;MACtBJ,KAAK,EAAE,eAAe;MACtBG,IAAI,EAAE,KAAK;MACXE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,oBAAoB;MAC3BJ,KAAK,EAAE,oBAAoB;MAC3BG,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,WAAW;MAClBJ,KAAK,EAAE,WAAW;MAClBG,IAAI,EAAE,UAAU;MAChBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,mBAAmB;MAC1BJ,KAAK,EAAE,kBAAkB;MACzBG,IAAI,EAAE,WAAW;MACjBE,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,cAAc;MACrBJ,KAAK,EAAE,cAAc;MACrBG,IAAI,EAAE,cAAc;MACpBE,WAAW,EAAE;KACd,CACF;;;;uCA5CUC,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvT5BvB,EAHN,CAAAC,cAAA,aAA2B,aACD,aACO,eACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EAC1B;UAEJH,EADF,CAAAC,cAAA,aAA6B,SACvB;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,WAAwB;UAAAD,EAAA,CAAAE,MAAA,iCAA0B;UAEtDF,EAFsD,CAAAG,YAAA,EAAI,EAClD,EACF;UAENH,EAAA,CAAAC,cAAA,uBAA+B;UAC7BD,EAAA,CAAAyB,UAAA,KAAAC,mDAAA,2BAImB;UAOrB1B,EAAA,CAAAG,YAAA,EAAe;UAEfH,EAAA,CAAA2B,SAAA,cAA+B;UAKzB3B,EAHN,CAAAC,cAAA,cAAwB,cACI,eACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;UAEJH,EADF,CAAAC,cAAA,eAA0B,gBACK;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAyB,aACoE,gBAC/E;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAIpBF,EAJoB,CAAAG,YAAA,EAAO,EACjB,EACA,EACF,EACF;;;UA/BiBH,EAAA,CAAAS,SAAA,IAAW;UAAXT,EAAA,CAAAI,UAAA,YAAAoB,GAAA,CAAAR,QAAA,CAAW;;;qBArBlCtB,YAAY,EAAAkC,EAAA,CAAAC,OAAA,EACZlC,YAAY,EAAAmC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA,EACZpC,aAAa,EAAAqC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,gBAAA,EACbzC,aAAa,EAAA0C,EAAA,CAAAC,OAAA,EACb1C,eAAe,EAAA2C,EAAA,CAAAC,SAAA,EACf3C,gBAAgB;MAAA4C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}