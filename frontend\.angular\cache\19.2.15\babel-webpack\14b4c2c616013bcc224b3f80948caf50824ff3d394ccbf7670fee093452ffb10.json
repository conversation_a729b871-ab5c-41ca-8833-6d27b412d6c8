{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { SEVERITY_COLORS } from '../../models/security.models';\nimport { SecurityMetricsChartComponent } from '../../shared/charts/security-metrics-chart/security-metrics-chart.component';\nimport { ScanProgressChartComponent } from '../../shared/charts/scan-progress-chart/scan-progress-chart.component';\nimport { SecurityTrendsDashboardComponent } from '../../shared/charts/security-trends-dashboard/security-trends-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/table\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/tooltip\";\nimport * as i9 from \"@angular/router\";\nconst _c0 = a0 => [\"/reports\", a0];\nfunction DashboardComponent_div_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"app-scan-progress-chart\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scanProgress\", ctx_r0.getActiveScan())(\"showRealTimeUpdates\", true)(\"updateInterval\", 2000);\n  }\n}\nfunction DashboardComponent_th_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", scan_r2.id.substring(0, 8), \"...\");\n  }\n}\nfunction DashboardComponent_th_142_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 69)(2, \"span\", 70);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 71);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getProjectName(scan_r3.project_path));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(scan_r3.project_path);\n  }\n}\nfunction DashboardComponent_th_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Chains\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_146_mat_chip_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chain_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(chain_r4);\n  }\n}\nfunction DashboardComponent_td_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 72)(2, \"mat-chip-set\");\n    i0.ɵɵtemplate(3, DashboardComponent_td_146_mat_chip_3_Template, 2, 1, \"mat-chip\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", scan_r5.chains);\n  }\n}\nfunction DashboardComponent_th_148_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 74)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + scan_r6.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getStatusIcon(scan_r6.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 4, scan_r6.status));\n  }\n}\nfunction DashboardComponent_th_151_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_152_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getIssueCount(scan_r7, \"critical\"), \" Critical \");\n  }\n}\nfunction DashboardComponent_td_152_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getIssueCount(scan_r7, \"high\"), \" High \");\n  }\n}\nfunction DashboardComponent_td_152_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getIssueCount(scan_r7, \"medium\"), \" Medium \");\n  }\n}\nfunction DashboardComponent_td_152_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83);\n    i0.ɵɵtext(1, \" No issues found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_152_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 75);\n    i0.ɵɵtemplate(2, DashboardComponent_td_152_span_2_Template, 2, 1, \"span\", 76)(3, DashboardComponent_td_152_span_3_Template, 2, 1, \"span\", 77)(4, DashboardComponent_td_152_span_4_Template, 2, 1, \"span\", 78)(5, DashboardComponent_td_152_span_5_Template, 2, 0, \"span\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getIssueCount(scan_r7, \"critical\") > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getIssueCount(scan_r7, \"high\") > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getIssueCount(scan_r7, \"medium\") > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !scan_r7.issues || scan_r7.issues.length === 0);\n  }\n}\nfunction DashboardComponent_th_154_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_155_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 84)(2, \"span\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 86);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(4, 2, scan_r8.created_at, \"MMM d, y\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 5, scan_r8.created_at, \"h:mm a\"));\n  }\n}\nfunction DashboardComponent_th_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_158_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 91)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"download\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_td_158_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 92)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"hourglass_empty\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_td_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"div\", 87)(2, \"button\", 88)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, DashboardComponent_td_158_button_5_Template, 3, 0, \"button\", 89)(6, DashboardComponent_td_158_button_6_Template, 3, 0, \"button\", 90);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, scan_r9.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", scan_r9.status === \"completed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", scan_r9.status === \"running\");\n  }\n}\nfunction DashboardComponent_tr_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 93);\n  }\n}\nfunction DashboardComponent_tr_160_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 94);\n  }\n}\nexport class DashboardComponent {\n  // Additional properties for enhanced header\n  get totalScans() {\n    return this.stats.totalScans;\n  }\n  get activeScans() {\n    return this.recentScans.filter(scan => scan.status === 'running').length;\n  }\n  get criticalIssues() {\n    return this.stats.criticalIssues;\n  }\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.stats = {\n      totalScans: 0,\n      criticalIssues: 0,\n      highIssues: 0,\n      mediumIssues: 0\n    };\n    this.recentScans = [];\n    this.topIssues = [];\n    this.displayedColumns = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: response => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: error => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n  loadMockData() {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts['critical'] || 0,\n      highIssues: mockScan.severity_counts['high'] || 0,\n      mediumIssues: mockScan.severity_counts['medium'] || 0\n    };\n  }\n  calculateStats(scans) {\n    this.stats.totalScans = scans.length;\n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues = [];\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.['critical'] || 0;\n      highTotal += scan.severity_counts?.['high'] || 0;\n      mediumTotal += scan.severity_counts?.['medium'] || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n    // Get top 5 most severe issues\n    this.topIssues = allIssues.sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity)).slice(0, 5);\n  }\n  getSeverityWeight(severity) {\n    const weights = {\n      critical: 4,\n      high: 3,\n      medium: 2,\n      low: 1,\n      info: 0\n    };\n    return weights[severity] || 0;\n  }\n  getSeverityColor(severity) {\n    return SEVERITY_COLORS[severity] || '#666';\n  }\n  getProjectName(path) {\n    return path.split('/').pop() || path;\n  }\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleDateString();\n  }\n  formatTime(dateString) {\n    return new Date(dateString).toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getStatusIcon(status) {\n    const icons = {\n      completed: 'check_circle',\n      running: 'hourglass_empty',\n      failed: 'error',\n      pending: 'schedule'\n    };\n    return icons[status] || 'help';\n  }\n  getIssueCount(scan, severity) {\n    return scan.severity_counts?.[severity] || 0;\n  }\n  // Chart data methods\n  getSecurityMetricsData() {\n    return [{\n      label: 'Critical',\n      value: this.stats.criticalIssues,\n      severity: 'critical',\n      trend: 15 // Mock trend data\n    }, {\n      label: 'High',\n      value: this.stats.highIssues,\n      severity: 'high',\n      trend: -5\n    }, {\n      label: 'Medium',\n      value: this.stats.mediumIssues,\n      severity: 'medium',\n      trend: 8\n    }, {\n      label: 'Low',\n      value: Math.max(0, this.stats.totalScans * 2 - this.stats.criticalIssues - this.stats.highIssues - this.stats.mediumIssues),\n      severity: 'low',\n      trend: -12\n    }];\n  }\n  getActiveScan() {\n    const activeScan = this.recentScans.find(scan => scan.status === 'running');\n    if (!activeScan) return null;\n    return {\n      scanId: activeScan.id,\n      projectName: this.getProjectName(activeScan.project_path),\n      status: 'running',\n      progress: 65,\n      // Mock progress\n      currentStep: 'Analyzing smart contracts',\n      totalSteps: 6,\n      completedSteps: 3,\n      startTime: new Date(activeScan.created_at),\n      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000),\n      // 5 minutes from now\n      filesScanned: 45,\n      totalFiles: 78,\n      issuesFound: activeScan.issues?.length || 0\n    };\n  }\n  getSecurityTrendsData() {\n    // Generate mock trend data\n    const now = new Date();\n    const data = [];\n    for (let i = 29; i >= 0; i--) {\n      const date = new Date(now);\n      date.setDate(date.getDate() - i);\n      data.push({\n        date,\n        critical: Math.floor(Math.random() * 5) + (i < 10 ? 2 : 0),\n        high: Math.floor(Math.random() * 8) + 3,\n        medium: Math.floor(Math.random() * 12) + 5,\n        low: Math.floor(Math.random() * 15) + 8,\n        totalScans: Math.floor(Math.random() * 3) + 1,\n        averageScore: Math.floor(Math.random() * 30) + 60\n      });\n    }\n    return {\n      period: '30d',\n      data,\n      summary: {\n        totalIssues: this.stats.criticalIssues + this.stats.highIssues + this.stats.mediumIssues,\n        criticalTrend: 15,\n        // Mock trend\n        averageScore: 72,\n        scanFrequency: 1.2\n      }\n    };\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 183,\n      vars: 18,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-text\"], [1, \"dashboard-title\"], [1, \"title-icon\"], [1, \"dashboard-subtitle\"], [1, \"header-stats\"], [1, \"quick-stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\", 1, \"action-button\", \"primary\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/reports\", 1, \"action-button\", \"secondary\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-scans\", \"elevated\"], [1, \"stat-card-header\"], [1, \"stat-icon-wrapper\", \"success\"], [1, \"stat-icon\"], [1, \"stat-trend\"], [1, \"trend-icon\", \"positive\"], [1, \"trend-value\"], [1, \"stat-card-content\"], [1, \"stat-number\"], [1, \"stat-description\"], [1, \"stat-card-footer\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [1, \"stat-card\", \"critical-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"critical\"], [1, \"trend-icon\", \"negative\"], [1, \"stat-number\", \"critical\"], [\"mat-button\", \"\", \"color\", \"warn\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"high-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"warning\"], [1, \"trend-icon\", \"neutral\"], [1, \"stat-number\", \"warning\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"medium-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"info\"], [1, \"stat-number\", \"info\"], [\"mat-button\", \"\", \"routerLink\", \"/checklist\"], [1, \"chart-section\"], [\"title\", \"Security Issues Distribution\", 3, \"data\", \"chartType\", \"showTrends\", \"animated\"], [\"class\", \"chart-section\", 4, \"ngIf\"], [3, \"trendsData\", \"showComparison\", \"autoRefresh\"], [1, \"recent-scans-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"scans-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"project\"], [\"matColumnDef\", \"chains\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [1, \"quick-actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/checklist\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/reports\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/settings\"], [3, \"scanProgress\", \"showRealTimeUpdates\", \"updateInterval\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"scan-id\"], [1, \"project-info\"], [1, \"project-name\"], [1, \"project-path\"], [1, \"chains-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"status-badge\"], [1, \"issues-summary\"], [\"class\", \"issue-count critical\", 4, \"ngIf\"], [\"class\", \"issue-count high\", 4, \"ngIf\"], [\"class\", \"issue-count medium\", 4, \"ngIf\"], [\"class\", \"no-issues\", 4, \"ngIf\"], [1, \"issue-count\", \"critical\"], [1, \"issue-count\", \"high\"], [1, \"issue-count\", \"medium\"], [1, \"no-issues\"], [1, \"date-info\"], [1, \"date\"], [1, \"time\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Report\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Download Report\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Progress\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Download Report\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Progress\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" Security Dashboard \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \"Monitor your blockchain security posture in real-time\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"span\", 9);\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 10);\n          i0.ɵɵtext(15, \"Total Scans\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 8)(17, \"span\", 9);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 10);\n          i0.ɵɵtext(20, \"Active\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 8)(22, \"span\", 9);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 10);\n          i0.ɵɵtext(25, \"Critical\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 11)(27, \"button\", 12)(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Start New Scan \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"button\", 13)(32, \"mat-icon\");\n          i0.ɵɵtext(33, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34, \" View Reports \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 14)(36, \"mat-card\", 15)(37, \"div\", 16)(38, \"div\", 17)(39, \"mat-icon\", 18);\n          i0.ɵɵtext(40, \"security\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 19)(42, \"mat-icon\", 20);\n          i0.ɵɵtext(43, \"trending_up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 21);\n          i0.ɵɵtext(45, \"+12%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 22)(47, \"div\", 23);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 10);\n          i0.ɵɵtext(50, \"Total Scans\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 24);\n          i0.ɵɵtext(52, \"Completed this month\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 25)(54, \"button\", 26)(55, \"mat-icon\");\n          i0.ɵɵtext(56, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" New Scan \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"mat-card\", 27)(59, \"div\", 16)(60, \"div\", 28)(61, \"mat-icon\", 18);\n          i0.ɵɵtext(62, \"error\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 19)(64, \"mat-icon\", 29);\n          i0.ɵɵtext(65, \"trending_up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"span\", 21);\n          i0.ɵɵtext(67, \"+3\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"div\", 22)(69, \"div\", 30);\n          i0.ɵɵtext(70);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 10);\n          i0.ɵɵtext(72, \"Critical Issues\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 24);\n          i0.ɵɵtext(74, \"Require immediate attention\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 25)(76, \"button\", 31)(77, \"mat-icon\");\n          i0.ɵɵtext(78, \"priority_high\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(79, \" View Details \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"mat-card\", 32)(81, \"div\", 16)(82, \"div\", 33)(83, \"mat-icon\", 18);\n          i0.ɵɵtext(84, \"warning\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 19)(86, \"mat-icon\", 34);\n          i0.ɵɵtext(87, \"trending_flat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"span\", 21);\n          i0.ɵɵtext(89, \"0\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 22)(91, \"div\", 35);\n          i0.ɵɵtext(92);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"div\", 10);\n          i0.ɵɵtext(94, \"High Priority\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 24);\n          i0.ɵɵtext(96, \"Should be addressed soon\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"div\", 25)(98, \"button\", 36)(99, \"mat-icon\");\n          i0.ɵɵtext(100, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(101, \" Review \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(102, \"mat-card\", 37)(103, \"div\", 16)(104, \"div\", 38)(105, \"mat-icon\", 18);\n          i0.ɵɵtext(106, \"info\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\", 19)(108, \"mat-icon\", 20);\n          i0.ɵɵtext(109, \"trending_down\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"span\", 21);\n          i0.ɵɵtext(111, \"-5\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(112, \"div\", 22)(113, \"div\", 39);\n          i0.ɵɵtext(114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\", 10);\n          i0.ɵɵtext(116, \"Medium Priority\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 24);\n          i0.ɵɵtext(118, \"Monitor and plan fixes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(119, \"div\", 25)(120, \"button\", 40)(121, \"mat-icon\");\n          i0.ɵɵtext(122, \"checklist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(123, \" Checklist \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(124, \"div\", 41);\n          i0.ɵɵelement(125, \"app-security-metrics-chart\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(126, DashboardComponent_div_126_Template, 2, 3, \"div\", 43);\n          i0.ɵɵelementStart(127, \"div\", 41);\n          i0.ɵɵelement(128, \"app-security-trends-dashboard\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"mat-card\", 45)(130, \"mat-card-header\")(131, \"mat-card-title\");\n          i0.ɵɵtext(132, \"Recent Scans\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"mat-card-subtitle\");\n          i0.ɵɵtext(134, \"Latest security scan results\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(135, \"mat-card-content\")(136, \"div\", 46)(137, \"table\", 47);\n          i0.ɵɵelementContainerStart(138, 48);\n          i0.ɵɵtemplate(139, DashboardComponent_th_139_Template, 2, 0, \"th\", 49)(140, DashboardComponent_td_140_Template, 3, 1, \"td\", 50);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(141, 51);\n          i0.ɵɵtemplate(142, DashboardComponent_th_142_Template, 2, 0, \"th\", 49)(143, DashboardComponent_td_143_Template, 6, 2, \"td\", 50);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(144, 52);\n          i0.ɵɵtemplate(145, DashboardComponent_th_145_Template, 2, 0, \"th\", 49)(146, DashboardComponent_td_146_Template, 4, 1, \"td\", 50);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(147, 53);\n          i0.ɵɵtemplate(148, DashboardComponent_th_148_Template, 2, 0, \"th\", 49)(149, DashboardComponent_td_149_Template, 7, 6, \"td\", 50);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(150, 54);\n          i0.ɵɵtemplate(151, DashboardComponent_th_151_Template, 2, 0, \"th\", 49)(152, DashboardComponent_td_152_Template, 6, 4, \"td\", 50);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(153, 55);\n          i0.ɵɵtemplate(154, DashboardComponent_th_154_Template, 2, 0, \"th\", 49)(155, DashboardComponent_td_155_Template, 8, 8, \"td\", 50);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(156, 56);\n          i0.ɵɵtemplate(157, DashboardComponent_th_157_Template, 2, 0, \"th\", 49)(158, DashboardComponent_td_158_Template, 7, 5, \"td\", 50);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(159, DashboardComponent_tr_159_Template, 1, 0, \"tr\", 57)(160, DashboardComponent_tr_160_Template, 1, 0, \"tr\", 58);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(161, \"mat-card\", 59)(162, \"mat-card-header\")(163, \"mat-card-title\");\n          i0.ɵɵtext(164, \"Quick Actions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(165, \"mat-card-content\")(166, \"div\", 60)(167, \"button\", 61)(168, \"mat-icon\");\n          i0.ɵɵtext(169, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(170, \" Start New Scan \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(171, \"button\", 62)(172, \"mat-icon\");\n          i0.ɵɵtext(173, \"checklist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(174, \" Security Checklist \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(175, \"button\", 63)(176, \"mat-icon\");\n          i0.ɵɵtext(177, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(178, \" Generate Report \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(179, \"button\", 64)(180, \"mat-icon\");\n          i0.ɵɵtext(181, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(182, \" Settings \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate(ctx.totalScans);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.activeScans);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.criticalIssues);\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate(ctx.stats.totalScans);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.stats.criticalIssues);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.stats.highIssues);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.stats.mediumIssues);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"data\", ctx.getSecurityMetricsData())(\"chartType\", \"doughnut\")(\"showTrends\", true)(\"animated\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveScan());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"trendsData\", ctx.getSecurityTrendsData())(\"showComparison\", true)(\"autoRefresh\", false);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"dataSource\", ctx.recentScans);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, i2.DatePipe, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, i5.MatIconButton, MatTableModule, i6.MatTable, i6.MatHeaderCellDef, i6.MatHeaderRowDef, i6.MatColumnDef, i6.MatCellDef, i6.MatRowDef, i6.MatHeaderCell, i6.MatCell, i6.MatHeaderRow, i6.MatRow, MatChipsModule, i7.MatChip, i7.MatChipSet, MatProgressBarModule, MatTooltipModule, i8.MatTooltip, RouterModule, i9.RouterLink, SecurityMetricsChartComponent, ScanProgressChartComponent, SecurityTrendsDashboardComponent],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  max-width: 100%;\\n  margin: 0;\\n  background: var(--spt-bg-secondary);\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);\\n  color: white;\\n  padding: var(--spt-space-12) var(--spt-space-8);\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: var(--spt-space-8);\\n  border-radius: 0 0 var(--spt-radius-3xl) var(--spt-radius-3xl);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-12);\\n  align-items: flex-start;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.header-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.dashboard-title[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-2) 0;\\n  font-size: var(--spt-text-4xl);\\n  font-weight: var(--spt-font-bold);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  line-height: var(--spt-leading-tight);\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: var(--spt-radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.dashboard-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-normal);\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n.header-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-6);\\n  margin-top: var(--spt-space-2);\\n}\\n\\n.quick-stat[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: var(--spt-space-3) var(--spt-space-4);\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: var(--spt-radius-xl);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-2xl);\\n  font-weight: var(--spt-font-bold);\\n  line-height: 1;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n  margin-top: var(--spt-space-1);\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-3);\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.action-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  padding: 0 var(--spt-space-6);\\n  border-radius: var(--spt-radius-xl);\\n  font-weight: var(--spt-font-semibold);\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n\\n.action-button.primary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n}\\n\\n.action-button.primary[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: var(--spt-shadow-lg);\\n}\\n\\n.action-button.secondary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n\\n.action-button.secondary[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-1px);\\n}\\n\\n\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: var(--spt-space-6);\\n  margin: 0 var(--spt-space-8) var(--spt-space-12) var(--spt-space-8);\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: var(--spt-radius-2xl);\\n  border: 1px solid var(--spt-border);\\n  background: var(--spt-surface);\\n  box-shadow: var(--spt-shadow-sm);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stat-card.elevated[_ngcontent-%COMP%] {\\n  box-shadow: var(--spt-shadow-lg);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: var(--spt-shadow-xl);\\n  border-color: var(--spt-primary-200);\\n}\\n\\n.stat-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spt-space-5) var(--spt-space-6) var(--spt-space-3) var(--spt-space-6);\\n}\\n\\n.stat-card-content[_ngcontent-%COMP%] {\\n  padding: 0 var(--spt-space-6) var(--spt-space-4) var(--spt-space-6);\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-3) var(--spt-space-6) var(--spt-space-5) var(--spt-space-6);\\n  border-top: 1px solid var(--spt-border-light);\\n  background: var(--spt-bg-secondary);\\n}\\n\\n.stat-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: var(--spt-radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-icon-wrapper.success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-success-100) 0%, var(--spt-success-200) 100%);\\n  border: 1px solid var(--spt-success-300);\\n}\\n\\n.stat-icon-wrapper.critical[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-error-100) 0%, var(--spt-error-200) 100%);\\n  border: 1px solid var(--spt-error-300);\\n}\\n\\n.stat-icon-wrapper.warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-warning-100) 0%, var(--spt-warning-200) 100%);\\n  border: 1px solid var(--spt-warning-300);\\n}\\n\\n.stat-icon-wrapper.info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-info-100) 0%, var(--spt-info-200) 100%);\\n  border: 1px solid var(--spt-info-300);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover   .stat-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.stat-icon-wrapper.success[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-success-700);\\n}\\n\\n.stat-icon-wrapper.critical[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-error-700);\\n}\\n\\n.stat-icon-wrapper.warning[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-700);\\n}\\n\\n.stat-icon-wrapper.info[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-info-700);\\n}\\n\\n.stat-trend[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-1);\\n  padding: var(--spt-space-1) var(--spt-space-2);\\n  border-radius: var(--spt-radius-lg);\\n  font-size: var(--spt-text-xs);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.trend-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.trend-icon.positive[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n}\\n\\n.trend-icon.negative[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.trend-icon.neutral[_ngcontent-%COMP%] {\\n  color: var(--spt-text-tertiary);\\n}\\n\\n.trend-value[_ngcontent-%COMP%] {\\n  font-weight: var(--spt-font-bold);\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-4xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-text-primary);\\n  margin-bottom: var(--spt-space-1);\\n  line-height: var(--spt-leading-none);\\n  transition: color 0.3s ease;\\n}\\n\\n.stat-number.critical[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.stat-number.warning[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n}\\n\\n.stat-number.info[_ngcontent-%COMP%] {\\n  color: var(--spt-info-600);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  color: var(--spt-text-primary);\\n  font-weight: var(--spt-font-semibold);\\n  margin-bottom: var(--spt-space-1);\\n  line-height: var(--spt-leading-tight);\\n}\\n\\n.stat-description[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n  font-weight: var(--spt-font-normal);\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  justify-content: flex-start;\\n  gap: var(--spt-space-2);\\n  padding: var(--spt-space-2) var(--spt-space-3);\\n  border-radius: var(--spt-radius-lg);\\n  font-weight: var(--spt-font-medium);\\n  transition: all 0.2s ease;\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateX(4px);\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n\\n\\n.chart-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-8);\\n}\\n\\n.chart-section[_ngcontent-%COMP%]:last-of-type {\\n  margin-bottom: var(--spt-space-6);\\n}\\n\\n\\n\\n.recent-scans-card[_ngcontent-%COMP%] {\\n  margin: 0 var(--spt-space-8) var(--spt-space-8) var(--spt-space-8);\\n  border-radius: var(--spt-radius-2xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n  overflow: hidden;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  max-width: 100%;\\n}\\n\\n.scans-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: var(--spt-surface);\\n}\\n\\n.scan-id[_ngcontent-%COMP%] {\\n  font-family: var(--spt-font-mono);\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n  background: var(--spt-gray-100);\\n  padding: var(--spt-space-1) var(--spt-space-2);\\n  border-radius: var(--spt-radius-md);\\n}\\n\\n.project-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-1);\\n}\\n\\n.project-name[_ngcontent-%COMP%] {\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-text-primary);\\n}\\n\\n.project-path[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-secondary);\\n  font-family: var(--spt-font-mono);\\n}\\n\\n.chains-list[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: var(--spt-space-1);\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  padding: var(--spt-space-2) var(--spt-space-3);\\n  border-radius: var(--spt-radius-lg);\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-medium);\\n  text-transform: capitalize;\\n}\\n\\n.status-badge.status-completed[_ngcontent-%COMP%] {\\n  background: var(--spt-success-100);\\n  color: var(--spt-success-700);\\n}\\n\\n.status-badge.status-running[_ngcontent-%COMP%] {\\n  background: var(--spt-info-100);\\n  color: var(--spt-info-700);\\n}\\n\\n.status-badge.status-failed[_ngcontent-%COMP%] {\\n  background: var(--spt-error-100);\\n  color: var(--spt-error-700);\\n}\\n\\n.status-badge.status-pending[_ngcontent-%COMP%] {\\n  background: var(--spt-warning-100);\\n  color: var(--spt-warning-700);\\n}\\n\\n.issues-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-1);\\n}\\n\\n.issue-count[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  font-weight: var(--spt-font-semibold);\\n  padding: var(--spt-space-1) var(--spt-space-2);\\n  border-radius: var(--spt-radius-md);\\n}\\n\\n.issue-count.critical[_ngcontent-%COMP%] {\\n  background: var(--spt-error-100);\\n  color: var(--spt-error-700);\\n}\\n\\n.issue-count.high[_ngcontent-%COMP%] {\\n  background: var(--spt-warning-100);\\n  color: var(--spt-warning-700);\\n}\\n\\n.issue-count.medium[_ngcontent-%COMP%] {\\n  background: var(--spt-info-100);\\n  color: var(--spt-info-700);\\n}\\n\\n.no-issues[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-secondary);\\n  font-style: italic;\\n}\\n\\n.date-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-1);\\n}\\n\\n.date[_ngcontent-%COMP%] {\\n  font-weight: var(--spt-font-medium);\\n  color: var(--spt-text-primary);\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-text-secondary);\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-1);\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: var(--spt-radius-lg);\\n  color: var(--spt-text-secondary);\\n  transition: all 0.2s ease;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-50);\\n  color: var(--spt-primary-600);\\n  transform: scale(1.1);\\n}\\n\\n\\n\\n.quick-actions-card[_ngcontent-%COMP%] {\\n  margin: 0 var(--spt-space-8) var(--spt-space-8) var(--spt-space-8);\\n  border-radius: var(--spt-radius-2xl);\\n  border: 1px solid var(--spt-border);\\n  box-shadow: var(--spt-shadow-sm);\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: var(--spt-space-4);\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 64px;\\n  border-radius: var(--spt-radius-xl);\\n  font-weight: var(--spt-font-semibold);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--spt-space-3);\\n  transition: all 0.3s ease;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--spt-shadow-lg);\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .chart-section[_ngcontent-%COMP%] {\\n    margin-bottom: var(--spt-space-6);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatTableModule", "MatChipsModule", "MatProgressBarModule", "MatTooltipModule", "RouterModule", "SEVERITY_COLORS", "SecurityMetricsChartComponent", "ScanProgressChartComponent", "SecurityTrendsDashboardComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "getActiveScan", "ɵɵtext", "ɵɵtextInterpolate1", "scan_r2", "id", "substring", "ɵɵtextInterpolate", "getProjectName", "scan_r3", "project_path", "chain_r4", "ɵɵtemplate", "DashboardComponent_td_146_mat_chip_3_Template", "scan_r5", "chains", "ɵɵclassMap", "scan_r6", "status", "getStatusIcon", "ɵɵpipeBind1", "getIssueCount", "scan_r7", "DashboardComponent_td_152_span_2_Template", "DashboardComponent_td_152_span_3_Template", "DashboardComponent_td_152_span_4_Template", "DashboardComponent_td_152_span_5_Template", "issues", "length", "ɵɵpipeBind2", "scan_r8", "created_at", "DashboardComponent_td_158_button_5_Template", "DashboardComponent_td_158_button_6_Template", "ɵɵpureFunction1", "_c0", "scan_r9", "DashboardComponent", "totalScans", "stats", "activeScans", "recentScans", "filter", "scan", "criticalIssues", "constructor", "apiService", "highIssues", "mediumIssues", "topIssues", "displayedColumns", "ngOnInit", "loadDashboardData", "getScanHistory", "subscribe", "next", "response", "scans", "slice", "calculateStats", "error", "console", "loadMockData", "mockScan", "generateMockScanResult", "severity_counts", "criticalTotal", "highTotal", "mediumTotal", "allIssues", "for<PERSON>ach", "concat", "sort", "a", "b", "getSeverityWeight", "severity", "weights", "critical", "high", "medium", "low", "info", "getSeverityColor", "path", "split", "pop", "formatDate", "dateString", "Date", "toLocaleDateString", "formatTime", "toLocaleTimeString", "hour", "minute", "icons", "completed", "running", "failed", "pending", "getSecurityMetricsData", "label", "value", "trend", "Math", "max", "activeScan", "find", "scanId", "projectName", "progress", "currentStep", "totalSteps", "completedSteps", "startTime", "estimatedCompletion", "now", "filesScanned", "totalFiles", "issuesFound", "getSecurityTrendsData", "data", "i", "date", "setDate", "getDate", "push", "floor", "random", "averageScore", "period", "summary", "totalIssues", "criticalTrend", "scanFrequency", "ɵɵdirectiveInject", "i1", "ApiService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_126_Template", "ɵɵelementContainerStart", "DashboardComponent_th_139_Template", "DashboardComponent_td_140_Template", "DashboardComponent_th_142_Template", "DashboardComponent_td_143_Template", "DashboardComponent_th_145_Template", "DashboardComponent_td_146_Template", "DashboardComponent_th_148_Template", "DashboardComponent_td_149_Template", "DashboardComponent_th_151_Template", "DashboardComponent_td_152_Template", "DashboardComponent_th_154_Template", "DashboardComponent_td_155_Template", "DashboardComponent_th_157_Template", "DashboardComponent_td_158_Template", "DashboardComponent_tr_159_Template", "DashboardComponent_tr_160_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "TitleCasePipe", "DatePipe", "i3", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i4", "MatIcon", "i5", "MatButton", "MatIconButton", "i6", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i7", "MatChip", "MatChipSet", "i8", "MatTooltip", "i9", "RouterLink", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\dashboard\\dashboard.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { ApiService } from '../../services/api.service';\nimport { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';\nimport { SecurityMetricsChartComponent, SecurityMetric } from '../../shared/charts/security-metrics-chart/security-metrics-chart.component';\nimport { ScanProgressChartComponent, ScanProgress } from '../../shared/charts/scan-progress-chart/scan-progress-chart.component';\nimport { SecurityTrendsDashboardComponent, SecurityTrend, TrendDataPoint } from '../../shared/charts/security-trends-dashboard/security-trends-dashboard.component';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatTableModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    MatTooltipModule,\n    RouterModule,\n    SecurityMetricsChartComponent,\n    ScanProgressChartComponent,\n    SecurityTrendsDashboardComponent\n  ],\n  templateUrl: './dashboard.component.html',\n\n  styleUrls: ['./dashboard.component.scss']\n\n})\nexport class DashboardComponent implements OnInit {\n  stats = {\n    totalScans: 0,\n    criticalIssues: 0,\n    highIssues: 0,\n    mediumIssues: 0\n  };\n\n  recentScans: ScanResult[] = [];\n  topIssues: SecurityIssue[] = [];\n  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n\n  // Additional properties for enhanced header\n  get totalScans(): number { return this.stats.totalScans; }\n  get activeScans(): number { return this.recentScans.filter(scan => scan.status === 'running').length; }\n  get criticalIssues(): number { return this.stats.criticalIssues; }\n\n  constructor(private apiService: ApiService) {}\n\n  ngOnInit(): void {\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: (response) => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: (error) => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n\n  loadMockData(): void {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts['critical'] || 0,\n      highIssues: mockScan.severity_counts['high'] || 0,\n      mediumIssues: mockScan.severity_counts['medium'] || 0\n    };\n  }\n\n  calculateStats(scans: ScanResult[]): void {\n    this.stats.totalScans = scans.length;\n    \n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues: SecurityIssue[] = [];\n\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.['critical'] || 0;\n      highTotal += scan.severity_counts?.['high'] || 0;\n      mediumTotal += scan.severity_counts?.['medium'] || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n\n    // Get top 5 most severe issues\n    this.topIssues = allIssues\n      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))\n      .slice(0, 5);\n  }\n\n  getSeverityWeight(severity: string): number {\n    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };\n    return weights[severity as keyof typeof weights] || 0;\n  }\n\n  getSeverityColor(severity: string): string {\n    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';\n  }\n\n  getProjectName(path: string): string {\n    return path.split('/').pop() || path;\n  }\n\n  formatDate(dateString: string): string {\n    return new Date(dateString).toLocaleDateString();\n  }\n\n  formatTime(dateString: string): string {\n    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  }\n\n  getStatusIcon(status: string): string {\n    const icons = {\n      completed: 'check_circle',\n      running: 'hourglass_empty',\n      failed: 'error',\n      pending: 'schedule'\n    };\n    return icons[status as keyof typeof icons] || 'help';\n  }\n\n  getIssueCount(scan: ScanResult, severity: string): number {\n    return scan.severity_counts?.[severity] || 0;\n  }\n\n  // Chart data methods\n  getSecurityMetricsData(): SecurityMetric[] {\n    return [\n      {\n        label: 'Critical',\n        value: this.stats.criticalIssues,\n        severity: 'critical',\n        trend: 15 // Mock trend data\n      },\n      {\n        label: 'High',\n        value: this.stats.highIssues,\n        severity: 'high',\n        trend: -5\n      },\n      {\n        label: 'Medium',\n        value: this.stats.mediumIssues,\n        severity: 'medium',\n        trend: 8\n      },\n      {\n        label: 'Low',\n        value: Math.max(0, this.stats.totalScans * 2 - this.stats.criticalIssues - this.stats.highIssues - this.stats.mediumIssues),\n        severity: 'low',\n        trend: -12\n      }\n    ];\n  }\n\n  getActiveScan(): ScanProgress | null {\n    const activeScan = this.recentScans.find(scan => scan.status === 'running');\n    if (!activeScan) return null;\n\n    return {\n      scanId: activeScan.id,\n      projectName: this.getProjectName(activeScan.project_path),\n      status: 'running',\n      progress: 65, // Mock progress\n      currentStep: 'Analyzing smart contracts',\n      totalSteps: 6,\n      completedSteps: 3,\n      startTime: new Date(activeScan.created_at),\n      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes from now\n      filesScanned: 45,\n      totalFiles: 78,\n      issuesFound: activeScan.issues?.length || 0\n    };\n  }\n\n  getSecurityTrendsData(): SecurityTrend | null {\n    // Generate mock trend data\n    const now = new Date();\n    const data: TrendDataPoint[] = [];\n\n    for (let i = 29; i >= 0; i--) {\n      const date = new Date(now);\n      date.setDate(date.getDate() - i);\n\n      data.push({\n        date,\n        critical: Math.floor(Math.random() * 5) + (i < 10 ? 2 : 0),\n        high: Math.floor(Math.random() * 8) + 3,\n        medium: Math.floor(Math.random() * 12) + 5,\n        low: Math.floor(Math.random() * 15) + 8,\n        totalScans: Math.floor(Math.random() * 3) + 1,\n        averageScore: Math.floor(Math.random() * 30) + 60\n      });\n    }\n\n    return {\n      period: '30d',\n      data,\n      summary: {\n        totalIssues: this.stats.criticalIssues + this.stats.highIssues + this.stats.mediumIssues,\n        criticalTrend: 15, // Mock trend\n        averageScore: 72,\n        scanFrequency: 1.2\n      }\n    };\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Enhanced Header Section -->\n  <div class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"header-text\">\n        <h1 class=\"dashboard-title\">\n          <mat-icon class=\"title-icon\">dashboard</mat-icon>\n          Security Dashboard\n        </h1>\n        <p class=\"dashboard-subtitle\">Monitor your blockchain security posture in real-time</p>\n      </div>\n      <div class=\"header-stats\">\n        <div class=\"quick-stat\">\n          <span class=\"stat-value\">{{ totalScans }}</span>\n          <span class=\"stat-label\">Total Scans</span>\n        </div>\n        <div class=\"quick-stat\">\n          <span class=\"stat-value\">{{ activeScans }}</span>\n          <span class=\"stat-label\">Active</span>\n        </div>\n        <div class=\"quick-stat\">\n          <span class=\"stat-value\">{{ criticalIssues }}</span>\n          <span class=\"stat-label\">Critical</span>\n        </div>\n      </div>\n    </div>\n    <div class=\"header-actions\">\n      <button mat-raised-button color=\"primary\" routerLink=\"/scan\" class=\"action-button primary\">\n        <mat-icon>security</mat-icon>\n        Start New Scan\n      </button>\n      <button mat-stroked-button routerLink=\"/reports\" class=\"action-button secondary\">\n        <mat-icon>assessment</mat-icon>\n        View Reports\n      </button>\n    </div>\n  </div>\n\n  <!-- Enhanced Stats Grid -->\n  <div class=\"stats-grid\">\n    <mat-card class=\"stat-card total-scans elevated\">\n      <div class=\"stat-card-header\">\n        <div class=\"stat-icon-wrapper success\">\n          <mat-icon class=\"stat-icon\">security</mat-icon>\n        </div>\n        <div class=\"stat-trend\">\n          <mat-icon class=\"trend-icon positive\">trending_up</mat-icon>\n          <span class=\"trend-value\">+12%</span>\n        </div>\n      </div>\n      <div class=\"stat-card-content\">\n        <div class=\"stat-number\">{{ stats.totalScans }}</div>\n        <div class=\"stat-label\">Total Scans</div>\n        <div class=\"stat-description\">Completed this month</div>\n      </div>\n      <div class=\"stat-card-footer\">\n        <button mat-button color=\"primary\" routerLink=\"/scan\">\n          <mat-icon>add</mat-icon>\n          New Scan\n        </button>\n      </div>\n    </mat-card>\n\n    <mat-card class=\"stat-card critical-issues elevated\">\n      <div class=\"stat-card-header\">\n        <div class=\"stat-icon-wrapper critical\">\n          <mat-icon class=\"stat-icon\">error</mat-icon>\n        </div>\n        <div class=\"stat-trend\">\n          <mat-icon class=\"trend-icon negative\">trending_up</mat-icon>\n          <span class=\"trend-value\">+3</span>\n        </div>\n      </div>\n      <div class=\"stat-card-content\">\n        <div class=\"stat-number critical\">{{ stats.criticalIssues }}</div>\n        <div class=\"stat-label\">Critical Issues</div>\n        <div class=\"stat-description\">Require immediate attention</div>\n      </div>\n      <div class=\"stat-card-footer\">\n        <button mat-button color=\"warn\" routerLink=\"/reports\">\n          <mat-icon>priority_high</mat-icon>\n          View Details\n        </button>\n      </div>\n    </mat-card>\n\n    <mat-card class=\"stat-card high-issues elevated\">\n      <div class=\"stat-card-header\">\n        <div class=\"stat-icon-wrapper warning\">\n          <mat-icon class=\"stat-icon\">warning</mat-icon>\n        </div>\n        <div class=\"stat-trend\">\n          <mat-icon class=\"trend-icon neutral\">trending_flat</mat-icon>\n          <span class=\"trend-value\">0</span>\n        </div>\n      </div>\n      <div class=\"stat-card-content\">\n        <div class=\"stat-number warning\">{{ stats.highIssues }}</div>\n        <div class=\"stat-label\">High Priority</div>\n        <div class=\"stat-description\">Should be addressed soon</div>\n      </div>\n      <div class=\"stat-card-footer\">\n        <button mat-button color=\"accent\" routerLink=\"/reports\">\n          <mat-icon>visibility</mat-icon>\n          Review\n        </button>\n      </div>\n    </mat-card>\n\n    <mat-card class=\"stat-card medium-issues elevated\">\n      <div class=\"stat-card-header\">\n        <div class=\"stat-icon-wrapper info\">\n          <mat-icon class=\"stat-icon\">info</mat-icon>\n        </div>\n        <div class=\"stat-trend\">\n          <mat-icon class=\"trend-icon positive\">trending_down</mat-icon>\n          <span class=\"trend-value\">-5</span>\n        </div>\n      </div>\n      <div class=\"stat-card-content\">\n        <div class=\"stat-number info\">{{ stats.mediumIssues }}</div>\n        <div class=\"stat-label\">Medium Priority</div>\n        <div class=\"stat-description\">Monitor and plan fixes</div>\n      </div>\n      <div class=\"stat-card-footer\">\n        <button mat-button routerLink=\"/checklist\">\n          <mat-icon>checklist</mat-icon>\n          Checklist\n        </button>\n      </div>\n    </mat-card>\n  </div>\n\n  <!-- Security Metrics Chart -->\n  <div class=\"chart-section\">\n    <app-security-metrics-chart\n      title=\"Security Issues Distribution\"\n      [data]=\"getSecurityMetricsData()\"\n      [chartType]=\"'doughnut'\"\n      [showTrends]=\"true\"\n      [animated]=\"true\">\n    </app-security-metrics-chart>\n  </div>\n\n  <!-- Active Scan Progress -->\n  <div class=\"chart-section\" *ngIf=\"getActiveScan()\">\n    <app-scan-progress-chart\n      [scanProgress]=\"getActiveScan()\"\n      [showRealTimeUpdates]=\"true\"\n      [updateInterval]=\"2000\">\n    </app-scan-progress-chart>\n  </div>\n\n  <!-- Security Trends Dashboard -->\n  <div class=\"chart-section\">\n    <app-security-trends-dashboard\n      [trendsData]=\"getSecurityTrendsData()\"\n      [showComparison]=\"true\"\n      [autoRefresh]=\"false\">\n    </app-security-trends-dashboard>\n  </div>\n\n  <!-- Recent Scans Table -->\n  <mat-card class=\"recent-scans-card\">\n    <mat-card-header>\n      <mat-card-title>Recent Scans</mat-card-title>\n      <mat-card-subtitle>Latest security scan results</mat-card-subtitle>\n    </mat-card-header>\n    <mat-card-content>\n      <div class=\"table-container\">\n        <table mat-table [dataSource]=\"recentScans\" class=\"scans-table\">\n          <!-- ID Column -->\n          <ng-container matColumnDef=\"id\">\n            <th mat-header-cell *matHeaderCellDef>ID</th>\n            <td mat-cell *matCellDef=\"let scan\">\n              <span class=\"scan-id\">{{ scan.id.substring(0, 8) }}...</span>\n            </td>\n          </ng-container>\n\n          <!-- Project Column -->\n          <ng-container matColumnDef=\"project\">\n            <th mat-header-cell *matHeaderCellDef>Project</th>\n            <td mat-cell *matCellDef=\"let scan\">\n              <div class=\"project-info\">\n                <span class=\"project-name\">{{ getProjectName(scan.project_path) }}</span>\n                <span class=\"project-path\">{{ scan.project_path }}</span>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Chains Column -->\n          <ng-container matColumnDef=\"chains\">\n            <th mat-header-cell *matHeaderCellDef>Chains</th>\n            <td mat-cell *matCellDef=\"let scan\">\n              <div class=\"chains-list\">\n                <mat-chip-set>\n                  <mat-chip *ngFor=\"let chain of scan.chains\">{{ chain }}</mat-chip>\n                </mat-chip-set>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Status Column -->\n          <ng-container matColumnDef=\"status\">\n            <th mat-header-cell *matHeaderCellDef>Status</th>\n            <td mat-cell *matCellDef=\"let scan\">\n              <div class=\"status-badge\" [class]=\"'status-' + scan.status\">\n                <mat-icon>{{ getStatusIcon(scan.status) }}</mat-icon>\n                <span>{{ scan.status | titlecase }}</span>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Issues Column -->\n          <ng-container matColumnDef=\"issues\">\n            <th mat-header-cell *matHeaderCellDef>Issues</th>\n            <td mat-cell *matCellDef=\"let scan\">\n              <div class=\"issues-summary\">\n                <span class=\"issue-count critical\" *ngIf=\"getIssueCount(scan, 'critical') > 0\">\n                  {{ getIssueCount(scan, 'critical') }} Critical\n                </span>\n                <span class=\"issue-count high\" *ngIf=\"getIssueCount(scan, 'high') > 0\">\n                  {{ getIssueCount(scan, 'high') }} High\n                </span>\n                <span class=\"issue-count medium\" *ngIf=\"getIssueCount(scan, 'medium') > 0\">\n                  {{ getIssueCount(scan, 'medium') }} Medium\n                </span>\n                <span class=\"no-issues\" *ngIf=\"!scan.issues || scan.issues.length === 0\">\n                  No issues found\n                </span>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Date Column -->\n          <ng-container matColumnDef=\"date\">\n            <th mat-header-cell *matHeaderCellDef>Date</th>\n            <td mat-cell *matCellDef=\"let scan\">\n              <div class=\"date-info\">\n                <span class=\"date\">{{ scan.created_at | date:'MMM d, y' }}</span>\n                <span class=\"time\">{{ scan.created_at | date:'h:mm a' }}</span>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Actions Column -->\n          <ng-container matColumnDef=\"actions\">\n            <th mat-header-cell *matHeaderCellDef>Actions</th>\n            <td mat-cell *matCellDef=\"let scan\">\n              <div class=\"action-buttons\">\n                <button mat-icon-button \n                        [routerLink]=\"['/reports', scan.id]\"\n                        matTooltip=\"View Report\">\n                  <mat-icon>visibility</mat-icon>\n                </button>\n                <button mat-icon-button \n                        *ngIf=\"scan.status === 'completed'\"\n                        matTooltip=\"Download Report\">\n                  <mat-icon>download</mat-icon>\n                </button>\n                <button mat-icon-button \n                        *ngIf=\"scan.status === 'running'\"\n                        matTooltip=\"View Progress\">\n                  <mat-icon>hourglass_empty</mat-icon>\n                </button>\n              </div>\n            </td>\n          </ng-container>\n\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n        </table>\n      </div>\n    </mat-card-content>\n  </mat-card>\n\n  <!-- Quick Actions -->\n  <mat-card class=\"quick-actions-card\">\n    <mat-card-header>\n      <mat-card-title>Quick Actions</mat-card-title>\n    </mat-card-header>\n    <mat-card-content>\n      <div class=\"actions-grid\">\n        <button mat-raised-button color=\"primary\" routerLink=\"/scan\">\n          <mat-icon>security</mat-icon>\n          Start New Scan\n        </button>\n        <button mat-raised-button color=\"accent\" routerLink=\"/checklist\">\n          <mat-icon>checklist</mat-icon>\n          Security Checklist\n        </button>\n        <button mat-raised-button routerLink=\"/reports\">\n          <mat-icon>assessment</mat-icon>\n          Generate Report\n        </button>\n        <button mat-raised-button routerLink=\"/settings\">\n          <mat-icon>settings</mat-icon>\n          Settings\n        </button>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAAoCC,eAAe,QAAQ,8BAA8B;AACzF,SAASC,6BAA6B,QAAwB,6EAA6E;AAC3I,SAASC,0BAA0B,QAAsB,uEAAuE;AAChI,SAASC,gCAAgC,QAAuC,mFAAmF;;;;;;;;;;;;;;ICmIjKC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,kCAI0B;IAC5BF,EAAA,CAAAG,YAAA,EAAM;;;;IAJFH,EAAA,CAAAI,SAAA,EAAgC;IAEhCJ,EAFA,CAAAK,UAAA,iBAAAC,MAAA,CAAAC,aAAA,GAAgC,6BACJ,wBACL;;;;;IAwBjBP,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAQ,MAAA,SAAE;IAAAR,EAAA,CAAAG,YAAA,EAAK;;;;;IAE3CH,EADF,CAAAC,cAAA,aAAoC,eACZ;IAAAD,EAAA,CAAAQ,MAAA,GAAgC;IACxDR,EADwD,CAAAG,YAAA,EAAO,EAC1D;;;;IADmBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAS,kBAAA,KAAAC,OAAA,CAAAC,EAAA,CAAAC,SAAA,cAAgC;;;;;IAMxDZ,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAQ,MAAA,cAAO;IAAAR,EAAA,CAAAG,YAAA,EAAK;;;;;IAG9CH,EAFJ,CAAAC,cAAA,aAAoC,cACR,eACG;IAAAD,EAAA,CAAAQ,MAAA,GAAuC;IAAAR,EAAA,CAAAG,YAAA,EAAO;IACzEH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAQ,MAAA,GAAuB;IAEtDR,EAFsD,CAAAG,YAAA,EAAO,EACrD,EACH;;;;;IAH0BH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,cAAA,CAAAC,OAAA,CAAAC,YAAA,EAAuC;IACvChB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAa,iBAAA,CAAAE,OAAA,CAAAC,YAAA,CAAuB;;;;;IAOtDhB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAQ,MAAA,aAAM;IAAAR,EAAA,CAAAG,YAAA,EAAK;;;;;IAI3CH,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAQ,MAAA,GAAW;IAAAR,EAAA,CAAAG,YAAA,EAAW;;;;IAAtBH,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAa,iBAAA,CAAAI,QAAA,CAAW;;;;;IADzDjB,EAFJ,CAAAC,cAAA,aAAoC,cACT,mBACT;IACZD,EAAA,CAAAkB,UAAA,IAAAC,6CAAA,uBAA4C;IAGlDnB,EAFI,CAAAG,YAAA,EAAe,EACX,EACH;;;;IAH6BH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,UAAA,YAAAe,OAAA,CAAAC,MAAA,CAAc;;;;;IAQhDrB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAQ,MAAA,aAAM;IAAAR,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAFJ,CAAAC,cAAA,aAAoC,cAC0B,eAChD;IAAAD,EAAA,CAAAQ,MAAA,GAAgC;IAAAR,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAQ,MAAA,GAA6B;;IAEvCR,EAFuC,CAAAG,YAAA,EAAO,EACtC,EACH;;;;;IAJuBH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAsB,UAAA,aAAAC,OAAA,CAAAC,MAAA,CAAiC;IAC/CxB,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAmB,aAAA,CAAAF,OAAA,CAAAC,MAAA,EAAgC;IACpCxB,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAA0B,WAAA,OAAAH,OAAA,CAAAC,MAAA,EAA6B;;;;;IAOvCxB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAQ,MAAA,aAAM;IAAAR,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAAA,CAAAC,cAAA,eAA+E;IAC7ED,EAAA,CAAAQ,MAAA,GACF;IAAAR,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAH,MAAA,CAAAqB,aAAA,CAAAC,OAAA,4BACF;;;;;IACA5B,EAAA,CAAAC,cAAA,eAAuE;IACrED,EAAA,CAAAQ,MAAA,GACF;IAAAR,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAH,MAAA,CAAAqB,aAAA,CAAAC,OAAA,oBACF;;;;;IACA5B,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAQ,MAAA,GACF;IAAAR,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAH,MAAA,CAAAqB,aAAA,CAAAC,OAAA,wBACF;;;;;IACA5B,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAQ,MAAA,wBACF;IAAAR,EAAA,CAAAG,YAAA,EAAO;;;;;IAZTH,EADF,CAAAC,cAAA,aAAoC,cACN;IAU1BD,EATA,CAAAkB,UAAA,IAAAW,yCAAA,mBAA+E,IAAAC,yCAAA,mBAGR,IAAAC,yCAAA,mBAGI,IAAAC,yCAAA,mBAGF;IAI7EhC,EADE,CAAAG,YAAA,EAAM,EACH;;;;;IAbmCH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAqB,aAAA,CAAAC,OAAA,kBAAyC;IAG7C5B,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAqB,aAAA,CAAAC,OAAA,cAAqC;IAGnC5B,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAqB,aAAA,CAAAC,OAAA,gBAAuC;IAGhD5B,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAK,UAAA,UAAAuB,OAAA,CAAAK,MAAA,IAAAL,OAAA,CAAAK,MAAA,CAAAC,MAAA,OAA8C;;;;;IAS3ElC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAQ,MAAA,WAAI;IAAAR,EAAA,CAAAG,YAAA,EAAK;;;;;IAG3CH,EAFJ,CAAAC,cAAA,aAAoC,cACX,eACF;IAAAD,EAAA,CAAAQ,MAAA,GAAuC;;IAAAR,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAQ,MAAA,GAAqC;;IAE5DR,EAF4D,CAAAG,YAAA,EAAO,EAC3D,EACH;;;;IAHkBH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAmC,WAAA,OAAAC,OAAA,CAAAC,UAAA,cAAuC;IACvCrC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAmC,WAAA,OAAAC,OAAA,CAAAC,UAAA,YAAqC;;;;;IAO5DrC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAQ,MAAA,cAAO;IAAAR,EAAA,CAAAG,YAAA,EAAK;;;;;IAW5CH,EAHF,CAAAC,cAAA,iBAEqC,eACzB;IAAAD,EAAA,CAAAQ,MAAA,eAAQ;IACpBR,EADoB,CAAAG,YAAA,EAAW,EACtB;;;;;IAIPH,EAHF,CAAAC,cAAA,iBAEmC,eACvB;IAAAD,EAAA,CAAAQ,MAAA,sBAAe;IAC3BR,EAD2B,CAAAG,YAAA,EAAW,EAC7B;;;;;IAXPH,EALN,CAAAC,cAAA,aAAoC,cACN,iBAGO,eACrB;IAAAD,EAAA,CAAAQ,MAAA,iBAAU;IACtBR,EADsB,CAAAG,YAAA,EAAW,EACxB;IAMTH,EALA,CAAAkB,UAAA,IAAAoB,2CAAA,qBAEqC,IAAAC,2CAAA,qBAKF;IAIvCvC,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAfOH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAwC,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAA/B,EAAA,EAAoC;IAKnCX,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,UAAA,SAAAqC,OAAA,CAAAlB,MAAA,iBAAiC;IAKjCxB,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAK,UAAA,SAAAqC,OAAA,CAAAlB,MAAA,eAA+B;;;;;IAQ9CxB,EAAA,CAAAE,SAAA,aAA4D;;;;;IAC5DF,EAAA,CAAAE,SAAA,aAAkE;;;ADxO5E,OAAM,MAAOyC,kBAAkB;EAY7B;EACA,IAAIC,UAAUA,CAAA;IAAa,OAAO,IAAI,CAACC,KAAK,CAACD,UAAU;EAAE;EACzD,IAAIE,WAAWA,CAAA;IAAa,OAAO,IAAI,CAACC,WAAW,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzB,MAAM,KAAK,SAAS,CAAC,CAACU,MAAM;EAAE;EACtG,IAAIgB,cAAcA,CAAA;IAAa,OAAO,IAAI,CAACL,KAAK,CAACK,cAAc;EAAE;EAEjEC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAhB9B,KAAAP,KAAK,GAAG;MACND,UAAU,EAAE,CAAC;MACbM,cAAc,EAAE,CAAC;MACjBG,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE;KACf;IAED,KAAAP,WAAW,GAAiB,EAAE;IAC9B,KAAAQ,SAAS,GAAoB,EAAE;IAC/B,KAAAC,gBAAgB,GAAa,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;EAOlD;EAE7CC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACN,UAAU,CAACO,cAAc,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACf,WAAW,GAAGe,QAAQ,CAACC,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAACC,cAAc,CAACH,QAAQ,CAACC,KAAK,CAAC;MACrC,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACA,IAAI,CAACE,YAAY,EAAE;MACrB;KACD,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACjB,UAAU,CAACkB,sBAAsB,EAAE;IACzD,IAAI,CAACvB,WAAW,GAAG,CAACsB,QAAQ,CAAC;IAC7B,IAAI,CAACd,SAAS,GAAGc,QAAQ,CAACpC,MAAM;IAChC,IAAI,CAACY,KAAK,GAAG;MACXD,UAAU,EAAE,CAAC;MACbM,cAAc,EAAEmB,QAAQ,CAACE,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;MACzDlB,UAAU,EAAEgB,QAAQ,CAACE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;MACjDjB,YAAY,EAAEe,QAAQ,CAACE,eAAe,CAAC,QAAQ,CAAC,IAAI;KACrD;EACH;EAEAN,cAAcA,CAACF,KAAmB;IAChC,IAAI,CAAClB,KAAK,CAACD,UAAU,GAAGmB,KAAK,CAAC7B,MAAM;IAEpC,IAAIsC,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,SAAS,GAAoB,EAAE;IAEnCZ,KAAK,CAACa,OAAO,CAAC3B,IAAI,IAAG;MACnBuB,aAAa,IAAIvB,IAAI,CAACsB,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC;MACxDE,SAAS,IAAIxB,IAAI,CAACsB,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC;MAChDG,WAAW,IAAIzB,IAAI,CAACsB,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;MACpDI,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC5B,IAAI,CAAChB,MAAM,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC;IAEF,IAAI,CAACY,KAAK,CAACK,cAAc,GAAGsB,aAAa;IACzC,IAAI,CAAC3B,KAAK,CAACQ,UAAU,GAAGoB,SAAS;IACjC,IAAI,CAAC5B,KAAK,CAACS,YAAY,GAAGoB,WAAW;IAErC;IACA,IAAI,CAACnB,SAAS,GAAGoB,SAAS,CACvBG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAACE,QAAQ,CAAC,GAAG,IAAI,CAACD,iBAAiB,CAACF,CAAC,CAACG,QAAQ,CAAC,CAAC,CACvFlB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEAiB,iBAAiBA,CAACC,QAAgB;IAChC,MAAMC,OAAO,GAAG;MAAEC,QAAQ,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAC,CAAE;IACpE,OAAOL,OAAO,CAACD,QAAgC,CAAC,IAAI,CAAC;EACvD;EAEAO,gBAAgBA,CAACP,QAAgB;IAC/B,OAAOtF,eAAe,CAACsF,QAAwC,CAAC,IAAI,MAAM;EAC5E;EAEApE,cAAcA,CAAC4E,IAAY;IACzB,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIF,IAAI;EACtC;EAEAG,UAAUA,CAACC,UAAkB;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,EAAE;EAClD;EAEAC,UAAUA,CAACH,UAAkB;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACI,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAE,CAAC;EAC5F;EAEA3E,aAAaA,CAACD,MAAc;IAC1B,MAAM6E,KAAK,GAAG;MACZC,SAAS,EAAE,cAAc;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;KACV;IACD,OAAOJ,KAAK,CAAC7E,MAA4B,CAAC,IAAI,MAAM;EACtD;EAEAG,aAAaA,CAACsB,IAAgB,EAAEiC,QAAgB;IAC9C,OAAOjC,IAAI,CAACsB,eAAe,GAAGW,QAAQ,CAAC,IAAI,CAAC;EAC9C;EAEA;EACAwB,sBAAsBA,CAAA;IACpB,OAAO,CACL;MACEC,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,IAAI,CAAC/D,KAAK,CAACK,cAAc;MAChCgC,QAAQ,EAAE,UAAU;MACpB2B,KAAK,EAAE,EAAE,CAAC;KACX,EACD;MACEF,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,IAAI,CAAC/D,KAAK,CAACQ,UAAU;MAC5B6B,QAAQ,EAAE,MAAM;MAChB2B,KAAK,EAAE,CAAC;KACT,EACD;MACEF,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,IAAI,CAAC/D,KAAK,CAACS,YAAY;MAC9B4B,QAAQ,EAAE,QAAQ;MAClB2B,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,KAAK;MACZC,KAAK,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClE,KAAK,CAACD,UAAU,GAAG,CAAC,GAAG,IAAI,CAACC,KAAK,CAACK,cAAc,GAAG,IAAI,CAACL,KAAK,CAACQ,UAAU,GAAG,IAAI,CAACR,KAAK,CAACS,YAAY,CAAC;MAC3H4B,QAAQ,EAAE,KAAK;MACf2B,KAAK,EAAE,CAAC;KACT,CACF;EACH;EAEAtG,aAAaA,CAAA;IACX,MAAMyG,UAAU,GAAG,IAAI,CAACjE,WAAW,CAACkE,IAAI,CAAChE,IAAI,IAAIA,IAAI,CAACzB,MAAM,KAAK,SAAS,CAAC;IAC3E,IAAI,CAACwF,UAAU,EAAE,OAAO,IAAI;IAE5B,OAAO;MACLE,MAAM,EAAEF,UAAU,CAACrG,EAAE;MACrBwG,WAAW,EAAE,IAAI,CAACrG,cAAc,CAACkG,UAAU,CAAChG,YAAY,CAAC;MACzDQ,MAAM,EAAE,SAAS;MACjB4F,QAAQ,EAAE,EAAE;MAAE;MACdC,WAAW,EAAE,2BAA2B;MACxCC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE,CAAC;MACjBC,SAAS,EAAE,IAAIzB,IAAI,CAACiB,UAAU,CAAC3E,UAAU,CAAC;MAC1CoF,mBAAmB,EAAE,IAAI1B,IAAI,CAACA,IAAI,CAAC2B,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAC3DC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAEb,UAAU,CAAC/E,MAAM,EAAEC,MAAM,IAAI;KAC3C;EACH;EAEA4F,qBAAqBA,CAAA;IACnB;IACA,MAAMJ,GAAG,GAAG,IAAI3B,IAAI,EAAE;IACtB,MAAMgC,IAAI,GAAqB,EAAE;IAEjC,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMC,IAAI,GAAG,IAAIlC,IAAI,CAAC2B,GAAG,CAAC;MAC1BO,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,EAAE,GAAGH,CAAC,CAAC;MAEhCD,IAAI,CAACK,IAAI,CAAC;QACRH,IAAI;QACJ7C,QAAQ,EAAE0B,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,CAAC,CAAC,IAAIN,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1D3C,IAAI,EAAEyB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QACvChD,MAAM,EAAEwB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;QAC1C/C,GAAG,EAAEuB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;QACvC1F,UAAU,EAAEkE,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QAC7CC,YAAY,EAAEzB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG;OAChD,CAAC;IACJ;IAEA,OAAO;MACLE,MAAM,EAAE,KAAK;MACbT,IAAI;MACJU,OAAO,EAAE;QACPC,WAAW,EAAE,IAAI,CAAC7F,KAAK,CAACK,cAAc,GAAG,IAAI,CAACL,KAAK,CAACQ,UAAU,GAAG,IAAI,CAACR,KAAK,CAACS,YAAY;QACxFqF,aAAa,EAAE,EAAE;QAAE;QACnBJ,YAAY,EAAE,EAAE;QAChBK,aAAa,EAAE;;KAElB;EACH;;;uCA/LWjG,kBAAkB,EAAA3C,EAAA,CAAA6I,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAlBpG,kBAAkB;MAAAqG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCrBtJ,EANV,CAAAC,cAAA,aAAiC,aAED,aACA,aACD,YACK,kBACG;UAAAD,EAAA,CAAAQ,MAAA,gBAAS;UAAAR,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAQ,MAAA,2BACF;UAAAR,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA8B;UAAAD,EAAA,CAAAQ,MAAA,4DAAqD;UACrFR,EADqF,CAAAG,YAAA,EAAI,EACnF;UAGFH,EAFJ,CAAAC,cAAA,cAA0B,cACA,eACG;UAAAD,EAAA,CAAAQ,MAAA,IAAgB;UAAAR,EAAA,CAAAG,YAAA,EAAO;UAChDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAQ,MAAA,mBAAW;UACtCR,EADsC,CAAAG,YAAA,EAAO,EACvC;UAEJH,EADF,CAAAC,cAAA,cAAwB,eACG;UAAAD,EAAA,CAAAQ,MAAA,IAAiB;UAAAR,EAAA,CAAAG,YAAA,EAAO;UACjDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAQ,MAAA,cAAM;UACjCR,EADiC,CAAAG,YAAA,EAAO,EAClC;UAEJH,EADF,CAAAC,cAAA,cAAwB,eACG;UAAAD,EAAA,CAAAQ,MAAA,IAAoB;UAAAR,EAAA,CAAAG,YAAA,EAAO;UACpDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAQ,MAAA,gBAAQ;UAGvCR,EAHuC,CAAAG,YAAA,EAAO,EACpC,EACF,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAA4B,kBACiE,gBAC/E;UAAAD,EAAA,CAAAQ,MAAA,gBAAQ;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAQ,MAAA,wBACF;UAAAR,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,kBAAiF,gBACrE;UAAAD,EAAA,CAAAQ,MAAA,kBAAU;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAQ,MAAA,sBACF;UAEJR,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAOEH,EAJR,CAAAC,cAAA,eAAwB,oBAC2B,eACjB,eACW,oBACT;UAAAD,EAAA,CAAAQ,MAAA,gBAAQ;UACtCR,EADsC,CAAAG,YAAA,EAAW,EAC3C;UAEJH,EADF,CAAAC,cAAA,eAAwB,oBACgB;UAAAD,EAAA,CAAAQ,MAAA,mBAAW;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC5DH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAQ,MAAA,YAAI;UAElCR,EAFkC,CAAAG,YAAA,EAAO,EACjC,EACF;UAEJH,EADF,CAAAC,cAAA,eAA+B,eACJ;UAAAD,EAAA,CAAAQ,MAAA,IAAsB;UAAAR,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAQ,MAAA,mBAAW;UAAAR,EAAA,CAAAG,YAAA,EAAM;UACzCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAQ,MAAA,4BAAoB;UACpDR,EADoD,CAAAG,YAAA,EAAM,EACpD;UAGFH,EAFJ,CAAAC,cAAA,eAA8B,kBAC0B,gBAC1C;UAAAD,EAAA,CAAAQ,MAAA,WAAG;UAAAR,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAQ,MAAA,kBACF;UAEJR,EAFI,CAAAG,YAAA,EAAS,EACL,EACG;UAKLH,EAHN,CAAAC,cAAA,oBAAqD,eACrB,eACY,oBACV;UAAAD,EAAA,CAAAQ,MAAA,aAAK;UACnCR,EADmC,CAAAG,YAAA,EAAW,EACxC;UAEJH,EADF,CAAAC,cAAA,eAAwB,oBACgB;UAAAD,EAAA,CAAAQ,MAAA,mBAAW;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC5DH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAQ,MAAA,UAAE;UAEhCR,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;UAEJH,EADF,CAAAC,cAAA,eAA+B,eACK;UAAAD,EAAA,CAAAQ,MAAA,IAA0B;UAAAR,EAAA,CAAAG,YAAA,EAAM;UAClEH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAQ,MAAA,uBAAe;UAAAR,EAAA,CAAAG,YAAA,EAAM;UAC7CH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAQ,MAAA,mCAA2B;UAC3DR,EAD2D,CAAAG,YAAA,EAAM,EAC3D;UAGFH,EAFJ,CAAAC,cAAA,eAA8B,kBAC0B,gBAC1C;UAAAD,EAAA,CAAAQ,MAAA,qBAAa;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAQ,MAAA,sBACF;UAEJR,EAFI,CAAAG,YAAA,EAAS,EACL,EACG;UAKLH,EAHN,CAAAC,cAAA,oBAAiD,eACjB,eACW,oBACT;UAAAD,EAAA,CAAAQ,MAAA,eAAO;UACrCR,EADqC,CAAAG,YAAA,EAAW,EAC1C;UAEJH,EADF,CAAAC,cAAA,eAAwB,oBACe;UAAAD,EAAA,CAAAQ,MAAA,qBAAa;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC7DH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAQ,MAAA,SAAC;UAE/BR,EAF+B,CAAAG,YAAA,EAAO,EAC9B,EACF;UAEJH,EADF,CAAAC,cAAA,eAA+B,eACI;UAAAD,EAAA,CAAAQ,MAAA,IAAsB;UAAAR,EAAA,CAAAG,YAAA,EAAM;UAC7DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAQ,MAAA,qBAAa;UAAAR,EAAA,CAAAG,YAAA,EAAM;UAC3CH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAQ,MAAA,gCAAwB;UACxDR,EADwD,CAAAG,YAAA,EAAM,EACxD;UAGFH,EAFJ,CAAAC,cAAA,eAA8B,kBAC4B,gBAC5C;UAAAD,EAAA,CAAAQ,MAAA,mBAAU;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAQ,MAAA,iBACF;UAEJR,EAFI,CAAAG,YAAA,EAAS,EACL,EACG;UAKLH,EAHN,CAAAC,cAAA,qBAAmD,gBACnB,gBACQ,qBACN;UAAAD,EAAA,CAAAQ,MAAA,aAAI;UAClCR,EADkC,CAAAG,YAAA,EAAW,EACvC;UAEJH,EADF,CAAAC,cAAA,gBAAwB,qBACgB;UAAAD,EAAA,CAAAQ,MAAA,sBAAa;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC9DH,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAQ,MAAA,WAAE;UAEhCR,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA+B,gBACC;UAAAD,EAAA,CAAAQ,MAAA,KAAwB;UAAAR,EAAA,CAAAG,YAAA,EAAM;UAC5DH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAQ,MAAA,wBAAe;UAAAR,EAAA,CAAAG,YAAA,EAAM;UAC7CH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAQ,MAAA,+BAAsB;UACtDR,EADsD,CAAAG,YAAA,EAAM,EACtD;UAGFH,EAFJ,CAAAC,cAAA,gBAA8B,mBACe,iBAC/B;UAAAD,EAAA,CAAAQ,MAAA,kBAAS;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAQ,MAAA,oBACF;UAGNR,EAHM,CAAAG,YAAA,EAAS,EACL,EACG,EACP;UAGNH,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAE,SAAA,uCAM6B;UAC/BF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAkB,UAAA,MAAAsI,mCAAA,kBAAmD;UASnDxJ,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAE,SAAA,0CAIgC;UAClCF,EAAA,CAAAG,YAAA,EAAM;UAKFH,EAFJ,CAAAC,cAAA,qBAAoC,wBACjB,uBACC;UAAAD,EAAA,CAAAQ,MAAA,qBAAY;UAAAR,EAAA,CAAAG,YAAA,EAAiB;UAC7CH,EAAA,CAAAC,cAAA,0BAAmB;UAAAD,EAAA,CAAAQ,MAAA,qCAA4B;UACjDR,EADiD,CAAAG,YAAA,EAAoB,EACnD;UAGdH,EAFJ,CAAAC,cAAA,yBAAkB,gBACa,kBACqC;UAE9DD,EAAA,CAAAyJ,uBAAA,SAAgC;UAE9BzJ,EADA,CAAAkB,UAAA,MAAAwI,kCAAA,iBAAsC,MAAAC,kCAAA,iBACF;;UAMtC3J,EAAA,CAAAyJ,uBAAA,SAAqC;UAEnCzJ,EADA,CAAAkB,UAAA,MAAA0I,kCAAA,iBAAsC,MAAAC,kCAAA,iBACF;;UAStC7J,EAAA,CAAAyJ,uBAAA,SAAoC;UAElCzJ,EADA,CAAAkB,UAAA,MAAA4I,kCAAA,iBAAsC,MAAAC,kCAAA,iBACF;;UAUtC/J,EAAA,CAAAyJ,uBAAA,SAAoC;UAElCzJ,EADA,CAAAkB,UAAA,MAAA8I,kCAAA,iBAAsC,MAAAC,kCAAA,iBACF;;UAStCjK,EAAA,CAAAyJ,uBAAA,SAAoC;UAElCzJ,EADA,CAAAkB,UAAA,MAAAgJ,kCAAA,iBAAsC,MAAAC,kCAAA,iBACF;;UAmBtCnK,EAAA,CAAAyJ,uBAAA,SAAkC;UAEhCzJ,EADA,CAAAkB,UAAA,MAAAkJ,kCAAA,iBAAsC,MAAAC,kCAAA,iBACF;;UAStCrK,EAAA,CAAAyJ,uBAAA,SAAqC;UAEnCzJ,EADA,CAAAkB,UAAA,MAAAoJ,kCAAA,iBAAsC,MAAAC,kCAAA,iBACF;;UAsBtCvK,EADA,CAAAkB,UAAA,MAAAsJ,kCAAA,iBAAuD,MAAAC,kCAAA,iBACM;UAIrEzK,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,qBAAqC,wBAClB,uBACC;UAAAD,EAAA,CAAAQ,MAAA,sBAAa;UAC/BR,EAD+B,CAAAG,YAAA,EAAiB,EAC9B;UAIZH,EAHN,CAAAC,cAAA,yBAAkB,gBACU,mBACqC,iBACjD;UAAAD,EAAA,CAAAQ,MAAA,iBAAQ;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAQ,MAAA,yBACF;UAAAR,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAiE,iBACrD;UAAAD,EAAA,CAAAQ,MAAA,kBAAS;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAQ,MAAA,6BACF;UAAAR,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAgD,iBACpC;UAAAD,EAAA,CAAAQ,MAAA,mBAAU;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAQ,MAAA,0BACF;UAAAR,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAiD,iBACrC;UAAAD,EAAA,CAAAQ,MAAA,iBAAQ;UAAAR,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAQ,MAAA,mBACF;UAIRR,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;UAjS6BH,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAa,iBAAA,CAAA0I,GAAA,CAAA3G,UAAA,CAAgB;UAIhB5C,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAa,iBAAA,CAAA0I,GAAA,CAAAzG,WAAA,CAAiB;UAIjB9C,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAa,iBAAA,CAAA0I,GAAA,CAAArG,cAAA,CAAoB;UA8BtBlD,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAa,iBAAA,CAAA0I,GAAA,CAAA1G,KAAA,CAAAD,UAAA,CAAsB;UAuBb5C,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAa,iBAAA,CAAA0I,GAAA,CAAA1G,KAAA,CAAAK,cAAA,CAA0B;UAuB3BlD,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAa,iBAAA,CAAA0I,GAAA,CAAA1G,KAAA,CAAAQ,UAAA,CAAsB;UAuBzBrD,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAa,iBAAA,CAAA0I,GAAA,CAAA1G,KAAA,CAAAS,YAAA,CAAwB;UAiBxDtD,EAAA,CAAAI,SAAA,IAAiC;UAGjCJ,EAHA,CAAAK,UAAA,SAAAkJ,GAAA,CAAA7C,sBAAA,GAAiC,yBACT,oBACL,kBACF;UAKO1G,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAAhJ,aAAA,GAAqB;UAW7CP,EAAA,CAAAI,SAAA,GAAsC;UAEtCJ,EAFA,CAAAK,UAAA,eAAAkJ,GAAA,CAAAzB,qBAAA,GAAsC,wBACf,sBACF;UAYF9H,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAK,UAAA,eAAAkJ,GAAA,CAAAxG,WAAA,CAA0B;UAmGrB/C,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAK,UAAA,oBAAAkJ,GAAA,CAAA/F,gBAAA,CAAiC;UACpBxD,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAK,UAAA,qBAAAkJ,GAAA,CAAA/F,gBAAA,CAA0B;;;qBD1PjErE,YAAY,EAAAuL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ1L,aAAa,EAAA2L,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,YAAA,EACb/L,aAAa,EAAAgM,EAAA,CAAAC,OAAA,EACbhM,eAAe,EAAAiM,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACflM,cAAc,EAAAmM,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACd5M,cAAc,EAAA6M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA,EACd9M,oBAAoB,EACpBC,gBAAgB,EAAA8M,EAAA,CAAAC,UAAA,EAChB9M,YAAY,EAAA+M,EAAA,CAAAC,UAAA,EACZ9M,6BAA6B,EAC7BC,0BAA0B,EAC1BC,gCAAgC;MAAA6M,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}