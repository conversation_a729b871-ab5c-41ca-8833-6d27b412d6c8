{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { SEVERITY_COLORS } from '../../models/security.models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/table\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/router\";\nconst _c0 = a0 => [\"/scan\", a0];\nfunction DashboardComponent_th_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Scan ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(scan_r1.id);\n  }\n}\nfunction DashboardComponent_th_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getProjectName(scan_r2.project_path));\n  }\n}\nfunction DashboardComponent_th_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Chains\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_82_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chain_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(chain_r4);\n  }\n}\nfunction DashboardComponent_td_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50)(1, \"mat-chip-listbox\");\n    i0.ɵɵtemplate(2, DashboardComponent_td_82_mat_chip_2_Template, 2, 1, \"mat-chip\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", scan_r5.chains);\n  }\n}\nfunction DashboardComponent_th_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + scan_r6.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(scan_r6.status);\n  }\n}\nfunction DashboardComponent_th_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate((scan_r7.issues == null ? null : scan_r7.issues.length) || 0);\n  }\n}\nfunction DashboardComponent_th_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scan_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(scan_r8.created_at));\n  }\n}\nfunction DashboardComponent_th_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_td_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50)(1, \"button\", 52)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, scan_r9.id));\n  }\n}\nfunction DashboardComponent_tr_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 53);\n  }\n}\nfunction DashboardComponent_tr_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 54);\n  }\n}\nfunction DashboardComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"mat-chip\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 57)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 58)(12, \"mat-chip\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const issue_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getSeverityColor(issue_r10.severity));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", issue_r10.severity, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r10.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", issue_r10.file, \":\", issue_r10.line, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.chain);\n  }\n}\nexport class DashboardComponent {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.stats = {\n      totalScans: 0,\n      criticalIssues: 0,\n      highIssues: 0,\n      mediumIssues: 0\n    };\n    this.recentScans = [];\n    this.topIssues = [];\n    this.displayedColumns = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: response => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: error => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n  loadMockData() {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts['critical'] || 0,\n      highIssues: mockScan.severity_counts['high'] || 0,\n      mediumIssues: mockScan.severity_counts['medium'] || 0\n    };\n  }\n  calculateStats(scans) {\n    this.stats.totalScans = scans.length;\n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues = [];\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.['critical'] || 0;\n      highTotal += scan.severity_counts?.['high'] || 0;\n      mediumTotal += scan.severity_counts?.['medium'] || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n    // Get top 5 most severe issues\n    this.topIssues = allIssues.sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity)).slice(0, 5);\n  }\n  getSeverityWeight(severity) {\n    const weights = {\n      critical: 4,\n      high: 3,\n      medium: 2,\n      low: 1,\n      info: 0\n    };\n    return weights[severity] || 0;\n  }\n  getSeverityColor(severity) {\n    return SEVERITY_COLORS[severity] || '#666';\n  }\n  getProjectName(path) {\n    return path.split('/').pop() || path;\n  }\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleDateString();\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 126,\n      vars: 8,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"dashboard-title\"], [1, \"title-icon\"], [1, \"dashboard-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\", 1, \"action-button\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-scans\"], [1, \"stat-card-content\"], [1, \"stat-icon-wrapper\"], [1, \"stat-icon\"], [1, \"stat-details\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-trend\", \"positive\"], [1, \"stat-card\", \"critical-issues\"], [1, \"stat-icon-wrapper\", \"critical\"], [1, \"stat-trend\", \"negative\"], [1, \"stat-card\", \"high-issues\"], [1, \"stat-icon-wrapper\", \"high\"], [1, \"stat-trend\", \"neutral\"], [1, \"stat-card\", \"medium-issues\"], [1, \"stat-icon-wrapper\", \"medium\"], [1, \"recent-scans-card\"], [\"mat-button\", \"\", \"routerLink\", \"/scan\", \"color\", \"primary\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"scans-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"project\"], [\"matColumnDef\", \"chains\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [1, \"top-issues-card\"], [1, \"issues-list\"], [\"class\", \"issue-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"quick-actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/checklist\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/reports\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/settings\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"mat-icon-button\", \"\", 3, \"routerLink\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"issue-item\"], [1, \"issue-severity\"], [1, \"issue-details\"], [1, \"issue-chain\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Security Dashboard \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 5);\n          i0.ɵɵtext(8, \"Monitor your blockchain security posture\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"button\", 7)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" Start New Scan \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"mat-card\", 9)(16, \"div\", 10)(17, \"div\", 11)(18, \"mat-icon\", 12);\n          i0.ɵɵtext(19, \"security\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"div\", 14);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 15);\n          i0.ɵɵtext(24, \"Total Scans\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 16);\n          i0.ɵɵtext(26, \"+12% this month\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"mat-card\", 17)(28, \"div\", 10)(29, \"div\", 18)(30, \"mat-icon\", 12);\n          i0.ɵɵtext(31, \"error\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 13)(33, \"div\", 14);\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 15);\n          i0.ɵɵtext(36, \"Critical Issues\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 19);\n          i0.ɵɵtext(38, \"-5% this week\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(39, \"mat-card\", 20)(40, \"div\", 10)(41, \"div\", 21)(42, \"mat-icon\", 12);\n          i0.ɵɵtext(43, \"warning\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 13)(45, \"div\", 14);\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 15);\n          i0.ɵɵtext(48, \"High Issues\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 22);\n          i0.ɵɵtext(50, \"No change\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"mat-card\", 23)(52, \"div\", 10)(53, \"div\", 24)(54, \"mat-icon\", 12);\n          i0.ɵɵtext(55, \"info\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"div\", 13)(57, \"div\", 14);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 15);\n          i0.ɵɵtext(60, \"Medium Issues\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 16);\n          i0.ɵɵtext(62, \"-8% this week\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(63, \"mat-card\", 25)(64, \"mat-card-header\")(65, \"mat-card-title\");\n          i0.ɵɵtext(66, \"Recent Scans\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"button\", 26)(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" New Scan \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"mat-card-content\")(72, \"div\", 27)(73, \"table\", 28);\n          i0.ɵɵelementContainerStart(74, 29);\n          i0.ɵɵtemplate(75, DashboardComponent_th_75_Template, 2, 0, \"th\", 30)(76, DashboardComponent_td_76_Template, 2, 1, \"td\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(77, 32);\n          i0.ɵɵtemplate(78, DashboardComponent_th_78_Template, 2, 0, \"th\", 30)(79, DashboardComponent_td_79_Template, 2, 1, \"td\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(80, 33);\n          i0.ɵɵtemplate(81, DashboardComponent_th_81_Template, 2, 0, \"th\", 30)(82, DashboardComponent_td_82_Template, 3, 1, \"td\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(83, 34);\n          i0.ɵɵtemplate(84, DashboardComponent_th_84_Template, 2, 0, \"th\", 30)(85, DashboardComponent_td_85_Template, 3, 3, \"td\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(86, 35);\n          i0.ɵɵtemplate(87, DashboardComponent_th_87_Template, 2, 0, \"th\", 30)(88, DashboardComponent_td_88_Template, 2, 1, \"td\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(89, 36);\n          i0.ɵɵtemplate(90, DashboardComponent_th_90_Template, 2, 0, \"th\", 30)(91, DashboardComponent_td_91_Template, 2, 1, \"td\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(92, 37);\n          i0.ɵɵtemplate(93, DashboardComponent_th_93_Template, 2, 0, \"th\", 30)(94, DashboardComponent_td_94_Template, 4, 3, \"td\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(95, DashboardComponent_tr_95_Template, 1, 0, \"tr\", 38)(96, DashboardComponent_tr_96_Template, 1, 0, \"tr\", 39);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(97, \"mat-card\", 40)(98, \"mat-card-header\")(99, \"mat-card-title\");\n          i0.ɵɵtext(100, \"Top Security Issues\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"mat-card-content\")(102, \"div\", 41);\n          i0.ɵɵtemplate(103, DashboardComponent_div_103_Template, 14, 8, \"div\", 42);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(104, \"mat-card\", 43)(105, \"mat-card-header\")(106, \"mat-card-title\");\n          i0.ɵɵtext(107, \"Quick Actions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(108, \"mat-card-content\")(109, \"div\", 44)(110, \"button\", 45)(111, \"mat-icon\");\n          i0.ɵɵtext(112, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(113, \" Start New Scan \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"button\", 46)(115, \"mat-icon\");\n          i0.ɵɵtext(116, \"checklist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(117, \" Security Checklist \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"button\", 47)(119, \"mat-icon\");\n          i0.ɵɵtext(120, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(121, \" Generate Report \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"button\", 48)(123, \"mat-icon\");\n          i0.ɵɵtext(124, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(125, \" Settings \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.stats.totalScans);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.criticalIssues);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.highIssues);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(ctx.stats.mediumIssues);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"dataSource\", ctx.recentScans);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topIssues);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, i5.MatIconButton, MatTableModule, i6.MatTable, i6.MatHeaderCellDef, i6.MatHeaderRowDef, i6.MatColumnDef, i6.MatCellDef, i6.MatRowDef, i6.MatHeaderCell, i6.MatCell, i6.MatHeaderRow, i6.MatRow, MatChipsModule, i7.MatChip, i7.MatChipListbox, MatProgressBarModule, RouterModule, i8.RouterLink],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  color: #333;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  margin-bottom: 10px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  margin-right: 10px;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: bold;\\n  color: #333;\\n}\\n\\n.stat-card.critical[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.stat-card.high[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n.stat-card.medium[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  color: #ffeb3b;\\n}\\n\\n.recent-scans-card[_ngcontent-%COMP%], \\n.top-issues-card[_ngcontent-%COMP%], \\n.quick-actions-card[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.recent-scans-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.scans-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n  color: white;\\n}\\n\\n.status-running[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n  color: white;\\n}\\n\\n.status-failed[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  color: white;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n}\\n\\n.issues-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.issue-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.issue-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 12px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 60px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatTableModule", "MatChipsModule", "MatProgressBarModule", "RouterModule", "SEVERITY_COLORS", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "scan_r1", "id", "ctx_r2", "getProjectName", "scan_r2", "project_path", "chain_r4", "ɵɵtemplate", "DashboardComponent_td_82_mat_chip_2_Template", "ɵɵproperty", "scan_r5", "chains", "ɵɵclassMap", "scan_r6", "status", "scan_r7", "issues", "length", "formatDate", "scan_r8", "created_at", "ɵɵpureFunction1", "_c0", "scan_r9", "ɵɵelement", "ɵɵstyleProp", "getSeverityColor", "issue_r10", "severity", "ɵɵtextInterpolate1", "title", "description", "ɵɵtextInterpolate2", "file", "line", "chain", "DashboardComponent", "constructor", "apiService", "stats", "totalScans", "criticalIssues", "highIssues", "mediumIssues", "recentScans", "topIssues", "displayedColumns", "ngOnInit", "loadDashboardData", "getScanHistory", "subscribe", "next", "response", "scans", "slice", "calculateStats", "error", "console", "loadMockData", "mockScan", "generateMockScanResult", "severity_counts", "criticalTotal", "highTotal", "mediumTotal", "allIssues", "for<PERSON>ach", "scan", "concat", "sort", "a", "b", "getSeverityWeight", "weights", "critical", "high", "medium", "low", "info", "path", "split", "pop", "dateString", "Date", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "ApiService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "DashboardComponent_th_75_Template", "DashboardComponent_td_76_Template", "DashboardComponent_th_78_Template", "DashboardComponent_td_79_Template", "DashboardComponent_th_81_Template", "DashboardComponent_td_82_Template", "DashboardComponent_th_84_Template", "DashboardComponent_td_85_Template", "DashboardComponent_th_87_Template", "DashboardComponent_td_88_Template", "DashboardComponent_th_90_Template", "DashboardComponent_td_91_Template", "DashboardComponent_th_93_Template", "DashboardComponent_td_94_Template", "DashboardComponent_tr_95_Template", "DashboardComponent_tr_96_Template", "DashboardComponent_div_103_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i4", "MatIcon", "i5", "MatButton", "MatIconButton", "i6", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i7", "MatChip", "MatChipListbox", "i8", "RouterLink", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { ApiService } from '../../services/api.service';\nimport { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatTableModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    RouterModule\n  ],\n  template: `\n    <div class=\"dashboard-container\">\n      <!-- Header Section -->\n      <div class=\"dashboard-header\">\n        <div class=\"header-content\">\n          <h1 class=\"dashboard-title\">\n            <mat-icon class=\"title-icon\">dashboard</mat-icon>\n            Security Dashboard\n          </h1>\n          <p class=\"dashboard-subtitle\">Monitor your blockchain security posture</p>\n        </div>\n        <div class=\"header-actions\">\n          <button mat-raised-button color=\"primary\" routerLink=\"/scan\" class=\"action-button\">\n            <mat-icon>security</mat-icon>\n            Start New Scan\n          </button>\n        </div>\n      </div>\n\n      <!-- Stats Cards -->\n      <div class=\"stats-grid\">\n        <mat-card class=\"stat-card total-scans\">\n          <div class=\"stat-card-content\">\n            <div class=\"stat-icon-wrapper\">\n              <mat-icon class=\"stat-icon\">security</mat-icon>\n            </div>\n            <div class=\"stat-details\">\n              <div class=\"stat-number\">{{ stats.totalScans }}</div>\n              <div class=\"stat-label\">Total Scans</div>\n              <div class=\"stat-trend positive\">+12% this month</div>\n            </div>\n          </div>\n        </mat-card>\n\n        <mat-card class=\"stat-card critical-issues\">\n          <div class=\"stat-card-content\">\n            <div class=\"stat-icon-wrapper critical\">\n              <mat-icon class=\"stat-icon\">error</mat-icon>\n            </div>\n            <div class=\"stat-details\">\n              <div class=\"stat-number\">{{ stats.criticalIssues }}</div>\n              <div class=\"stat-label\">Critical Issues</div>\n              <div class=\"stat-trend negative\">-5% this week</div>\n            </div>\n          </div>\n        </mat-card>\n\n        <mat-card class=\"stat-card high-issues\">\n          <div class=\"stat-card-content\">\n            <div class=\"stat-icon-wrapper high\">\n              <mat-icon class=\"stat-icon\">warning</mat-icon>\n            </div>\n            <div class=\"stat-details\">\n              <div class=\"stat-number\">{{ stats.highIssues }}</div>\n              <div class=\"stat-label\">High Issues</div>\n              <div class=\"stat-trend neutral\">No change</div>\n            </div>\n          </div>\n        </mat-card>\n\n        <mat-card class=\"stat-card medium-issues\">\n          <div class=\"stat-card-content\">\n            <div class=\"stat-icon-wrapper medium\">\n              <mat-icon class=\"stat-icon\">info</mat-icon>\n            </div>\n            <div class=\"stat-details\">\n              <div class=\"stat-number\">{{ stats.mediumIssues }}</div>\n              <div class=\"stat-label\">Medium Issues</div>\n              <div class=\"stat-trend positive\">-8% this week</div>\n            </div>\n          </div>\n        </mat-card>\n      </div>\n\n      <!-- Recent Scans -->\n      <mat-card class=\"recent-scans-card\">\n        <mat-card-header>\n          <mat-card-title>Recent Scans</mat-card-title>\n          <button mat-button routerLink=\"/scan\" color=\"primary\">\n            <mat-icon>add</mat-icon>\n            New Scan\n          </button>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"table-container\">\n            <table mat-table [dataSource]=\"recentScans\" class=\"scans-table\">\n              <ng-container matColumnDef=\"id\">\n                <th mat-header-cell *matHeaderCellDef>Scan ID</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ scan.id }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"project\">\n                <th mat-header-cell *matHeaderCellDef>Project</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ getProjectName(scan.project_path) }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"chains\">\n                <th mat-header-cell *matHeaderCellDef>Chains</th>\n                <td mat-cell *matCellDef=\"let scan\">\n                  <mat-chip-listbox>\n                    <mat-chip *ngFor=\"let chain of scan.chains\">{{ chain }}</mat-chip>\n                  </mat-chip-listbox>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"status\">\n                <th mat-header-cell *matHeaderCellDef>Status</th>\n                <td mat-cell *matCellDef=\"let scan\">\n                  <mat-chip [class]=\"'status-' + scan.status\">{{ scan.status }}</mat-chip>\n                </td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"issues\">\n                <th mat-header-cell *matHeaderCellDef>Issues</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ scan.issues?.length || 0 }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"date\">\n                <th mat-header-cell *matHeaderCellDef>Date</th>\n                <td mat-cell *matCellDef=\"let scan\">{{ formatDate(scan.created_at) }}</td>\n              </ng-container>\n\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let scan\">\n                  <button mat-icon-button [routerLink]=\"['/scan', scan.id]\">\n                    <mat-icon>visibility</mat-icon>\n                  </button>\n                </td>\n              </ng-container>\n\n              <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n            </table>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Top Issues -->\n      <mat-card class=\"top-issues-card\">\n        <mat-card-header>\n          <mat-card-title>Top Security Issues</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"issues-list\">\n            <div *ngFor=\"let issue of topIssues\" class=\"issue-item\">\n              <div class=\"issue-severity\">\n                <mat-chip [style.background-color]=\"getSeverityColor(issue.severity)\">\n                  {{ issue.severity }}\n                </mat-chip>\n              </div>\n              <div class=\"issue-details\">\n                <h4>{{ issue.title }}</h4>\n                <p>{{ issue.description }}</p>\n                <small>{{ issue.file }}:{{ issue.line }}</small>\n              </div>\n              <div class=\"issue-chain\">\n                <mat-chip>{{ issue.chain }}</mat-chip>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Quick Actions -->\n      <mat-card class=\"quick-actions-card\">\n        <mat-card-header>\n          <mat-card-title>Quick Actions</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"actions-grid\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/scan\">\n              <mat-icon>security</mat-icon>\n              Start New Scan\n            </button>\n            <button mat-raised-button color=\"accent\" routerLink=\"/checklist\">\n              <mat-icon>checklist</mat-icon>\n              Security Checklist\n            </button>\n            <button mat-raised-button routerLink=\"/reports\">\n              <mat-icon>assessment</mat-icon>\n              Generate Report\n            </button>\n            <button mat-raised-button routerLink=\"/settings\">\n              <mat-icon>settings</mat-icon>\n              Settings\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .dashboard-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    h1 {\n      margin-bottom: 30px;\n      color: #333;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n\n    .stat-card {\n      text-align: center;\n    }\n\n    .stat-card mat-card-header {\n      justify-content: center;\n      margin-bottom: 10px;\n    }\n\n    .stat-card mat-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n      margin-right: 10px;\n    }\n\n    .stat-number {\n      font-size: 48px;\n      font-weight: bold;\n      color: #333;\n    }\n\n    .stat-card.critical .stat-number {\n      color: #f44336;\n    }\n\n    .stat-card.high .stat-number {\n      color: #ff9800;\n    }\n\n    .stat-card.medium .stat-number {\n      color: #ffeb3b;\n    }\n\n    .recent-scans-card,\n    .top-issues-card,\n    .quick-actions-card {\n      margin-bottom: 30px;\n    }\n\n    .recent-scans-card mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .scans-table {\n      width: 100%;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-running {\n      background-color: #2196f3;\n      color: white;\n    }\n\n    .status-failed {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .status-pending {\n      background-color: #ff9800;\n      color: white;\n    }\n\n    .issues-list {\n      display: flex;\n      flex-direction: column;\n      gap: 15px;\n    }\n\n    .issue-item {\n      display: flex;\n      align-items: center;\n      gap: 15px;\n      padding: 15px;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .issue-details {\n      flex: 1;\n    }\n\n    .issue-details h4 {\n      margin: 0 0 5px 0;\n      color: #333;\n    }\n\n    .issue-details p {\n      margin: 0 0 5px 0;\n      color: #666;\n      font-size: 14px;\n    }\n\n    .issue-details small {\n      color: #999;\n      font-size: 12px;\n    }\n\n    .actions-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n    }\n\n    .actions-grid button {\n      height: 60px;\n      display: flex;\n      flex-direction: column;\n      gap: 5px;\n    }\n\n    .actions-grid mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n  `]\n})\nexport class DashboardComponent implements OnInit {\n  stats = {\n    totalScans: 0,\n    criticalIssues: 0,\n    highIssues: 0,\n    mediumIssues: 0\n  };\n\n  recentScans: ScanResult[] = [];\n  topIssues: SecurityIssue[] = [];\n  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n\n  constructor(private apiService: ApiService) {}\n\n  ngOnInit(): void {\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: (response) => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: (error) => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n\n  loadMockData(): void {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts['critical'] || 0,\n      highIssues: mockScan.severity_counts['high'] || 0,\n      mediumIssues: mockScan.severity_counts['medium'] || 0\n    };\n  }\n\n  calculateStats(scans: ScanResult[]): void {\n    this.stats.totalScans = scans.length;\n    \n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues: SecurityIssue[] = [];\n\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.['critical'] || 0;\n      highTotal += scan.severity_counts?.['high'] || 0;\n      mediumTotal += scan.severity_counts?.['medium'] || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n\n    // Get top 5 most severe issues\n    this.topIssues = allIssues\n      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))\n      .slice(0, 5);\n  }\n\n  getSeverityWeight(severity: string): number {\n    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };\n    return weights[severity as keyof typeof weights] || 0;\n  }\n\n  getSeverityColor(severity: string): string {\n    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';\n  }\n\n  getProjectName(path: string): string {\n    return path.split('/').pop() || path;\n  }\n\n  formatDate(dateString: string): string {\n    return new Date(dateString).toLocaleDateString();\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAAoCC,eAAe,QAAQ,8BAA8B;;;;;;;;;;;;;IAsGzEC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,EAAA,CAAa;;;;;IAIjDP,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAA5CH,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAC,cAAA,CAAAC,OAAA,CAAAC,YAAA,EAAuC;;;;;IAI3EX,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAAtBH,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAO,QAAA,CAAW;;;;;IADzDZ,EADF,CAAAC,cAAA,aAAoC,uBAChB;IAChBD,EAAA,CAAAa,UAAA,IAAAC,4CAAA,uBAA4C;IAEhDd,EADE,CAAAG,YAAA,EAAmB,EAChB;;;;IAF2BH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAe,UAAA,YAAAC,OAAA,CAAAC,MAAA,CAAc;;;;;IAM9CjB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAoC,eACU;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC/DF,EAD+D,CAAAG,YAAA,EAAW,EACrE;;;;IADOH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAkB,UAAA,aAAAC,OAAA,CAAAC,MAAA,CAAiC;IAACpB,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAc,OAAA,CAAAC,MAAA,CAAiB;;;;;IAK/DpB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACjDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAnCH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,EAAAgB,OAAA,CAAAC,MAAA,kBAAAD,OAAA,CAAAC,MAAA,CAAAC,MAAA,OAA8B;;;;;IAIlEvB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC/CH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAG,MAAA,CAAAgB,UAAA,CAAAC,OAAA,CAAAC,UAAA,EAAiC;;;;;IAIrE1B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG9CH,EAFJ,CAAAC,cAAA,aAAoC,iBACwB,eAC9C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAExBF,EAFwB,CAAAG,YAAA,EAAW,EACxB,EACN;;;;IAHqBH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAA2B,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAtB,EAAA,EAAiC;;;;;IAM7DP,EAAA,CAAA8B,SAAA,aAA4D;;;;;IAC5D9B,EAAA,CAAA8B,SAAA,aAAkE;;;;;IAehE9B,EAFJ,CAAAC,cAAA,cAAwD,cAC1B,eAC4C;IACpED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9BH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAC1CF,EAD0C,CAAAG,YAAA,EAAQ,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACb;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAE/BF,EAF+B,CAAAG,YAAA,EAAW,EAClC,EACF;;;;;IAZQH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAA+B,WAAA,qBAAAvB,MAAA,CAAAwB,gBAAA,CAAAC,SAAA,CAAAC,QAAA,EAA2D;IACnElC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAmC,kBAAA,MAAAF,SAAA,CAAAC,QAAA,MACF;IAGIlC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA4B,SAAA,CAAAG,KAAA,CAAiB;IAClBpC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAA4B,SAAA,CAAAI,WAAA,CAAuB;IACnBrC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAsC,kBAAA,KAAAL,SAAA,CAAAM,IAAA,OAAAN,SAAA,CAAAO,IAAA,KAAiC;IAG9BxC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA4B,SAAA,CAAAQ,KAAA,CAAiB;;;AAuL3C,OAAM,MAAOC,kBAAkB;EAY7BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAX9B,KAAAC,KAAK,GAAG;MACNC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE;KACf;IAED,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,SAAS,GAAoB,EAAE;IAC/B,KAAAC,gBAAgB,GAAa,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;EAElD;EAE7CC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACV,UAAU,CAACW,cAAc,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACR,WAAW,GAAGQ,QAAQ,CAACC,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAACC,cAAc,CAACH,QAAQ,CAACC,KAAK,CAAC;MACrC,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACA,IAAI,CAACE,YAAY,EAAE;MACrB;KACD,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACrB,UAAU,CAACsB,sBAAsB,EAAE;IACzD,IAAI,CAAChB,WAAW,GAAG,CAACe,QAAQ,CAAC;IAC7B,IAAI,CAACd,SAAS,GAAGc,QAAQ,CAAC3C,MAAM;IAChC,IAAI,CAACuB,KAAK,GAAG;MACXC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAEkB,QAAQ,CAACE,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;MACzDnB,UAAU,EAAEiB,QAAQ,CAACE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;MACjDlB,YAAY,EAAEgB,QAAQ,CAACE,eAAe,CAAC,QAAQ,CAAC,IAAI;KACrD;EACH;EAEAN,cAAcA,CAACF,KAAmB;IAChC,IAAI,CAACd,KAAK,CAACC,UAAU,GAAGa,KAAK,CAACpC,MAAM;IAEpC,IAAI6C,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,SAAS,GAAoB,EAAE;IAEnCZ,KAAK,CAACa,OAAO,CAACC,IAAI,IAAG;MACnBL,aAAa,IAAIK,IAAI,CAACN,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC;MACxDE,SAAS,IAAII,IAAI,CAACN,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC;MAChDG,WAAW,IAAIG,IAAI,CAACN,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;MACpDI,SAAS,GAAGA,SAAS,CAACG,MAAM,CAACD,IAAI,CAACnD,MAAM,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC;IAEF,IAAI,CAACuB,KAAK,CAACE,cAAc,GAAGqB,aAAa;IACzC,IAAI,CAACvB,KAAK,CAACG,UAAU,GAAGqB,SAAS;IACjC,IAAI,CAACxB,KAAK,CAACI,YAAY,GAAGqB,WAAW;IAErC;IACA,IAAI,CAACnB,SAAS,GAAGoB,SAAS,CACvBI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAAC3C,QAAQ,CAAC,GAAG,IAAI,CAAC4C,iBAAiB,CAACF,CAAC,CAAC1C,QAAQ,CAAC,CAAC,CACvF0B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEAkB,iBAAiBA,CAAC5C,QAAgB;IAChC,MAAM6C,OAAO,GAAG;MAAEC,QAAQ,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAC,CAAE;IACpE,OAAOL,OAAO,CAAC7C,QAAgC,CAAC,IAAI,CAAC;EACvD;EAEAF,gBAAgBA,CAACE,QAAgB;IAC/B,OAAOnC,eAAe,CAACmC,QAAwC,CAAC,IAAI,MAAM;EAC5E;EAEAzB,cAAcA,CAAC4E,IAAY;IACzB,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIF,IAAI;EACtC;EAEA7D,UAAUA,CAACgE,UAAkB;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,EAAE;EAClD;;;uCAtFWhD,kBAAkB,EAAA1C,EAAA,CAAA2F,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAlBnD,kBAAkB;MAAAoD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9UnBpG,EALR,CAAAC,cAAA,aAAiC,aAED,aACA,YACE,kBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA8B;UAAAD,EAAA,CAAAE,MAAA,+CAAwC;UACxEF,EADwE,CAAAG,YAAA,EAAI,EACtE;UAGFH,EAFJ,CAAAC,cAAA,aAA4B,iBACyD,gBACvE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,wBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAOEH,EAJR,CAAAC,cAAA,cAAwB,mBACkB,eACP,eACE,oBACD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACtCF,EADsC,CAAAG,YAAA,EAAW,EAC3C;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACC;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzCH,EAAA,CAAAC,cAAA,eAAiC;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAGtDF,EAHsD,CAAAG,YAAA,EAAM,EAClD,EACF,EACG;UAKLH,EAHN,CAAAC,cAAA,oBAA4C,eACX,eACW,oBACV;UAAAD,EAAA,CAAAE,MAAA,aAAK;UACnCF,EADmC,CAAAG,YAAA,EAAW,EACxC;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACC;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7CH,EAAA,CAAAC,cAAA,eAAiC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAGpDF,EAHoD,CAAAG,YAAA,EAAM,EAChD,EACF,EACG;UAKLH,EAHN,CAAAC,cAAA,oBAAwC,eACP,eACO,oBACN;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACrCF,EADqC,CAAAG,YAAA,EAAW,EAC1C;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACC;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzCH,EAAA,CAAAC,cAAA,eAAgC;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAG/CF,EAH+C,CAAAG,YAAA,EAAM,EAC3C,EACF,EACG;UAKLH,EAHN,CAAAC,cAAA,oBAA0C,eACT,eACS,oBACR;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACC;UAAAD,EAAA,CAAAE,MAAA,IAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACvDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3CH,EAAA,CAAAC,cAAA,eAAiC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAItDF,EAJsD,CAAAG,YAAA,EAAM,EAChD,EACF,EACG,EACP;UAKFH,EAFJ,CAAAC,cAAA,oBAAoC,uBACjB,sBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAE3CH,EADF,CAAAC,cAAA,kBAAsD,gBAC1C;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,kBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACO;UAGdH,EAFJ,CAAAC,cAAA,wBAAkB,eACa,iBACqC;UAC9DD,EAAA,CAAAsG,uBAAA,QAAgC;UAE9BtG,EADA,CAAAa,UAAA,KAAA0F,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtCxG,EAAA,CAAAsG,uBAAA,QAAqC;UAEnCtG,EADA,CAAAa,UAAA,KAAA4F,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtC1G,EAAA,CAAAsG,uBAAA,QAAoC;UAElCtG,EADA,CAAAa,UAAA,KAAA8F,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAOtC5G,EAAA,CAAAsG,uBAAA,QAAoC;UAElCtG,EADA,CAAAa,UAAA,KAAAgG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAKtC9G,EAAA,CAAAsG,uBAAA,QAAoC;UAElCtG,EADA,CAAAa,UAAA,KAAAkG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtChH,EAAA,CAAAsG,uBAAA,QAAkC;UAEhCtG,EADA,CAAAa,UAAA,KAAAoG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAGtClH,EAAA,CAAAsG,uBAAA,QAAqC;UAEnCtG,EADA,CAAAa,UAAA,KAAAsG,iCAAA,iBAAsC,KAAAC,iCAAA,iBACF;;UAQtCpH,EADA,CAAAa,UAAA,KAAAwG,iCAAA,iBAAuD,KAAAC,iCAAA,iBACM;UAIrEtH,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,oBAAkC,uBACf,sBACC;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;UAEhBH,EADF,CAAAC,cAAA,yBAAkB,gBACS;UACvBD,EAAA,CAAAa,UAAA,MAAA0G,mCAAA,mBAAwD;UAiB9DvH,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,qBAAqC,wBAClB,uBACC;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAC/BF,EAD+B,CAAAG,YAAA,EAAiB,EAC9B;UAIZH,EAHN,CAAAC,cAAA,yBAAkB,gBACU,mBACqC,iBACjD;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,yBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAiE,iBACrD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAgD,iBACpC;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAiD,iBACrC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,mBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;UAnK6BH,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAC,UAAA,CAAsB;UAatB9C,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAE,cAAA,CAA0B;UAa1B/C,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAG,UAAA,CAAsB;UAatBhD,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAK,iBAAA,CAAAgG,GAAA,CAAAxD,KAAA,CAAAI,YAAA,CAAwB;UAmBlCjD,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAe,UAAA,eAAAsF,GAAA,CAAAnD,WAAA,CAA0B;UA8CrBlD,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAe,UAAA,oBAAAsF,GAAA,CAAAjD,gBAAA,CAAiC;UACpBpD,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAe,UAAA,qBAAAsF,GAAA,CAAAjD,gBAAA,CAA0B;UAatCpD,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAe,UAAA,YAAAsF,GAAA,CAAAlD,SAAA,CAAY;;;qBA1J3C5D,YAAY,EAAAiI,EAAA,CAAAC,OAAA,EACZjI,aAAa,EAAAkI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbrI,aAAa,EAAAsI,EAAA,CAAAC,OAAA,EACbtI,eAAe,EAAAuI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfxI,cAAc,EAAAyI,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACdlJ,cAAc,EAAAmJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EACdpJ,oBAAoB,EACpBC,YAAY,EAAAoJ,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}