{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/theme.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/badge\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@angular/material/list\";\nfunction NavigationComponent_a_10_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 35);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"matBadge\", item_r2.badge);\n  }\n}\nfunction NavigationComponent_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, NavigationComponent_a_10_span_5_Template, 1, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r2.route)(\"matTooltip\", item_r2.tooltip || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.badge);\n  }\n}\nfunction NavigationComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"p\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 40);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notification_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"notification-icon \" + notification_r3.type);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(notification_r3.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notification_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r3.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r3.time);\n  }\n}\nexport let NavigationComponent = /*#__PURE__*/(() => {\n  class NavigationComponent {\n    constructor(themeService, authService, router) {\n      this.themeService = themeService;\n      this.authService = authService;\n      this.router = router;\n      this.currentUser = null;\n      this.navigationItems = [{\n        label: 'Dashboard',\n        route: '/dashboard',\n        icon: 'dashboard',\n        tooltip: 'Security Overview'\n      }, {\n        label: 'Scan',\n        route: '/scan',\n        icon: 'security',\n        tooltip: 'Start New Scan'\n      }, {\n        label: 'Reports',\n        route: '/reports',\n        icon: 'assessment',\n        tooltip: 'Security Reports'\n      }, {\n        label: 'Checklist',\n        route: '/checklist',\n        icon: 'checklist',\n        tooltip: 'Security Checklist'\n      }, {\n        label: 'Projects',\n        route: '/projects',\n        icon: 'folder',\n        tooltip: 'Manage Projects'\n      }];\n      this.notifications = [{\n        type: 'success',\n        icon: 'check_circle',\n        title: 'Scan Completed',\n        message: 'Project scan finished successfully',\n        time: '2 minutes ago'\n      }, {\n        type: 'warning',\n        icon: 'warning',\n        title: 'High Risk Detected',\n        message: '3 critical vulnerabilities found',\n        time: '5 minutes ago'\n      }, {\n        type: 'error',\n        icon: 'error',\n        title: 'Scan Failed',\n        message: 'Unable to access project files',\n        time: '10 minutes ago'\n      }];\n      this.isDark$ = this.themeService.isDark$;\n    }\n    ngOnInit() {\n      // Get current user info\n      this.currentUser = this.authService.getCurrentUser();\n    }\n    toggleTheme() {\n      this.themeService.toggleTheme();\n    }\n    logout() {\n      this.authService.logout();\n      this.router.navigate(['/login']);\n    }\n    static {\n      this.ɵfac = function NavigationComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NavigationComponent)(i0.ɵɵdirectiveInject(i1.ThemeService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NavigationComponent,\n        selectors: [[\"app-navigation\"]],\n        decls: 69,\n        vars: 13,\n        consts: [[\"notificationsMenu\", \"matMenu\"], [\"userMenu\", \"matMenu\"], [1, \"spt-navbar\"], [1, \"navbar-brand\"], [\"mat-icon-button\", \"\", \"routerLink\", \"/dashboard\", 1, \"brand-button\"], [1, \"brand-icon\"], [\"routerLink\", \"/dashboard\", 1, \"brand-text\"], [1, \"brand-subtitle\"], [1, \"navbar-nav\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", \"class\", \"nav-link\", \"matTooltipPosition\", \"below\", 3, \"routerLink\", \"matTooltip\", 4, \"ngFor\", \"ngForOf\"], [1, \"navbar-spacer\"], [1, \"navbar-actions\"], [\"mat-icon-button\", \"\", 1, \"theme-toggle\", 3, \"click\", \"matTooltip\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Notifications\", 1, \"notifications-button\", 3, \"matMenuTriggerFor\"], [\"matBadge\", \"3\", \"matBadgeColor\", \"warn\", \"matBadgeSize\", \"small\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-avatar\"], [1, \"user-name\"], [1, \"dropdown-icon\"], [1, \"notifications-menu\"], [1, \"menu-header\"], [\"mat-icon-button\", \"\"], [\"class\", \"notification-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"menu-footer\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"user-menu\"], [1, \"user-menu-header\"], [1, \"user-avatar-large\"], [1, \"user-info\"], [1, \"user-email\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/settings\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", \"matTooltipPosition\", \"below\", 1, \"nav-link\", 3, \"routerLink\", \"matTooltip\"], [\"matBadgeColor\", \"warn\", \"matBadgeSize\", \"small\", \"class\", \"nav-badge\", 3, \"matBadge\", 4, \"ngIf\"], [\"matBadgeColor\", \"warn\", \"matBadgeSize\", \"small\", 1, \"nav-badge\", 3, \"matBadge\"], [1, \"notification-item\"], [1, \"notification-content\"], [1, \"notification-title\"], [1, \"notification-message\"], [1, \"notification-time\"]],\n        template: function NavigationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"mat-toolbar\", 2)(1, \"div\", 3)(2, \"button\", 4)(3, \"mat-icon\", 5);\n            i0.ɵɵtext(4, \"security\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"span\", 6);\n            i0.ɵɵtext(6, \"SPT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 7);\n            i0.ɵɵtext(8, \"Security Platform\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"nav\", 8);\n            i0.ɵɵtemplate(10, NavigationComponent_a_10_Template, 6, 5, \"a\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"div\", 10);\n            i0.ɵɵelementStart(12, \"div\", 11)(13, \"button\", 12);\n            i0.ɵɵpipe(14, \"async\");\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_13_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.toggleTheme());\n            });\n            i0.ɵɵelementStart(15, \"mat-icon\");\n            i0.ɵɵtext(16);\n            i0.ɵɵpipe(17, \"async\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"button\", 13)(19, \"mat-icon\", 14);\n            i0.ɵɵtext(20, \"notifications\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"button\", 15)(22, \"div\", 16)(23, \"mat-icon\");\n            i0.ɵɵtext(24, \"account_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"span\", 17);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"mat-icon\", 18);\n            i0.ɵɵtext(28, \"expand_more\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(29, \"mat-menu\", 19, 0)(31, \"div\", 20)(32, \"h3\");\n            i0.ɵɵtext(33, \"Notifications\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"button\", 21)(35, \"mat-icon\");\n            i0.ɵɵtext(36, \"settings\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(37, NavigationComponent_div_37_Template, 10, 6, \"div\", 22);\n            i0.ɵɵelementStart(38, \"div\", 23)(39, \"button\", 24);\n            i0.ɵɵtext(40, \"View All\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"mat-menu\", 25, 1)(43, \"div\", 26)(44, \"div\", 27)(45, \"mat-icon\");\n            i0.ɵɵtext(46, \"account_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 28)(48, \"p\", 17);\n            i0.ɵɵtext(49);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"p\", 29);\n            i0.ɵɵtext(51);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(52, \"mat-divider\");\n            i0.ɵɵelementStart(53, \"button\", 30)(54, \"mat-icon\");\n            i0.ɵɵtext(55, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"span\");\n            i0.ɵɵtext(57, \"Settings\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"button\", 31)(59, \"mat-icon\");\n            i0.ɵɵtext(60, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"span\");\n            i0.ɵɵtext(62, \"Profile\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(63, \"mat-divider\");\n            i0.ɵɵelementStart(64, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_64_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.logout());\n            });\n            i0.ɵɵelementStart(65, \"mat-icon\");\n            i0.ɵɵtext(66, \"logout\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"span\");\n            i0.ɵɵtext(68, \"Logout\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const notificationsMenu_r4 = i0.ɵɵreference(30);\n            const userMenu_r5 = i0.ɵɵreference(42);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.navigationItems);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"matTooltip\", i0.ɵɵpipeBind1(14, 9, ctx.isDark$) ? \"Switch to light mode\" : \"Switch to dark mode\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 11, ctx.isDark$) ? \"light_mode\" : \"dark_mode\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"matMenuTriggerFor\", notificationsMenu_r4);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.username) || \"Admin\");\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.username) || \"Admin\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.email) || \"<EMAIL>\");\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.AsyncPipe, RouterModule, i3.RouterLink, i3.RouterLinkActive, MatToolbarModule, i5.MatToolbar, MatButtonModule, i6.MatAnchor, i6.MatButton, i6.MatIconButton, MatIconModule, i7.MatIcon, MatMenuModule, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, MatBadgeModule, i9.MatBadge, MatTooltipModule, i10.MatTooltip, MatSlideToggleModule, MatDividerModule, i11.MatDivider],\n        styles: [\".spt-navbar[_ngcontent-%COMP%]{background:var(--spt-surface);border-bottom:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm);height:64px;padding:0 var(--spt-space-6);position:sticky;top:0;z-index:var(--spt-z-sticky);transition:all .2s ease}.navbar-brand[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);margin-right:var(--spt-space-8)}.brand-button[_ngcontent-%COMP%]{width:40px;height:40px;background:var(--spt-primary-600);color:#fff;border-radius:var(--spt-radius-lg)}.brand-button[_ngcontent-%COMP%]:hover{background:var(--spt-primary-700);transform:scale(1.05)}.brand-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.brand-text[_ngcontent-%COMP%]{font-size:var(--spt-text-xl);font-weight:var(--spt-font-bold);color:var(--spt-text-primary);cursor:pointer;text-decoration:none}.brand-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-text-secondary);font-weight:var(--spt-font-medium)}.navbar-nav[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-2)}.nav-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);padding:var(--spt-space-2) var(--spt-space-4);border-radius:var(--spt-radius-lg);color:var(--spt-text-secondary);text-decoration:none;transition:all .2s ease;font-weight:var(--spt-font-medium);position:relative}.nav-link[_ngcontent-%COMP%]:hover{background:var(--spt-primary-50);color:var(--spt-primary-700);transform:translateY(-1px)}.nav-link.active[_ngcontent-%COMP%]{background:var(--spt-primary-100);color:var(--spt-primary-700);font-weight:var(--spt-font-semibold)}.navbar-spacer[_ngcontent-%COMP%]{flex:1}.navbar-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2)}.theme-toggle[_ngcontent-%COMP%], .notifications-button[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:var(--spt-radius-lg);color:var(--spt-text-secondary);transition:all .2s ease}.theme-toggle[_ngcontent-%COMP%]:hover, .notifications-button[_ngcontent-%COMP%]:hover{background:var(--spt-gray-100);color:var(--spt-text-primary);transform:scale(1.05)}.user-menu-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);padding:var(--spt-space-2) var(--spt-space-3);border-radius:var(--spt-radius-lg);color:var(--spt-text-primary);transition:all .2s ease}.user-menu-button[_ngcontent-%COMP%]:hover{background:var(--spt-gray-100)}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:var(--spt-radius-full);background:var(--spt-primary-100);color:var(--spt-primary-600);display:flex;align-items:center;justify-content:center}.user-name[_ngcontent-%COMP%]{font-weight:var(--spt-font-medium);font-size:var(--spt-text-sm)}.dropdown-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:var(--spt-text-secondary)}.notifications-menu[_ngcontent-%COMP%], .user-menu[_ngcontent-%COMP%]{border-radius:var(--spt-radius-xl);box-shadow:var(--spt-shadow-xl);border:1px solid var(--spt-border);overflow:hidden}.menu-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:var(--spt-space-4);border-bottom:1px solid var(--spt-border);background:var(--spt-bg-secondary)}.menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold)}.notification-item[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-3);padding:var(--spt-space-3) var(--spt-space-4);border-bottom:1px solid var(--spt-border-light);transition:background .2s ease}.notification-item[_ngcontent-%COMP%]:hover{background:var(--spt-bg-secondary)}.notification-icon[_ngcontent-%COMP%]{width:20px;height:20px;font-size:20px;margin-top:var(--spt-space-1)}.notification-icon.success[_ngcontent-%COMP%]{color:var(--spt-success-600)}.notification-icon.warning[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.notification-icon.error[_ngcontent-%COMP%]{color:var(--spt-error-600)}.notification-content[_ngcontent-%COMP%]{flex:1}.notification-title[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-1) 0;font-weight:var(--spt-font-medium);font-size:var(--spt-text-sm)}.notification-message[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-1) 0;font-size:var(--spt-text-xs);color:var(--spt-text-secondary)}.notification-time[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-tertiary)}.user-menu-header[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-3);padding:var(--spt-space-4);background:var(--spt-bg-secondary)}.user-avatar-large[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:var(--spt-radius-full);background:var(--spt-primary-100);color:var(--spt-primary-600);display:flex;align-items:center;justify-content:center}.user-avatar-large[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px}.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-1) 0;font-weight:var(--spt-font-semibold);font-size:var(--spt-text-base)}.user-info[_ngcontent-%COMP%]   .user-email[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-sm);color:var(--spt-text-secondary)}.menu-footer[_ngcontent-%COMP%]{padding:var(--spt-space-3) var(--spt-space-4);border-top:1px solid var(--spt-border);background:var(--spt-bg-secondary)}@media (max-width: 768px){.navbar-nav[_ngcontent-%COMP%], .brand-subtitle[_ngcontent-%COMP%], .user-name[_ngcontent-%COMP%]{display:none}}\"]\n      });\n    }\n  }\n  return NavigationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}