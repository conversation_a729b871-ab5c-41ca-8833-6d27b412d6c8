{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/checkbox\";\nfunction LoginComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 44);\n  }\n}\nfunction LoginComponent_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  loginWithDemo() {\n    this.isLoading = true;\n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', {\n        duration: 3000\n      });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 114,\n      vars: 11,\n      consts: [[1, \"login-container\"], [1, \"background-pattern\"], [1, \"app-header\"], [1, \"header-content\"], [1, \"brand\"], [1, \"brand-icon\"], [1, \"brand-text\"], [1, \"header-actions\"], [\"mat-button\", \"\", \"routerLink\", \"/register\", 1, \"header-link\"], [1, \"login-content\"], [1, \"login-card-wrapper\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"logo-icon-wrapper\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"subtitle\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"form-options\"], [\"formControlName\", \"rememberMe\"], [\"href\", \"#\", 1, \"forgot-password\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"demo-button\", 3, \"click\", \"disabled\"], [1, \"card-actions\"], [\"routerLink\", \"/register\", 1, \"register-link\"], [1, \"features-info\"], [1, \"features-header\"], [1, \"features-icon\"], [1, \"features-grid\"], [1, \"feature-item\"], [1, \"stats\"], [1, \"stat-item\"], [1, \"stat-number\"], [1, \"stat-label\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"header\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"shield\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"SPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵtext(11, \" Create Account \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"mat-card\", 11)(15, \"mat-card-header\", 12)(16, \"div\", 13)(17, \"div\", 14)(18, \"mat-icon\", 15);\n          i0.ɵɵtext(19, \"security\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"h1\");\n          i0.ɵɵtext(22, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 17);\n          i0.ɵɵtext(24, \"Sign in to your security dashboard\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"mat-card-content\")(26, \"form\", 18);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_26_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(27, \"div\", 19)(28, \"mat-form-field\", 20)(29, \"mat-label\");\n          i0.ɵɵtext(30, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"input\", 21);\n          i0.ɵɵelementStart(32, \"mat-icon\", 22);\n          i0.ɵɵtext(33, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, LoginComponent_mat_error_34_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"mat-form-field\", 20)(36, \"mat-label\");\n          i0.ɵɵtext(37, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"input\", 24);\n          i0.ɵɵelementStart(39, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_39_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(40, \"mat-icon\");\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(42, LoginComponent_mat_error_42_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 26)(44, \"mat-checkbox\", 27);\n          i0.ɵɵtext(45, \" Remember me \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"a\", 28);\n          i0.ɵɵtext(47, \"Forgot password?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 29)(49, \"button\", 30);\n          i0.ɵɵtemplate(50, LoginComponent_mat_spinner_50_Template, 1, 0, \"mat-spinner\", 31)(51, LoginComponent_span_51_Template, 2, 0, \"span\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_52_listener() {\n            return ctx.loginWithDemo();\n          });\n          i0.ɵɵtext(53, \" Demo Login \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(54, \"mat-card-actions\", 33)(55, \"p\");\n          i0.ɵɵtext(56, \"Don't have an account? \");\n          i0.ɵɵelementStart(57, \"a\", 34);\n          i0.ɵɵtext(58, \"Sign up\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"div\", 35)(60, \"div\", 36)(61, \"mat-icon\", 37);\n          i0.ɵɵtext(62, \"verified_user\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"h3\");\n          i0.ɵɵtext(64, \"Security Features\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 38)(66, \"div\", 39)(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\")(70, \"h4\");\n          i0.ɵɵtext(71, \"Smart Contract Analysis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"p\");\n          i0.ɵɵtext(73, \"Advanced static analysis for Solidity contracts\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(74, \"div\", 39)(75, \"mat-icon\");\n          i0.ɵɵtext(76, \"bug_report\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\")(78, \"h4\");\n          i0.ɵɵtext(79, \"Vulnerability Detection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"p\");\n          i0.ɵɵtext(81, \"Identify security flaws and potential exploits\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(82, \"div\", 39)(83, \"mat-icon\");\n          i0.ɵɵtext(84, \"monitor\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"div\")(86, \"h4\");\n          i0.ɵɵtext(87, \"Real-time Monitoring\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"p\");\n          i0.ɵɵtext(89, \"Continuous security monitoring and alerts\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 39)(91, \"mat-icon\");\n          i0.ɵɵtext(92, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"div\")(94, \"h4\");\n          i0.ɵɵtext(95, \"Comprehensive Reports\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"p\");\n          i0.ɵɵtext(97, \"Detailed security reports and recommendations\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(98, \"div\", 40)(99, \"div\", 41)(100, \"span\", 42);\n          i0.ɵɵtext(101, \"10K+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"span\", 43);\n          i0.ɵɵtext(103, \"Contracts Analyzed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"div\", 41)(105, \"span\", 42);\n          i0.ɵɵtext(106, \"500+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"span\", 43);\n          i0.ɵɵtext(108, \"Vulnerabilities Found\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"div\", 41)(110, \"span\", 42);\n          i0.ɵɵtext(111, \"99.9%\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"span\", 43);\n          i0.ɵɵtext(113, \"Accuracy Rate\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.hasError(\"required\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i12.MatCheckbox],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.background-pattern[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.app-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 16px 24px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  color: white;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  letter-spacing: -0.5px;\\n}\\n\\n.header-link[_ngcontent-%COMP%] {\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 8px;\\n  padding: 8px 16px;\\n  transition: all 0.2s ease;\\n}\\n\\n.header-link[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.login-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.login-card-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 60px;\\n  align-items: center;\\n  max-width: 1200px;\\n  width: 100%;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  flex: 0 0 480px;\\n  padding: 48px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 24px;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_slideInUp 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.login-header[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.logo-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: white;\\n}\\n\\n.logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1a202c;\\n  font-weight: 700;\\n  font-size: 28px;\\n  letter-spacing: -0.5px;\\n  line-height: 1.2;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  margin: 4px 0 0 0;\\n  color: #64748b;\\n  font-size: 16px;\\n  font-weight: 400;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 14px;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 56px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.login-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s ease;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n\\n.demo-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  color: #667eea;\\n  border: 2px solid rgba(102, 126, 234, 0.2);\\n  border-radius: 12px;\\n  font-weight: 500;\\n  background: rgba(102, 126, 234, 0.05);\\n  transition: all 0.3s ease;\\n}\\n\\n.demo-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n  border-color: rgba(102, 126, 234, 0.3);\\n  transform: translateY(-1px);\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.register-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.features-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  color: white;\\n  padding: 48px;\\n  border-radius: 24px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out 0.2s both;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n.features-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  margin-bottom: 32px;\\n}\\n\\n.features-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.features-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  margin: 0;\\n  font-weight: 700;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 24px;\\n  margin-bottom: 40px;\\n}\\n\\n.feature-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 20px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.feature-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateY(-2px);\\n}\\n\\n.feature-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-top: 2px;\\n}\\n\\n.feature-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n}\\n\\n.feature-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  opacity: 0.9;\\n  line-height: 1.4;\\n}\\n\\n.stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 24px;\\n  padding-top: 32px;\\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n@media (max-width: 1024px) {\\n  .login-card-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 40px;\\n    max-width: 600px;\\n  }\\n  .login-card[_ngcontent-%COMP%] {\\n    flex: none;\\n    max-width: 100%;\\n  }\\n  .features-info[_ngcontent-%COMP%] {\\n    order: -1;\\n  }\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .stats[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .brand-text[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .login-content[_ngcontent-%COMP%] {\\n    padding: 20px 16px;\\n  }\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 32px 24px;\\n  }\\n  .features-info[_ngcontent-%COMP%] {\\n    padding: 32px 24px;\\n  }\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .feature-item[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .logo[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 12px;\\n  }\\n  .logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n}\\nmat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "hidePassword", "isLoading", "returnUrl", "loginForm", "group", "username", "required", "password", "rememberMe", "ngOnInit", "snapshot", "queryParams", "onSubmit", "valid", "credentials", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "message", "loginWithDemo", "setTimeout", "mockLogin", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_26_listener", "ɵɵtemplate", "LoginComponent_mat_error_34_Template", "LoginComponent_Template_button_click_39_listener", "LoginComponent_mat_error_42_Template", "LoginComponent_mat_spinner_50_Template", "LoginComponent_span_51_Template", "LoginComponent_Template_button_click_52_listener", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "tmp_6_0", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatButton", "MatIconButton", "i10", "MatIcon", "i11", "MatProgressSpinner", "i12", "MatCheckbox", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\n\nimport { AuthService, LoginRequest } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './login.component.html',\n  styles: [`\n    .login-container {\n      min-height: 100vh;\n      display: flex;\n      flex-direction: column;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      position: relative;\n      overflow: hidden;\n    }\n\n    .background-pattern {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-image:\n        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\n        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);\n      pointer-events: none;\n    }\n\n    .app-header {\n      position: relative;\n      z-index: 10;\n      background: rgba(255, 255, 255, 0.1);\n      backdrop-filter: blur(10px);\n      border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    }\n\n    .header-content {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 16px 24px;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .brand {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      color: white;\n    }\n\n    .brand-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n    }\n\n    .brand-text {\n      font-size: 24px;\n      font-weight: 700;\n      letter-spacing: -0.5px;\n    }\n\n    .header-link {\n      color: white;\n      border: 1px solid rgba(255, 255, 255, 0.3);\n      border-radius: 8px;\n      padding: 8px 16px;\n      transition: all 0.2s ease;\n    }\n\n    .header-link:hover {\n      background: rgba(255, 255, 255, 0.1);\n      border-color: rgba(255, 255, 255, 0.5);\n    }\n\n    .login-content {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 40px 20px;\n      position: relative;\n      z-index: 1;\n    }\n\n    .login-card-wrapper {\n      display: flex;\n      gap: 60px;\n      align-items: center;\n      max-width: 1200px;\n      width: 100%;\n    }\n\n    .login-card {\n      flex: 0 0 480px;\n      padding: 48px;\n      background: rgba(255, 255, 255, 0.95);\n      backdrop-filter: blur(20px);\n      border-radius: 24px;\n      box-shadow:\n        0 20px 25px -5px rgba(0, 0, 0, 0.1),\n        0 10px 10px -5px rgba(0, 0, 0, 0.04);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      animation: slideInUp 0.6s ease-out;\n    }\n\n    @keyframes slideInUp {\n      from {\n        opacity: 0;\n        transform: translateY(30px);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0);\n      }\n    }\n\n    .login-header {\n      margin-bottom: 40px;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      margin-bottom: 8px;\n    }\n\n    .logo-icon-wrapper {\n      width: 64px;\n      height: 64px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 16px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);\n    }\n\n    .logo-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n      color: white;\n    }\n\n    .logo-text h1 {\n      margin: 0;\n      color: #1a202c;\n      font-weight: 700;\n      font-size: 28px;\n      letter-spacing: -0.5px;\n      line-height: 1.2;\n    }\n\n    .subtitle {\n      margin: 4px 0 0 0;\n      color: #64748b;\n      font-size: 16px;\n      font-weight: 400;\n    }\n\n    .form-fields {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n\n    .full-width {\n      width: 100%;\n    }\n\n    .form-options {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-top: 10px;\n    }\n\n    .forgot-password {\n      color: #667eea;\n      text-decoration: none;\n      font-size: 14px;\n    }\n\n    .forgot-password:hover {\n      text-decoration: underline;\n    }\n\n    .form-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 15px;\n    }\n\n    .login-button {\n      height: 56px;\n      font-size: 16px;\n      font-weight: 600;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 16px;\n      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);\n      transition: all 0.3s ease;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .login-button::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n      transition: left 0.5s ease;\n    }\n\n    .login-button:hover::before {\n      left: 100%;\n    }\n\n    .login-button:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);\n    }\n\n    .login-button:active {\n      transform: translateY(0);\n    }\n\n    .demo-button {\n      height: 48px;\n      color: #667eea;\n      border: 2px solid rgba(102, 126, 234, 0.2);\n      border-radius: 12px;\n      font-weight: 500;\n      background: rgba(102, 126, 234, 0.05);\n      transition: all 0.3s ease;\n    }\n\n    .demo-button:hover {\n      background: rgba(102, 126, 234, 0.1);\n      border-color: rgba(102, 126, 234, 0.3);\n      transform: translateY(-1px);\n    }\n\n    .card-actions {\n      text-align: center;\n      padding-top: 20px;\n      border-top: 1px solid #e0e0e0;\n    }\n\n    .register-link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .register-link:hover {\n      text-decoration: underline;\n    }\n\n    .features-info {\n      flex: 1;\n      background: rgba(255, 255, 255, 0.1);\n      backdrop-filter: blur(20px);\n      color: white;\n      padding: 48px;\n      border-radius: 24px;\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      animation: slideInRight 0.6s ease-out 0.2s both;\n    }\n\n    @keyframes slideInRight {\n      from {\n        opacity: 0;\n        transform: translateX(30px);\n      }\n      to {\n        opacity: 1;\n        transform: translateX(0);\n      }\n    }\n\n    .features-header {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      margin-bottom: 32px;\n    }\n\n    .features-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n    }\n\n    .features-header h3 {\n      font-size: 28px;\n      margin: 0;\n      font-weight: 700;\n    }\n\n    .features-grid {\n      display: grid;\n      gap: 24px;\n      margin-bottom: 40px;\n    }\n\n    .feature-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 16px;\n      padding: 20px;\n      background: rgba(255, 255, 255, 0.1);\n      border-radius: 16px;\n      border: 1px solid rgba(255, 255, 255, 0.1);\n      transition: all 0.3s ease;\n    }\n\n    .feature-item:hover {\n      background: rgba(255, 255, 255, 0.15);\n      transform: translateY(-2px);\n    }\n\n    .feature-item mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n      margin-top: 2px;\n    }\n\n    .feature-item h4 {\n      margin: 0 0 4px 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n\n    .feature-item p {\n      margin: 0;\n      font-size: 14px;\n      opacity: 0.9;\n      line-height: 1.4;\n    }\n\n    .stats {\n      display: grid;\n      grid-template-columns: repeat(3, 1fr);\n      gap: 24px;\n      padding-top: 32px;\n      border-top: 1px solid rgba(255, 255, 255, 0.2);\n    }\n\n    .stat-item {\n      text-align: center;\n    }\n\n    .stat-number {\n      display: block;\n      font-size: 24px;\n      font-weight: 700;\n      margin-bottom: 4px;\n    }\n\n    .stat-label {\n      font-size: 12px;\n      opacity: 0.8;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    @media (max-width: 1024px) {\n      .login-card-wrapper {\n        flex-direction: column;\n        gap: 40px;\n        max-width: 600px;\n      }\n\n      .login-card {\n        flex: none;\n        max-width: 100%;\n      }\n\n      .features-info {\n        order: -1;\n      }\n\n      .features-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n\n      .stats {\n        grid-template-columns: repeat(3, 1fr);\n      }\n    }\n\n    @media (max-width: 768px) {\n      .header-content {\n        padding: 12px 16px;\n      }\n\n      .brand-text {\n        font-size: 20px;\n      }\n\n      .login-content {\n        padding: 20px 16px;\n      }\n\n      .login-card {\n        padding: 32px 24px;\n      }\n\n      .features-info {\n        padding: 32px 24px;\n      }\n\n      .features-grid {\n        grid-template-columns: 1fr;\n        gap: 16px;\n      }\n\n      .feature-item {\n        padding: 16px;\n      }\n\n      .stats {\n        grid-template-columns: 1fr;\n        gap: 16px;\n      }\n\n      .logo {\n        flex-direction: column;\n        text-align: center;\n        gap: 12px;\n      }\n\n      .logo-text h1 {\n        font-size: 24px;\n      }\n    }\n\n    mat-spinner {\n      margin-right: 10px;\n    }\n  `]\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  hidePassword = true;\n  isLoading = false;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      \n      const credentials: LoginRequest = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n\n  loginWithDemo(): void {\n    this.isLoading = true;\n    \n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n}\n", "<div class=\"login-container\">\n  <!-- Background Pattern -->\n  <div class=\"background-pattern\"></div>\n\n  <!-- Header -->\n  <header class=\"app-header\">\n    <div class=\"header-content\">\n      <div class=\"brand\">\n        <mat-icon class=\"brand-icon\">shield</mat-icon>\n        <span class=\"brand-text\">SPT</span>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-button routerLink=\"/register\" class=\"header-link\">\n          Create Account\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <div class=\"login-content\">\n    <div class=\"login-card-wrapper\">\n      <mat-card class=\"login-card\">\n        <mat-card-header class=\"login-header\">\n          <div class=\"logo\">\n            <div class=\"logo-icon-wrapper\">\n              <mat-icon class=\"logo-icon\">security</mat-icon>\n            </div>\n            <div class=\"logo-text\">\n              <h1>Welcome Back</h1>\n              <p class=\"subtitle\">Sign in to your security dashboard</p>\n            </div>\n          </div>\n        </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n          <div class=\"form-fields\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Username</mat-label>\n              <input matInput formControlName=\"username\" autocomplete=\"username\">\n              <mat-icon matSuffix>person</mat-icon>\n              <mat-error *ngIf=\"loginForm.get('username')?.hasError('required')\">\n                Username is required\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Password</mat-label>\n              <input matInput \n                     [type]=\"hidePassword ? 'password' : 'text'\" \n                     formControlName=\"password\"\n                     autocomplete=\"current-password\">\n              <button mat-icon-button matSuffix \n                      (click)=\"hidePassword = !hidePassword\" \n                      [attr.aria-label]=\"'Hide password'\" \n                      [attr.aria-pressed]=\"hidePassword\"\n                      type=\"button\">\n                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n              </button>\n              <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                Password is required\n              </mat-error>\n            </mat-form-field>\n\n            <div class=\"form-options\">\n              <mat-checkbox formControlName=\"rememberMe\">\n                Remember me\n              </mat-checkbox>\n              <a href=\"#\" class=\"forgot-password\">Forgot password?</a>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button mat-raised-button \n                    color=\"primary\" \n                    type=\"submit\" \n                    class=\"login-button\"\n                    [disabled]=\"loginForm.invalid || isLoading\">\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n              <span *ngIf=\"!isLoading\">Sign In</span>\n            </button>\n\n            <button mat-button \n                    type=\"button\" \n                    class=\"demo-button\"\n                    (click)=\"loginWithDemo()\"\n                    [disabled]=\"isLoading\">\n              Demo Login\n            </button>\n          </div>\n        </form>\n      </mat-card-content>\n\n      <mat-card-actions class=\"card-actions\">\n        <p>Don't have an account? \n          <a routerLink=\"/register\" class=\"register-link\">Sign up</a>\n        </p>\n      </mat-card-actions>\n    </mat-card>\n\n    <div class=\"features-info\">\n      <div class=\"features-header\">\n        <mat-icon class=\"features-icon\">verified_user</mat-icon>\n        <h3>Security Features</h3>\n      </div>\n      <div class=\"features-grid\">\n        <div class=\"feature-item\">\n          <mat-icon>code</mat-icon>\n          <div>\n            <h4>Smart Contract Analysis</h4>\n            <p>Advanced static analysis for Solidity contracts</p>\n          </div>\n        </div>\n        <div class=\"feature-item\">\n          <mat-icon>bug_report</mat-icon>\n          <div>\n            <h4>Vulnerability Detection</h4>\n            <p>Identify security flaws and potential exploits</p>\n          </div>\n        </div>\n        <div class=\"feature-item\">\n          <mat-icon>monitor</mat-icon>\n          <div>\n            <h4>Real-time Monitoring</h4>\n            <p>Continuous security monitoring and alerts</p>\n          </div>\n        </div>\n        <div class=\"feature-item\">\n          <mat-icon>assessment</mat-icon>\n          <div>\n            <h4>Comprehensive Reports</h4>\n            <p>Detailed security reports and recommendations</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"stats\">\n        <div class=\"stat-item\">\n          <span class=\"stat-number\">10K+</span>\n          <span class=\"stat-label\">Contracts Analyzed</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-number\">500+</span>\n          <span class=\"stat-label\">Vulnerabilities Found</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-number\">99.9%</span>\n          <span class=\"stat-label\">Accuracy Rate</span>\n        </div>\n      </div>\n    </div>\n  </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACxF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;IC8BhDC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAiBZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD8YrD,OAAM,MAAOE,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MAErB,MAAMa,WAAW,GAAiB;QAChCT,QAAQ,EAAE,IAAI,CAACF,SAAS,CAACY,KAAK,CAACV,QAAQ;QACvCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACY,KAAK,CAACR;OAChC;MAED,IAAI,CAACX,WAAW,CAACoB,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;QACxC,CAAC;QACDqB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;OACD,CAAC;IACJ;EACF;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACxB,SAAS,GAAG,IAAI;IAErB;IACAyB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9B,WAAW,CAAC+B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;MAC5C,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;IACxC,CAAC,EAAE,IAAI,CAAC;EACV;;;uCA1DWT,cAAc,EAAAL,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA/C,EAAA,CAAAwC,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd5C,cAAc;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7d3BxD,EAAA,CAAAC,cAAA,aAA6B;UAE3BD,EAAA,CAAAI,SAAA,aAAsC;UAMhCJ,EAHN,CAAAC,cAAA,gBAA2B,aACG,aACP,kBACY;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,UAAG;UAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;UAEJH,EADF,CAAAC,cAAA,aAA4B,iBACoC;UAC5DD,EAAA,CAAAE,MAAA,wBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACC;UAQGH,EANZ,CAAAC,cAAA,cAA2B,eACO,oBACD,2BACW,eAClB,eACe,oBACD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACtCF,EADsC,CAAAG,YAAA,EAAW,EAC3C;UAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,aAAoB;UAAAD,EAAA,CAAAE,MAAA,0CAAkC;UAG5DF,EAH4D,CAAAG,YAAA,EAAI,EACtD,EACF,EACU;UAGlBH,EADF,CAAAC,cAAA,wBAAkB,gBACsC;UAAxBD,EAAA,CAAA0D,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAG/CxB,EAFJ,CAAAC,cAAA,eAAyB,0BACiC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAAmE;UACnEJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA4D,UAAA,KAAAC,oCAAA,wBAAmE;UAGrE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAGuC;UACvCJ,EAAA,CAAAC,cAAA,kBAIsB;UAHdD,EAAA,CAAA0D,UAAA,mBAAAI,iDAAA;YAAA,OAAAL,GAAA,CAAA7C,YAAA,IAAA6C,GAAA,CAAA7C,YAAA;UAAA,EAAsC;UAI5CZ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAA4D,UAAA,KAAAG,oCAAA,wBAAmE;UAGrE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,wBACmB;UACzCD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAExDF,EAFwD,CAAAG,YAAA,EAAI,EACpD,EACF;UAGJH,EADF,CAAAC,cAAA,eAA0B,kBAK4B;UAElDD,EADA,CAAA4D,UAAA,KAAAI,sCAAA,0BAA6C,KAAAC,+BAAA,mBACpB;UAC3BjE,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAI+B;UADvBD,EAAA,CAAA0D,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAE/BrC,EAAA,CAAAE,MAAA,oBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACU;UAGjBH,EADF,CAAAC,cAAA,4BAAuC,SAClC;UAAAD,EAAA,CAAAE,MAAA,+BACD;UAAAF,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAG7DF,EAH6D,CAAAG,YAAA,EAAI,EACzD,EACa,EACV;UAIPH,EAFJ,CAAAC,cAAA,eAA2B,eACI,oBACK;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACvBF,EADuB,CAAAG,YAAA,EAAK,EACtB;UAGFH,EAFJ,CAAAC,cAAA,eAA2B,eACC,gBACd;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEvBH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uDAA+C;UAEtDF,EAFsD,CAAAG,YAAA,EAAI,EAClD,EACF;UAEJH,EADF,CAAAC,cAAA,eAA0B,gBACd;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE7BH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sDAA8C;UAErDF,EAFqD,CAAAG,YAAA,EAAI,EACjD,EACF;UAEJH,EADF,CAAAC,cAAA,eAA0B,gBACd;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE1BH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,iDAAyC;UAEhDF,EAFgD,CAAAG,YAAA,EAAI,EAC5C,EACF;UAEJH,EADF,CAAAC,cAAA,eAA0B,gBACd;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE7BH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qDAA6C;UAGtDF,EAHsD,CAAAG,YAAA,EAAI,EAChD,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAmB,eACM,iBACK;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;UAEJH,EADF,CAAAC,cAAA,gBAAuB,iBACK;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAChDF,EADgD,CAAAG,YAAA,EAAO,EACjD;UAEJH,EADF,CAAAC,cAAA,gBAAuB,iBACK;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAMhDF,EANgD,CAAAG,YAAA,EAAO,EACzC,EACF,EACF,EACF,EACA,EACF;;;;;UAtHQH,EAAA,CAAAmE,SAAA,IAAuB;UAAvBnE,EAAA,CAAAoE,UAAA,cAAAX,GAAA,CAAA1C,SAAA,CAAuB;UAMXf,EAAA,CAAAmE,SAAA,GAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAC,OAAA,GAAAZ,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAD,OAAA,CAAAE,QAAA,aAAqD;UAQ1DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA7C,YAAA,uBAA2C;UAK1CZ,EAAA,CAAAmE,SAAA,EAAmC;;UAG/BnE,EAAA,CAAAmE,SAAA,GAAkD;UAAlDnE,EAAA,CAAAwE,iBAAA,CAAAf,GAAA,CAAA7C,YAAA,mCAAkD;UAElDZ,EAAA,CAAAmE,SAAA,EAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAK,OAAA,GAAAhB,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAG,OAAA,CAAAF,QAAA,aAAqD;UAkB3DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA1C,SAAA,CAAA2D,OAAA,IAAAjB,GAAA,CAAA5C,SAAA,CAA2C;UACnCb,EAAA,CAAAmE,SAAA,EAAe;UAAfnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA5C,SAAA,CAAe;UACtBb,EAAA,CAAAmE,SAAA,EAAgB;UAAhBnE,EAAA,CAAAoE,UAAA,UAAAX,GAAA,CAAA5C,SAAA,CAAgB;UAOjBb,EAAA,CAAAmE,SAAA,EAAsB;UAAtBnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA5C,SAAA,CAAsB;;;qBDnEtCzB,YAAY,EAAAuF,EAAA,CAAAC,IAAA,EACZvF,mBAAmB,EAAAoD,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,kBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EACnB3F,YAAY,EAAAsD,EAAA,CAAAsC,UAAA,EACZ3F,aAAa,EAAA4F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EACb/F,kBAAkB,EAAAgG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBnG,cAAc,EAAAoG,EAAA,CAAAC,QAAA,EACdpG,eAAe,EAAAqG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACftG,aAAa,EAAAuG,GAAA,CAAAC,OAAA,EACbvG,wBAAwB,EAAAwG,GAAA,CAAAC,kBAAA,EACxBxG,iBAAiB,EACjBC,iBAAiB,EAAAwG,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}