package api

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

// LoginRequest represents a login request
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	Token     string      `json:"token"`
	User      models.User `json:"user"`
	ExpiresIn int64       `json:"expires_in"`
}

// RefreshRequest represents a token refresh request
type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// UserStorage interface for user operations
type UserStorage interface {
	GetUserByUsername(username string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	CreateUser(user *models.User) error
	UpdateUser(user *models.User) error
	UserExists(username string) (bool, error)
}

// APIKeyStorage interface for API key operations
type APIKeyStorage interface {
	GetAPIKeyByHash(keyHash string) (*models.APIKey, error)
	UpdateAPIKeyLastUsed(id string) error
	CreateAPIKey(apiKey *models.APIKey) error
	GetAPIKeysByUserID(userID string) ([]*models.APIKey, error)
	UpdateAPIKey(apiKey *models.APIKey) error
	DeleteAPIKey(id string) error
}

// Login handles user authentication
func (h *Handler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Authentication service unavailable",
		})
		return
	}

	// Find user by username
	user, err := userStorage.GetUserByUsername(req.Username)
	if err != nil {
		h.logger.WithError(err).Warnf("Failed to find user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Account is disabled",
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		h.logger.WithError(err).Warnf("Invalid password for user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Generate authentication token (simple implementation)
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate authentication token",
		})
		return
	}

	// Update last login time
	now := time.Now()
	user.LastLogin = &now
	if err := userStorage.UpdateUser(user); err != nil {
		h.logger.WithError(err).Warn("Failed to update last login time")
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusOK, AuthResponse{
		Token:     token,
		User:      *user,
		ExpiresIn: 3600, // 1 hour
	})
}

// Register handles user registration
func (h *Handler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration service unavailable",
		})
		return
	}

	// Check if username already exists
	exists, err := userStorage.UserExists(req.Username)
	if err != nil {
		h.logger.WithError(err).Error("Failed to check if user exists")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	if exists {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Username already exists",
		})
		return
	}

	// Check if email already exists
	if _, err := userStorage.GetUserByEmail(req.Email); err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Email already registered",
		})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		h.logger.WithError(err).Error("Failed to hash password")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	// Create new user
	user := &models.User{
		Username:  req.Username,
		Email:     req.Email,
		Password:  string(hashedPassword),
		FirstName: stringPtr(req.FirstName),
		LastName:  stringPtr(req.LastName),
		Role:      models.UserRoleDeveloper, // Default role
		IsActive:  true,
	}

	if err := userStorage.CreateUser(user); err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	// Generate authentication token
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration successful but failed to generate token",
		})
		return
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusCreated, AuthResponse{
		Token:     token,
		User:      *user,
		ExpiresIn: 3600, // 1 hour
	})
}

// Logout handles user logout
func (h *Handler) Logout(c *gin.Context) {
	// In a real implementation, you would invalidate the token
	// For now, we'll just return success
	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
	})
}

// RefreshToken handles token refresh
func (h *Handler) RefreshToken(c *gin.Context) {
	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// In a real implementation, you would validate the refresh token
	// and generate a new access token
	c.JSON(http.StatusNotImplemented, gin.H{
		"error": "Token refresh not implemented yet",
	})
}

// GetCurrentUser returns the current authenticated user
func (h *Handler) GetCurrentUser(c *gin.Context) {
	// Get user from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Not authenticated",
		})
		return
	}

	// For now, return a mock user based on the user_id
	// In a real implementation, fetch from database
	c.JSON(http.StatusOK, gin.H{
		"user": gin.H{
			"id":       userID,
			"username": "current_user",
			"role":     "developer",
		},
	})
}

// generateAuthToken generates a JWT authentication token
func (h *Handler) generateAuthToken(user *models.User) (string, error) {
	// Create JWT claims
	claims := JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)), // 24 hours
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "spt-backend",
			Subject:   user.ID,
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token with secret
	tokenString, err := token.SignedString(jwtSecret)
	if err != nil {
		return "", fmt.Errorf("failed to sign JWT token: %w", err)
	}

	return tokenString, nil
}

// VSCodeAuth handles VS Code extension authentication redirect
func (h *Handler) VSCodeAuth(c *gin.Context) {
	state := c.Query("state")
	redirectURI := c.Query("redirect_uri")

	if state == "" || redirectURI == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Missing required parameters",
		})
		return
	}

	// Render login page with VS Code specific parameters
	c.HTML(http.StatusOK, "vscode_auth.html", gin.H{
		"state":       state,
		"redirectURI": redirectURI,
		"serverURL":   c.Request.Host,
	})
}

// VSCodeAuthCallback handles the authentication callback for VS Code
func (h *Handler) VSCodeAuthCallback(c *gin.Context) {
	var req struct {
		State    string `json:"state" binding:"required"`
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Authentication service unavailable",
		})
		return
	}

	// Find user by username
	user, err := userStorage.GetUserByUsername(req.Username)
	if err != nil {
		h.logger.WithError(err).Warnf("Failed to find user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Account is disabled",
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		h.logger.WithError(err).Warnf("Invalid password for user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Generate authentication token
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate authentication token",
		})
		return
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"token":      token,
		"user":       user,
		"expires_in": 86400, // 24 hours
		"state":      req.State,
	})
}

// CreateAPIKeyRequest represents an API key creation request
type CreateAPIKeyRequest struct {
	Name        string   `json:"name" binding:"required"`
	Permissions []string `json:"permissions"`
	ExpiresAt   *string  `json:"expires_at,omitempty"` // ISO 8601 format
}

// CreateAPIKeyResponse represents an API key creation response
type CreateAPIKeyResponse struct {
	APIKey  string        `json:"api_key"`  // The actual key (only returned once)
	KeyInfo models.APIKey `json:"key_info"` // Key metadata
	Message string        `json:"message"`
}

// CreateAPIKey handles API key creation
func (h *Handler) CreateAPIKey(c *gin.Context) {
	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get API key storage interface
	apiKeyStorage, ok := h.storage.(APIKeyStorage)
	if !ok {
		h.logger.Error("Storage does not implement APIKeyStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "API key service unavailable",
		})
		return
	}

	// Generate a new API key
	apiKeyBytes := make([]byte, 32)
	if _, err := rand.Read(apiKeyBytes); err != nil {
		h.logger.WithError(err).Error("Failed to generate API key")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate API key",
		})
		return
	}

	apiKey := fmt.Sprintf("spt_%s", hex.EncodeToString(apiKeyBytes))

	// Hash the API key for storage
	hasher := sha256.New()
	hasher.Write([]byte(apiKey))
	keyHash := hex.EncodeToString(hasher.Sum(nil))

	// Parse expiration date if provided
	var expiresAt *time.Time
	if req.ExpiresAt != nil {
		if parsed, err := time.Parse(time.RFC3339, *req.ExpiresAt); err == nil {
			expiresAt = &parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid expires_at format. Use ISO 8601 format (e.g., 2024-12-31T23:59:59Z)",
			})
			return
		}
	}

	// Set default permissions if none provided
	permissions := req.Permissions
	if len(permissions) == 0 {
		permissions = []string{"scan:read", "scan:write", "report:read"}
	}

	// Create API key record
	apiKeyRecord := &models.APIKey{
		UserID:      userID.(string),
		Name:        req.Name,
		KeyHash:     keyHash,
		Prefix:      apiKey[:10], // Store first 10 characters for identification
		Permissions: permissions,
		IsActive:    true,
		ExpiresAt:   expiresAt,
	}

	if err := apiKeyStorage.CreateAPIKey(apiKeyRecord); err != nil {
		h.logger.WithError(err).Error("Failed to create API key")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create API key",
		})
		return
	}

	// Remove sensitive data from response
	apiKeyRecord.KeyHash = ""

	c.JSON(http.StatusCreated, CreateAPIKeyResponse{
		APIKey:  apiKey,
		KeyInfo: *apiKeyRecord,
		Message: "API key created successfully. Please store it securely as it won't be shown again.",
	})
}

// GetAPIKeys handles listing user's API keys
func (h *Handler) GetAPIKeys(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get API key storage interface
	apiKeyStorage, ok := h.storage.(APIKeyStorage)
	if !ok {
		h.logger.Error("Storage does not implement APIKeyStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "API key service unavailable",
		})
		return
	}

	apiKeys, err := apiKeyStorage.GetAPIKeysByUserID(userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get API keys")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to retrieve API keys",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"api_keys": apiKeys,
	})
}

// DeleteAPIKey handles API key deletion
func (h *Handler) DeleteAPIKey(c *gin.Context) {
	keyID := c.Param("id")
	if keyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "API key ID is required",
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get API key storage interface
	apiKeyStorage, ok := h.storage.(APIKeyStorage)
	if !ok {
		h.logger.Error("Storage does not implement APIKeyStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "API key service unavailable",
		})
		return
	}

	// Verify the API key belongs to the user
	userAPIKeys, err := apiKeyStorage.GetAPIKeysByUserID(userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user API keys")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to verify API key ownership",
		})
		return
	}

	found := false
	for _, apiKey := range userAPIKeys {
		if apiKey.ID == keyID {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "API key not found or does not belong to user",
		})
		return
	}

	if err := apiKeyStorage.DeleteAPIKey(keyID); err != nil {
		h.logger.WithError(err).Error("Failed to delete API key")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete API key",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "API key deleted successfully",
	})
}

// Helper function
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
