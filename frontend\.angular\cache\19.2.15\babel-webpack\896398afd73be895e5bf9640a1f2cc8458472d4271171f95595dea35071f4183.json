{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/checkbox\";\nfunction RegisterComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username must be at least 3 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" You must accept the terms and conditions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_spinner_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 25);\n  }\n}\nfunction RegisterComponent_span_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control) {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return {\n      'passwordMismatch': true\n    };\n  }\n  return null;\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    constructor(formBuilder, authService, router, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.hidePassword = true;\n      this.hideConfirmPassword = true;\n      this.isLoading = false;\n      this.registerForm = this.formBuilder.group({\n        username: ['', [Validators.required, Validators.minLength(3)]],\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(8)]],\n        confirmPassword: ['', [Validators.required]],\n        acceptTerms: [false, [Validators.requiredTrue]]\n      }, {\n        validators: passwordMatchValidator\n      });\n    }\n    onSubmit() {\n      if (this.registerForm.valid) {\n        this.isLoading = true;\n        const userData = {\n          username: this.registerForm.value.username,\n          email: this.registerForm.value.email,\n          password: this.registerForm.value.password,\n          confirmPassword: this.registerForm.value.confirmPassword\n        };\n        this.authService.register(userData).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', {\n              duration: 3000\n            });\n            this.router.navigate(['/dashboard']);\n          },\n          error: error => {\n            this.isLoading = false;\n            this.snackBar.open(error.message || 'Registration failed', 'Close', {\n              duration: 5000\n            });\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegisterComponent,\n        selectors: [[\"app-register\"]],\n        decls: 82,\n        vars: 17,\n        consts: [[1, \"register-container\"], [1, \"register-card-wrapper\"], [1, \"register-card\"], [1, \"register-header\"], [1, \"logo\"], [1, \"logo-icon\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"terms-checkbox\"], [\"formControlName\", \"acceptTerms\"], [\"href\", \"#\", 1, \"terms-link\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"register-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"card-actions\"], [\"routerLink\", \"/login\", 1, \"login-link\"], [1, \"benefits-info\"], [\"diameter\", \"20\"]],\n        template: function RegisterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n            i0.ɵɵtext(6, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"h1\");\n            i0.ɵɵtext(8, \"Create Account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n            i0.ɵɵtext(10, \"Join SPT Security Platform\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"form\", 6);\n            i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_12_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(13, \"div\", 7)(14, \"mat-form-field\", 8)(15, \"mat-label\");\n            i0.ɵɵtext(16, \"Username\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"input\", 9);\n            i0.ɵɵelementStart(18, \"mat-icon\", 10);\n            i0.ɵɵtext(19, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(20, RegisterComponent_mat_error_20_Template, 2, 0, \"mat-error\", 11)(21, RegisterComponent_mat_error_21_Template, 2, 0, \"mat-error\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"mat-form-field\", 8)(23, \"mat-label\");\n            i0.ɵɵtext(24, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 12);\n            i0.ɵɵelementStart(26, \"mat-icon\", 10);\n            i0.ɵɵtext(27, \"email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(28, RegisterComponent_mat_error_28_Template, 2, 0, \"mat-error\", 11)(29, RegisterComponent_mat_error_29_Template, 2, 0, \"mat-error\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"mat-form-field\", 8)(31, \"mat-label\");\n            i0.ɵɵtext(32, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"input\", 13);\n            i0.ɵɵelementStart(34, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_34_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelementStart(35, \"mat-icon\");\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(37, RegisterComponent_mat_error_37_Template, 2, 0, \"mat-error\", 11)(38, RegisterComponent_mat_error_38_Template, 2, 0, \"mat-error\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"mat-form-field\", 8)(40, \"mat-label\");\n            i0.ɵɵtext(41, \"Confirm Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(42, \"input\", 15);\n            i0.ɵɵelementStart(43, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_43_listener() {\n              return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n            });\n            i0.ɵɵelementStart(44, \"mat-icon\");\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(46, RegisterComponent_mat_error_46_Template, 2, 0, \"mat-error\", 11)(47, RegisterComponent_mat_error_47_Template, 2, 0, \"mat-error\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 16)(49, \"mat-checkbox\", 17);\n            i0.ɵɵtext(50, \" I agree to the \");\n            i0.ɵɵelementStart(51, \"a\", 18);\n            i0.ɵɵtext(52, \"Terms of Service\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(53, \" and \");\n            i0.ɵɵelementStart(54, \"a\", 18);\n            i0.ɵɵtext(55, \"Privacy Policy\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(56, RegisterComponent_mat_error_56_Template, 2, 0, \"mat-error\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"div\", 19)(58, \"button\", 20);\n            i0.ɵɵtemplate(59, RegisterComponent_mat_spinner_59_Template, 1, 0, \"mat-spinner\", 21)(60, RegisterComponent_span_60_Template, 2, 0, \"span\", 11);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(61, \"mat-card-actions\", 22)(62, \"p\");\n            i0.ɵɵtext(63, \"Already have an account? \");\n            i0.ɵɵelementStart(64, \"a\", 23);\n            i0.ɵɵtext(65, \"Sign in\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(66, \"div\", 24)(67, \"h3\");\n            i0.ɵɵtext(68, \"\\uD83D\\uDE80 Why Choose SPT?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"ul\")(70, \"li\");\n            i0.ɵɵtext(71, \"\\uD83D\\uDD0D Advanced vulnerability detection\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"li\");\n            i0.ɵɵtext(73, \"\\uD83D\\uDCCA Real-time security monitoring\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"li\");\n            i0.ɵɵtext(75, \"\\uD83D\\uDCC8 Comprehensive analytics\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"li\");\n            i0.ɵɵtext(77, \"\\uD83D\\uDEE1\\uFE0F Enterprise-grade security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"li\");\n            i0.ɵɵtext(79, \"\\uD83D\\uDD27 Easy integration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"li\");\n            i0.ɵɵtext(81, \"\\uD83D\\uDCF1 Multi-platform support\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_7_0;\n            let tmp_8_0;\n            let tmp_11_0;\n            let tmp_12_0;\n            let tmp_13_0;\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_2_0.hasError(\"minlength\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.hasError(\"required\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_4_0.hasError(\"email\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_7_0.hasError(\"required\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.hasError(\"minlength\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_11_0.hasError(\"required\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.registerForm.hasError(\"passwordMismatch\") && !((tmp_12_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.hasError(\"required\")));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_13_0.hasError(\"required\")) && ((tmp_13_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_13_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i12.MatCheckbox],\n        styles: [\".register-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.register-card-wrapper[_ngcontent-%COMP%]{display:flex;gap:40px;align-items:center;max-width:1000px;width:100%}.register-card[_ngcontent-%COMP%]{flex:1;max-width:450px;padding:20px}.register-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:30px}.logo[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:10px;margin-bottom:10px}.logo-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:#667eea}.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;color:#333;font-weight:300}.form-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;margin-bottom:30px}.full-width[_ngcontent-%COMP%]{width:100%}.terms-checkbox[_ngcontent-%COMP%]{margin-top:10px}.terms-link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none}.terms-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.form-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.register-button[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500}.card-actions[_ngcontent-%COMP%]{text-align:center;padding-top:20px;border-top:1px solid #e0e0e0}.login-link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-weight:500}.login-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.benefits-info[_ngcontent-%COMP%]{flex:1;color:#fff;padding:40px}.benefits-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;margin-bottom:20px;font-weight:300}.benefits-info[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0}.benefits-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:8px 0;font-size:16px;opacity:.9}@media (max-width: 768px){.register-card-wrapper[_ngcontent-%COMP%]{flex-direction:column;gap:20px}.benefits-info[_ngcontent-%COMP%]{order:-1;padding:20px;text-align:center}.register-card[_ngcontent-%COMP%]{max-width:100%}}mat-spinner[_ngcontent-%COMP%]{margin-right:10px}mat-error[_ngcontent-%COMP%]{font-size:12px;margin-top:5px}\"]\n      });\n    }\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}