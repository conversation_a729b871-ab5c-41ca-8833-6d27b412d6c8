{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { SEVERITY_COLORS } from '../../models/security.models';\nimport { SecurityMetricsChartComponent } from '../../shared/charts/security-metrics-chart/security-metrics-chart.component';\nimport { ScanProgressChartComponent } from '../../shared/charts/scan-progress-chart/scan-progress-chart.component';\nimport { SecurityTrendsDashboardComponent } from '../../shared/charts/security-trends-dashboard/security-trends-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/table\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/tooltip\";\nimport * as i9 from \"@angular/router\";\nconst _c0 = a0 => [\"/scan\", a0];\nfunction DashboardComponent_div_137_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Scan ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", scan_r1.id.substring(0, 8), \"...\");\n  }\n}\nfunction DashboardComponent_div_137_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 77)(2, \"mat-icon\", 78);\n    i0.ɵɵtext(3, \"folder\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getProjectName(scan_r2.project_path));\n  }\n}\nfunction DashboardComponent_div_137_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Chains\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_10_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chain_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", chain_r4, \" \");\n  }\n}\nfunction DashboardComponent_div_137_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 79);\n    i0.ɵɵtemplate(2, DashboardComponent_div_137_td_10_mat_chip_2_Template, 2, 1, \"mat-chip\", 80);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", scan_r5.chains);\n  }\n}\nfunction DashboardComponent_div_137_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"mat-chip\")(2, \"mat-icon\", 82);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-chip status-\" + scan_r6.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusIcon(scan_r6.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, scan_r6.status), \" \");\n  }\n}\nfunction DashboardComponent_div_137_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 83)(2, \"span\", 84);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 85);\n    i0.ɵɵtext(5, \"issues\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((scan_r7.issues == null ? null : scan_r7.issues.length) || 0);\n  }\n}\nfunction DashboardComponent_div_137_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 88);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(scan_r8.created_at));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatTime(scan_r8.created_at));\n  }\n}\nfunction DashboardComponent_div_137_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 89)(2, \"button\", 90)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 91)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"download\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const scan_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, scan_r9.id));\n  }\n}\nfunction DashboardComponent_div_137_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 92);\n  }\n}\nfunction DashboardComponent_div_137_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 93);\n  }\n}\nfunction DashboardComponent_div_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"table\", 62);\n    i0.ɵɵelementContainerStart(2, 63);\n    i0.ɵɵtemplate(3, DashboardComponent_div_137_th_3_Template, 2, 0, \"th\", 64)(4, DashboardComponent_div_137_td_4_Template, 3, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 66);\n    i0.ɵɵtemplate(6, DashboardComponent_div_137_th_6_Template, 2, 0, \"th\", 64)(7, DashboardComponent_div_137_td_7_Template, 6, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 67);\n    i0.ɵɵtemplate(9, DashboardComponent_div_137_th_9_Template, 2, 0, \"th\", 64)(10, DashboardComponent_div_137_td_10_Template, 3, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 68);\n    i0.ɵɵtemplate(12, DashboardComponent_div_137_th_12_Template, 2, 0, \"th\", 64)(13, DashboardComponent_div_137_td_13_Template, 6, 6, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 69);\n    i0.ɵɵtemplate(15, DashboardComponent_div_137_th_15_Template, 2, 0, \"th\", 64)(16, DashboardComponent_div_137_td_16_Template, 6, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 70);\n    i0.ɵɵtemplate(18, DashboardComponent_div_137_th_18_Template, 2, 0, \"th\", 64)(19, DashboardComponent_div_137_td_19_Template, 6, 2, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(20, 71);\n    i0.ɵɵtemplate(21, DashboardComponent_div_137_th_21_Template, 2, 0, \"th\", 64)(22, DashboardComponent_div_137_td_22_Template, 8, 3, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(23, DashboardComponent_div_137_tr_23_Template, 1, 0, \"tr\", 72)(24, DashboardComponent_div_137_tr_24_Template, 1, 0, \"tr\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.recentScans);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n  }\n}\nfunction DashboardComponent_ng_template_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"mat-icon\", 95);\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No scans yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start your first security scan to see results here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 57)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Start First Scan \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97)(2, \"mat-chip\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 98)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 99)(12, \"mat-chip\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const issue_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getSeverityColor(issue_r10.severity));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", issue_r10.severity, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r10.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", issue_r10.file, \":\", issue_r10.line, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.chain);\n  }\n}\nfunction DashboardComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"app-scan-progress-chart\", 100);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scanProgress\", ctx_r2.getActiveScan())(\"showRealTimeUpdates\", true)(\"updateInterval\", 2000);\n  }\n}\nexport class DashboardComponent {\n  // Additional properties for enhanced header\n  get totalScans() {\n    return this.stats.totalScans;\n  }\n  get activeScans() {\n    return this.recentScans.filter(scan => scan.status === 'running').length;\n  }\n  get criticalIssues() {\n    return this.stats.criticalIssues;\n  }\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.stats = {\n      totalScans: 0,\n      criticalIssues: 0,\n      highIssues: 0,\n      mediumIssues: 0\n    };\n    this.recentScans = [];\n    this.topIssues = [];\n    this.displayedColumns = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: response => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: error => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n  loadMockData() {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts['critical'] || 0,\n      highIssues: mockScan.severity_counts['high'] || 0,\n      mediumIssues: mockScan.severity_counts['medium'] || 0\n    };\n  }\n  calculateStats(scans) {\n    this.stats.totalScans = scans.length;\n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues = [];\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.['critical'] || 0;\n      highTotal += scan.severity_counts?.['high'] || 0;\n      mediumTotal += scan.severity_counts?.['medium'] || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n    // Get top 5 most severe issues\n    this.topIssues = allIssues.sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity)).slice(0, 5);\n  }\n  getSeverityWeight(severity) {\n    const weights = {\n      critical: 4,\n      high: 3,\n      medium: 2,\n      low: 1,\n      info: 0\n    };\n    return weights[severity] || 0;\n  }\n  getSeverityColor(severity) {\n    return SEVERITY_COLORS[severity] || '#666';\n  }\n  getProjectName(path) {\n    return path.split('/').pop() || path;\n  }\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleDateString();\n  }\n  formatTime(dateString) {\n    return new Date(dateString).toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getStatusIcon(status) {\n    const icons = {\n      completed: 'check_circle',\n      running: 'hourglass_empty',\n      failed: 'error',\n      pending: 'schedule'\n    };\n    return icons[status] || 'help';\n  }\n  // Chart data methods\n  getSecurityMetricsData() {\n    return [{\n      label: 'Critical',\n      value: this.stats.criticalIssues,\n      severity: 'critical',\n      trend: 15 // Mock trend data\n    }, {\n      label: 'High',\n      value: this.stats.highIssues,\n      severity: 'high',\n      trend: -5\n    }, {\n      label: 'Medium',\n      value: this.stats.mediumIssues,\n      severity: 'medium',\n      trend: 8\n    }, {\n      label: 'Low',\n      value: Math.max(0, this.stats.totalScans * 2 - this.stats.criticalIssues - this.stats.highIssues - this.stats.mediumIssues),\n      severity: 'low',\n      trend: -12\n    }];\n  }\n  getActiveScan() {\n    const activeScan = this.recentScans.find(scan => scan.status === 'running');\n    if (!activeScan) return null;\n    return {\n      scanId: activeScan.id,\n      projectName: this.getProjectName(activeScan.project_path),\n      status: 'running',\n      progress: 65,\n      // Mock progress\n      currentStep: 'Analyzing smart contracts',\n      totalSteps: 6,\n      completedSteps: 3,\n      startTime: new Date(activeScan.created_at),\n      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000),\n      // 5 minutes from now\n      filesScanned: 45,\n      totalFiles: 78,\n      issuesFound: activeScan.issues?.length || 0\n    };\n  }\n  getSecurityTrendsData() {\n    // Generate mock trend data\n    const now = new Date();\n    const data = [];\n    for (let i = 29; i >= 0; i--) {\n      const date = new Date(now);\n      date.setDate(date.getDate() - i);\n      data.push({\n        date,\n        critical: Math.floor(Math.random() * 5) + (i < 10 ? 2 : 0),\n        high: Math.floor(Math.random() * 8) + 3,\n        medium: Math.floor(Math.random() * 12) + 5,\n        low: Math.floor(Math.random() * 15) + 8,\n        totalScans: Math.floor(Math.random() * 3) + 1,\n        averageScore: Math.floor(Math.random() * 30) + 60\n      });\n    }\n    return {\n      period: '30d',\n      data,\n      summary: {\n        totalIssues: this.stats.criticalIssues + this.stats.highIssues + this.stats.mediumIssues,\n        criticalTrend: 15,\n        // Mock trend\n        averageScore: 72,\n        scanFrequency: 1.2\n      }\n    };\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 174,\n      vars: 18,\n      consts: [[\"noScans\", \"\"], [1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-text\"], [1, \"dashboard-title\"], [1, \"title-icon\"], [1, \"dashboard-subtitle\"], [1, \"header-stats\"], [1, \"quick-stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\", 1, \"action-button\", \"primary\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/reports\", 1, \"action-button\", \"secondary\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-scans\", \"elevated\"], [1, \"stat-card-header\"], [1, \"stat-icon-wrapper\", \"success\"], [1, \"stat-icon\"], [1, \"stat-trend\"], [1, \"trend-icon\", \"positive\"], [1, \"trend-value\"], [1, \"stat-card-content\"], [1, \"stat-number\"], [1, \"stat-description\"], [1, \"stat-card-footer\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [1, \"stat-card\", \"critical-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"critical\"], [1, \"trend-icon\", \"negative\"], [1, \"stat-number\", \"critical\"], [\"mat-button\", \"\", \"color\", \"warn\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"high-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"warning\"], [1, \"trend-icon\", \"neutral\"], [1, \"stat-number\", \"warning\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"medium-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"info\"], [1, \"stat-number\", \"info\"], [\"mat-button\", \"\", \"routerLink\", \"/checklist\"], [1, \"content-section\"], [1, \"recent-scans-card\"], [1, \"section-header\"], [1, \"section-title\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/scan\", 1, \"secondary-button\"], [\"class\", \"table-container\", 4, \"ngIf\", \"ngIfElse\"], [1, \"top-issues-card\"], [1, \"issues-list\"], [\"class\", \"issue-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-section\"], [\"title\", \"Security Issues Distribution\", 3, \"data\", \"chartType\", \"showTrends\", \"animated\"], [\"class\", \"chart-section\", 4, \"ngIf\"], [3, \"trendsData\", \"showComparison\", \"autoRefresh\"], [1, \"quick-actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/checklist\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/reports\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/settings\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"scans-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"project\"], [\"matColumnDef\", \"chains\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"scan-row\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"scan-id\"], [1, \"project-info\"], [1, \"project-icon\"], [1, \"chains-container\"], [\"class\", \"chain-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"chain-chip\"], [1, \"status-icon\"], [1, \"issues-count\"], [1, \"count\"], [1, \"label\"], [1, \"date-info\"], [1, \"date\"], [1, \"time\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Details\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Download Report\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"scan-row\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"issue-item\"], [1, \"issue-severity\"], [1, \"issue-details\"], [1, \"issue-chain\"], [3, \"scanProgress\", \"showRealTimeUpdates\", \"updateInterval\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\", 5)(5, \"mat-icon\", 6);\n          i0.ɵɵtext(6, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" Security Dashboard \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9, \"Monitor your blockchain security posture in real-time\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"span\", 10);\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 11);\n          i0.ɵɵtext(15, \"Total Scans\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 9)(17, \"span\", 10);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 11);\n          i0.ɵɵtext(20, \"Active\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 9)(22, \"span\", 10);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 11);\n          i0.ɵɵtext(25, \"Critical\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 12)(27, \"button\", 13)(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Start New Scan \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"button\", 14)(32, \"mat-icon\");\n          i0.ɵɵtext(33, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34, \" View Reports \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"mat-card\", 16)(37, \"div\", 17)(38, \"div\", 18)(39, \"mat-icon\", 19);\n          i0.ɵɵtext(40, \"security\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 20)(42, \"mat-icon\", 21);\n          i0.ɵɵtext(43, \"trending_up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 22);\n          i0.ɵɵtext(45, \"+12%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 23)(47, \"div\", 24);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 11);\n          i0.ɵɵtext(50, \"Total Scans\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 25);\n          i0.ɵɵtext(52, \"Completed this month\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 26)(54, \"button\", 27)(55, \"mat-icon\");\n          i0.ɵɵtext(56, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" New Scan \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"mat-card\", 28)(59, \"div\", 17)(60, \"div\", 29)(61, \"mat-icon\", 19);\n          i0.ɵɵtext(62, \"error\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 20)(64, \"mat-icon\", 30);\n          i0.ɵɵtext(65, \"trending_up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"span\", 22);\n          i0.ɵɵtext(67, \"+3\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"div\", 23)(69, \"div\", 31);\n          i0.ɵɵtext(70);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 11);\n          i0.ɵɵtext(72, \"Critical Issues\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 25);\n          i0.ɵɵtext(74, \"Require immediate attention\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 26)(76, \"button\", 32)(77, \"mat-icon\");\n          i0.ɵɵtext(78, \"priority_high\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(79, \" View Details \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"mat-card\", 33)(81, \"div\", 17)(82, \"div\", 34)(83, \"mat-icon\", 19);\n          i0.ɵɵtext(84, \"warning\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 20)(86, \"mat-icon\", 35);\n          i0.ɵɵtext(87, \"trending_flat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"span\", 22);\n          i0.ɵɵtext(89, \"0\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 23)(91, \"div\", 36);\n          i0.ɵɵtext(92);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"div\", 11);\n          i0.ɵɵtext(94, \"High Priority\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 25);\n          i0.ɵɵtext(96, \"Should be addressed soon\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"div\", 26)(98, \"button\", 37)(99, \"mat-icon\");\n          i0.ɵɵtext(100, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(101, \" Review \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(102, \"mat-card\", 38)(103, \"div\", 17)(104, \"div\", 39)(105, \"mat-icon\", 19);\n          i0.ɵɵtext(106, \"info\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\", 20)(108, \"mat-icon\", 21);\n          i0.ɵɵtext(109, \"trending_down\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"span\", 22);\n          i0.ɵɵtext(111, \"-5\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(112, \"div\", 23)(113, \"div\", 40);\n          i0.ɵɵtext(114);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\", 11);\n          i0.ɵɵtext(116, \"Medium Priority\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 25);\n          i0.ɵɵtext(118, \"Monitor and plan fixes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(119, \"div\", 26)(120, \"button\", 41)(121, \"mat-icon\");\n          i0.ɵɵtext(122, \"checklist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(123, \" Checklist \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(124, \"div\", 42)(125, \"mat-card\", 43)(126, \"mat-card-header\", 44)(127, \"div\", 45)(128, \"mat-icon\");\n          i0.ɵɵtext(129, \"history\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"mat-card-title\");\n          i0.ɵɵtext(131, \"Recent Scans\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(132, \"button\", 46)(133, \"mat-icon\");\n          i0.ɵɵtext(134, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(135, \" New Scan \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(136, \"mat-card-content\");\n          i0.ɵɵtemplate(137, DashboardComponent_div_137_Template, 25, 3, \"div\", 47)(138, DashboardComponent_ng_template_138_Template, 11, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(140, \"mat-card\", 48)(141, \"mat-card-header\")(142, \"mat-card-title\");\n          i0.ɵɵtext(143, \"Top Security Issues\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(144, \"mat-card-content\")(145, \"div\", 49);\n          i0.ɵɵtemplate(146, DashboardComponent_div_146_Template, 14, 8, \"div\", 50);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(147, \"div\", 51);\n          i0.ɵɵelement(148, \"app-security-metrics-chart\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(149, DashboardComponent_div_149_Template, 2, 3, \"div\", 53);\n          i0.ɵɵelementStart(150, \"div\", 51);\n          i0.ɵɵelement(151, \"app-security-trends-dashboard\", 54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"mat-card\", 55)(153, \"mat-card-header\")(154, \"mat-card-title\");\n          i0.ɵɵtext(155, \"Quick Actions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(156, \"mat-card-content\")(157, \"div\", 56)(158, \"button\", 57)(159, \"mat-icon\");\n          i0.ɵɵtext(160, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(161, \" Start New Scan \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(162, \"button\", 58)(163, \"mat-icon\");\n          i0.ɵɵtext(164, \"checklist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(165, \" Security Checklist \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(166, \"button\", 59)(167, \"mat-icon\");\n          i0.ɵɵtext(168, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(169, \" Generate Report \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(170, \"button\", 60)(171, \"mat-icon\");\n          i0.ɵɵtext(172, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(173, \" Settings \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const noScans_r11 = i0.ɵɵreference(139);\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate(ctx.totalScans);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.activeScans);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.criticalIssues);\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate(ctx.stats.totalScans);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.stats.criticalIssues);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.stats.highIssues);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.stats.mediumIssues);\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentScans.length > 0)(\"ngIfElse\", noScans_r11);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topIssues);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.getSecurityMetricsData())(\"chartType\", \"doughnut\")(\"showTrends\", true)(\"animated\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveScan());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"trendsData\", ctx.getSecurityTrendsData())(\"showComparison\", true)(\"autoRefresh\", false);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, i5.MatIconButton, MatTableModule, i6.MatTable, i6.MatHeaderCellDef, i6.MatHeaderRowDef, i6.MatColumnDef, i6.MatCellDef, i6.MatRowDef, i6.MatHeaderCell, i6.MatCell, i6.MatHeaderRow, i6.MatRow, MatChipsModule, i7.MatChip, MatProgressBarModule, MatTooltipModule, i8.MatTooltip, RouterModule, i9.RouterLink, SecurityMetricsChartComponent, ScanProgressChartComponent, SecurityTrendsDashboardComponent],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  max-width: 100%;\\n  margin: 0;\\n  min-height: 100%;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);\\n  color: white;\\n  padding: var(--spt-space-12) var(--spt-space-8);\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: var(--spt-space-8);\\n  border-radius: 0 0 var(--spt-radius-3xl) var(--spt-radius-3xl);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-12);\\n  align-items: flex-start;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.header-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.dashboard-title[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-2) 0;\\n  font-size: var(--spt-text-4xl);\\n  font-weight: var(--spt-font-bold);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-3);\\n  line-height: var(--spt-leading-tight);\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: var(--spt-radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.dashboard-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-normal);\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n.header-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-6);\\n  margin-top: var(--spt-space-2);\\n}\\n\\n.quick-stat[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: var(--spt-space-3) var(--spt-space-4);\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: var(--spt-radius-xl);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-2xl);\\n  font-weight: var(--spt-font-bold);\\n  line-height: 1;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xs);\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n  margin-top: var(--spt-space-1);\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-3);\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.action-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  padding: 0 var(--spt-space-6);\\n  border-radius: var(--spt-radius-xl);\\n  font-weight: var(--spt-font-semibold);\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n\\n.action-button.primary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n}\\n\\n.action-button.primary[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: var(--spt-shadow-lg);\\n}\\n\\n.action-button.secondary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n\\n.action-button.secondary[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-1px);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: var(--spt-space-6);\\n  margin: 0 var(--spt-space-8) var(--spt-space-12) var(--spt-space-8);\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: var(--spt-radius-2xl);\\n  border: 1px solid var(--spt-border);\\n  background: var(--spt-surface);\\n  box-shadow: var(--spt-shadow-sm);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stat-card.elevated[_ngcontent-%COMP%] {\\n  box-shadow: var(--spt-shadow-lg);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: var(--spt-shadow-xl);\\n  border-color: var(--spt-primary-200);\\n}\\n\\n.stat-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spt-space-5) var(--spt-space-6) var(--spt-space-3) var(--spt-space-6);\\n}\\n\\n.stat-card-content[_ngcontent-%COMP%] {\\n  padding: 0 var(--spt-space-6) var(--spt-space-4) var(--spt-space-6);\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-3) var(--spt-space-6) var(--spt-space-5) var(--spt-space-6);\\n  border-top: 1px solid var(--spt-border-light);\\n  background: var(--spt-bg-secondary);\\n}\\n\\n.stat-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: var(--spt-radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-icon-wrapper.success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-success-100) 0%, var(--spt-success-200) 100%);\\n  border: 1px solid var(--spt-success-300);\\n}\\n\\n.stat-icon-wrapper.critical[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-error-100) 0%, var(--spt-error-200) 100%);\\n  border: 1px solid var(--spt-error-300);\\n}\\n\\n.stat-icon-wrapper.warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-warning-100) 0%, var(--spt-warning-200) 100%);\\n  border: 1px solid var(--spt-warning-300);\\n}\\n\\n.stat-icon-wrapper.info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-info-100) 0%, var(--spt-info-200) 100%);\\n  border: 1px solid var(--spt-info-300);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover   .stat-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.stat-icon-wrapper.success[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-success-700);\\n}\\n\\n.stat-icon-wrapper.critical[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-error-700);\\n}\\n\\n.stat-icon-wrapper.warning[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-700);\\n}\\n\\n.stat-icon-wrapper.info[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-info-700);\\n}\\n\\n.stat-trend[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-1);\\n  padding: var(--spt-space-1) var(--spt-space-2);\\n  border-radius: var(--spt-radius-lg);\\n  font-size: var(--spt-text-xs);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.trend-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.trend-icon.positive[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n}\\n\\n.trend-icon.negative[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.trend-icon.neutral[_ngcontent-%COMP%] {\\n  color: var(--spt-text-tertiary);\\n}\\n\\n.trend-value[_ngcontent-%COMP%] {\\n  font-weight: var(--spt-font-bold);\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-4xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-text-primary);\\n  margin-bottom: var(--spt-space-1);\\n  line-height: var(--spt-leading-none);\\n  transition: color 0.3s ease;\\n}\\n\\n.stat-number.critical[_ngcontent-%COMP%] {\\n  color: var(--spt-error-600);\\n}\\n\\n.stat-number.warning[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n}\\n\\n.stat-number.info[_ngcontent-%COMP%] {\\n  color: var(--spt-info-600);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  color: var(--spt-text-primary);\\n  font-weight: var(--spt-font-semibold);\\n  margin-bottom: var(--spt-space-1);\\n  line-height: var(--spt-leading-tight);\\n}\\n\\n.stat-description[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-text-secondary);\\n  font-weight: var(--spt-font-normal);\\n  line-height: var(--spt-leading-relaxed);\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  justify-content: flex-start;\\n  gap: var(--spt-space-2);\\n  padding: var(--spt-space-2) var(--spt-space-3);\\n  border-radius: var(--spt-radius-lg);\\n  font-weight: var(--spt-font-medium);\\n  transition: all 0.2s ease;\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateX(4px);\\n}\\n\\n.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.content-section[_ngcontent-%COMP%] {\\n  margin: 0 32px 40px 32px;\\n}\\n\\n.recent-scans-card[_ngcontent-%COMP%], \\n.top-issues-card[_ngcontent-%COMP%], \\n.quick-actions-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  border: none;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 24px 24px 16px 24px;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.section-title[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n\\n.secondary-button[_ngcontent-%COMP%] {\\n  border: 2px solid #667eea;\\n  color: #667eea;\\n  border-radius: 8px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.secondary-button[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  margin: 0 -24px;\\n}\\n\\n.scans-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: transparent;\\n}\\n\\n.scans-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  color: #64748b;\\n  font-weight: 600;\\n  font-size: 12px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  padding: 16px 24px;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.scans-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.scan-row[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.scan-row[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n}\\n\\n.scan-id[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background: #f1f5f9;\\n  padding: 4px 8px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  color: #475569;\\n}\\n\\n.project-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.project-icon[_ngcontent-%COMP%] {\\n  color: #64748b;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.chains-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n  flex-wrap: wrap;\\n}\\n\\n.chain-chip[_ngcontent-%COMP%] {\\n  background: #e0e7ff;\\n  color: #3730a3;\\n  font-size: 11px;\\n  font-weight: 500;\\n  height: 24px;\\n  border-radius: 6px;\\n}\\n\\n.status-chip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  height: 28px;\\n  border-radius: 8px;\\n  padding: 0 12px;\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background: #dcfce7;\\n  color: #166534;\\n}\\n\\n.status-running[_ngcontent-%COMP%] {\\n  background: #dbeafe;\\n  color: #1d4ed8;\\n}\\n\\n.status-failed[_ngcontent-%COMP%] {\\n  background: #fee2e2;\\n  color: #dc2626;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background: #fef3c7;\\n  color: #d97706;\\n}\\n\\n.issues-count[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.issues-count[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n\\n.issues-count[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #64748b;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.date-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.date-info[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #1e293b;\\n  font-weight: 500;\\n}\\n\\n.date-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #64748b;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  color: #64748b;\\n  transition: all 0.2s ease;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n  color: #667eea;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  color: #64748b;\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #cbd5e1;\\n  margin-bottom: 16px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #475569;\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n}\\n\\n.issues-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.issue-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.issue-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #333;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n.issue-details[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 12px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 60px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n\\n\\n.chart-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-8);\\n}\\n\\n.chart-section[_ngcontent-%COMP%]:last-of-type {\\n  margin-bottom: var(--spt-space-6);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .chart-section[_ngcontent-%COMP%] {\\n    margin-bottom: var(--spt-space-6);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatTableModule", "MatChipsModule", "MatProgressBarModule", "MatTooltipModule", "RouterModule", "SEVERITY_COLORS", "SecurityMetricsChartComponent", "ScanProgressChartComponent", "SecurityTrendsDashboardComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "scan_r1", "id", "substring", "ɵɵtextInterpolate", "ctx_r2", "getProjectName", "scan_r2", "project_path", "chain_r4", "ɵɵtemplate", "DashboardComponent_div_137_td_10_mat_chip_2_Template", "ɵɵproperty", "scan_r5", "chains", "ɵɵclassMap", "scan_r6", "status", "getStatusIcon", "ɵɵpipeBind1", "scan_r7", "issues", "length", "formatDate", "scan_r8", "created_at", "formatTime", "ɵɵpureFunction1", "_c0", "scan_r9", "ɵɵelement", "ɵɵelementContainerStart", "DashboardComponent_div_137_th_3_Template", "DashboardComponent_div_137_td_4_Template", "DashboardComponent_div_137_th_6_Template", "DashboardComponent_div_137_td_7_Template", "DashboardComponent_div_137_th_9_Template", "DashboardComponent_div_137_td_10_Template", "DashboardComponent_div_137_th_12_Template", "DashboardComponent_div_137_td_13_Template", "DashboardComponent_div_137_th_15_Template", "DashboardComponent_div_137_td_16_Template", "DashboardComponent_div_137_th_18_Template", "DashboardComponent_div_137_td_19_Template", "DashboardComponent_div_137_th_21_Template", "DashboardComponent_div_137_td_22_Template", "DashboardComponent_div_137_tr_23_Template", "DashboardComponent_div_137_tr_24_Template", "recentScans", "displayedColumns", "ɵɵstyleProp", "getSeverityColor", "issue_r10", "severity", "title", "description", "ɵɵtextInterpolate2", "file", "line", "chain", "getActiveScan", "DashboardComponent", "totalScans", "stats", "activeScans", "filter", "scan", "criticalIssues", "constructor", "apiService", "highIssues", "mediumIssues", "topIssues", "ngOnInit", "loadDashboardData", "getScanHistory", "subscribe", "next", "response", "scans", "slice", "calculateStats", "error", "console", "loadMockData", "mockScan", "generateMockScanResult", "severity_counts", "criticalTotal", "highTotal", "mediumTotal", "allIssues", "for<PERSON>ach", "concat", "sort", "a", "b", "getSeverityWeight", "weights", "critical", "high", "medium", "low", "info", "path", "split", "pop", "dateString", "Date", "toLocaleDateString", "toLocaleTimeString", "hour", "minute", "icons", "completed", "running", "failed", "pending", "getSecurityMetricsData", "label", "value", "trend", "Math", "max", "activeScan", "find", "scanId", "projectName", "progress", "currentStep", "totalSteps", "completedSteps", "startTime", "estimatedCompletion", "now", "filesScanned", "totalFiles", "issuesFound", "getSecurityTrendsData", "data", "i", "date", "setDate", "getDate", "push", "floor", "random", "averageScore", "period", "summary", "totalIssues", "criticalTrend", "scanFrequency", "ɵɵdirectiveInject", "i1", "ApiService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_137_Template", "DashboardComponent_ng_template_138_Template", "ɵɵtemplateRefExtractor", "DashboardComponent_div_146_Template", "DashboardComponent_div_149_Template", "noScans_r11", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "TitleCasePipe", "i3", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i4", "MatIcon", "i5", "MatButton", "MatIconButton", "i6", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i7", "MatChip", "i8", "MatTooltip", "i9", "RouterLink", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { ApiService } from '../../services/api.service';\nimport { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';\nimport { SecurityMetricsChartComponent, SecurityMetric } from '../../shared/charts/security-metrics-chart/security-metrics-chart.component';\nimport { ScanProgressChartComponent, ScanProgress } from '../../shared/charts/scan-progress-chart/scan-progress-chart.component';\nimport { SecurityTrendsDashboardComponent, SecurityTrend, TrendDataPoint } from '../../shared/charts/security-trends-dashboard/security-trends-dashboard.component';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatTableModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    MatTooltipModule,\n    RouterModule,\n    SecurityMetricsChartComponent,\n    ScanProgressChartComponent,\n    SecurityTrendsDashboardComponent\n  ],\n  template: `\n    <div class=\"dashboard-container\">\n      <!-- Enhanced Header Section -->\n      <div class=\"dashboard-header\">\n        <div class=\"header-content\">\n          <div class=\"header-text\">\n            <h1 class=\"dashboard-title\">\n              <mat-icon class=\"title-icon\">dashboard</mat-icon>\n              Security Dashboard\n            </h1>\n            <p class=\"dashboard-subtitle\">Monitor your blockchain security posture in real-time</p>\n          </div>\n          <div class=\"header-stats\">\n            <div class=\"quick-stat\">\n              <span class=\"stat-value\">{{ totalScans }}</span>\n              <span class=\"stat-label\">Total Scans</span>\n            </div>\n            <div class=\"quick-stat\">\n              <span class=\"stat-value\">{{ activeScans }}</span>\n              <span class=\"stat-label\">Active</span>\n            </div>\n            <div class=\"quick-stat\">\n              <span class=\"stat-value\">{{ criticalIssues }}</span>\n              <span class=\"stat-label\">Critical</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"header-actions\">\n          <button mat-raised-button color=\"primary\" routerLink=\"/scan\" class=\"action-button primary\">\n            <mat-icon>security</mat-icon>\n            Start New Scan\n          </button>\n          <button mat-stroked-button routerLink=\"/reports\" class=\"action-button secondary\">\n            <mat-icon>assessment</mat-icon>\n            View Reports\n          </button>\n        </div>\n      </div>\n\n      <!-- Enhanced Stats Grid -->\n      <div class=\"stats-grid\">\n        <mat-card class=\"stat-card total-scans elevated\">\n          <div class=\"stat-card-header\">\n            <div class=\"stat-icon-wrapper success\">\n              <mat-icon class=\"stat-icon\">security</mat-icon>\n            </div>\n            <div class=\"stat-trend\">\n              <mat-icon class=\"trend-icon positive\">trending_up</mat-icon>\n              <span class=\"trend-value\">+12%</span>\n            </div>\n          </div>\n          <div class=\"stat-card-content\">\n            <div class=\"stat-number\">{{ stats.totalScans }}</div>\n            <div class=\"stat-label\">Total Scans</div>\n            <div class=\"stat-description\">Completed this month</div>\n          </div>\n          <div class=\"stat-card-footer\">\n            <button mat-button color=\"primary\" routerLink=\"/scan\">\n              <mat-icon>add</mat-icon>\n              New Scan\n            </button>\n          </div>\n        </mat-card>\n\n        <mat-card class=\"stat-card critical-issues elevated\">\n          <div class=\"stat-card-header\">\n            <div class=\"stat-icon-wrapper critical\">\n              <mat-icon class=\"stat-icon\">error</mat-icon>\n            </div>\n            <div class=\"stat-trend\">\n              <mat-icon class=\"trend-icon negative\">trending_up</mat-icon>\n              <span class=\"trend-value\">+3</span>\n            </div>\n          </div>\n          <div class=\"stat-card-content\">\n            <div class=\"stat-number critical\">{{ stats.criticalIssues }}</div>\n            <div class=\"stat-label\">Critical Issues</div>\n            <div class=\"stat-description\">Require immediate attention</div>\n          </div>\n          <div class=\"stat-card-footer\">\n            <button mat-button color=\"warn\" routerLink=\"/reports\">\n              <mat-icon>priority_high</mat-icon>\n              View Details\n            </button>\n          </div>\n        </mat-card>\n\n        <mat-card class=\"stat-card high-issues elevated\">\n          <div class=\"stat-card-header\">\n            <div class=\"stat-icon-wrapper warning\">\n              <mat-icon class=\"stat-icon\">warning</mat-icon>\n            </div>\n            <div class=\"stat-trend\">\n              <mat-icon class=\"trend-icon neutral\">trending_flat</mat-icon>\n              <span class=\"trend-value\">0</span>\n            </div>\n          </div>\n          <div class=\"stat-card-content\">\n            <div class=\"stat-number warning\">{{ stats.highIssues }}</div>\n            <div class=\"stat-label\">High Priority</div>\n            <div class=\"stat-description\">Should be addressed soon</div>\n          </div>\n          <div class=\"stat-card-footer\">\n            <button mat-button color=\"accent\" routerLink=\"/reports\">\n              <mat-icon>visibility</mat-icon>\n              Review\n            </button>\n          </div>\n        </mat-card>\n\n        <mat-card class=\"stat-card medium-issues elevated\">\n          <div class=\"stat-card-header\">\n            <div class=\"stat-icon-wrapper info\">\n              <mat-icon class=\"stat-icon\">info</mat-icon>\n            </div>\n            <div class=\"stat-trend\">\n              <mat-icon class=\"trend-icon positive\">trending_down</mat-icon>\n              <span class=\"trend-value\">-5</span>\n            </div>\n          </div>\n          <div class=\"stat-card-content\">\n            <div class=\"stat-number info\">{{ stats.mediumIssues }}</div>\n            <div class=\"stat-label\">Medium Priority</div>\n            <div class=\"stat-description\">Monitor and plan fixes</div>\n          </div>\n          <div class=\"stat-card-footer\">\n            <button mat-button routerLink=\"/checklist\">\n              <mat-icon>checklist</mat-icon>\n              Checklist\n            </button>\n          </div>\n        </mat-card>\n      </div>\n\n      <!-- Recent Scans -->\n      <div class=\"content-section\">\n        <mat-card class=\"recent-scans-card\">\n          <mat-card-header class=\"section-header\">\n            <div class=\"section-title\">\n              <mat-icon>history</mat-icon>\n              <mat-card-title>Recent Scans</mat-card-title>\n            </div>\n            <button mat-stroked-button routerLink=\"/scan\" class=\"secondary-button\">\n              <mat-icon>add</mat-icon>\n              New Scan\n            </button>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"table-container\" *ngIf=\"recentScans.length > 0; else noScans\">\n              <table mat-table [dataSource]=\"recentScans\" class=\"scans-table\">\n                <ng-container matColumnDef=\"id\">\n                  <th mat-header-cell *matHeaderCellDef>Scan ID</th>\n                  <td mat-cell *matCellDef=\"let scan\">\n                    <span class=\"scan-id\">{{ scan.id.substring(0, 8) }}...</span>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"project\">\n                  <th mat-header-cell *matHeaderCellDef>Project</th>\n                  <td mat-cell *matCellDef=\"let scan\">\n                    <div class=\"project-info\">\n                      <mat-icon class=\"project-icon\">folder</mat-icon>\n                      <span>{{ getProjectName(scan.project_path) }}</span>\n                    </div>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"chains\">\n                  <th mat-header-cell *matHeaderCellDef>Chains</th>\n                  <td mat-cell *matCellDef=\"let scan\">\n                    <div class=\"chains-container\">\n                      <mat-chip *ngFor=\"let chain of scan.chains\" class=\"chain-chip\">\n                        {{ chain }}\n                      </mat-chip>\n                    </div>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"status\">\n                  <th mat-header-cell *matHeaderCellDef>Status</th>\n                  <td mat-cell *matCellDef=\"let scan\">\n                    <mat-chip [class]=\"'status-chip status-' + scan.status\">\n                      <mat-icon class=\"status-icon\">{{ getStatusIcon(scan.status) }}</mat-icon>\n                      {{ scan.status | titlecase }}\n                    </mat-chip>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"issues\">\n                  <th mat-header-cell *matHeaderCellDef>Issues</th>\n                  <td mat-cell *matCellDef=\"let scan\">\n                    <div class=\"issues-count\">\n                      <span class=\"count\">{{ scan.issues?.length || 0 }}</span>\n                      <span class=\"label\">issues</span>\n                    </div>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"date\">\n                  <th mat-header-cell *matHeaderCellDef>Date</th>\n                  <td mat-cell *matCellDef=\"let scan\">\n                    <div class=\"date-info\">\n                      <span class=\"date\">{{ formatDate(scan.created_at) }}</span>\n                      <span class=\"time\">{{ formatTime(scan.created_at) }}</span>\n                    </div>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"actions\">\n                  <th mat-header-cell *matHeaderCellDef>Actions</th>\n                  <td mat-cell *matCellDef=\"let scan\">\n                    <div class=\"action-buttons\">\n                      <button mat-icon-button [routerLink]=\"['/scan', scan.id]\" matTooltip=\"View Details\">\n                        <mat-icon>visibility</mat-icon>\n                      </button>\n                      <button mat-icon-button matTooltip=\"Download Report\">\n                        <mat-icon>download</mat-icon>\n                      </button>\n                    </div>\n                  </td>\n                </ng-container>\n\n                <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n                <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" class=\"scan-row\"></tr>\n              </table>\n            </div>\n\n            <ng-template #noScans>\n              <div class=\"empty-state\">\n                <mat-icon class=\"empty-icon\">security</mat-icon>\n                <h3>No scans yet</h3>\n                <p>Start your first security scan to see results here</p>\n                <button mat-raised-button color=\"primary\" routerLink=\"/scan\">\n                  <mat-icon>add</mat-icon>\n                  Start First Scan\n                </button>\n              </div>\n            </ng-template>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Top Issues -->\n      <mat-card class=\"top-issues-card\">\n        <mat-card-header>\n          <mat-card-title>Top Security Issues</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"issues-list\">\n            <div *ngFor=\"let issue of topIssues\" class=\"issue-item\">\n              <div class=\"issue-severity\">\n                <mat-chip [style.background-color]=\"getSeverityColor(issue.severity)\">\n                  {{ issue.severity }}\n                </mat-chip>\n              </div>\n              <div class=\"issue-details\">\n                <h4>{{ issue.title }}</h4>\n                <p>{{ issue.description }}</p>\n                <small>{{ issue.file }}:{{ issue.line }}</small>\n              </div>\n              <div class=\"issue-chain\">\n                <mat-chip>{{ issue.chain }}</mat-chip>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Security Metrics Chart -->\n      <div class=\"chart-section\">\n        <app-security-metrics-chart\n          title=\"Security Issues Distribution\"\n          [data]=\"getSecurityMetricsData()\"\n          [chartType]=\"'doughnut'\"\n          [showTrends]=\"true\"\n          [animated]=\"true\">\n        </app-security-metrics-chart>\n      </div>\n\n      <!-- Active Scan Progress -->\n      <div class=\"chart-section\" *ngIf=\"getActiveScan()\">\n        <app-scan-progress-chart\n          [scanProgress]=\"getActiveScan()\"\n          [showRealTimeUpdates]=\"true\"\n          [updateInterval]=\"2000\">\n        </app-scan-progress-chart>\n      </div>\n\n      <!-- Security Trends Dashboard -->\n      <div class=\"chart-section\">\n        <app-security-trends-dashboard\n          [trendsData]=\"getSecurityTrendsData()\"\n          [showComparison]=\"true\"\n          [autoRefresh]=\"false\">\n        </app-security-trends-dashboard>\n      </div>\n\n      <!-- Quick Actions -->\n      <mat-card class=\"quick-actions-card\">\n        <mat-card-header>\n          <mat-card-title>Quick Actions</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"actions-grid\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/scan\">\n              <mat-icon>security</mat-icon>\n              Start New Scan\n            </button>\n            <button mat-raised-button color=\"accent\" routerLink=\"/checklist\">\n              <mat-icon>checklist</mat-icon>\n              Security Checklist\n            </button>\n            <button mat-raised-button routerLink=\"/reports\">\n              <mat-icon>assessment</mat-icon>\n              Generate Report\n            </button>\n            <button mat-raised-button routerLink=\"/settings\">\n              <mat-icon>settings</mat-icon>\n              Settings\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .dashboard-container {\n      padding: 0;\n      max-width: 100%;\n      margin: 0;\n      min-height: 100%;\n    }\n\n    .dashboard-header {\n      background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);\n      color: white;\n      padding: var(--spt-space-12) var(--spt-space-8);\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: var(--spt-space-8);\n      border-radius: 0 0 var(--spt-radius-3xl) var(--spt-radius-3xl);\n      position: relative;\n      overflow: hidden;\n    }\n\n    .dashboard-header::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n      opacity: 0.3;\n    }\n\n    .header-content {\n      display: flex;\n      gap: var(--spt-space-12);\n      align-items: flex-start;\n      position: relative;\n      z-index: 1;\n    }\n\n    .header-text {\n      flex: 1;\n    }\n\n    .dashboard-title {\n      margin: 0 0 var(--spt-space-2) 0;\n      font-size: var(--spt-text-4xl);\n      font-weight: var(--spt-font-bold);\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-3);\n      line-height: var(--spt-leading-tight);\n    }\n\n    .title-icon {\n      font-size: 40px;\n      width: 40px;\n      height: 40px;\n      background: rgba(255, 255, 255, 0.2);\n      border-radius: var(--spt-radius-xl);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .dashboard-subtitle {\n      margin: 0;\n      opacity: 0.9;\n      font-size: var(--spt-text-lg);\n      font-weight: var(--spt-font-normal);\n      line-height: var(--spt-leading-relaxed);\n    }\n\n    .header-stats {\n      display: flex;\n      gap: var(--spt-space-6);\n      margin-top: var(--spt-space-2);\n    }\n\n    .quick-stat {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: var(--spt-space-3) var(--spt-space-4);\n      background: rgba(255, 255, 255, 0.15);\n      border-radius: var(--spt-radius-xl);\n      backdrop-filter: blur(10px);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n    }\n\n    .stat-value {\n      font-size: var(--spt-text-2xl);\n      font-weight: var(--spt-font-bold);\n      line-height: 1;\n    }\n\n    .stat-label {\n      font-size: var(--spt-text-xs);\n      opacity: 0.8;\n      text-transform: uppercase;\n      letter-spacing: 0.05em;\n      margin-top: var(--spt-space-1);\n    }\n\n    .header-actions {\n      display: flex;\n      gap: var(--spt-space-3);\n      position: relative;\n      z-index: 1;\n    }\n\n    .action-button {\n      height: 48px;\n      padding: 0 var(--spt-space-6);\n      border-radius: var(--spt-radius-xl);\n      font-weight: var(--spt-font-semibold);\n      transition: all 0.3s ease;\n      backdrop-filter: blur(10px);\n      border: 1px solid rgba(255, 255, 255, 0.3);\n    }\n\n    .action-button.primary {\n      background: rgba(255, 255, 255, 0.2);\n      color: white;\n    }\n\n    .action-button.primary:hover {\n      background: rgba(255, 255, 255, 0.3);\n      transform: translateY(-2px);\n      box-shadow: var(--spt-shadow-lg);\n    }\n\n    .action-button.secondary {\n      background: rgba(255, 255, 255, 0.1);\n      color: white;\n    }\n\n    .action-button.secondary:hover {\n      background: rgba(255, 255, 255, 0.2);\n      transform: translateY(-1px);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n      gap: var(--spt-space-6);\n      margin: 0 var(--spt-space-8) var(--spt-space-12) var(--spt-space-8);\n    }\n\n    .stat-card {\n      border-radius: var(--spt-radius-2xl);\n      border: 1px solid var(--spt-border);\n      background: var(--spt-surface);\n      box-shadow: var(--spt-shadow-sm);\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      overflow: hidden;\n      position: relative;\n    }\n\n    .stat-card.elevated {\n      box-shadow: var(--spt-shadow-lg);\n    }\n\n    .stat-card:hover {\n      transform: translateY(-4px);\n      box-shadow: var(--spt-shadow-xl);\n      border-color: var(--spt-primary-200);\n    }\n\n    .stat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: var(--spt-space-5) var(--spt-space-6) var(--spt-space-3) var(--spt-space-6);\n    }\n\n    .stat-card-content {\n      padding: 0 var(--spt-space-6) var(--spt-space-4) var(--spt-space-6);\n    }\n\n    .stat-card-footer {\n      padding: var(--spt-space-3) var(--spt-space-6) var(--spt-space-5) var(--spt-space-6);\n      border-top: 1px solid var(--spt-border-light);\n      background: var(--spt-bg-secondary);\n    }\n\n    .stat-icon-wrapper {\n      width: 56px;\n      height: 56px;\n      border-radius: var(--spt-radius-xl);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n      transition: all 0.3s ease;\n    }\n\n    .stat-icon-wrapper.success {\n      background: linear-gradient(135deg, var(--spt-success-100) 0%, var(--spt-success-200) 100%);\n      border: 1px solid var(--spt-success-300);\n    }\n\n    .stat-icon-wrapper.critical {\n      background: linear-gradient(135deg, var(--spt-error-100) 0%, var(--spt-error-200) 100%);\n      border: 1px solid var(--spt-error-300);\n    }\n\n    .stat-icon-wrapper.warning {\n      background: linear-gradient(135deg, var(--spt-warning-100) 0%, var(--spt-warning-200) 100%);\n      border: 1px solid var(--spt-warning-300);\n    }\n\n    .stat-icon-wrapper.info {\n      background: linear-gradient(135deg, var(--spt-info-100) 0%, var(--spt-info-200) 100%);\n      border: 1px solid var(--spt-info-300);\n    }\n\n    .stat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n      transition: transform 0.3s ease;\n    }\n\n    .stat-card:hover .stat-icon {\n      transform: scale(1.1);\n    }\n\n    .stat-icon-wrapper.success .stat-icon { color: var(--spt-success-700); }\n    .stat-icon-wrapper.critical .stat-icon { color: var(--spt-error-700); }\n    .stat-icon-wrapper.warning .stat-icon { color: var(--spt-warning-700); }\n    .stat-icon-wrapper.info .stat-icon { color: var(--spt-info-700); }\n\n    .stat-trend {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-1);\n      padding: var(--spt-space-1) var(--spt-space-2);\n      border-radius: var(--spt-radius-lg);\n      font-size: var(--spt-text-xs);\n      font-weight: var(--spt-font-semibold);\n    }\n\n    .trend-icon {\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    .trend-icon.positive { color: var(--spt-success-600); }\n    .trend-icon.negative { color: var(--spt-error-600); }\n    .trend-icon.neutral { color: var(--spt-text-tertiary); }\n\n    .trend-value {\n      font-weight: var(--spt-font-bold);\n    }\n\n    .stat-number {\n      font-size: var(--spt-text-4xl);\n      font-weight: var(--spt-font-bold);\n      color: var(--spt-text-primary);\n      margin-bottom: var(--spt-space-1);\n      line-height: var(--spt-leading-none);\n      transition: color 0.3s ease;\n    }\n\n    .stat-number.critical { color: var(--spt-error-600); }\n    .stat-number.warning { color: var(--spt-warning-600); }\n    .stat-number.info { color: var(--spt-info-600); }\n\n    .stat-label {\n      font-size: var(--spt-text-base);\n      color: var(--spt-text-primary);\n      font-weight: var(--spt-font-semibold);\n      margin-bottom: var(--spt-space-1);\n      line-height: var(--spt-leading-tight);\n    }\n\n    .stat-description {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-text-secondary);\n      font-weight: var(--spt-font-normal);\n      line-height: var(--spt-leading-relaxed);\n    }\n\n    .stat-card-footer button {\n      width: 100%;\n      justify-content: flex-start;\n      gap: var(--spt-space-2);\n      padding: var(--spt-space-2) var(--spt-space-3);\n      border-radius: var(--spt-radius-lg);\n      font-weight: var(--spt-font-medium);\n      transition: all 0.2s ease;\n    }\n\n    .stat-card-footer button:hover {\n      transform: translateX(4px);\n    }\n\n    .stat-card-footer button mat-icon {\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .content-section {\n      margin: 0 32px 40px 32px;\n    }\n\n    .recent-scans-card,\n    .top-issues-card,\n    .quick-actions-card {\n      border-radius: 16px;\n      border: none;\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);\n    }\n\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 24px 24px 16px 24px;\n      border-bottom: 1px solid #e2e8f0;\n    }\n\n    .section-title {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .section-title mat-icon {\n      color: #667eea;\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .section-title mat-card-title {\n      margin: 0;\n      font-size: 20px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n\n    .secondary-button {\n      border: 2px solid #667eea;\n      color: #667eea;\n      border-radius: 8px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n    }\n\n    .secondary-button:hover {\n      background: #667eea;\n      color: white;\n    }\n\n    .table-container {\n      overflow-x: auto;\n      margin: 0 -24px;\n    }\n\n    .scans-table {\n      width: 100%;\n      background: transparent;\n    }\n\n    .scans-table th {\n      background: #f8fafc;\n      color: #64748b;\n      font-weight: 600;\n      font-size: 12px;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n      padding: 16px 24px;\n      border-bottom: 1px solid #e2e8f0;\n    }\n\n    .scans-table td {\n      padding: 16px 24px;\n      border-bottom: 1px solid #f1f5f9;\n    }\n\n    .scan-row {\n      transition: all 0.2s ease;\n    }\n\n    .scan-row:hover {\n      background: #f8fafc;\n    }\n\n    .scan-id {\n      font-family: 'Courier New', monospace;\n      background: #f1f5f9;\n      padding: 4px 8px;\n      border-radius: 6px;\n      font-size: 12px;\n      color: #475569;\n    }\n\n    .project-info {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .project-icon {\n      color: #64748b;\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .chains-container {\n      display: flex;\n      gap: 6px;\n      flex-wrap: wrap;\n    }\n\n    .chain-chip {\n      background: #e0e7ff;\n      color: #3730a3;\n      font-size: 11px;\n      font-weight: 500;\n      height: 24px;\n      border-radius: 6px;\n    }\n\n    .status-chip {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      font-size: 12px;\n      font-weight: 500;\n      height: 28px;\n      border-radius: 8px;\n      padding: 0 12px;\n    }\n\n    .status-icon {\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    .status-completed {\n      background: #dcfce7;\n      color: #166534;\n    }\n\n    .status-running {\n      background: #dbeafe;\n      color: #1d4ed8;\n    }\n\n    .status-failed {\n      background: #fee2e2;\n      color: #dc2626;\n    }\n\n    .status-pending {\n      background: #fef3c7;\n      color: #d97706;\n    }\n\n    .issues-count {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n    }\n\n    .issues-count .count {\n      font-size: 18px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n\n    .issues-count .label {\n      font-size: 11px;\n      color: #64748b;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    .date-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .date-info .date {\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 500;\n    }\n\n    .date-info .time {\n      font-size: 12px;\n      color: #64748b;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 4px;\n    }\n\n    .action-buttons button {\n      width: 36px;\n      height: 36px;\n      border-radius: 8px;\n      color: #64748b;\n      transition: all 0.2s ease;\n    }\n\n    .action-buttons button:hover {\n      background: #f1f5f9;\n      color: #667eea;\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 60px 20px;\n      color: #64748b;\n    }\n\n    .empty-icon {\n      font-size: 64px;\n      width: 64px;\n      height: 64px;\n      color: #cbd5e1;\n      margin-bottom: 16px;\n    }\n\n    .empty-state h3 {\n      margin: 0 0 8px 0;\n      color: #475569;\n      font-size: 20px;\n      font-weight: 600;\n    }\n\n    .empty-state p {\n      margin: 0 0 24px 0;\n      font-size: 14px;\n    }\n\n    .issues-list {\n      display: flex;\n      flex-direction: column;\n      gap: 15px;\n    }\n\n    .issue-item {\n      display: flex;\n      align-items: center;\n      gap: 15px;\n      padding: 15px;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .issue-details {\n      flex: 1;\n    }\n\n    .issue-details h4 {\n      margin: 0 0 5px 0;\n      color: #333;\n    }\n\n    .issue-details p {\n      margin: 0 0 5px 0;\n      color: #666;\n      font-size: 14px;\n    }\n\n    .issue-details small {\n      color: #999;\n      font-size: 12px;\n    }\n\n    .actions-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n    }\n\n    .actions-grid button {\n      height: 60px;\n      display: flex;\n      flex-direction: column;\n      gap: 5px;\n    }\n\n    .actions-grid mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    /* Chart Sections */\n    .chart-section {\n      margin-bottom: var(--spt-space-8);\n    }\n\n    .chart-section:last-of-type {\n      margin-bottom: var(--spt-space-6);\n    }\n\n    /* Responsive adjustments for charts */\n    @media (max-width: 768px) {\n      .chart-section {\n        margin-bottom: var(--spt-space-6);\n      }\n    }\n  `]\n})\nexport class DashboardComponent implements OnInit {\n  stats = {\n    totalScans: 0,\n    criticalIssues: 0,\n    highIssues: 0,\n    mediumIssues: 0\n  };\n\n  recentScans: ScanResult[] = [];\n  topIssues: SecurityIssue[] = [];\n  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n\n  // Additional properties for enhanced header\n  get totalScans(): number { return this.stats.totalScans; }\n  get activeScans(): number { return this.recentScans.filter(scan => scan.status === 'running').length; }\n  get criticalIssues(): number { return this.stats.criticalIssues; }\n\n  constructor(private apiService: ApiService) {}\n\n  ngOnInit(): void {\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    // Load recent scans\n    this.apiService.getScanHistory().subscribe({\n      next: (response) => {\n        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n        this.calculateStats(response.scans);\n      },\n      error: (error) => {\n        console.error('Error loading scan history:', error);\n        // Use mock data for development\n        this.loadMockData();\n      }\n    });\n  }\n\n  loadMockData(): void {\n    // Generate mock data for development\n    const mockScan = this.apiService.generateMockScanResult();\n    this.recentScans = [mockScan];\n    this.topIssues = mockScan.issues;\n    this.stats = {\n      totalScans: 1,\n      criticalIssues: mockScan.severity_counts['critical'] || 0,\n      highIssues: mockScan.severity_counts['high'] || 0,\n      mediumIssues: mockScan.severity_counts['medium'] || 0\n    };\n  }\n\n  calculateStats(scans: ScanResult[]): void {\n    this.stats.totalScans = scans.length;\n    \n    let criticalTotal = 0;\n    let highTotal = 0;\n    let mediumTotal = 0;\n    let allIssues: SecurityIssue[] = [];\n\n    scans.forEach(scan => {\n      criticalTotal += scan.severity_counts?.['critical'] || 0;\n      highTotal += scan.severity_counts?.['high'] || 0;\n      mediumTotal += scan.severity_counts?.['medium'] || 0;\n      allIssues = allIssues.concat(scan.issues || []);\n    });\n\n    this.stats.criticalIssues = criticalTotal;\n    this.stats.highIssues = highTotal;\n    this.stats.mediumIssues = mediumTotal;\n\n    // Get top 5 most severe issues\n    this.topIssues = allIssues\n      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))\n      .slice(0, 5);\n  }\n\n  getSeverityWeight(severity: string): number {\n    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };\n    return weights[severity as keyof typeof weights] || 0;\n  }\n\n  getSeverityColor(severity: string): string {\n    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';\n  }\n\n  getProjectName(path: string): string {\n    return path.split('/').pop() || path;\n  }\n\n  formatDate(dateString: string): string {\n    return new Date(dateString).toLocaleDateString();\n  }\n\n  formatTime(dateString: string): string {\n    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  }\n\n  getStatusIcon(status: string): string {\n    const icons = {\n      completed: 'check_circle',\n      running: 'hourglass_empty',\n      failed: 'error',\n      pending: 'schedule'\n    };\n    return icons[status as keyof typeof icons] || 'help';\n  }\n\n  // Chart data methods\n  getSecurityMetricsData(): SecurityMetric[] {\n    return [\n      {\n        label: 'Critical',\n        value: this.stats.criticalIssues,\n        severity: 'critical',\n        trend: 15 // Mock trend data\n      },\n      {\n        label: 'High',\n        value: this.stats.highIssues,\n        severity: 'high',\n        trend: -5\n      },\n      {\n        label: 'Medium',\n        value: this.stats.mediumIssues,\n        severity: 'medium',\n        trend: 8\n      },\n      {\n        label: 'Low',\n        value: Math.max(0, this.stats.totalScans * 2 - this.stats.criticalIssues - this.stats.highIssues - this.stats.mediumIssues),\n        severity: 'low',\n        trend: -12\n      }\n    ];\n  }\n\n  getActiveScan(): ScanProgress | null {\n    const activeScan = this.recentScans.find(scan => scan.status === 'running');\n    if (!activeScan) return null;\n\n    return {\n      scanId: activeScan.id,\n      projectName: this.getProjectName(activeScan.project_path),\n      status: 'running',\n      progress: 65, // Mock progress\n      currentStep: 'Analyzing smart contracts',\n      totalSteps: 6,\n      completedSteps: 3,\n      startTime: new Date(activeScan.created_at),\n      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes from now\n      filesScanned: 45,\n      totalFiles: 78,\n      issuesFound: activeScan.issues?.length || 0\n    };\n  }\n\n  getSecurityTrendsData(): SecurityTrend | null {\n    // Generate mock trend data\n    const now = new Date();\n    const data: TrendDataPoint[] = [];\n\n    for (let i = 29; i >= 0; i--) {\n      const date = new Date(now);\n      date.setDate(date.getDate() - i);\n\n      data.push({\n        date,\n        critical: Math.floor(Math.random() * 5) + (i < 10 ? 2 : 0),\n        high: Math.floor(Math.random() * 8) + 3,\n        medium: Math.floor(Math.random() * 12) + 5,\n        low: Math.floor(Math.random() * 15) + 8,\n        totalScans: Math.floor(Math.random() * 3) + 1,\n        averageScore: Math.floor(Math.random() * 30) + 60\n      });\n    }\n\n    return {\n      period: '30d',\n      data,\n      summary: {\n        totalIssues: this.stats.criticalIssues + this.stats.highIssues + this.stats.mediumIssues,\n        criticalTrend: 15, // Mock trend\n        averageScore: 72,\n        scanFrequency: 1.2\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAAoCC,eAAe,QAAQ,8BAA8B;AACzF,SAASC,6BAA6B,QAAwB,6EAA6E;AAC3I,SAASC,0BAA0B,QAAsB,uEAAuE;AAChI,SAASC,gCAAgC,QAAuC,mFAAmF;;;;;;;;;;;;;;IA0KjJC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAoC,eACZ;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IACxDF,EADwD,CAAAG,YAAA,EAAO,EAC1D;;;;IADmBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,KAAAC,OAAA,CAAAC,EAAA,CAAAC,SAAA,cAAgC;;;;;IAKxDR,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG9CH,EAFJ,CAAAC,cAAA,aAAoC,cACR,mBACO;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAEjDF,EAFiD,CAAAG,YAAA,EAAO,EAChD,EACH;;;;;IAFKH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA,CAAAC,YAAA,EAAuC;;;;;IAMjDb,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAAA,CAAAC,cAAA,mBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAS,QAAA,MACF;;;;;IAHFd,EADF,CAAAC,cAAA,aAAoC,cACJ;IAC5BD,EAAA,CAAAe,UAAA,IAAAC,oDAAA,uBAA+D;IAInEhB,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAJ2BH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAiB,UAAA,YAAAC,OAAA,CAAAC,MAAA,CAAc;;;;;IAQ9CnB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAFJ,CAAAC,cAAA,aAAoC,eACsB,mBACxB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzEH,EAAA,CAAAE,MAAA,GACF;;IACFF,EADE,CAAAG,YAAA,EAAW,EACR;;;;;IAJOH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAoB,UAAA,yBAAAC,OAAA,CAAAC,MAAA,CAA6C;IACvBtB,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAa,aAAA,CAAAF,OAAA,CAAAC,MAAA,EAAgC;IAC9DtB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAwB,WAAA,OAAAH,OAAA,CAAAC,MAAA,OACF;;;;;IAKFtB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAFJ,CAAAC,cAAA,aAAoC,cACR,eACJ;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAE9BF,EAF8B,CAAAG,YAAA,EAAO,EAC7B,EACH;;;;IAHmBH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAS,iBAAA,EAAAgB,OAAA,CAAAC,MAAA,kBAAAD,OAAA,CAAAC,MAAA,CAAAC,MAAA,OAA8B;;;;;IAOtD3B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG3CH,EAFJ,CAAAC,cAAA,aAAoC,cACX,eACF;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAExDF,EAFwD,CAAAG,YAAA,EAAO,EACvD,EACH;;;;;IAHkBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAkB,UAAA,CAAAC,OAAA,CAAAC,UAAA,EAAiC;IACjC9B,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAqB,UAAA,CAAAF,OAAA,CAAAC,UAAA,EAAiC;;;;;IAMxD9B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAI5CH,EAHN,CAAAC,cAAA,aAAoC,cACN,iBAC0D,eACxE;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IAEPH,EADF,CAAAC,cAAA,iBAAqD,eACzC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAGxBF,EAHwB,CAAAG,YAAA,EAAW,EACtB,EACL,EACH;;;;IAPuBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAA3B,EAAA,EAAiC;;;;;IAU/DP,EAAA,CAAAmC,SAAA,aAA4D;;;;;IAC5DnC,EAAA,CAAAmC,SAAA,aAAmF;;;;;IA1ErFnC,EADF,CAAAC,cAAA,cAA0E,gBACR;IAC9DD,EAAA,CAAAoC,uBAAA,OAAgC;IAE9BpC,EADA,CAAAe,UAAA,IAAAsB,wCAAA,iBAAsC,IAAAC,wCAAA,iBACF;;IAKtCtC,EAAA,CAAAoC,uBAAA,OAAqC;IAEnCpC,EADA,CAAAe,UAAA,IAAAwB,wCAAA,iBAAsC,IAAAC,wCAAA,iBACF;;IAQtCxC,EAAA,CAAAoC,uBAAA,OAAoC;IAElCpC,EADA,CAAAe,UAAA,IAAA0B,wCAAA,iBAAsC,KAAAC,yCAAA,iBACF;;IAStC1C,EAAA,CAAAoC,uBAAA,QAAoC;IAElCpC,EADA,CAAAe,UAAA,KAAA4B,yCAAA,iBAAsC,KAAAC,yCAAA,iBACF;;IAQtC5C,EAAA,CAAAoC,uBAAA,QAAoC;IAElCpC,EADA,CAAAe,UAAA,KAAA8B,yCAAA,iBAAsC,KAAAC,yCAAA,iBACF;;IAQtC9C,EAAA,CAAAoC,uBAAA,QAAkC;IAEhCpC,EADA,CAAAe,UAAA,KAAAgC,yCAAA,iBAAsC,KAAAC,yCAAA,iBACF;;IAQtChD,EAAA,CAAAoC,uBAAA,QAAqC;IAEnCpC,EADA,CAAAe,UAAA,KAAAkC,yCAAA,iBAAsC,KAAAC,yCAAA,iBACF;;IAatClD,EADA,CAAAe,UAAA,KAAAoC,yCAAA,iBAAuD,KAAAC,yCAAA,iBACuB;IAElFpD,EADE,CAAAG,YAAA,EAAQ,EACJ;;;;IA5EaH,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAiB,UAAA,eAAAP,MAAA,CAAA2C,WAAA,CAA0B;IAyErBrD,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,oBAAAP,MAAA,CAAA4C,gBAAA,CAAiC;IACpBtD,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAiB,UAAA,qBAAAP,MAAA,CAAA4C,gBAAA,CAA0B;;;;;IAM3DtD,EADF,CAAAC,cAAA,cAAyB,mBACM;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yDAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEvDH,EADF,CAAAC,cAAA,iBAA6D,eACjD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAE,MAAA,0BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAeJH,EAFJ,CAAAC,cAAA,cAAwD,cAC1B,eAC4C;IACpED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9BH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAC1CF,EAD0C,CAAAG,YAAA,EAAQ,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACb;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAE/BF,EAF+B,CAAAG,YAAA,EAAW,EAClC,EACF;;;;;IAZQH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAuD,WAAA,qBAAA7C,MAAA,CAAA8C,gBAAA,CAAAC,SAAA,CAAAC,QAAA,EAA2D;IACnE1D,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAoD,SAAA,CAAAC,QAAA,MACF;IAGI1D,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAS,iBAAA,CAAAgD,SAAA,CAAAE,KAAA,CAAiB;IAClB3D,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAS,iBAAA,CAAAgD,SAAA,CAAAG,WAAA,CAAuB;IACnB5D,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAA6D,kBAAA,KAAAJ,SAAA,CAAAK,IAAA,OAAAL,SAAA,CAAAM,IAAA,KAAiC;IAG9B/D,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAS,iBAAA,CAAAgD,SAAA,CAAAO,KAAA,CAAiB;;;;;IAmBrChE,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAmC,SAAA,mCAI0B;IAC5BnC,EAAA,CAAAG,YAAA,EAAM;;;;IAJFH,EAAA,CAAAI,SAAA,EAAgC;IAEhCJ,EAFA,CAAAiB,UAAA,iBAAAP,MAAA,CAAAuD,aAAA,GAAgC,6BACJ,wBACL;;;AAipBjC,OAAM,MAAOC,kBAAkB;EAY7B;EACA,IAAIC,UAAUA,CAAA;IAAa,OAAO,IAAI,CAACC,KAAK,CAACD,UAAU;EAAE;EACzD,IAAIE,WAAWA,CAAA;IAAa,OAAO,IAAI,CAAChB,WAAW,CAACiB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjD,MAAM,KAAK,SAAS,CAAC,CAACK,MAAM;EAAE;EACtG,IAAI6C,cAAcA,CAAA;IAAa,OAAO,IAAI,CAACJ,KAAK,CAACI,cAAc;EAAE;EAEjEC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAhB9B,KAAAN,KAAK,GAAG;MACND,UAAU,EAAE,CAAC;MACbK,cAAc,EAAE,CAAC;MACjBG,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE;KACf;IAED,KAAAvB,WAAW,GAAiB,EAAE;IAC9B,KAAAwB,SAAS,GAAoB,EAAE;IAC/B,KAAAvB,gBAAgB,GAAa,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;EAOlD;EAE7CwB,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACL,UAAU,CAACM,cAAc,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC9B,WAAW,GAAG8B,QAAQ,CAACC,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAACC,cAAc,CAACH,QAAQ,CAACC,KAAK,CAAC;MACrC,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACA,IAAI,CAACE,YAAY,EAAE;MACrB;KACD,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAChB,UAAU,CAACiB,sBAAsB,EAAE;IACzD,IAAI,CAACtC,WAAW,GAAG,CAACqC,QAAQ,CAAC;IAC7B,IAAI,CAACb,SAAS,GAAGa,QAAQ,CAAChE,MAAM;IAChC,IAAI,CAAC0C,KAAK,GAAG;MACXD,UAAU,EAAE,CAAC;MACbK,cAAc,EAAEkB,QAAQ,CAACE,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;MACzDjB,UAAU,EAAEe,QAAQ,CAACE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;MACjDhB,YAAY,EAAEc,QAAQ,CAACE,eAAe,CAAC,QAAQ,CAAC,IAAI;KACrD;EACH;EAEAN,cAAcA,CAACF,KAAmB;IAChC,IAAI,CAAChB,KAAK,CAACD,UAAU,GAAGiB,KAAK,CAACzD,MAAM;IAEpC,IAAIkE,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,SAAS,GAAoB,EAAE;IAEnCZ,KAAK,CAACa,OAAO,CAAC1B,IAAI,IAAG;MACnBsB,aAAa,IAAItB,IAAI,CAACqB,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC;MACxDE,SAAS,IAAIvB,IAAI,CAACqB,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC;MAChDG,WAAW,IAAIxB,IAAI,CAACqB,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;MACpDI,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC3B,IAAI,CAAC7C,MAAM,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC;IAEF,IAAI,CAAC0C,KAAK,CAACI,cAAc,GAAGqB,aAAa;IACzC,IAAI,CAACzB,KAAK,CAACO,UAAU,GAAGmB,SAAS;IACjC,IAAI,CAAC1B,KAAK,CAACQ,YAAY,GAAGmB,WAAW;IAErC;IACA,IAAI,CAAClB,SAAS,GAAGmB,SAAS,CACvBG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAAC3C,QAAQ,CAAC,GAAG,IAAI,CAAC4C,iBAAiB,CAACF,CAAC,CAAC1C,QAAQ,CAAC,CAAC,CACvF2B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEAiB,iBAAiBA,CAAC5C,QAAgB;IAChC,MAAM6C,OAAO,GAAG;MAAEC,QAAQ,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAC,CAAE;IACpE,OAAOL,OAAO,CAAC7C,QAAgC,CAAC,IAAI,CAAC;EACvD;EAEAF,gBAAgBA,CAACE,QAAgB;IAC/B,OAAO9D,eAAe,CAAC8D,QAAwC,CAAC,IAAI,MAAM;EAC5E;EAEA/C,cAAcA,CAACkG,IAAY;IACzB,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIF,IAAI;EACtC;EAEAjF,UAAUA,CAACoF,UAAkB;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,EAAE;EAClD;EAEAnF,UAAUA,CAACiF,UAAkB;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACG,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAE,CAAC;EAC5F;EAEA9F,aAAaA,CAACD,MAAc;IAC1B,MAAMgG,KAAK,GAAG;MACZC,SAAS,EAAE,cAAc;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;KACV;IACD,OAAOJ,KAAK,CAAChG,MAA4B,CAAC,IAAI,MAAM;EACtD;EAEA;EACAqG,sBAAsBA,CAAA;IACpB,OAAO,CACL;MACEC,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,IAAI,CAACzD,KAAK,CAACI,cAAc;MAChCd,QAAQ,EAAE,UAAU;MACpBoE,KAAK,EAAE,EAAE,CAAC;KACX,EACD;MACEF,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,IAAI,CAACzD,KAAK,CAACO,UAAU;MAC5BjB,QAAQ,EAAE,MAAM;MAChBoE,KAAK,EAAE,CAAC;KACT,EACD;MACEF,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,IAAI,CAACzD,KAAK,CAACQ,YAAY;MAC9BlB,QAAQ,EAAE,QAAQ;MAClBoE,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,KAAK;MACZC,KAAK,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC5D,KAAK,CAACD,UAAU,GAAG,CAAC,GAAG,IAAI,CAACC,KAAK,CAACI,cAAc,GAAG,IAAI,CAACJ,KAAK,CAACO,UAAU,GAAG,IAAI,CAACP,KAAK,CAACQ,YAAY,CAAC;MAC3HlB,QAAQ,EAAE,KAAK;MACfoE,KAAK,EAAE,CAAC;KACT,CACF;EACH;EAEA7D,aAAaA,CAAA;IACX,MAAMgE,UAAU,GAAG,IAAI,CAAC5E,WAAW,CAAC6E,IAAI,CAAC3D,IAAI,IAAIA,IAAI,CAACjD,MAAM,KAAK,SAAS,CAAC;IAC3E,IAAI,CAAC2G,UAAU,EAAE,OAAO,IAAI;IAE5B,OAAO;MACLE,MAAM,EAAEF,UAAU,CAAC1H,EAAE;MACrB6H,WAAW,EAAE,IAAI,CAACzH,cAAc,CAACsH,UAAU,CAACpH,YAAY,CAAC;MACzDS,MAAM,EAAE,SAAS;MACjB+G,QAAQ,EAAE,EAAE;MAAE;MACdC,WAAW,EAAE,2BAA2B;MACxCC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE,CAAC;MACjBC,SAAS,EAAE,IAAIxB,IAAI,CAACgB,UAAU,CAACnG,UAAU,CAAC;MAC1C4G,mBAAmB,EAAE,IAAIzB,IAAI,CAACA,IAAI,CAAC0B,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAC3DC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAEb,UAAU,CAACvG,MAAM,EAAEC,MAAM,IAAI;KAC3C;EACH;EAEAoH,qBAAqBA,CAAA;IACnB;IACA,MAAMJ,GAAG,GAAG,IAAI1B,IAAI,EAAE;IACtB,MAAM+B,IAAI,GAAqB,EAAE;IAEjC,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMC,IAAI,GAAG,IAAIjC,IAAI,CAAC0B,GAAG,CAAC;MAC1BO,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,EAAE,GAAGH,CAAC,CAAC;MAEhCD,IAAI,CAACK,IAAI,CAAC;QACRH,IAAI;QACJ1C,QAAQ,EAAEuB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,CAAC,CAAC,IAAIN,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1DxC,IAAI,EAAEsB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QACvC7C,MAAM,EAAEqB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;QAC1C5C,GAAG,EAAEoB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;QACvCpF,UAAU,EAAE4D,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QAC7CC,YAAY,EAAEzB,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACwB,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG;OAChD,CAAC;IACJ;IAEA,OAAO;MACLE,MAAM,EAAE,KAAK;MACbT,IAAI;MACJU,OAAO,EAAE;QACPC,WAAW,EAAE,IAAI,CAACvF,KAAK,CAACI,cAAc,GAAG,IAAI,CAACJ,KAAK,CAACO,UAAU,GAAG,IAAI,CAACP,KAAK,CAACQ,YAAY;QACxFgF,aAAa,EAAE,EAAE;QAAE;QACnBJ,YAAY,EAAE,EAAE;QAChBK,aAAa,EAAE;;KAElB;EACH;;;uCA3LW3F,kBAAkB,EAAAlE,EAAA,CAAA8J,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAlB9F,kBAAkB;MAAA+F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAt6BjBvK,EANV,CAAAC,cAAA,aAAiC,aAED,aACA,aACD,YACK,kBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA8B;UAAAD,EAAA,CAAAE,MAAA,4DAAqD;UACrFF,EADqF,CAAAG,YAAA,EAAI,EACnF;UAGFH,EAFJ,CAAAC,cAAA,cAA0B,cACA,gBACG;UAAAD,EAAA,CAAAE,MAAA,IAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACtCF,EADsC,CAAAG,YAAA,EAAO,EACvC;UAEJH,EADF,CAAAC,cAAA,cAAwB,gBACG;UAAAD,EAAA,CAAAE,MAAA,IAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;UAEJH,EADF,CAAAC,cAAA,cAAwB,gBACG;UAAAD,EAAA,CAAAE,MAAA,IAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAGvCF,EAHuC,CAAAG,YAAA,EAAO,EACpC,EACF,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAA4B,kBACiE,gBAC/E;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,kBAAiF,gBACrE;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAOEH,EAJR,CAAAC,cAAA,eAAwB,oBAC2B,eACjB,eACW,oBACT;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACtCF,EADsC,CAAAG,YAAA,EAAW,EAC3C;UAEJH,EADF,CAAAC,cAAA,eAAwB,oBACgB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5DH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAElCF,EAFkC,CAAAG,YAAA,EAAO,EACjC,EACF;UAEJH,EADF,CAAAC,cAAA,eAA+B,eACJ;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UACpDF,EADoD,CAAAG,YAAA,EAAM,EACpD;UAGFH,EAFJ,CAAAC,cAAA,eAA8B,kBAC0B,gBAC1C;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,kBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACG;UAKLH,EAHN,CAAAC,cAAA,oBAAqD,eACrB,eACY,oBACV;UAAAD,EAAA,CAAAE,MAAA,aAAK;UACnCF,EADmC,CAAAG,YAAA,EAAW,EACxC;UAEJH,EADF,CAAAC,cAAA,eAAwB,oBACgB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5DH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAEhCF,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;UAEJH,EADF,CAAAC,cAAA,eAA+B,eACK;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAClEH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7CH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,mCAA2B;UAC3DF,EAD2D,CAAAG,YAAA,EAAM,EAC3D;UAGFH,EAFJ,CAAAC,cAAA,eAA8B,kBAC0B,gBAC1C;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACG;UAKLH,EAHN,CAAAC,cAAA,oBAAiD,eACjB,eACW,oBACT;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACrCF,EADqC,CAAAG,YAAA,EAAW,EAC1C;UAEJH,EADF,CAAAC,cAAA,eAAwB,oBACe;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7DH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAE/BF,EAF+B,CAAAG,YAAA,EAAO,EAC9B,EACF;UAEJH,EADF,CAAAC,cAAA,eAA+B,eACI;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3CH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UACxDF,EADwD,CAAAG,YAAA,EAAM,EACxD;UAGFH,EAFJ,CAAAC,cAAA,eAA8B,kBAC4B,gBAC5C;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,iBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACG;UAKLH,EAHN,CAAAC,cAAA,qBAAmD,gBACnB,gBACQ,qBACN;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;UAEJH,EADF,CAAAC,cAAA,gBAAwB,qBACgB;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9DH,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAEhCF,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA+B,gBACC;UAAAD,EAAA,CAAAE,MAAA,KAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5DH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7CH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,+BAAsB;UACtDF,EADsD,CAAAG,YAAA,EAAM,EACtD;UAGFH,EAFJ,CAAAC,cAAA,gBAA8B,mBACe,iBAC/B;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,oBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACG,EACP;UAOEH,EAJR,CAAAC,cAAA,gBAA6B,qBACS,4BACM,gBACX,iBACf;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAC,cAAA,uBAAgB;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAC9BF,EAD8B,CAAAG,YAAA,EAAiB,EACzC;UAEJH,EADF,CAAAC,cAAA,mBAAuE,iBAC3D;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,mBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACO;UAClBH,EAAA,CAAAC,cAAA,yBAAkB;UAgFhBD,EA/EA,CAAAe,UAAA,MAAA0J,mCAAA,mBAA0E,MAAAC,2CAAA,iCAAA1K,EAAA,CAAA2K,sBAAA,CA+EpD;UAa5B3K,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;UAKFH,EAFJ,CAAAC,cAAA,qBAAkC,wBACf,uBACC;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;UAEhBH,EADF,CAAAC,cAAA,yBAAkB,gBACS;UACvBD,EAAA,CAAAe,UAAA,MAAA6J,mCAAA,mBAAwD;UAiB9D5K,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;UAGXH,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAmC,SAAA,uCAM6B;UAC/BnC,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAe,UAAA,MAAA8J,mCAAA,kBAAmD;UASnD7K,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAmC,SAAA,0CAIgC;UAClCnC,EAAA,CAAAG,YAAA,EAAM;UAKFH,EAFJ,CAAAC,cAAA,qBAAqC,wBAClB,uBACC;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAC/BF,EAD+B,CAAAG,YAAA,EAAiB,EAC9B;UAIZH,EAHN,CAAAC,cAAA,yBAAkB,gBACU,mBACqC,iBACjD;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,yBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAiE,iBACrD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAgD,iBACpC;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAiD,iBACrC;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,mBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;UArT6BH,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAS,iBAAA,CAAA+J,GAAA,CAAArG,UAAA,CAAgB;UAIhBnE,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAS,iBAAA,CAAA+J,GAAA,CAAAnG,WAAA,CAAiB;UAIjBrE,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAS,iBAAA,CAAA+J,GAAA,CAAAhG,cAAA,CAAoB;UA8BtBxE,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAS,iBAAA,CAAA+J,GAAA,CAAApG,KAAA,CAAAD,UAAA,CAAsB;UAuBbnE,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAS,iBAAA,CAAA+J,GAAA,CAAApG,KAAA,CAAAI,cAAA,CAA0B;UAuB3BxE,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAS,iBAAA,CAAA+J,GAAA,CAAApG,KAAA,CAAAO,UAAA,CAAsB;UAuBzB3E,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAS,iBAAA,CAAA+J,GAAA,CAAApG,KAAA,CAAAQ,YAAA,CAAwB;UA2BxB5E,EAAA,CAAAI,SAAA,IAA8B;UAAAJ,EAA9B,CAAAiB,UAAA,SAAAuJ,GAAA,CAAAnH,WAAA,CAAA1B,MAAA,KAA8B,aAAAmJ,WAAA,CAAY;UAqGjD9K,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAiB,UAAA,YAAAuJ,GAAA,CAAA3F,SAAA,CAAY;UAuBrC7E,EAAA,CAAAI,SAAA,GAAiC;UAGjCJ,EAHA,CAAAiB,UAAA,SAAAuJ,GAAA,CAAA7C,sBAAA,GAAiC,yBACT,oBACL,kBACF;UAKO3H,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAiB,UAAA,SAAAuJ,GAAA,CAAAvG,aAAA,GAAqB;UAW7CjE,EAAA,CAAAI,SAAA,GAAsC;UAEtCJ,EAFA,CAAAiB,UAAA,eAAAuJ,GAAA,CAAAzB,qBAAA,GAAsC,wBACf,sBACF;;;qBAlT3B5J,YAAY,EAAA4L,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,aAAA,EACZ9L,aAAa,EAAA+L,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACblM,aAAa,EAAAmM,EAAA,CAAAC,OAAA,EACbnM,eAAe,EAAAoM,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfrM,cAAc,EAAAsM,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACd/M,cAAc,EAAAgN,EAAA,CAAAC,OAAA,EACdhN,oBAAoB,EACpBC,gBAAgB,EAAAgN,EAAA,CAAAC,UAAA,EAChBhN,YAAY,EAAAiN,EAAA,CAAAC,UAAA,EACZhN,6BAA6B,EAC7BC,0BAA0B,EAC1BC,gCAAgC;MAAA+M,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}