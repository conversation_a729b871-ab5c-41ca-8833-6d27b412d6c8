{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/expansion\";\nimport * as i6 from \"@angular/material/chips\";\nimport * as i7 from \"@angular/material/table\";\nfunction CliGuideComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", feature_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r1.description);\n  }\n}\nfunction CliGuideComponent_mat_card_28_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_card_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 36)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 30)(10, \"div\", 31)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Commands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"pre\")(16, \"code\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, CliGuideComponent_mat_card_28_div_18_Template, 5, 1, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(method_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.description);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(method_r2.commands);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Flag\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r3.flag);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r4.type);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r5.description);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Default\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"code\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtemplate(1, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template, 2, 1, \"code\", 58)(2, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", option_r6.default);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 59);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 60);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"h4\");\n    i0.ɵɵtext(2, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"table\", 47);\n    i0.ɵɵelementContainerStart(4, 48);\n    i0.ɵɵtemplate(5, CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template, 2, 0, \"th\", 49)(6, CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template, 3, 1, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 51);\n    i0.ɵɵtemplate(8, CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template, 2, 0, \"th\", 49)(9, CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template, 3, 1, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 52);\n    i0.ɵɵtemplate(11, CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template, 2, 0, \"th\", 49)(12, CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template, 2, 1, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 53);\n    i0.ɵɵtemplate(14, CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template, 2, 0, \"th\", 49)(15, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template, 3, 2, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(16, CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template, 1, 0, \"tr\", 54)(17, CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template, 1, 0, \"tr\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dataSource\", command_r7.options);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r7.optionColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r7.optionColumns);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 31)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Output\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const example_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"p\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"div\", 31)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Command\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"pre\")(10, \"code\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template, 9, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const example_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(example_r9.description);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(example_r9.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"h4\");\n    i0.ɵɵtext(2, \"Examples\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template, 13, 3, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", command_r7.examples);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"h4\");\n    i0.ɵɵtext(2, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 39)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"code\", 40);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-panel-description\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 41)(8, \"div\", 42)(9, \"h4\");\n    i0.ɵɵtext(10, \"Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 30)(12, \"pre\")(13, \"code\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, CliGuideComponent_mat_expansion_panel_36_div_15_Template, 18, 3, \"div\", 43)(16, CliGuideComponent_mat_expansion_panel_36_div_16_Template, 4, 1, \"div\", 44)(17, CliGuideComponent_mat_expansion_panel_36_div_17_Template, 8, 1, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"spt \", command_r7.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", command_r7.description, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(command_r7.usage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.options.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.examples.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"mat-icon\", 78);\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"strong\");\n    i0.ɵɵtext(5, \"Implementation Notes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const integration_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(integration_r11.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_27_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"mat-icon\", 82);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r12 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r12);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"h5\");\n    i0.ɵɵtext(2, \"Quick Tips:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 80);\n    i0.ɵɵtemplate(4, CliGuideComponent_mat_expansion_panel_45_div_27_li_4_Template, 5, 1, \"li\", 81);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.getPlatformTips(integration_r11.platform));\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 69)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵelement(3, \"i\", 70);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-panel-description\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 71)(8, \"div\", 72)(9, \"h4\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function CliGuideComponent_mat_expansion_panel_45_Template_button_click_11_listener() {\n      const integration_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.copyToClipboard(integration_r11.config));\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 30)(15, \"div\", 31)(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function CliGuideComponent_mat_expansion_panel_45_Template_button_click_20_listener() {\n      const integration_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.copyToClipboard(integration_r11.config));\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"pre\")(24, \"code\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(26, CliGuideComponent_mat_expansion_panel_45_div_26_Template, 8, 1, \"div\", 75)(27, CliGuideComponent_mat_expansion_panel_45_div_27_Template, 5, 1, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r11 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"expanded\", i_r13 === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"platform-icon \", integration_r11.icon, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", integration_r11.platform, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", integration_r11.description, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Configuration File: \", integration_r11.filename, \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(integration_r11.filename);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(integration_r11.config);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", integration_r11.notes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.getPlatformTips(integration_r11.platform).length > 0);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_52_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 88)(1, \"mat-icon\", 89);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r14 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r14);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 83)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"mat-icon\", 84);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 85)(9, \"ul\", 86);\n    i0.ɵɵtemplate(10, CliGuideComponent_mat_expansion_panel_52_li_10_Template, 5, 1, \"li\", 87);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const practice_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", practice_r15.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(practice_r15.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r15.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", practice_r15.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", practice_r15.tips);\n  }\n}\nfunction CliGuideComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"h4\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"div\", 31)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function CliGuideComponent_div_66_Template_button_click_9_listener() {\n      const env_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.copyToClipboard(env_r17.config));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"pre\")(13, \"code\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"p\", 92);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const env_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(env_r17.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(env_r17.type);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(env_r17.config);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(env_r17.description);\n  }\n}\nfunction CliGuideComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r18 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r18.key);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r18.description);\n  }\n}\nexport class CliGuideComponent {\n  constructor() {\n    this.optionColumns = ['flag', 'type', 'description', 'default'];\n    this.cliFeatures = [{\n      title: 'Security Scanning',\n      description: 'Comprehensive security analysis for blockchain applications',\n      icon: 'security',\n      color: '#1976d2'\n    }, {\n      title: 'Multiple Formats',\n      description: 'Output results in JSON, YAML, CSV, or human-readable formats',\n      icon: 'description',\n      color: '#4caf50'\n    }, {\n      title: 'CI/CD Integration',\n      description: 'Easy integration with continuous integration pipelines',\n      icon: 'integration_instructions',\n      color: '#ff9800'\n    }, {\n      title: 'Configurable',\n      description: 'Flexible configuration options for different environments',\n      icon: 'tune',\n      color: '#9c27b0'\n    }];\n    this.installationMethods = [{\n      title: 'From Source',\n      description: 'Build from source code',\n      icon: 'code',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\ngo build -o spt cmd/main.go`,\n      notes: 'Requires Go 1.21+ to be installed'\n    }, {\n      title: 'Using Make',\n      description: 'Build using Makefile',\n      icon: 'build',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\nmake cli`,\n      notes: 'Binary will be created in build/ directory'\n    }, {\n      title: 'Go Install',\n      description: 'Install directly with Go',\n      icon: 'download',\n      commands: `go install github.com/blockchain-spt/cmd/spt@latest`,\n      notes: 'Installs to $GOPATH/bin'\n    }];\n    this.cliCommands = [{\n      name: 'scan',\n      description: 'Perform security scan on files or directories',\n      usage: 'spt scan [flags] [path]',\n      options: [{\n        flag: '--chain',\n        type: 'string',\n        description: 'Blockchain chain to analyze',\n        default: 'all'\n      }, {\n        flag: '--format',\n        type: 'string',\n        description: 'Output format (json, yaml, csv, table)',\n        default: 'table'\n      }, {\n        flag: '--output',\n        type: 'string',\n        description: 'Output file path'\n      }, {\n        flag: '--severity',\n        type: 'string',\n        description: 'Minimum severity level',\n        default: 'medium'\n      }, {\n        flag: '--recursive',\n        type: 'boolean',\n        description: 'Scan directories recursively',\n        default: 'true'\n      }],\n      examples: [{\n        command: 'spt scan ./contracts',\n        description: 'Scan all files in contracts directory',\n        output: `Scanning ./contracts...\nFound 3 issues:\n  HIGH: Potential reentrancy in contract.sol:42\n  MEDIUM: Unchecked return value in token.sol:15\n  MEDIUM: Gas optimization opportunity in utils.sol:8`\n      }, {\n        command: 'spt scan --chain ethereum --format json ./src',\n        description: 'Scan for Ethereum-specific issues and output as JSON'\n      }],\n      notes: 'Use --help flag with any command to see detailed usage information'\n    }, {\n      name: 'audit',\n      description: 'Perform comprehensive security audit',\n      usage: 'spt audit [flags] [path]',\n      options: [{\n        flag: '--generate-report',\n        type: 'boolean',\n        description: 'Generate detailed report',\n        default: 'false'\n      }, {\n        flag: '--report-path',\n        type: 'string',\n        description: 'Report output path',\n        default: './audit-report.html'\n      }, {\n        flag: '--template',\n        type: 'string',\n        description: 'Report template',\n        default: 'standard'\n      }],\n      examples: [{\n        command: 'spt audit --generate-report ./project',\n        description: 'Perform audit and generate HTML report'\n      }]\n    }, {\n      name: 'check',\n      description: 'Run specific security checks',\n      usage: 'spt check [subcommand] [flags] [path]',\n      options: [{\n        flag: '--fix',\n        type: 'boolean',\n        description: 'Attempt to fix issues automatically',\n        default: 'false'\n      }],\n      examples: [{\n        command: 'spt check deps --fix',\n        description: 'Check dependencies and fix known vulnerabilities'\n      }, {\n        command: 'spt check env',\n        description: 'Check environment configuration for security issues'\n      }]\n    }];\n    this.integrationExamples = [{\n      platform: 'GitHub Actions',\n      description: 'Integrate SPT into GitHub Actions workflow',\n      icon: 'fab fa-github',\n      filename: '.github/workflows/security.yml',\n      config: `name: Security Scan\non: [push, pull_request]\n\njobs:\n  security:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - uses: actions/setup-go@v3\n        with:\n          go-version: '1.21'\n      - name: Install SPT\n        run: go install github.com/blockchain-spt/cmd/spt@latest\n      - name: Run Security Scan\n        run: spt scan --format json --output security-report.json ./\n      - name: Upload Results\n        uses: actions/upload-artifact@v3\n        with:\n          name: security-report\n          path: security-report.json`,\n      notes: 'Add GITHUB_TOKEN to secrets for private repositories'\n    }, {\n      platform: 'GitLab CI',\n      description: 'Integrate SPT into GitLab CI/CD pipeline',\n      icon: 'fab fa-gitlab',\n      filename: '.gitlab-ci.yml',\n      config: `security_scan:\n  stage: test\n  image: golang:1.21\n  script:\n    - go install github.com/blockchain-spt/cmd/spt@latest\n    - spt scan --format json --output security-report.json ./\n    - spt audit --generate-report --report-path audit-report.html ./\n  artifacts:\n    reports:\n      junit: security-report.json\n    paths:\n      - audit-report.html\n    expire_in: 1 week\n  only:\n    - merge_requests\n    - main`,\n      notes: 'Configure artifact storage for report persistence'\n    }, {\n      platform: 'Azure DevOps',\n      description: 'Integrate SPT into Azure DevOps Pipeline',\n      icon: 'fab fa-microsoft',\n      filename: 'azure-pipelines.yml',\n      config: `trigger:\n  branches:\n    include:\n      - main\n      - develop\n  paths:\n    include:\n      - contracts/*\n      - src/*\n\npool:\n  vmImage: 'ubuntu-latest'\n\nvariables:\n  GO_VERSION: '1.21'\n  SPT_VERSION: 'latest'\n\nstages:\n- stage: SecurityScan\n  displayName: 'Security Analysis'\n  jobs:\n  - job: SPTScan\n    displayName: 'SPT Security Scan'\n    steps:\n    - task: GoTool@0\n      displayName: 'Install Go'\n      inputs:\n        version: '\\$(GO_VERSION)'\n\n    - script: |\n        go install github.com/blockchain-spt/cmd/spt@\\$(SPT_VERSION)\n        echo \"SPT installed successfully\"\n      displayName: 'Install SPT CLI'\n\n    - script: |\n        spt scan --format json --output \\$(Agent.TempDirectory)/security-report.json ./\n        spt audit --generate-report --report-path \\$(Agent.TempDirectory)/audit-report.html ./\n      displayName: 'Run Security Scan'\n      continueOnError: true\n\n    - task: PublishTestResults@2\n      displayName: 'Publish Security Results'\n      inputs:\n        testResultsFormat: 'JUnit'\n        testResultsFiles: '\\$(Agent.TempDirectory)/security-report.json'\n        testRunTitle: 'SPT Security Scan Results'\n      condition: always()\n\n    - task: PublishBuildArtifacts@1\n      displayName: 'Publish Security Reports'\n      inputs:\n        pathToPublish: '\\$(Agent.TempDirectory)'\n        artifactName: 'security-reports'\n        publishLocation: 'Container'\n      condition: always()\n\n    - script: |\n        if [ -f \"\\$(Agent.TempDirectory)/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' \\$(Agent.TempDirectory)/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' \\$(Agent.TempDirectory)/security-report.json)\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ] || [ \"\\$HIGH_COUNT\" -gt 5 ]; then\n            echo \"##vso[task.logissue type=error]Critical security issues found: \\$CRITICAL_COUNT critical, \\$HIGH_COUNT high\"\n            exit 1\n          fi\n        fi\n      displayName: 'Evaluate Security Results'\n      condition: always()`,\n      notes: 'Configure service connections for private repositories and adjust thresholds as needed'\n    }, {\n      platform: 'AWS CodeBuild',\n      description: 'Integrate SPT into AWS CodeBuild pipeline',\n      icon: 'fab fa-aws',\n      filename: 'buildspec.yml',\n      config: `version: 0.2\n\nenv:\n  variables:\n    GO_VERSION: \"1.21\"\n    SPT_VERSION: \"latest\"\n  parameter-store:\n    GITHUB_TOKEN: \"/spt/github-token\"  # Optional for private repos\n\nphases:\n  install:\n    runtime-versions:\n      golang: \\$GO_VERSION\n    commands:\n      - echo \"Installing SPT CLI...\"\n      - go install github.com/blockchain-spt/cmd/spt@\\$SPT_VERSION\n      - spt version\n\n  pre_build:\n    commands:\n      - echo \"Preparing security scan...\"\n      - mkdir -p reports\n      - echo \"Current directory contents:\"\n      - ls -la\n\n  build:\n    commands:\n      - echo \"Running SPT security scan...\"\n      - spt scan --format json --output reports/security-report.json ./\n      - spt audit --generate-report --report-path reports/audit-report.html ./\n      - echo \"Security scan completed\"\n\n  post_build:\n    commands:\n      - echo \"Processing security results...\"\n      - |\n        if [ -f \"reports/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' reports/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' reports/security-report.json)\n          MEDIUM_COUNT=\\$(jq '.summary.medium // 0' reports/security-report.json)\n\n          echo \"Security Summary:\"\n          echo \"  Critical: \\$CRITICAL_COUNT\"\n          echo \"  High: \\$HIGH_COUNT\"\n          echo \"  Medium: \\$MEDIUM_COUNT\"\n\n          # Fail build if critical issues found\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ]; then\n            echo \"Build failed due to critical security issues\"\n            exit 1\n          fi\n\n          # Warning for high issues\n          if [ \"\\$HIGH_COUNT\" -gt 10 ]; then\n            echo \"Warning: High number of high-severity issues (\\$HIGH_COUNT)\"\n          fi\n        else\n          echo \"Security report not found\"\n          exit 1\n        fi\n\nartifacts:\n  files:\n    - 'reports/**/*'\n  name: spt-security-reports\n\nreports:\n  spt-security:\n    files:\n      - 'reports/security-report.json'\n    file-format: 'JUNITXML'`,\n      notes: 'Store sensitive tokens in AWS Parameter Store or Secrets Manager'\n    }, {\n      platform: 'AWS CodePipeline',\n      description: 'Complete AWS CodePipeline with SPT integration',\n      icon: 'fab fa-aws',\n      filename: 'cloudformation-pipeline.yml',\n      config: `AWSTemplateFormatVersion: '2010-09-09'\nDescription: 'SPT Security Pipeline with CodePipeline'\n\nParameters:\n  GitHubRepo:\n    Type: String\n    Description: GitHub repository name\n  GitHubOwner:\n    Type: String\n    Description: GitHub repository owner\n  GitHubToken:\n    Type: String\n    NoEcho: true\n    Description: GitHub personal access token\n\nResources:\n  # S3 Bucket for artifacts\n  ArtifactsBucket:\n    Type: AWS::S3::Bucket\n    Properties:\n      BucketName: !Sub '\\${AWS::StackName}-spt-artifacts'\n      VersioningConfiguration:\n        Status: Enabled\n      PublicAccessBlockConfiguration:\n        BlockPublicAcls: true\n        BlockPublicPolicy: true\n        IgnorePublicAcls: true\n        RestrictPublicBuckets: true\n\n  # CodeBuild Project for SPT Security Scan\n  SPTSecurityProject:\n    Type: AWS::CodeBuild::Project\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-security'\n      ServiceRole: !GetAtt CodeBuildRole.Arn\n      Artifacts:\n        Type: CODEPIPELINE\n      Environment:\n        Type: LINUX_CONTAINER\n        ComputeType: BUILD_GENERAL1_MEDIUM\n        Image: aws/codebuild/amazonlinux2-x86_64-standard:3.0\n        EnvironmentVariables:\n          - Name: GITHUB_TOKEN\n            Value: !Ref GitHubToken\n            Type: PARAMETER_STORE\n      Source:\n        Type: CODEPIPELINE\n        BuildSpec: |\n          version: 0.2\n          phases:\n            install:\n              runtime-versions:\n                golang: 1.21\n              commands:\n                - go install github.com/blockchain-spt/cmd/spt@latest\n            build:\n              commands:\n                - mkdir -p reports\n                - spt scan --format json --output reports/security-report.json ./\n                - spt audit --generate-report --report-path reports/audit-report.html ./\n          artifacts:\n            files:\n              - 'reports/**/*'\n\n  # CodePipeline\n  SPTPipeline:\n    Type: AWS::CodePipeline::Pipeline\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-pipeline'\n      RoleArn: !GetAtt CodePipelineRole.Arn\n      ArtifactStore:\n        Type: S3\n        Location: !Ref ArtifactsBucket\n      Stages:\n        - Name: Source\n          Actions:\n            - Name: SourceAction\n              ActionTypeId:\n                Category: Source\n                Owner: ThirdParty\n                Provider: GitHub\n                Version: '1'\n              Configuration:\n                Owner: !Ref GitHubOwner\n                Repo: !Ref GitHubRepo\n                Branch: main\n                OAuthToken: !Ref GitHubToken\n              OutputArtifacts:\n                - Name: SourceOutput\n\n        - Name: SecurityScan\n          Actions:\n            - Name: SPTScan\n              ActionTypeId:\n                Category: Build\n                Owner: AWS\n                Provider: CodeBuild\n                Version: '1'\n              Configuration:\n                ProjectName: !Ref SPTSecurityProject\n              InputArtifacts:\n                - Name: SourceOutput\n              OutputArtifacts:\n                - Name: SecurityOutput\n\n  # IAM Roles (simplified - add specific permissions as needed)\n  CodeBuildRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codebuild.amazonaws.com\n            Action: sts:AssumeRole\n      ManagedPolicyArns:\n        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess\n      Policies:\n        - PolicyName: S3Access\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                Resource: !Sub '\\${ArtifactsBucket}/*'\n\n  CodePipelineRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codepipeline.amazonaws.com\n            Action: sts:AssumeRole\n      Policies:\n        - PolicyName: PipelinePolicy\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                  - s3:GetBucketVersioning\n                Resource:\n                  - !Sub '\\${ArtifactsBucket}'\n                  - !Sub '\\${ArtifactsBucket}/*'\n              - Effect: Allow\n                Action:\n                  - codebuild:BatchGetBuilds\n                  - codebuild:StartBuild\n                Resource: !GetAtt SPTSecurityProject.Arn`,\n      notes: 'Deploy using AWS CloudFormation. Customize IAM permissions based on your security requirements.'\n    }, {\n      platform: 'Docker',\n      description: 'Containerized SPT for consistent CI/CD environments',\n      icon: 'fab fa-docker',\n      filename: 'Dockerfile',\n      config: `# Multi-stage Dockerfile for SPT CLI\nFROM golang:1.21-alpine AS builder\n\n# Install dependencies\nRUN apk add --no-cache git ca-certificates\n\n# Set working directory\nWORKDIR /app\n\n# Install SPT CLI\nRUN go install github.com/blockchain-spt/cmd/spt@latest\n\n# Create final image\nFROM alpine:latest\n\n# Install runtime dependencies\nRUN apk add --no-cache ca-certificates jq curl\n\n# Copy SPT binary from builder\nCOPY --from=builder /go/bin/spt /usr/local/bin/spt\n\n# Create non-root user\nRUN addgroup -g 1001 spt && \\\\\n    adduser -D -u 1001 -G spt spt\n\n# Set working directory\nWORKDIR /workspace\n\n# Change ownership\nRUN chown -R spt:spt /workspace\n\n# Switch to non-root user\nUSER spt\n\n# Set entrypoint\nENTRYPOINT [\"spt\"]\nCMD [\"--help\"]\n\n# Usage examples:\n# docker build -t spt-cli .\n# docker run --rm -v \\$(pwd):/workspace spt-cli scan ./\n# docker run --rm -v \\$(pwd):/workspace spt-cli audit --generate-report ./`,\n      notes: 'Use this Docker image in any CI/CD system that supports containers'\n    }, {\n      platform: 'Jenkins',\n      description: 'Jenkins Pipeline with SPT integration',\n      icon: 'fab fa-jenkins',\n      filename: 'Jenkinsfile',\n      config: `pipeline {\n    agent any\n\n    environment {\n        GO_VERSION = '1.21'\n        SPT_VERSION = 'latest'\n        REPORTS_DIR = 'reports'\n    }\n\n    tools {\n        go 'go-1.21'  // Configure in Jenkins Global Tools\n    }\n\n    stages {\n        stage('Checkout') {\n            steps {\n                checkout scm\n                script {\n                    env.GIT_COMMIT_SHORT = sh(\n                        script: 'git rev-parse --short HEAD',\n                        returnStdout: true\n                    ).trim()\n                }\n            }\n        }\n\n        stage('Install SPT') {\n            steps {\n                sh '''\n                    echo \"Installing SPT CLI...\"\n                    go install github.com/blockchain-spt/cmd/spt@\\${SPT_VERSION}\n                    spt version\n                '''\n            }\n        }\n\n        stage('Security Scan') {\n            steps {\n                sh '''\n                    echo \"Creating reports directory...\"\n                    mkdir -p \\${REPORTS_DIR}\n\n                    echo \"Running SPT security scan...\"\n                    spt scan --format json --output \\${REPORTS_DIR}/security-report.json ./\n\n                    echo \"Generating audit report...\"\n                    spt audit --generate-report --report-path \\${REPORTS_DIR}/audit-report.html ./\n\n                    echo \"Security scan completed\"\n                '''\n            }\n            post {\n                always {\n                    // Archive artifacts\n                    archiveArtifacts artifacts: '\\${REPORTS_DIR}/**/*', fingerprint: true\n\n                    // Publish HTML reports\n                    publishHTML([\n                        allowMissing: false,\n                        alwaysLinkToLastBuild: true,\n                        keepAll: true,\n                        reportDir: '\\${REPORTS_DIR}',\n                        reportFiles: 'audit-report.html',\n                        reportName: 'SPT Security Report'\n                    ])\n                }\n            }\n        }\n\n        stage('Evaluate Results') {\n            steps {\n                script {\n                    if (fileExists(\"\\${REPORTS_DIR}/security-report.json\")) {\n                        def report = readJSON file: \"\\${REPORTS_DIR}/security-report.json\"\n                        def critical = report.summary?.critical ?: 0\n                        def high = report.summary?.high ?: 0\n                        def medium = report.summary?.medium ?: 0\n\n                        echo \"Security Summary:\"\n                        echo \"  Critical: \\${critical}\"\n                        echo \"  High: \\${high}\"\n                        echo \"  Medium: \\${medium}\"\n\n                        // Set build status based on results\n                        if (critical > 0) {\n                            currentBuild.result = 'FAILURE'\n                            error(\"Build failed due to \\${critical} critical security issues\")\n                        } else if (high > 10) {\n                            currentBuild.result = 'UNSTABLE'\n                            echo \"Build marked unstable due to \\${high} high-severity issues\"\n                        }\n\n                        // Add build description\n                        currentBuild.description = \"Critical: \\${critical}, High: \\${high}, Medium: \\${medium}\"\n                    } else {\n                        currentBuild.result = 'FAILURE'\n                        error(\"Security report not found\")\n                    }\n                }\n            }\n        }\n    }\n\n    post {\n        always {\n            // Clean workspace\n            cleanWs()\n        }\n        failure {\n            // Send notifications on failure\n            emailext (\n                subject: \"SPT Security Scan Failed: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan failed for commit \\${env.GIT_COMMIT_SHORT}. Check the build logs for details.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n        unstable {\n            // Send notifications on unstable builds\n            emailext (\n                subject: \"SPT Security Scan Unstable: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan completed with warnings for commit \\${env.GIT_COMMIT_SHORT}. Review the security report.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n    }\n}`,\n      notes: 'Configure Go tools and email notifications in Jenkins. Install required plugins: Pipeline, HTML Publisher, Email Extension.'\n    }];\n    this.cicdBestPractices = [{\n      title: 'Fail Fast Strategy',\n      description: 'Configure your pipeline to fail immediately on critical security issues to prevent vulnerable code from progressing.',\n      icon: 'block',\n      color: '#f44336',\n      tips: ['Set critical severity threshold to 0', 'Use exit codes to stop pipeline execution', 'Implement security gates at multiple stages', 'Configure notifications for security failures']\n    }, {\n      title: 'Artifact Management',\n      description: 'Properly store and manage security reports and artifacts for compliance and tracking.',\n      icon: 'inventory',\n      color: '#2196f3',\n      tips: ['Archive security reports for audit trails', 'Use versioned artifact storage', 'Implement retention policies', 'Enable artifact encryption for sensitive data']\n    }, {\n      title: 'Parallel Execution',\n      description: 'Optimize scan performance by running security checks in parallel with other tests.',\n      icon: 'call_split',\n      color: '#4caf50',\n      tips: ['Run security scans parallel to unit tests', 'Use matrix builds for multiple environments', 'Implement caching for faster scans', 'Configure resource limits appropriately']\n    }, {\n      title: 'Security Thresholds',\n      description: 'Define appropriate security thresholds based on your project maturity and risk tolerance.',\n      icon: 'tune',\n      color: '#ff9800',\n      tips: ['Start with strict thresholds for new projects', 'Gradually improve legacy project thresholds', 'Use different thresholds for different branches', 'Document threshold decisions and rationale']\n    }, {\n      title: 'Integration Testing',\n      description: 'Test your CI/CD integration thoroughly before deploying to production pipelines.',\n      icon: 'integration_instructions',\n      color: '#9c27b0',\n      tips: ['Test with sample vulnerable code', 'Verify artifact generation and storage', 'Test notification mechanisms', 'Validate security gate functionality']\n    }, {\n      title: 'Monitoring & Alerting',\n      description: 'Implement comprehensive monitoring and alerting for your security pipeline.',\n      icon: 'monitoring',\n      color: '#607d8b',\n      tips: ['Monitor pipeline execution times', 'Set up alerts for scan failures', 'Track security metrics over time', 'Implement dashboard for security trends']\n    }];\n    this.environmentConfigs = [{\n      name: 'Development Environment',\n      type: 'Environment Variables',\n      config: `# Development - More verbose, all severities\nexport SPT_SEVERITY_THRESHOLD=low\nexport SPT_OUTPUT_FORMAT=table\nexport SPT_COLORS=true\nexport SPT_VERBOSE=true\nexport SPT_PARALLEL_SCANS=2\nexport SPT_TIMEOUT=10m\nexport SPT_CACHE_ENABLED=true\nexport SPT_CACHE_DIR=~/.spt/cache`,\n      description: 'Development environment with verbose output and lower thresholds for learning and debugging.'\n    }, {\n      name: 'Staging Environment',\n      type: 'Environment Variables',\n      config: `# Staging - Production-like with medium threshold\nexport SPT_SEVERITY_THRESHOLD=medium\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=4\nexport SPT_TIMEOUT=15m\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_GENERATE_REPORTS=true`,\n      description: 'Staging environment that mirrors production settings with moderate security requirements.'\n    }, {\n      name: 'Production Environment',\n      type: 'Environment Variables',\n      config: `# Production - Strict security requirements\nexport SPT_SEVERITY_THRESHOLD=high\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=8\nexport SPT_TIMEOUT=30m\nexport SPT_FAIL_ON_CRITICAL=true\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_AUDIT_ENABLED=true\nexport SPT_COMPLIANCE_MODE=true`,\n      description: 'Production environment with strict security requirements and comprehensive auditing.'\n    }, {\n      name: 'Docker Configuration',\n      type: 'Docker Environment',\n      config: `# Docker container environment variables\nENV SPT_SEVERITY_THRESHOLD=medium\nENV SPT_OUTPUT_FORMAT=json\nENV SPT_PARALLEL_SCANS=4\nENV SPT_TIMEOUT=20m\nENV SPT_CACHE_ENABLED=false\nENV SPT_WORKSPACE=/workspace\nENV SPT_REPORTS_DIR=/reports\n\n# Volume mounts\n# docker run -v \\$(pwd):/workspace -v \\$(pwd)/reports:/reports spt-cli`,\n      description: 'Containerized environment configuration for consistent cross-platform execution.'\n    }, {\n      name: 'Cloud-Native Configuration',\n      type: 'Kubernetes ConfigMap',\n      config: `apiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: spt-config\n  namespace: security\ndata:\n  SPT_SEVERITY_THRESHOLD: \"medium\"\n  SPT_OUTPUT_FORMAT: \"json\"\n  SPT_PARALLEL_SCANS: \"6\"\n  SPT_TIMEOUT: \"25m\"\n  SPT_CLOUD_STORAGE: \"true\"\n  SPT_METRICS_ENABLED: \"true\"\n  SPT_DISTRIBUTED_SCAN: \"true\"\n---\napiVersion: v1\nkind: Secret\nmetadata:\n  name: spt-secrets\n  namespace: security\ntype: Opaque\nstringData:\n  github-token: \"your-github-token\"\n  api-key: \"your-api-key\"`,\n      description: 'Kubernetes-native configuration using ConfigMaps and Secrets for cloud deployments.'\n    }];\n    this.configExample = `{\n  \"scanning\": {\n    \"chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n    \"severity_threshold\": \"medium\",\n    \"max_file_size\": \"10MB\",\n    \"timeout\": \"5m\",\n    \"parallel_scans\": 4\n  },\n  \"output\": {\n    \"format\": \"table\",\n    \"colors\": true,\n    \"verbose\": false\n  },\n  \"rules\": {\n    \"ethereum\": {\n      \"check_reentrancy\": true,\n      \"check_overflow\": true,\n      \"check_access_control\": true\n    },\n    \"bitcoin\": {\n      \"check_key_management\": true,\n      \"check_transaction_validation\": true\n    }\n  },\n  \"integrations\": {\n    \"vscode\": {\n      \"enabled\": true,\n      \"server_url\": \"http://localhost:8080\"\n    }\n  }\n}`;\n    this.configOptions = [{\n      key: 'scanning.chains',\n      description: 'Array of blockchain chains to analyze'\n    }, {\n      key: 'scanning.severity_threshold',\n      description: 'Minimum severity level to report'\n    }, {\n      key: 'output.format',\n      description: 'Default output format for scan results'\n    }, {\n      key: 'rules.ethereum',\n      description: 'Ethereum-specific security rules configuration'\n    }, {\n      key: 'rules.bitcoin',\n      description: 'Bitcoin-specific security rules configuration'\n    }, {\n      key: 'integrations',\n      description: 'Configuration for IDE and tool integrations'\n    }];\n    this.platformTips = {\n      'GitHub Actions': ['Use GitHub Secrets for sensitive tokens', 'Enable branch protection rules with status checks', 'Configure matrix builds for multiple Go versions', 'Use actions/cache for faster builds'],\n      'GitLab CI': ['Use GitLab CI/CD variables for configuration', 'Configure merge request pipelines', 'Use GitLab Container Registry for custom images', 'Enable pipeline schedules for regular scans'],\n      'Azure DevOps': ['Store secrets in Azure Key Vault', 'Use variable groups for environment-specific configs', 'Configure branch policies with build validation', 'Enable Azure Artifacts for report storage'],\n      'AWS CodeBuild': ['Use Parameter Store for secure configuration', 'Configure VPC settings for private repositories', 'Use CloudWatch for monitoring and alerting', 'Enable S3 artifact encryption'],\n      'AWS CodePipeline': ['Use CloudFormation for infrastructure as code', 'Configure cross-region artifact replication', 'Implement approval gates for production', 'Use EventBridge for pipeline notifications'],\n      'Docker': ['Use multi-stage builds for smaller images', 'Run containers as non-root user', 'Mount volumes for persistent reports', 'Use health checks for container monitoring'],\n      'Jenkins': ['Use Jenkins Credentials for secure storage', 'Configure build triggers with webhooks', 'Use Pipeline as Code with Jenkinsfile', 'Enable Blue Ocean for better UI']\n    };\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      // Could add a snackbar notification here\n      console.log('Configuration copied to clipboard');\n    }).catch(err => {\n      console.error('Failed to copy: ', err);\n    });\n  }\n  getPlatformTips(platform) {\n    return this.platformTips[platform] || [];\n  }\n  static {\n    this.ɵfac = function CliGuideComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CliGuideComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CliGuideComponent,\n      selectors: [[\"app-cli-guide\"]],\n      decls: 96,\n      vars: 8,\n      consts: [[1, \"cli-guide-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [1, \"cli-overview\"], [1, \"overview-card\"], [\"mat-card-avatar\", \"\"], [1, \"cli-features\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [\"animationDuration\", \"300ms\", 1, \"cli-tabs\"], [\"label\", \"Installation\"], [1, \"tab-content\"], [1, \"installation-methods\"], [\"class\", \"method-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Commands\"], [1, \"commands-list\"], [\"class\", \"command-panel\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"CI/CD Integration\"], [1, \"integration-examples\"], [1, \"integration-accordion\"], [\"class\", \"integration-panel\", 3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"best-practices-section\"], [1, \"section-description\"], [1, \"practices-accordion\"], [\"class\", \"practice-panel\", 4, \"ngFor\", \"ngForOf\"], [1, \"environment-configs\"], [1, \"env-config-card\"], [1, \"env-examples\"], [\"class\", \"env-example\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-card\"], [1, \"code-block\"], [1, \"code-header\"], [1, \"config-description\"], [1, \"config-options\"], [\"class\", \"config-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature\"], [1, \"method-card\"], [\"class\", \"method-notes\", 4, \"ngIf\"], [1, \"method-notes\"], [1, \"command-panel\"], [1, \"command-name\"], [1, \"command-details\"], [1, \"usage-section\"], [\"class\", \"options-section\", 4, \"ngIf\"], [\"class\", \"examples-section\", 4, \"ngIf\"], [\"class\", \"notes-section\", 4, \"ngIf\"], [1, \"options-section\"], [\"mat-table\", \"\", 1, \"options-table\", 3, \"dataSource\"], [\"matColumnDef\", \"flag\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"default\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [4, \"ngIf\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"examples-section\"], [\"class\", \"example\", 4, \"ngFor\", \"ngForOf\"], [1, \"example\"], [1, \"example-description\"], [\"class\", \"output-block\", 4, \"ngIf\"], [1, \"output-block\"], [1, \"notes-section\"], [1, \"notes-content\"], [1, \"integration-panel\", 3, \"expanded\"], [\"aria-hidden\", \"true\"], [1, \"integration-content\"], [1, \"integration-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy configuration\", 1, \"copy-config-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy to clipboard\", 1, \"copy-btn\", 3, \"click\"], [\"class\", \"integration-notes\", 4, \"ngIf\"], [\"class\", \"platform-tips\", 4, \"ngIf\"], [1, \"integration-notes\"], [1, \"notes-icon\"], [1, \"platform-tips\"], [1, \"tips-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"tip-icon\"], [1, \"practice-panel\"], [1, \"practice-icon\"], [1, \"practice-content\"], [1, \"practice-tips\"], [\"class\", \"practice-tip\", 4, \"ngFor\", \"ngForOf\"], [1, \"practice-tip\"], [1, \"tip-check-icon\"], [1, \"env-example\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy configuration\", 1, \"copy-btn\", 3, \"click\"], [1, \"env-description\"], [1, \"config-option\"]],\n      template: function CliGuideComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"terminal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" CLI Guide \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 2);\n          i0.ɵɵtext(7, \" Complete guide to the SPT command-line interface \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-header\")(11, \"mat-icon\", 5);\n          i0.ɵɵtext(12, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-card-title\");\n          i0.ɵɵtext(14, \"SPT CLI Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"mat-card-subtitle\");\n          i0.ɵɵtext(16, \"Powerful command-line security scanning tool\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"mat-card-content\")(18, \"p\");\n          i0.ɵɵtext(19, \"The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 6);\n          i0.ɵɵtemplate(21, CliGuideComponent_div_21_Template, 8, 5, \"div\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"mat-tab-group\", 8)(23, \"mat-tab\", 9)(24, \"div\", 10)(25, \"h2\");\n          i0.ɵɵtext(26, \"Installation Methods\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 11);\n          i0.ɵɵtemplate(28, CliGuideComponent_mat_card_28_Template, 19, 5, \"mat-card\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"mat-tab\", 13)(30, \"div\", 10)(31, \"h2\");\n          i0.ɵɵtext(32, \"Available Commands\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\");\n          i0.ɵɵtext(34, \"Complete reference for all SPT CLI commands and their options.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 14);\n          i0.ɵɵtemplate(36, CliGuideComponent_mat_expansion_panel_36_Template, 18, 6, \"mat-expansion-panel\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"mat-tab\", 16)(38, \"div\", 10)(39, \"h2\");\n          i0.ɵɵtext(40, \"CI/CD Integration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"p\");\n          i0.ɵɵtext(42, \"Integrate SPT CLI into your continuous integration and deployment pipelines.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 17)(44, \"mat-accordion\", 18);\n          i0.ɵɵtemplate(45, CliGuideComponent_mat_expansion_panel_45_Template, 28, 11, \"mat-expansion-panel\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 20)(47, \"h3\");\n          i0.ɵɵtext(48, \"CI/CD Best Practices\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"p\", 21);\n          i0.ɵɵtext(50, \"Essential practices for implementing SPT in your CI/CD pipeline effectively.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-accordion\", 22);\n          i0.ɵɵtemplate(52, CliGuideComponent_mat_expansion_panel_52_Template, 11, 6, \"mat-expansion-panel\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 24)(54, \"h3\");\n          i0.ɵɵtext(55, \"Environment-Specific Configurations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"mat-card\", 25)(57, \"mat-card-header\")(58, \"mat-icon\", 5);\n          i0.ɵɵtext(59, \"settings_applications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"mat-card-title\");\n          i0.ɵɵtext(61, \"Environment Variables\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"mat-card-subtitle\");\n          i0.ɵɵtext(63, \"Configure SPT for different environments\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"mat-card-content\")(65, \"div\", 26);\n          i0.ɵɵtemplate(66, CliGuideComponent_div_66_Template, 17, 4, \"div\", 27);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(67, \"mat-tab\", 28)(68, \"div\", 10)(69, \"h2\");\n          i0.ɵɵtext(70, \"Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"p\");\n          i0.ɵɵtext(72, \"Configure SPT CLI for your development environment and preferences.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"mat-card\", 29)(74, \"mat-card-header\")(75, \"mat-icon\", 5);\n          i0.ɵɵtext(76, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"mat-card-title\");\n          i0.ɵɵtext(78, \"Configuration File\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"mat-card-subtitle\");\n          i0.ɵɵtext(80, \"spt.config.json\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"mat-card-content\")(82, \"div\", 30)(83, \"div\", 31)(84, \"mat-icon\");\n          i0.ɵɵtext(85, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"span\");\n          i0.ɵɵtext(87, \"JSON Configuration\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"pre\")(89, \"code\");\n          i0.ɵɵtext(90);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(91, \"div\", 32)(92, \"h4\");\n          i0.ɵɵtext(93, \"Configuration Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"div\", 33);\n          i0.ɵɵtemplate(95, CliGuideComponent_div_95_Template, 5, 2, \"div\", 34);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cliFeatures);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.installationMethods);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cliCommands);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.integrationExamples);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cicdBestPractices);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngForOf\", ctx.environmentConfigs);\n          i0.ɵɵadvance(24);\n          i0.ɵɵtextInterpolate(ctx.configExample);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.configOptions);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatExpansionModule, i5.MatAccordion, i5.MatExpansionPanel, i5.MatExpansionPanelHeader, i5.MatExpansionPanelTitle, i5.MatExpansionPanelDescription, MatChipsModule, i6.MatChip, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow],\n      styles: [\".cli-guide-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  color: #1976d2;\\n  margin: 0 0 8px 0;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1em;\\n  margin: 0;\\n}\\n\\n.cli-overview[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.overview-card[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.cli-features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-top: 16px;\\n}\\n\\n.feature[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n}\\n\\n.feature[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n}\\n\\n.feature[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n\\n.feature[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n.cli-tabs[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 24px 0;\\n}\\n\\n.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  margin-bottom: 8px;\\n}\\n\\n.installation-methods[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 24px;\\n  margin-top: 24px;\\n}\\n\\n.method-card[_ngcontent-%COMP%] {\\n  height: fit-content;\\n}\\n\\n.method-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 12px;\\n  padding: 8px 12px;\\n  background: #e3f2fd;\\n  border-radius: 4px;\\n  color: #1976d2;\\n}\\n\\n.commands-list[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.command-panel[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.command-name[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: 500;\\n}\\n\\n.command-details[_ngcontent-%COMP%] {\\n  padding: 16px 0;\\n}\\n\\n.usage-section[_ngcontent-%COMP%], \\n.options-section[_ngcontent-%COMP%], \\n.examples-section[_ngcontent-%COMP%], \\n.notes-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.usage-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.options-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.examples-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.notes-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #1976d2;\\n}\\n\\n.code-block[_ngcontent-%COMP%] {\\n  border: 1px solid var(--spt-gray-200);\\n  border-radius: var(--spt-radius-xl);\\n  overflow: hidden;\\n  margin-bottom: var(--spt-space-4);\\n  box-shadow: var(--spt-shadow-sm);\\n  background: white;\\n}\\n\\n.code-header[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-50);\\n  padding: var(--spt-space-3) var(--spt-space-4);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  font-weight: var(--spt-font-medium);\\n  border-bottom: 1px solid var(--spt-gray-200);\\n  justify-content: space-between;\\n}\\n\\n.code-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-primary-600);\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.code-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-700);\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-medium);\\n  flex: 1;\\n}\\n\\n.copy-btn[_ngcontent-%COMP%] {\\n  color: var(--spt-primary-600) !important;\\n  background: var(--spt-primary-100) !important;\\n  border-radius: var(--spt-radius-md) !important;\\n  transition: all 0.2s ease !important;\\n}\\n\\n.copy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-200) !important;\\n  transform: scale(1.05);\\n}\\n\\n.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: var(--spt-space-4);\\n  background: var(--spt-gray-50);\\n  overflow-x: auto;\\n  font-family: \\\"JetBrains Mono\\\", \\\"Fira Code\\\", \\\"Courier New\\\", monospace;\\n  font-size: var(--spt-text-sm);\\n  line-height: 1.5;\\n  color: var(--spt-gray-800);\\n}\\n\\n.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: inherit;\\n  font-size: inherit;\\n  color: inherit;\\n}\\n\\n.output-block[_ngcontent-%COMP%] {\\n  border: 1px solid #4caf50;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-top: 8px;\\n}\\n\\n.output-block[_ngcontent-%COMP%]   .code-header[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  border-bottom-color: #4caf50;\\n}\\n\\n.output-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n}\\n\\n.options-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.options-table[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 0.9em;\\n}\\n\\n.example[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  padding: 16px;\\n  background: #f9f9f9;\\n  border-radius: 8px;\\n}\\n\\n.example-description[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-weight: 500;\\n  color: #1976d2;\\n}\\n\\n.notes-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  padding: 12px;\\n  background: #e3f2fd;\\n  border-radius: 8px;\\n  color: #1976d2;\\n}\\n\\n.notes-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.integration-examples[_ngcontent-%COMP%] {\\n  margin-top: var(--spt-space-6);\\n}\\n\\n.integration-accordion[_ngcontent-%COMP%] {\\n  box-shadow: var(--spt-shadow-sm);\\n  border-radius: var(--spt-radius-xl);\\n  overflow: hidden;\\n  border: 1px solid var(--spt-gray-200);\\n}\\n\\n.integration-panel[_ngcontent-%COMP%] {\\n  border: none !important;\\n  box-shadow: none !important;\\n  border-bottom: 1px solid var(--spt-gray-200) !important;\\n}\\n\\n.integration-panel[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none !important;\\n}\\n\\n.integration-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-4) var(--spt-space-6) !important;\\n  background: var(--spt-gray-50) !important;\\n  transition: all 0.2s ease !important;\\n}\\n\\n.integration-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-50) !important;\\n}\\n\\n.integration-panel.mat-expanded[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-100) !important;\\n  border-bottom: 1px solid var(--spt-primary-200) !important;\\n}\\n\\n.platform-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-primary-600);\\n  margin-right: var(--spt-space-3);\\n  font-size: 18px;\\n  width: 18px;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n\\n\\n.platform-icon.fa-github[_ngcontent-%COMP%] {\\n  color: #24292e;\\n}\\n\\n.platform-icon.fa-gitlab[_ngcontent-%COMP%] {\\n  color: #fc6d26;\\n}\\n\\n.platform-icon.fa-microsoft[_ngcontent-%COMP%] {\\n  color: #0078d4;\\n}\\n\\n.platform-icon.fa-aws[_ngcontent-%COMP%] {\\n  color: #ff9900;\\n}\\n\\n.platform-icon.fa-docker[_ngcontent-%COMP%] {\\n  color: #2496ed;\\n}\\n\\n.platform-icon.fa-jenkins[_ngcontent-%COMP%] {\\n  color: #d33833;\\n}\\n\\n.integration-content[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n  background: white;\\n}\\n\\n.integration-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.integration-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--spt-gray-900);\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.copy-config-btn[_ngcontent-%COMP%] {\\n  color: var(--spt-primary-600) !important;\\n  background: var(--spt-primary-100) !important;\\n  border-radius: var(--spt-radius-md) !important;\\n}\\n\\n.copy-config-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-200) !important;\\n}\\n\\n.integration-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: var(--spt-space-3);\\n  margin-top: var(--spt-space-4);\\n  padding: var(--spt-space-4);\\n  background: var(--spt-info-50);\\n  border-radius: var(--spt-radius-lg);\\n  border: 1px solid var(--spt-info-200);\\n}\\n\\n.notes-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-info-600);\\n  margin-top: var(--spt-space-1);\\n}\\n\\n.notes-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.notes-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--spt-info-800);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.notes-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: var(--spt-space-1) 0 0 0;\\n  color: var(--spt-info-700);\\n  line-height: 1.5;\\n}\\n\\n.platform-tips[_ngcontent-%COMP%] {\\n  margin-top: var(--spt-space-4);\\n  padding: var(--spt-space-4);\\n  background: var(--spt-success-50);\\n  border-radius: var(--spt-radius-lg);\\n  border: 1px solid var(--spt-success-200);\\n}\\n\\n.platform-tips[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--spt-space-3) 0;\\n  color: var(--spt-success-800);\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n}\\n\\n.tips-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.tips-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: var(--spt-space-2);\\n  margin-bottom: var(--spt-space-2);\\n  color: var(--spt-success-700);\\n  font-size: var(--spt-text-sm);\\n  line-height: 1.4;\\n}\\n\\n.tip-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  margin-top: 2px;\\n}\\n\\n\\n\\n.best-practices-section[_ngcontent-%COMP%] {\\n  margin-top: var(--spt-space-8);\\n  margin-bottom: var(--spt-space-8);\\n}\\n\\n.best-practices-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-900);\\n  font-size: var(--spt-text-2xl);\\n  font-weight: var(--spt-font-bold);\\n  margin-bottom: var(--spt-space-2);\\n}\\n\\n.section-description[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-600);\\n  font-size: var(--spt-text-base);\\n  margin-bottom: var(--spt-space-6);\\n  line-height: 1.6;\\n}\\n\\n.practices-accordion[_ngcontent-%COMP%] {\\n  box-shadow: var(--spt-shadow-sm);\\n  border-radius: var(--spt-radius-xl);\\n  overflow: hidden;\\n  border: 1px solid var(--spt-gray-200);\\n}\\n\\n.practice-panel[_ngcontent-%COMP%] {\\n  border: none !important;\\n  box-shadow: none !important;\\n  border-bottom: 1px solid var(--spt-gray-200) !important;\\n}\\n\\n.practice-panel[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none !important;\\n}\\n\\n.practice-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-4) var(--spt-space-6) !important;\\n  background: var(--spt-gray-50) !important;\\n  transition: all 0.2s ease !important;\\n}\\n\\n.practice-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-secondary-50) !important;\\n}\\n\\n.practice-panel.mat-expanded[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\\n  background: var(--spt-secondary-100) !important;\\n  border-bottom: 1px solid var(--spt-secondary-200) !important;\\n}\\n\\n.practice-icon[_ngcontent-%COMP%] {\\n  margin-right: var(--spt-space-3);\\n}\\n\\n.practice-content[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-6);\\n  background: white;\\n}\\n\\n.practice-tips[_ngcontent-%COMP%] {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.practice-tip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: var(--spt-space-2);\\n  margin-bottom: var(--spt-space-3);\\n  color: var(--spt-gray-700);\\n  font-size: var(--spt-text-sm);\\n  line-height: 1.5;\\n}\\n\\n.tip-check-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  margin-top: 2px;\\n}\\n\\n.config-card[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.config-description[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.config-description[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #1976d2;\\n}\\n\\n.config-options[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n}\\n\\n.config-option[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n}\\n\\n.config-option[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n  color: #1976d2;\\n}\\n\\n.config-option[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n@media (max-width: 768px) {\\n  .cli-features[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .installation-methods[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .integration-content[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-4);\\n  }\\n  .integration-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: var(--spt-space-2);\\n  }\\n  .integration-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: var(--spt-text-base);\\n  }\\n  .platform-tips[_ngcontent-%COMP%], \\n   .integration-notes[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-3);\\n  }\\n  .config-options[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9kb2N1bWVudGF0aW9uL2NvbXBvbmVudHMvY2xpLWd1aWRlL2NsaS1ndWlkZS5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSxpQkFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLGtCQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsU0FBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtBQUFOOztBQUdJO0VBQ0UsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLHlCQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxTQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsZUFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsZUFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxTQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLG1CQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSxnQkFBQTtBQUFOOztBQUdJO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxxQ0FBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxlQUFBO0FBQU47O0FBR0k7Ozs7RUFJRSxtQkFBQTtBQUFOOztBQUdJOzs7O0VBSUUsa0JBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSxxQ0FBQTtFQUNBLG1DQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQ0FBQTtFQUNBLGdDQUFBO0VBQ0EsaUJBQUE7QUFBTjs7QUFHSTtFQUNFLDhCQUFBO0VBQ0EsOENBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLG1DQUFBO0VBQ0EsNENBQUE7RUFDQSw4QkFBQTtBQUFOOztBQUdJO0VBQ0UsNkJBQUE7RUFDQSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFBTjs7QUFHSTtFQUNFLDBCQUFBO0VBQ0EsNkJBQUE7RUFDQSxtQ0FBQTtFQUNBLE9BQUE7QUFBTjs7QUFHSTtFQUNFLHdDQUFBO0VBQ0EsNkNBQUE7RUFDQSw4Q0FBQTtFQUNBLG9DQUFBO0FBQU47O0FBR0k7RUFDRSw2Q0FBQTtFQUNBLHNCQUFBO0FBQU47O0FBR0k7RUFDRSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSw4QkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0VBQUE7RUFDQSw2QkFBQTtFQUNBLGdCQUFBO0VBQ0EsMEJBQUE7QUFBTjs7QUFHSTtFQUNFLG9CQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtFQUNBLDRCQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7QUFBTjs7QUFHSTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsUUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBQUFOOztBQUdJO0VBQ0UsU0FBQTtBQUFOOztBQUdJO0VBQ0UsOEJBQUE7QUFBTjs7QUFHSTtFQUNFLGdDQUFBO0VBQ0EsbUNBQUE7RUFDQSxnQkFBQTtFQUNBLHFDQUFBO0FBQU47O0FBR0k7RUFDRSx1QkFBQTtFQUNBLDJCQUFBO0VBQ0EsdURBQUE7QUFBTjs7QUFHSTtFQUNFLDhCQUFBO0FBQU47O0FBR0k7RUFDRSx5REFBQTtFQUNBLHlDQUFBO0VBQ0Esb0NBQUE7QUFBTjs7QUFHSTtFQUNFLDRDQUFBO0FBQU47O0FBR0k7RUFDRSw2Q0FBQTtFQUNBLDBEQUFBO0FBQU47O0FBR0k7RUFDRSw2QkFBQTtFQUNBLGdDQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxvQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFBTjs7QUFHSSxrQ0FBQTtBQUNBO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsMkJBQUE7RUFDQSxpQkFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxpQ0FBQTtBQUFOOztBQUdJO0VBQ0UsU0FBQTtFQUNBLDBCQUFBO0VBQ0EsNkJBQUE7RUFDQSxxQ0FBQTtBQUFOOztBQUdJO0VBQ0Usd0NBQUE7RUFDQSw2Q0FBQTtFQUNBLDhDQUFBO0FBQU47O0FBR0k7RUFDRSw2Q0FBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsdUJBQUE7RUFDQSw4QkFBQTtFQUNBLDJCQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQ0FBQTtFQUNBLHFDQUFBO0FBQU47O0FBR0k7RUFDRSwwQkFBQTtFQUNBLDhCQUFBO0FBQU47O0FBR0k7RUFDRSxPQUFBO0FBQU47O0FBR0k7RUFDRSwwQkFBQTtFQUNBLHFDQUFBO0FBQU47O0FBR0k7RUFDRSxnQ0FBQTtFQUNBLDBCQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLDhCQUFBO0VBQ0EsMkJBQUE7RUFDQSxpQ0FBQTtFQUNBLG1DQUFBO0VBQ0Esd0NBQUE7QUFBTjs7QUFHSTtFQUNFLGdDQUFBO0VBQ0EsNkJBQUE7RUFDQSwrQkFBQTtFQUNBLHFDQUFBO0FBQU47O0FBR0k7RUFDRSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSx1QkFBQTtFQUNBLGlDQUFBO0VBQ0EsNkJBQUE7RUFDQSw2QkFBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSw2QkFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSSwyQkFBQTtBQUNBO0VBQ0UsOEJBQUE7RUFDQSxpQ0FBQTtBQUFOOztBQUdJO0VBQ0UsMEJBQUE7RUFDQSw4QkFBQTtFQUNBLGlDQUFBO0VBQ0EsaUNBQUE7QUFBTjs7QUFHSTtFQUNFLDBCQUFBO0VBQ0EsK0JBQUE7RUFDQSxpQ0FBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxnQ0FBQTtFQUNBLG1DQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQ0FBQTtBQUFOOztBQUdJO0VBQ0UsdUJBQUE7RUFDQSwyQkFBQTtFQUNBLHVEQUFBO0FBQU47O0FBR0k7RUFDRSw4QkFBQTtBQUFOOztBQUdJO0VBQ0UseURBQUE7RUFDQSx5Q0FBQTtFQUNBLG9DQUFBO0FBQU47O0FBR0k7RUFDRSw4Q0FBQTtBQUFOOztBQUdJO0VBQ0UsK0NBQUE7RUFDQSw0REFBQTtBQUFOOztBQUdJO0VBQ0UsZ0NBQUE7QUFBTjs7QUFHSTtFQUNFLDJCQUFBO0VBQ0EsaUJBQUE7QUFBTjs7QUFHSTtFQUNFLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLHVCQUFBO0VBQ0EsaUNBQUE7RUFDQSwwQkFBQTtFQUNBLDZCQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLDZCQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtBQUFOOztBQUdJO0VBQ0UsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRTtJQUNFLDBCQUFBO0VBQU47RUFHSTtJQUNFLDBCQUFBO0VBRE47RUFJSTtJQUNFLDJCQUFBO0VBRk47RUFLSTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7SUFDQSx1QkFBQTtFQUhOO0VBTUk7SUFDRSwrQkFBQTtFQUpOO0VBT0k7O0lBRUUsMkJBQUE7RUFMTjtFQVFJO0lBQ0UsMEJBQUE7RUFOTjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmNsaS1ndWlkZS1jb250YWluZXIge1xuICAgICAgbWF4LXdpZHRoOiAxMjAwcHg7XG4gICAgICBtYXJnaW46IDAgYXV0bztcbiAgICB9XG5cbiAgICAucGFnZS1oZWFkZXIge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMzJweDtcbiAgICB9XG5cbiAgICAucGFnZS1oZWFkZXIgaDEge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGdhcDogMTJweDtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgICAgbWFyZ2luOiAwIDAgOHB4IDA7XG4gICAgfVxuXG4gICAgLnBhZ2Utc3VidGl0bGUge1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICBmb250LXNpemU6IDEuMWVtO1xuICAgICAgbWFyZ2luOiAwO1xuICAgIH1cblxuICAgIC5jbGktb3ZlcnZpZXcge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMzJweDtcbiAgICB9XG5cbiAgICAub3ZlcnZpZXctY2FyZCB7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xuICAgIH1cblxuICAgIC5jbGktZmVhdHVyZXMge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjUwcHgsIDFmcikpO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgICAgbWFyZ2luLXRvcDogMTZweDtcbiAgICB9XG5cbiAgICAuZmVhdHVyZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgICBnYXA6IDEycHg7XG4gICAgICBwYWRkaW5nOiAxMnB4O1xuICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICB9XG5cbiAgICAuZmVhdHVyZSBtYXQtaWNvbiB7XG4gICAgICBtYXJnaW4tdG9wOiAycHg7XG4gICAgfVxuXG4gICAgLmZlYXR1cmUgc3Ryb25nIHtcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgIH1cblxuICAgIC5mZWF0dXJlIHAge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICBmb250LXNpemU6IDAuOWVtO1xuICAgIH1cblxuICAgIC5jbGktdGFicyB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xuICAgIH1cblxuICAgIC50YWItY29udGVudCB7XG4gICAgICBwYWRkaW5nOiAyNHB4IDA7XG4gICAgfVxuXG4gICAgLnRhYi1jb250ZW50IGgyIHtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgIH1cblxuICAgIC5pbnN0YWxsYXRpb24tbWV0aG9kcyB7XG4gICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCg0MDBweCwgMWZyKSk7XG4gICAgICBnYXA6IDI0cHg7XG4gICAgICBtYXJnaW4tdG9wOiAyNHB4O1xuICAgIH1cblxuICAgIC5tZXRob2QtY2FyZCB7XG4gICAgICBoZWlnaHQ6IGZpdC1jb250ZW50O1xuICAgIH1cblxuICAgIC5tZXRob2Qtbm90ZXMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDhweDtcbiAgICAgIG1hcmdpbi10b3A6IDEycHg7XG4gICAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICAgIGJhY2tncm91bmQ6ICNlM2YyZmQ7XG4gICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICBjb2xvcjogIzE5NzZkMjtcbiAgICB9XG5cbiAgICAuY29tbWFuZHMtbGlzdCB7XG4gICAgICBtYXJnaW4tdG9wOiAyNHB4O1xuICAgIH1cblxuICAgIC5jb21tYW5kLXBhbmVsIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgfVxuXG4gICAgLmNvbW1hbmQtbmFtZSB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZjVmNWY1O1xuICAgICAgcGFkZGluZzogNHB4IDhweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgIH1cblxuICAgIC5jb21tYW5kLWRldGFpbHMge1xuICAgICAgcGFkZGluZzogMTZweCAwO1xuICAgIH1cblxuICAgIC51c2FnZS1zZWN0aW9uLFxuICAgIC5vcHRpb25zLXNlY3Rpb24sXG4gICAgLmV4YW1wbGVzLXNlY3Rpb24sXG4gICAgLm5vdGVzLXNlY3Rpb24ge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgICB9XG5cbiAgICAudXNhZ2Utc2VjdGlvbiBoNCxcbiAgICAub3B0aW9ucy1zZWN0aW9uIGg0LFxuICAgIC5leGFtcGxlcy1zZWN0aW9uIGg0LFxuICAgIC5ub3Rlcy1zZWN0aW9uIGg0IHtcbiAgICAgIG1hcmdpbjogMCAwIDEycHggMDtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgIH1cblxuICAgIC5jb2RlLWJsb2NrIHtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLXNwdC1ncmF5LTIwMCk7XG4gICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1zcHQtcmFkaXVzLXhsKTtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcHQtc3BhY2UtNCk7XG4gICAgICBib3gtc2hhZG93OiB2YXIoLS1zcHQtc2hhZG93LXNtKTtcbiAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgIH1cblxuICAgIC5jb2RlLWhlYWRlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zcHQtZ3JheS01MCk7XG4gICAgICBwYWRkaW5nOiB2YXIoLS1zcHQtc3BhY2UtMykgdmFyKC0tc3B0LXNwYWNlLTQpO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IHZhcigtLXNwdC1zcGFjZS0yKTtcbiAgICAgIGZvbnQtd2VpZ2h0OiB2YXIoLS1zcHQtZm9udC1tZWRpdW0pO1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXNwdC1ncmF5LTIwMCk7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgfVxuXG4gICAgLmNvZGUtaGVhZGVyIG1hdC1pY29uIHtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtcHJpbWFyeS02MDApO1xuICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgICAgd2lkdGg6IDE4cHg7XG4gICAgICBoZWlnaHQ6IDE4cHg7XG4gICAgfVxuXG4gICAgLmNvZGUtaGVhZGVyIHNwYW4ge1xuICAgICAgY29sb3I6IHZhcigtLXNwdC1ncmF5LTcwMCk7XG4gICAgICBmb250LXNpemU6IHZhcigtLXNwdC10ZXh0LXNtKTtcbiAgICAgIGZvbnQtd2VpZ2h0OiB2YXIoLS1zcHQtZm9udC1tZWRpdW0pO1xuICAgICAgZmxleDogMTtcbiAgICB9XG5cbiAgICAuY29weS1idG4ge1xuICAgICAgY29sb3I6IHZhcigtLXNwdC1wcmltYXJ5LTYwMCkgIWltcG9ydGFudDtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1wcmltYXJ5LTEwMCkgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXNwdC1yYWRpdXMtbWQpICFpbXBvcnRhbnQ7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLmNvcHktYnRuOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1wcmltYXJ5LTIwMCkgIWltcG9ydGFudDtcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XG4gICAgfVxuXG4gICAgLmNvZGUtYmxvY2sgcHJlIHtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIHBhZGRpbmc6IHZhcigtLXNwdC1zcGFjZS00KTtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1ncmF5LTUwKTtcbiAgICAgIG92ZXJmbG93LXg6IGF1dG87XG4gICAgICBmb250LWZhbWlseTogJ0pldEJyYWlucyBNb25vJywgJ0ZpcmEgQ29kZScsICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTtcbiAgICAgIGZvbnQtc2l6ZTogdmFyKC0tc3B0LXRleHQtc20pO1xuICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtZ3JheS04MDApO1xuICAgIH1cblxuICAgIC5jb2RlLWJsb2NrIGNvZGUge1xuICAgICAgZm9udC1mYW1pbHk6IGluaGVyaXQ7XG4gICAgICBmb250LXNpemU6IGluaGVyaXQ7XG4gICAgICBjb2xvcjogaW5oZXJpdDtcbiAgICB9XG5cbiAgICAub3V0cHV0LWJsb2NrIHtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICM0Y2FmNTA7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgbWFyZ2luLXRvcDogOHB4O1xuICAgIH1cblxuICAgIC5vdXRwdXQtYmxvY2sgLmNvZGUtaGVhZGVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICNlOGY1ZTg7XG4gICAgICBib3JkZXItYm90dG9tLWNvbG9yOiAjNGNhZjUwO1xuICAgIH1cblxuICAgIC5vdXRwdXQtYmxvY2sgcHJlIHtcbiAgICAgIGJhY2tncm91bmQ6ICNmMWY4ZTk7XG4gICAgfVxuXG4gICAgLm9wdGlvbnMtdGFibGUge1xuICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgIH1cblxuICAgIC5vcHRpb25zLXRhYmxlIGNvZGUge1xuICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcbiAgICAgIHBhZGRpbmc6IDJweCA2cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICBmb250LXNpemU6IDAuOWVtO1xuICAgIH1cblxuICAgIC5leGFtcGxlIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgYmFja2dyb3VuZDogI2Y5ZjlmOTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICB9XG5cbiAgICAuZXhhbXBsZS1kZXNjcmlwdGlvbiB7XG4gICAgICBtYXJnaW46IDAgMCAxMnB4IDA7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgY29sb3I6ICMxOTc2ZDI7XG4gICAgfVxuXG4gICAgLm5vdGVzLWNvbnRlbnQge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBwYWRkaW5nOiAxMnB4O1xuICAgICAgYmFja2dyb3VuZDogI2UzZjJmZDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgIH1cblxuICAgIC5ub3Rlcy1jb250ZW50IHAge1xuICAgICAgbWFyZ2luOiAwO1xuICAgIH1cblxuICAgIC5pbnRlZ3JhdGlvbi1leGFtcGxlcyB7XG4gICAgICBtYXJnaW4tdG9wOiB2YXIoLS1zcHQtc3BhY2UtNik7XG4gICAgfVxuXG4gICAgLmludGVncmF0aW9uLWFjY29yZGlvbiB7XG4gICAgICBib3gtc2hhZG93OiB2YXIoLS1zcHQtc2hhZG93LXNtKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXNwdC1yYWRpdXMteGwpO1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLXNwdC1ncmF5LTIwMCk7XG4gICAgfVxuXG4gICAgLmludGVncmF0aW9uLXBhbmVsIHtcbiAgICAgIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXNwdC1ncmF5LTIwMCkgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAuaW50ZWdyYXRpb24tcGFuZWw6bGFzdC1jaGlsZCB7XG4gICAgICBib3JkZXItYm90dG9tOiBub25lICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLmludGVncmF0aW9uLXBhbmVsIC5tYXQtZXhwYW5zaW9uLXBhbmVsLWhlYWRlciB7XG4gICAgICBwYWRkaW5nOiB2YXIoLS1zcHQtc3BhY2UtNCkgdmFyKC0tc3B0LXNwYWNlLTYpICFpbXBvcnRhbnQ7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zcHQtZ3JheS01MCkgIWltcG9ydGFudDtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAuaW50ZWdyYXRpb24tcGFuZWwgLm1hdC1leHBhbnNpb24tcGFuZWwtaGVhZGVyOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1wcmltYXJ5LTUwKSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5pbnRlZ3JhdGlvbi1wYW5lbC5tYXQtZXhwYW5kZWQgLm1hdC1leHBhbnNpb24tcGFuZWwtaGVhZGVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1wcmltYXJ5LTEwMCkgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zcHQtcHJpbWFyeS0yMDApICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLnBsYXRmb3JtLWljb24ge1xuICAgICAgY29sb3I6IHZhcigtLXNwdC1wcmltYXJ5LTYwMCk7XG4gICAgICBtYXJnaW4tcmlnaHQ6IHZhcigtLXNwdC1zcGFjZS0zKTtcbiAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgIHdpZHRoOiAxOHB4O1xuICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgfVxuXG4gICAgLyogUGxhdGZvcm0tc3BlY2lmaWMgaWNvbiBjb2xvcnMgKi9cbiAgICAucGxhdGZvcm0taWNvbi5mYS1naXRodWIge1xuICAgICAgY29sb3I6ICMyNDI5MmU7XG4gICAgfVxuXG4gICAgLnBsYXRmb3JtLWljb24uZmEtZ2l0bGFiIHtcbiAgICAgIGNvbG9yOiAjZmM2ZDI2O1xuICAgIH1cblxuICAgIC5wbGF0Zm9ybS1pY29uLmZhLW1pY3Jvc29mdCB7XG4gICAgICBjb2xvcjogIzAwNzhkNDtcbiAgICB9XG5cbiAgICAucGxhdGZvcm0taWNvbi5mYS1hd3Mge1xuICAgICAgY29sb3I6ICNmZjk5MDA7XG4gICAgfVxuXG4gICAgLnBsYXRmb3JtLWljb24uZmEtZG9ja2VyIHtcbiAgICAgIGNvbG9yOiAjMjQ5NmVkO1xuICAgIH1cblxuICAgIC5wbGF0Zm9ybS1pY29uLmZhLWplbmtpbnMge1xuICAgICAgY29sb3I6ICNkMzM4MzM7XG4gICAgfVxuXG4gICAgLmludGVncmF0aW9uLWNvbnRlbnQge1xuICAgICAgcGFkZGluZzogdmFyKC0tc3B0LXNwYWNlLTYpO1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgfVxuXG4gICAgLmludGVncmF0aW9uLWhlYWRlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIG1hcmdpbi1ib3R0b206IHZhcigtLXNwdC1zcGFjZS00KTtcbiAgICB9XG5cbiAgICAuaW50ZWdyYXRpb24taGVhZGVyIGg0IHtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtZ3JheS05MDApO1xuICAgICAgZm9udC1zaXplOiB2YXIoLS1zcHQtdGV4dC1sZyk7XG4gICAgICBmb250LXdlaWdodDogdmFyKC0tc3B0LWZvbnQtc2VtaWJvbGQpO1xuICAgIH1cblxuICAgIC5jb3B5LWNvbmZpZy1idG4ge1xuICAgICAgY29sb3I6IHZhcigtLXNwdC1wcmltYXJ5LTYwMCkgIWltcG9ydGFudDtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1wcmltYXJ5LTEwMCkgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXNwdC1yYWRpdXMtbWQpICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLmNvcHktY29uZmlnLWJ0bjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zcHQtcHJpbWFyeS0yMDApICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLmludGVncmF0aW9uLW5vdGVzIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICAgIGdhcDogdmFyKC0tc3B0LXNwYWNlLTMpO1xuICAgICAgbWFyZ2luLXRvcDogdmFyKC0tc3B0LXNwYWNlLTQpO1xuICAgICAgcGFkZGluZzogdmFyKC0tc3B0LXNwYWNlLTQpO1xuICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3B0LWluZm8tNTApO1xuICAgICAgYm9yZGVyLXJhZGl1czogdmFyKC0tc3B0LXJhZGl1cy1sZyk7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1zcHQtaW5mby0yMDApO1xuICAgIH1cblxuICAgIC5ub3Rlcy1pY29uIHtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtaW5mby02MDApO1xuICAgICAgbWFyZ2luLXRvcDogdmFyKC0tc3B0LXNwYWNlLTEpO1xuICAgIH1cblxuICAgIC5ub3Rlcy1jb250ZW50IHtcbiAgICAgIGZsZXg6IDE7XG4gICAgfVxuXG4gICAgLm5vdGVzLWNvbnRlbnQgc3Ryb25nIHtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtaW5mby04MDApO1xuICAgICAgZm9udC13ZWlnaHQ6IHZhcigtLXNwdC1mb250LXNlbWlib2xkKTtcbiAgICB9XG5cbiAgICAubm90ZXMtY29udGVudCBwIHtcbiAgICAgIG1hcmdpbjogdmFyKC0tc3B0LXNwYWNlLTEpIDAgMCAwO1xuICAgICAgY29sb3I6IHZhcigtLXNwdC1pbmZvLTcwMCk7XG4gICAgICBsaW5lLWhlaWdodDogMS41O1xuICAgIH1cblxuICAgIC5wbGF0Zm9ybS10aXBzIHtcbiAgICAgIG1hcmdpbi10b3A6IHZhcigtLXNwdC1zcGFjZS00KTtcbiAgICAgIHBhZGRpbmc6IHZhcigtLXNwdC1zcGFjZS00KTtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1zdWNjZXNzLTUwKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXNwdC1yYWRpdXMtbGcpO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tc3B0LXN1Y2Nlc3MtMjAwKTtcbiAgICB9XG5cbiAgICAucGxhdGZvcm0tdGlwcyBoNSB7XG4gICAgICBtYXJnaW46IDAgMCB2YXIoLS1zcHQtc3BhY2UtMykgMDtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtc3VjY2Vzcy04MDApO1xuICAgICAgZm9udC1zaXplOiB2YXIoLS1zcHQtdGV4dC1iYXNlKTtcbiAgICAgIGZvbnQtd2VpZ2h0OiB2YXIoLS1zcHQtZm9udC1zZW1pYm9sZCk7XG4gICAgfVxuXG4gICAgLnRpcHMtbGlzdCB7XG4gICAgICBsaXN0LXN0eWxlOiBub25lO1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgcGFkZGluZzogMDtcbiAgICB9XG5cbiAgICAudGlwcy1saXN0IGxpIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICAgIGdhcDogdmFyKC0tc3B0LXNwYWNlLTIpO1xuICAgICAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3B0LXNwYWNlLTIpO1xuICAgICAgY29sb3I6IHZhcigtLXNwdC1zdWNjZXNzLTcwMCk7XG4gICAgICBmb250LXNpemU6IHZhcigtLXNwdC10ZXh0LXNtKTtcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XG4gICAgfVxuXG4gICAgLnRpcC1pY29uIHtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtc3VjY2Vzcy02MDApO1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgd2lkdGg6IDE2cHg7XG4gICAgICBoZWlnaHQ6IDE2cHg7XG4gICAgICBtYXJnaW4tdG9wOiAycHg7XG4gICAgfVxuXG4gICAgLyogQmVzdCBQcmFjdGljZXMgU2VjdGlvbiAqL1xuICAgIC5iZXN0LXByYWN0aWNlcy1zZWN0aW9uIHtcbiAgICAgIG1hcmdpbi10b3A6IHZhcigtLXNwdC1zcGFjZS04KTtcbiAgICAgIG1hcmdpbi1ib3R0b206IHZhcigtLXNwdC1zcGFjZS04KTtcbiAgICB9XG5cbiAgICAuYmVzdC1wcmFjdGljZXMtc2VjdGlvbiBoMyB7XG4gICAgICBjb2xvcjogdmFyKC0tc3B0LWdyYXktOTAwKTtcbiAgICAgIGZvbnQtc2l6ZTogdmFyKC0tc3B0LXRleHQtMnhsKTtcbiAgICAgIGZvbnQtd2VpZ2h0OiB2YXIoLS1zcHQtZm9udC1ib2xkKTtcbiAgICAgIG1hcmdpbi1ib3R0b206IHZhcigtLXNwdC1zcGFjZS0yKTtcbiAgICB9XG5cbiAgICAuc2VjdGlvbi1kZXNjcmlwdGlvbiB7XG4gICAgICBjb2xvcjogdmFyKC0tc3B0LWdyYXktNjAwKTtcbiAgICAgIGZvbnQtc2l6ZTogdmFyKC0tc3B0LXRleHQtYmFzZSk7XG4gICAgICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcHQtc3BhY2UtNik7XG4gICAgICBsaW5lLWhlaWdodDogMS42O1xuICAgIH1cblxuICAgIC5wcmFjdGljZXMtYWNjb3JkaW9uIHtcbiAgICAgIGJveC1zaGFkb3c6IHZhcigtLXNwdC1zaGFkb3ctc20pO1xuICAgICAgYm9yZGVyLXJhZGl1czogdmFyKC0tc3B0LXJhZGl1cy14bCk7XG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tc3B0LWdyYXktMjAwKTtcbiAgICB9XG5cbiAgICAucHJhY3RpY2UtcGFuZWwge1xuICAgICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XG4gICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XG4gICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tc3B0LWdyYXktMjAwKSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5wcmFjdGljZS1wYW5lbDpsYXN0LWNoaWxkIHtcbiAgICAgIGJvcmRlci1ib3R0b206IG5vbmUgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAucHJhY3RpY2UtcGFuZWwgLm1hdC1leHBhbnNpb24tcGFuZWwtaGVhZGVyIHtcbiAgICAgIHBhZGRpbmc6IHZhcigtLXNwdC1zcGFjZS00KSB2YXIoLS1zcHQtc3BhY2UtNikgIWltcG9ydGFudDtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNwdC1ncmF5LTUwKSAhaW1wb3J0YW50O1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5wcmFjdGljZS1wYW5lbCAubWF0LWV4cGFuc2lvbi1wYW5lbC1oZWFkZXI6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3B0LXNlY29uZGFyeS01MCkgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAucHJhY3RpY2UtcGFuZWwubWF0LWV4cGFuZGVkIC5tYXQtZXhwYW5zaW9uLXBhbmVsLWhlYWRlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zcHQtc2Vjb25kYXJ5LTEwMCkgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1zcHQtc2Vjb25kYXJ5LTIwMCkgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAucHJhY3RpY2UtaWNvbiB7XG4gICAgICBtYXJnaW4tcmlnaHQ6IHZhcigtLXNwdC1zcGFjZS0zKTtcbiAgICB9XG5cbiAgICAucHJhY3RpY2UtY29udGVudCB7XG4gICAgICBwYWRkaW5nOiB2YXIoLS1zcHQtc3BhY2UtNik7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICB9XG5cbiAgICAucHJhY3RpY2UtdGlwcyB7XG4gICAgICBsaXN0LXN0eWxlOiBub25lO1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgcGFkZGluZzogMDtcbiAgICB9XG5cbiAgICAucHJhY3RpY2UtdGlwIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICAgIGdhcDogdmFyKC0tc3B0LXNwYWNlLTIpO1xuICAgICAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3B0LXNwYWNlLTMpO1xuICAgICAgY29sb3I6IHZhcigtLXNwdC1ncmF5LTcwMCk7XG4gICAgICBmb250LXNpemU6IHZhcigtLXNwdC10ZXh0LXNtKTtcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7XG4gICAgfVxuXG4gICAgLnRpcC1jaGVjay1pY29uIHtcbiAgICAgIGNvbG9yOiB2YXIoLS1zcHQtc3VjY2Vzcy02MDApO1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgd2lkdGg6IDE2cHg7XG4gICAgICBoZWlnaHQ6IDE2cHg7XG4gICAgICBtYXJnaW4tdG9wOiAycHg7XG4gICAgfVxuXG4gICAgLmNvbmZpZy1jYXJkIHtcbiAgICAgIG1hcmdpbi10b3A6IDI0cHg7XG4gICAgfVxuXG4gICAgLmNvbmZpZy1kZXNjcmlwdGlvbiB7XG4gICAgICBtYXJnaW4tdG9wOiAyNHB4O1xuICAgIH1cblxuICAgIC5jb25maWctZGVzY3JpcHRpb24gaDQge1xuICAgICAgbWFyZ2luOiAwIDAgMTZweCAwO1xuICAgICAgY29sb3I6ICMxOTc2ZDI7XG4gICAgfVxuXG4gICAgLmNvbmZpZy1vcHRpb25zIHtcbiAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcbiAgICAgIGdhcDogMTZweDtcbiAgICB9XG5cbiAgICAuY29uZmlnLW9wdGlvbiB7XG4gICAgICBwYWRkaW5nOiAxMnB4O1xuICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICB9XG5cbiAgICAuY29uZmlnLW9wdGlvbiBzdHJvbmcge1xuICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgICBjb2xvcjogIzE5NzZkMjtcbiAgICB9XG5cbiAgICAuY29uZmlnLW9wdGlvbiBwIHtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgZm9udC1zaXplOiAwLjllbTtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIC5jbGktZmVhdHVyZXMge1xuICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICAgIH1cblxuICAgICAgLmluc3RhbGxhdGlvbi1tZXRob2RzIHtcbiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgICB9XG5cbiAgICAgIC5pbnRlZ3JhdGlvbi1jb250ZW50IHtcbiAgICAgICAgcGFkZGluZzogdmFyKC0tc3B0LXNwYWNlLTQpO1xuICAgICAgfVxuXG4gICAgICAuaW50ZWdyYXRpb24taGVhZGVyIHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgICAgIGdhcDogdmFyKC0tc3B0LXNwYWNlLTIpO1xuICAgICAgfVxuXG4gICAgICAuaW50ZWdyYXRpb24taGVhZGVyIGg0IHtcbiAgICAgICAgZm9udC1zaXplOiB2YXIoLS1zcHQtdGV4dC1iYXNlKTtcbiAgICAgIH1cblxuICAgICAgLnBsYXRmb3JtLXRpcHMsXG4gICAgICAuaW50ZWdyYXRpb24tbm90ZXMge1xuICAgICAgICBwYWRkaW5nOiB2YXIoLS1zcHQtc3BhY2UtMyk7XG4gICAgICB9XG5cbiAgICAgIC5jb25maWctb3B0aW9ucyB7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgICAgfVxuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatTabsModule", "MatCardModule", "MatIconModule", "MatExpansionModule", "MatChipsModule", "MatTableModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "feature_r1", "color", "ɵɵtextInterpolate", "icon", "title", "description", "method_r2", "notes", "ɵɵtemplate", "CliGuideComponent_mat_card_28_div_18_Template", "commands", "ɵɵproperty", "option_r3", "flag", "option_r4", "type", "option_r5", "option_r6", "default", "CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template", "ɵɵelement", "ɵɵelementContainerStart", "CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template", "command_r7", "options", "ctx_r7", "optionColumns", "example_r9", "output", "CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template", "command", "CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template", "examples", "CliGuideComponent_mat_expansion_panel_36_div_15_Template", "CliGuideComponent_mat_expansion_panel_36_div_16_Template", "CliGuideComponent_mat_expansion_panel_36_div_17_Template", "ɵɵtextInterpolate1", "name", "usage", "length", "integration_r11", "tip_r12", "CliGuideComponent_mat_expansion_panel_45_div_27_li_4_Template", "getPlatformTips", "platform", "ɵɵlistener", "CliGuideComponent_mat_expansion_panel_45_Template_button_click_11_listener", "ɵɵrestoreView", "_r10", "$implicit", "ɵɵnextContext", "ɵɵresetView", "copyToClipboard", "config", "CliGuideComponent_mat_expansion_panel_45_Template_button_click_20_listener", "CliGuideComponent_mat_expansion_panel_45_div_26_Template", "CliGuideComponent_mat_expansion_panel_45_div_27_Template", "i_r13", "ɵɵclassMapInterpolate1", "filename", "tip_r14", "CliGuideComponent_mat_expansion_panel_52_li_10_Template", "practice_r15", "tips", "CliGuideComponent_div_66_Template_button_click_9_listener", "env_r17", "_r16", "option_r18", "key", "CliGuideComponent", "constructor", "cliFeatures", "installationMethods", "cliCommands", "integrationExamples", "cicdBestPractices", "environmentConfigs", "config<PERSON><PERSON><PERSON>", "configOptions", "platformTips", "text", "navigator", "clipboard", "writeText", "then", "console", "log", "catch", "err", "error", "selectors", "decls", "vars", "consts", "template", "CliGuideComponent_Template", "rf", "ctx", "CliGuideComponent_div_21_Template", "CliGuideComponent_mat_card_28_Template", "CliGuideComponent_mat_expansion_panel_36_Template", "CliGuideComponent_mat_expansion_panel_45_Template", "CliGuideComponent_mat_expansion_panel_52_Template", "CliGuideComponent_div_66_Template", "CliGuideComponent_div_95_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "Mat<PERSON><PERSON>", "MatTabGroup", "i3", "MatCard", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i4", "MatIcon", "i5", "Mat<PERSON><PERSON>rdi<PERSON>", "MatExpansionPanel", "MatExpansionPanelHeader", "MatExpansionPanelTitle", "MatExpansionPanelDescription", "i6", "MatChip", "i7", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\cli-guide\\cli-guide.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\n\ninterface CliCommand {\n  name: string;\n  description: string;\n  usage: string;\n  options: CliOption[];\n  examples: CliExample[];\n  notes?: string;\n}\n\ninterface CliOption {\n  flag: string;\n  description: string;\n  type: string;\n  default?: string;\n  required?: boolean;\n}\n\ninterface CliExample {\n  command: string;\n  description: string;\n  output?: string;\n}\n\n@Component({\n  selector: 'app-cli-guide',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatTabsModule,\n    MatCardModule,\n    MatIconModule,\n    MatExpansionModule,\n    MatChipsModule,\n    MatTableModule\n  ],\n  template: `\n    <div class=\"cli-guide-container\">\n      <div class=\"page-header\">\n        <h1>\n          <mat-icon>terminal</mat-icon>\n          CLI Guide\n        </h1>\n        <p class=\"page-subtitle\">\n          Complete guide to the SPT command-line interface\n        </p>\n      </div>\n\n      <div class=\"cli-overview\">\n        <mat-card class=\"overview-card\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>info</mat-icon>\n            <mat-card-title>SPT CLI Overview</mat-card-title>\n            <mat-card-subtitle>Powerful command-line security scanning tool</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <p>The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.</p>\n            <div class=\"cli-features\">\n              <div class=\"feature\" *ngFor=\"let feature of cliFeatures\">\n                <mat-icon [style.color]=\"feature.color\">{{ feature.icon }}</mat-icon>\n                <div>\n                  <strong>{{ feature.title }}</strong>\n                  <p>{{ feature.description }}</p>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <mat-tab-group class=\"cli-tabs\" animationDuration=\"300ms\">\n        <!-- Installation Tab -->\n        <mat-tab label=\"Installation\">\n          <div class=\"tab-content\">\n            <h2>Installation Methods</h2>\n            \n            <div class=\"installation-methods\">\n              <mat-card class=\"method-card\" *ngFor=\"let method of installationMethods\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>{{ method.icon }}</mat-icon>\n                  <mat-card-title>{{ method.title }}</mat-card-title>\n                  <mat-card-subtitle>{{ method.description }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>terminal</mat-icon>\n                      <span>Commands</span>\n                    </div>\n                    <pre><code>{{ method.commands }}</code></pre>\n                  </div>\n                  <div class=\"method-notes\" *ngIf=\"method.notes\">\n                    <mat-icon>info</mat-icon>\n                    <span>{{ method.notes }}</span>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Commands Tab -->\n        <mat-tab label=\"Commands\">\n          <div class=\"tab-content\">\n            <h2>Available Commands</h2>\n            <p>Complete reference for all SPT CLI commands and their options.</p>\n            \n            <div class=\"commands-list\">\n              <mat-expansion-panel \n                *ngFor=\"let command of cliCommands\" \n                class=\"command-panel\">\n                <mat-expansion-panel-header>\n                  <mat-panel-title>\n                    <code class=\"command-name\">spt {{ command.name }}</code>\n                  </mat-panel-title>\n                  <mat-panel-description>\n                    {{ command.description }}\n                  </mat-panel-description>\n                </mat-expansion-panel-header>\n\n                <div class=\"command-details\">\n                  <div class=\"usage-section\">\n                    <h4>Usage</h4>\n                    <div class=\"code-block\">\n                      <pre><code>{{ command.usage }}</code></pre>\n                    </div>\n                  </div>\n\n                  <div class=\"options-section\" *ngIf=\"command.options.length > 0\">\n                    <h4>Options</h4>\n                    <table mat-table [dataSource]=\"command.options\" class=\"options-table\">\n                      <ng-container matColumnDef=\"flag\">\n                        <th mat-header-cell *matHeaderCellDef>Flag</th>\n                        <td mat-cell *matCellDef=\"let option\">\n                          <code>{{ option.flag }}</code>\n                        </td>\n                      </ng-container>\n                      <ng-container matColumnDef=\"type\">\n                        <th mat-header-cell *matHeaderCellDef>Type</th>\n                        <td mat-cell *matCellDef=\"let option\">\n                          <mat-chip>{{ option.type }}</mat-chip>\n                        </td>\n                      </ng-container>\n                      <ng-container matColumnDef=\"description\">\n                        <th mat-header-cell *matHeaderCellDef>Description</th>\n                        <td mat-cell *matCellDef=\"let option\">{{ option.description }}</td>\n                      </ng-container>\n                      <ng-container matColumnDef=\"default\">\n                        <th mat-header-cell *matHeaderCellDef>Default</th>\n                        <td mat-cell *matCellDef=\"let option\">\n                          <code *ngIf=\"option.default\">{{ option.default }}</code>\n                          <span *ngIf=\"!option.default\">-</span>\n                        </td>\n                      </ng-container>\n                      <tr mat-header-row *matHeaderRowDef=\"optionColumns\"></tr>\n                      <tr mat-row *matRowDef=\"let row; columns: optionColumns;\"></tr>\n                    </table>\n                  </div>\n\n                  <div class=\"examples-section\" *ngIf=\"command.examples.length > 0\">\n                    <h4>Examples</h4>\n                    <div class=\"example\" *ngFor=\"let example of command.examples\">\n                      <p class=\"example-description\">{{ example.description }}</p>\n                      <div class=\"code-block\">\n                        <div class=\"code-header\">\n                          <mat-icon>terminal</mat-icon>\n                          <span>Command</span>\n                        </div>\n                        <pre><code>{{ example.command }}</code></pre>\n                      </div>\n                      <div class=\"output-block\" *ngIf=\"example.output\">\n                        <div class=\"code-header\">\n                          <mat-icon>output</mat-icon>\n                          <span>Output</span>\n                        </div>\n                        <pre><code>{{ example.output }}</code></pre>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div class=\"notes-section\" *ngIf=\"command.notes\">\n                    <h4>Notes</h4>\n                    <div class=\"notes-content\">\n                      <mat-icon>info</mat-icon>\n                      <p>{{ command.notes }}</p>\n                    </div>\n                  </div>\n                </div>\n              </mat-expansion-panel>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Integration Tab -->\n        <mat-tab label=\"CI/CD Integration\">\n          <div class=\"tab-content\">\n            <h2>CI/CD Integration</h2>\n            <p>Integrate SPT CLI into your continuous integration and deployment pipelines.</p>\n            \n            <div class=\"integration-examples\">\n              <mat-accordion class=\"integration-accordion\">\n                <mat-expansion-panel\n                  *ngFor=\"let integration of integrationExamples; let i = index\"\n                  class=\"integration-panel\"\n                  [expanded]=\"i === 0\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <i class=\"platform-icon {{ integration.icon }}\" aria-hidden=\"true\"></i>\n                      {{ integration.platform }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      {{ integration.description }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n\n                  <div class=\"integration-content\">\n                    <div class=\"integration-header\">\n                      <h4>Configuration File: {{ integration.filename }}</h4>\n                      <button mat-icon-button class=\"copy-config-btn\"\n                              (click)=\"copyToClipboard(integration.config)\"\n                              matTooltip=\"Copy configuration\">\n                        <mat-icon>content_copy</mat-icon>\n                      </button>\n                    </div>\n\n                    <div class=\"code-block\">\n                      <div class=\"code-header\">\n                        <mat-icon>code</mat-icon>\n                        <span>{{ integration.filename }}</span>\n                        <button mat-icon-button class=\"copy-btn\"\n                                (click)=\"copyToClipboard(integration.config)\"\n                                matTooltip=\"Copy to clipboard\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre><code>{{ integration.config }}</code></pre>\n                    </div>\n\n                    <div class=\"integration-notes\" *ngIf=\"integration.notes\">\n                      <mat-icon class=\"notes-icon\">lightbulb</mat-icon>\n                      <div class=\"notes-content\">\n                        <strong>Implementation Notes:</strong>\n                        <p>{{ integration.notes }}</p>\n                      </div>\n                    </div>\n\n                    <!-- Platform-specific quick tips -->\n                    <div class=\"platform-tips\" *ngIf=\"getPlatformTips(integration.platform).length > 0\">\n                      <h5>Quick Tips:</h5>\n                      <ul class=\"tips-list\">\n                        <li *ngFor=\"let tip of getPlatformTips(integration.platform)\">\n                          <mat-icon class=\"tip-icon\">check_circle</mat-icon>\n                          <span>{{ tip }}</span>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n\n            <!-- CI/CD Best Practices Section -->\n            <div class=\"best-practices-section\">\n              <h3>CI/CD Best Practices</h3>\n              <p class=\"section-description\">Essential practices for implementing SPT in your CI/CD pipeline effectively.</p>\n\n              <mat-accordion class=\"practices-accordion\">\n                <mat-expansion-panel\n                  *ngFor=\"let practice of cicdBestPractices; let i = index\"\n                  class=\"practice-panel\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <mat-icon class=\"practice-icon\" [style.color]=\"practice.color\">{{ practice.icon }}</mat-icon>\n                      {{ practice.title }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      {{ practice.description }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n\n                  <div class=\"practice-content\">\n                    <ul class=\"practice-tips\">\n                      <li *ngFor=\"let tip of practice.tips\" class=\"practice-tip\">\n                        <mat-icon class=\"tip-check-icon\">check_circle</mat-icon>\n                        <span>{{ tip }}</span>\n                      </li>\n                    </ul>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n\n            <!-- Environment-Specific Configurations -->\n            <div class=\"environment-configs\">\n              <h3>Environment-Specific Configurations</h3>\n              <mat-card class=\"env-config-card\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>settings_applications</mat-icon>\n                  <mat-card-title>Environment Variables</mat-card-title>\n                  <mat-card-subtitle>Configure SPT for different environments</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"env-examples\">\n                    <div class=\"env-example\" *ngFor=\"let env of environmentConfigs\">\n                      <h4>{{ env.name }}</h4>\n                      <div class=\"code-block\">\n                        <div class=\"code-header\">\n                          <mat-icon>terminal</mat-icon>\n                          <span>{{ env.type }}</span>\n                          <button mat-icon-button class=\"copy-btn\"\n                                  (click)=\"copyToClipboard(env.config)\"\n                                  matTooltip=\"Copy configuration\">\n                            <mat-icon>content_copy</mat-icon>\n                          </button>\n                        </div>\n                        <pre><code>{{ env.config }}</code></pre>\n                      </div>\n                      <p class=\"env-description\">{{ env.description }}</p>\n                    </div>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Configuration Tab -->\n        <mat-tab label=\"Configuration\">\n          <div class=\"tab-content\">\n            <h2>Configuration</h2>\n            <p>Configure SPT CLI for your development environment and preferences.</p>\n            \n            <mat-card class=\"config-card\">\n              <mat-card-header>\n                <mat-icon mat-card-avatar>settings</mat-icon>\n                <mat-card-title>Configuration File</mat-card-title>\n                <mat-card-subtitle>spt.config.json</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"code-block\">\n                  <div class=\"code-header\">\n                    <mat-icon>code</mat-icon>\n                    <span>JSON Configuration</span>\n                  </div>\n                  <pre><code>{{ configExample }}</code></pre>\n                </div>\n                <div class=\"config-description\">\n                  <h4>Configuration Options</h4>\n                  <div class=\"config-options\">\n                    <div class=\"config-option\" *ngFor=\"let option of configOptions\">\n                      <strong>{{ option.key }}</strong>\n                      <p>{{ option.description }}</p>\n                    </div>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styles: [`\n    .cli-guide-container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .page-header {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .page-header h1 {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      color: #1976d2;\n      margin: 0 0 8px 0;\n    }\n\n    .page-subtitle {\n      color: #666;\n      font-size: 1.1em;\n      margin: 0;\n    }\n\n    .cli-overview {\n      margin-bottom: 32px;\n    }\n\n    .overview-card {\n      border: 1px solid #e0e0e0;\n    }\n\n    .cli-features {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 16px;\n      margin-top: 16px;\n    }\n\n    .feature {\n      display: flex;\n      align-items: flex-start;\n      gap: 12px;\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .feature mat-icon {\n      margin-top: 2px;\n    }\n\n    .feature strong {\n      display: block;\n      margin-bottom: 4px;\n    }\n\n    .feature p {\n      margin: 0;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    .cli-tabs {\n      margin-bottom: 32px;\n    }\n\n    .tab-content {\n      padding: 24px 0;\n    }\n\n    .tab-content h2 {\n      color: #1976d2;\n      margin-bottom: 8px;\n    }\n\n    .installation-methods {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .method-card {\n      height: fit-content;\n    }\n\n    .method-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #e3f2fd;\n      border-radius: 4px;\n      color: #1976d2;\n    }\n\n    .commands-list {\n      margin-top: 24px;\n    }\n\n    .command-panel {\n      margin-bottom: 8px;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .command-name {\n      background: #f5f5f5;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-family: 'Courier New', monospace;\n      font-weight: 500;\n    }\n\n    .command-details {\n      padding: 16px 0;\n    }\n\n    .usage-section,\n    .options-section,\n    .examples-section,\n    .notes-section {\n      margin-bottom: 24px;\n    }\n\n    .usage-section h4,\n    .options-section h4,\n    .examples-section h4,\n    .notes-section h4 {\n      margin: 0 0 12px 0;\n      color: #1976d2;\n    }\n\n    .code-block {\n      border: 1px solid var(--spt-gray-200);\n      border-radius: var(--spt-radius-xl);\n      overflow: hidden;\n      margin-bottom: var(--spt-space-4);\n      box-shadow: var(--spt-shadow-sm);\n      background: white;\n    }\n\n    .code-header {\n      background: var(--spt-gray-50);\n      padding: var(--spt-space-3) var(--spt-space-4);\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n      font-weight: var(--spt-font-medium);\n      border-bottom: 1px solid var(--spt-gray-200);\n      justify-content: space-between;\n    }\n\n    .code-header mat-icon {\n      color: var(--spt-primary-600);\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .code-header span {\n      color: var(--spt-gray-700);\n      font-size: var(--spt-text-sm);\n      font-weight: var(--spt-font-medium);\n      flex: 1;\n    }\n\n    .copy-btn {\n      color: var(--spt-primary-600) !important;\n      background: var(--spt-primary-100) !important;\n      border-radius: var(--spt-radius-md) !important;\n      transition: all 0.2s ease !important;\n    }\n\n    .copy-btn:hover {\n      background: var(--spt-primary-200) !important;\n      transform: scale(1.05);\n    }\n\n    .code-block pre {\n      margin: 0;\n      padding: var(--spt-space-4);\n      background: var(--spt-gray-50);\n      overflow-x: auto;\n      font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;\n      font-size: var(--spt-text-sm);\n      line-height: 1.5;\n      color: var(--spt-gray-800);\n    }\n\n    .code-block code {\n      font-family: inherit;\n      font-size: inherit;\n      color: inherit;\n    }\n\n    .output-block {\n      border: 1px solid #4caf50;\n      border-radius: 8px;\n      overflow: hidden;\n      margin-top: 8px;\n    }\n\n    .output-block .code-header {\n      background: #e8f5e8;\n      border-bottom-color: #4caf50;\n    }\n\n    .output-block pre {\n      background: #f1f8e9;\n    }\n\n    .options-table {\n      width: 100%;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .options-table code {\n      background: #f5f5f5;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-size: 0.9em;\n    }\n\n    .example {\n      margin-bottom: 24px;\n      padding: 16px;\n      background: #f9f9f9;\n      border-radius: 8px;\n    }\n\n    .example-description {\n      margin: 0 0 12px 0;\n      font-weight: 500;\n      color: #1976d2;\n    }\n\n    .notes-content {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n      padding: 12px;\n      background: #e3f2fd;\n      border-radius: 8px;\n      color: #1976d2;\n    }\n\n    .notes-content p {\n      margin: 0;\n    }\n\n    .integration-examples {\n      margin-top: var(--spt-space-6);\n    }\n\n    .integration-accordion {\n      box-shadow: var(--spt-shadow-sm);\n      border-radius: var(--spt-radius-xl);\n      overflow: hidden;\n      border: 1px solid var(--spt-gray-200);\n    }\n\n    .integration-panel {\n      border: none !important;\n      box-shadow: none !important;\n      border-bottom: 1px solid var(--spt-gray-200) !important;\n    }\n\n    .integration-panel:last-child {\n      border-bottom: none !important;\n    }\n\n    .integration-panel .mat-expansion-panel-header {\n      padding: var(--spt-space-4) var(--spt-space-6) !important;\n      background: var(--spt-gray-50) !important;\n      transition: all 0.2s ease !important;\n    }\n\n    .integration-panel .mat-expansion-panel-header:hover {\n      background: var(--spt-primary-50) !important;\n    }\n\n    .integration-panel.mat-expanded .mat-expansion-panel-header {\n      background: var(--spt-primary-100) !important;\n      border-bottom: 1px solid var(--spt-primary-200) !important;\n    }\n\n    .platform-icon {\n      color: var(--spt-primary-600);\n      margin-right: var(--spt-space-3);\n      font-size: 18px;\n      width: 18px;\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    /* Platform-specific icon colors */\n    .platform-icon.fa-github {\n      color: #24292e;\n    }\n\n    .platform-icon.fa-gitlab {\n      color: #fc6d26;\n    }\n\n    .platform-icon.fa-microsoft {\n      color: #0078d4;\n    }\n\n    .platform-icon.fa-aws {\n      color: #ff9900;\n    }\n\n    .platform-icon.fa-docker {\n      color: #2496ed;\n    }\n\n    .platform-icon.fa-jenkins {\n      color: #d33833;\n    }\n\n    .integration-content {\n      padding: var(--spt-space-6);\n      background: white;\n    }\n\n    .integration-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: var(--spt-space-4);\n    }\n\n    .integration-header h4 {\n      margin: 0;\n      color: var(--spt-gray-900);\n      font-size: var(--spt-text-lg);\n      font-weight: var(--spt-font-semibold);\n    }\n\n    .copy-config-btn {\n      color: var(--spt-primary-600) !important;\n      background: var(--spt-primary-100) !important;\n      border-radius: var(--spt-radius-md) !important;\n    }\n\n    .copy-config-btn:hover {\n      background: var(--spt-primary-200) !important;\n    }\n\n    .integration-notes {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spt-space-3);\n      margin-top: var(--spt-space-4);\n      padding: var(--spt-space-4);\n      background: var(--spt-info-50);\n      border-radius: var(--spt-radius-lg);\n      border: 1px solid var(--spt-info-200);\n    }\n\n    .notes-icon {\n      color: var(--spt-info-600);\n      margin-top: var(--spt-space-1);\n    }\n\n    .notes-content {\n      flex: 1;\n    }\n\n    .notes-content strong {\n      color: var(--spt-info-800);\n      font-weight: var(--spt-font-semibold);\n    }\n\n    .notes-content p {\n      margin: var(--spt-space-1) 0 0 0;\n      color: var(--spt-info-700);\n      line-height: 1.5;\n    }\n\n    .platform-tips {\n      margin-top: var(--spt-space-4);\n      padding: var(--spt-space-4);\n      background: var(--spt-success-50);\n      border-radius: var(--spt-radius-lg);\n      border: 1px solid var(--spt-success-200);\n    }\n\n    .platform-tips h5 {\n      margin: 0 0 var(--spt-space-3) 0;\n      color: var(--spt-success-800);\n      font-size: var(--spt-text-base);\n      font-weight: var(--spt-font-semibold);\n    }\n\n    .tips-list {\n      list-style: none;\n      margin: 0;\n      padding: 0;\n    }\n\n    .tips-list li {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spt-space-2);\n      margin-bottom: var(--spt-space-2);\n      color: var(--spt-success-700);\n      font-size: var(--spt-text-sm);\n      line-height: 1.4;\n    }\n\n    .tip-icon {\n      color: var(--spt-success-600);\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n      margin-top: 2px;\n    }\n\n    /* Best Practices Section */\n    .best-practices-section {\n      margin-top: var(--spt-space-8);\n      margin-bottom: var(--spt-space-8);\n    }\n\n    .best-practices-section h3 {\n      color: var(--spt-gray-900);\n      font-size: var(--spt-text-2xl);\n      font-weight: var(--spt-font-bold);\n      margin-bottom: var(--spt-space-2);\n    }\n\n    .section-description {\n      color: var(--spt-gray-600);\n      font-size: var(--spt-text-base);\n      margin-bottom: var(--spt-space-6);\n      line-height: 1.6;\n    }\n\n    .practices-accordion {\n      box-shadow: var(--spt-shadow-sm);\n      border-radius: var(--spt-radius-xl);\n      overflow: hidden;\n      border: 1px solid var(--spt-gray-200);\n    }\n\n    .practice-panel {\n      border: none !important;\n      box-shadow: none !important;\n      border-bottom: 1px solid var(--spt-gray-200) !important;\n    }\n\n    .practice-panel:last-child {\n      border-bottom: none !important;\n    }\n\n    .practice-panel .mat-expansion-panel-header {\n      padding: var(--spt-space-4) var(--spt-space-6) !important;\n      background: var(--spt-gray-50) !important;\n      transition: all 0.2s ease !important;\n    }\n\n    .practice-panel .mat-expansion-panel-header:hover {\n      background: var(--spt-secondary-50) !important;\n    }\n\n    .practice-panel.mat-expanded .mat-expansion-panel-header {\n      background: var(--spt-secondary-100) !important;\n      border-bottom: 1px solid var(--spt-secondary-200) !important;\n    }\n\n    .practice-icon {\n      margin-right: var(--spt-space-3);\n    }\n\n    .practice-content {\n      padding: var(--spt-space-6);\n      background: white;\n    }\n\n    .practice-tips {\n      list-style: none;\n      margin: 0;\n      padding: 0;\n    }\n\n    .practice-tip {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spt-space-2);\n      margin-bottom: var(--spt-space-3);\n      color: var(--spt-gray-700);\n      font-size: var(--spt-text-sm);\n      line-height: 1.5;\n    }\n\n    .tip-check-icon {\n      color: var(--spt-success-600);\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n      margin-top: 2px;\n    }\n\n    .config-card {\n      margin-top: 24px;\n    }\n\n    .config-description {\n      margin-top: 24px;\n    }\n\n    .config-description h4 {\n      margin: 0 0 16px 0;\n      color: #1976d2;\n    }\n\n    .config-options {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 16px;\n    }\n\n    .config-option {\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .config-option strong {\n      display: block;\n      margin-bottom: 4px;\n      color: #1976d2;\n    }\n\n    .config-option p {\n      margin: 0;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    @media (max-width: 768px) {\n      .cli-features {\n        grid-template-columns: 1fr;\n      }\n\n      .installation-methods {\n        grid-template-columns: 1fr;\n      }\n\n      .integration-content {\n        padding: var(--spt-space-4);\n      }\n\n      .integration-header {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: var(--spt-space-2);\n      }\n\n      .integration-header h4 {\n        font-size: var(--spt-text-base);\n      }\n\n      .platform-tips,\n      .integration-notes {\n        padding: var(--spt-space-3);\n      }\n\n      .config-options {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class CliGuideComponent {\n  optionColumns: string[] = ['flag', 'type', 'description', 'default'];\n\n  cliFeatures = [\n    {\n      title: 'Security Scanning',\n      description: 'Comprehensive security analysis for blockchain applications',\n      icon: 'security',\n      color: '#1976d2'\n    },\n    {\n      title: 'Multiple Formats',\n      description: 'Output results in JSON, YAML, CSV, or human-readable formats',\n      icon: 'description',\n      color: '#4caf50'\n    },\n    {\n      title: 'CI/CD Integration',\n      description: 'Easy integration with continuous integration pipelines',\n      icon: 'integration_instructions',\n      color: '#ff9800'\n    },\n    {\n      title: 'Configurable',\n      description: 'Flexible configuration options for different environments',\n      icon: 'tune',\n      color: '#9c27b0'\n    }\n  ];\n\n  installationMethods = [\n    {\n      title: 'From Source',\n      description: 'Build from source code',\n      icon: 'code',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\ngo build -o spt cmd/main.go`,\n      notes: 'Requires Go 1.21+ to be installed'\n    },\n    {\n      title: 'Using Make',\n      description: 'Build using Makefile',\n      icon: 'build',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\nmake cli`,\n      notes: 'Binary will be created in build/ directory'\n    },\n    {\n      title: 'Go Install',\n      description: 'Install directly with Go',\n      icon: 'download',\n      commands: `go install github.com/blockchain-spt/cmd/spt@latest`,\n      notes: 'Installs to $GOPATH/bin'\n    }\n  ];\n\n  cliCommands: CliCommand[] = [\n    {\n      name: 'scan',\n      description: 'Perform security scan on files or directories',\n      usage: 'spt scan [flags] [path]',\n      options: [\n        { flag: '--chain', type: 'string', description: 'Blockchain chain to analyze', default: 'all' },\n        { flag: '--format', type: 'string', description: 'Output format (json, yaml, csv, table)', default: 'table' },\n        { flag: '--output', type: 'string', description: 'Output file path' },\n        { flag: '--severity', type: 'string', description: 'Minimum severity level', default: 'medium' },\n        { flag: '--recursive', type: 'boolean', description: 'Scan directories recursively', default: 'true' }\n      ],\n      examples: [\n        {\n          command: 'spt scan ./contracts',\n          description: 'Scan all files in contracts directory',\n          output: `Scanning ./contracts...\nFound 3 issues:\n  HIGH: Potential reentrancy in contract.sol:42\n  MEDIUM: Unchecked return value in token.sol:15\n  MEDIUM: Gas optimization opportunity in utils.sol:8`\n        },\n        {\n          command: 'spt scan --chain ethereum --format json ./src',\n          description: 'Scan for Ethereum-specific issues and output as JSON'\n        }\n      ],\n      notes: 'Use --help flag with any command to see detailed usage information'\n    },\n    {\n      name: 'audit',\n      description: 'Perform comprehensive security audit',\n      usage: 'spt audit [flags] [path]',\n      options: [\n        { flag: '--generate-report', type: 'boolean', description: 'Generate detailed report', default: 'false' },\n        { flag: '--report-path', type: 'string', description: 'Report output path', default: './audit-report.html' },\n        { flag: '--template', type: 'string', description: 'Report template', default: 'standard' }\n      ],\n      examples: [\n        {\n          command: 'spt audit --generate-report ./project',\n          description: 'Perform audit and generate HTML report'\n        }\n      ]\n    },\n    {\n      name: 'check',\n      description: 'Run specific security checks',\n      usage: 'spt check [subcommand] [flags] [path]',\n      options: [\n        { flag: '--fix', type: 'boolean', description: 'Attempt to fix issues automatically', default: 'false' }\n      ],\n      examples: [\n        {\n          command: 'spt check deps --fix',\n          description: 'Check dependencies and fix known vulnerabilities'\n        },\n        {\n          command: 'spt check env',\n          description: 'Check environment configuration for security issues'\n        }\n      ]\n    }\n  ];\n\n  integrationExamples = [\n    {\n      platform: 'GitHub Actions',\n      description: 'Integrate SPT into GitHub Actions workflow',\n      icon: 'fab fa-github',\n      filename: '.github/workflows/security.yml',\n      config: `name: Security Scan\non: [push, pull_request]\n\njobs:\n  security:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - uses: actions/setup-go@v3\n        with:\n          go-version: '1.21'\n      - name: Install SPT\n        run: go install github.com/blockchain-spt/cmd/spt@latest\n      - name: Run Security Scan\n        run: spt scan --format json --output security-report.json ./\n      - name: Upload Results\n        uses: actions/upload-artifact@v3\n        with:\n          name: security-report\n          path: security-report.json`,\n      notes: 'Add GITHUB_TOKEN to secrets for private repositories'\n    },\n    {\n      platform: 'GitLab CI',\n      description: 'Integrate SPT into GitLab CI/CD pipeline',\n      icon: 'fab fa-gitlab',\n      filename: '.gitlab-ci.yml',\n      config: `security_scan:\n  stage: test\n  image: golang:1.21\n  script:\n    - go install github.com/blockchain-spt/cmd/spt@latest\n    - spt scan --format json --output security-report.json ./\n    - spt audit --generate-report --report-path audit-report.html ./\n  artifacts:\n    reports:\n      junit: security-report.json\n    paths:\n      - audit-report.html\n    expire_in: 1 week\n  only:\n    - merge_requests\n    - main`,\n      notes: 'Configure artifact storage for report persistence'\n    },\n    {\n      platform: 'Azure DevOps',\n      description: 'Integrate SPT into Azure DevOps Pipeline',\n      icon: 'fab fa-microsoft',\n      filename: 'azure-pipelines.yml',\n      config: `trigger:\n  branches:\n    include:\n      - main\n      - develop\n  paths:\n    include:\n      - contracts/*\n      - src/*\n\npool:\n  vmImage: 'ubuntu-latest'\n\nvariables:\n  GO_VERSION: '1.21'\n  SPT_VERSION: 'latest'\n\nstages:\n- stage: SecurityScan\n  displayName: 'Security Analysis'\n  jobs:\n  - job: SPTScan\n    displayName: 'SPT Security Scan'\n    steps:\n    - task: GoTool@0\n      displayName: 'Install Go'\n      inputs:\n        version: '\\$(GO_VERSION)'\n\n    - script: |\n        go install github.com/blockchain-spt/cmd/spt@\\$(SPT_VERSION)\n        echo \"SPT installed successfully\"\n      displayName: 'Install SPT CLI'\n\n    - script: |\n        spt scan --format json --output \\$(Agent.TempDirectory)/security-report.json ./\n        spt audit --generate-report --report-path \\$(Agent.TempDirectory)/audit-report.html ./\n      displayName: 'Run Security Scan'\n      continueOnError: true\n\n    - task: PublishTestResults@2\n      displayName: 'Publish Security Results'\n      inputs:\n        testResultsFormat: 'JUnit'\n        testResultsFiles: '\\$(Agent.TempDirectory)/security-report.json'\n        testRunTitle: 'SPT Security Scan Results'\n      condition: always()\n\n    - task: PublishBuildArtifacts@1\n      displayName: 'Publish Security Reports'\n      inputs:\n        pathToPublish: '\\$(Agent.TempDirectory)'\n        artifactName: 'security-reports'\n        publishLocation: 'Container'\n      condition: always()\n\n    - script: |\n        if [ -f \"\\$(Agent.TempDirectory)/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' \\$(Agent.TempDirectory)/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' \\$(Agent.TempDirectory)/security-report.json)\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ] || [ \"\\$HIGH_COUNT\" -gt 5 ]; then\n            echo \"##vso[task.logissue type=error]Critical security issues found: \\$CRITICAL_COUNT critical, \\$HIGH_COUNT high\"\n            exit 1\n          fi\n        fi\n      displayName: 'Evaluate Security Results'\n      condition: always()`,\n      notes: 'Configure service connections for private repositories and adjust thresholds as needed'\n    },\n    {\n      platform: 'AWS CodeBuild',\n      description: 'Integrate SPT into AWS CodeBuild pipeline',\n      icon: 'fab fa-aws',\n      filename: 'buildspec.yml',\n      config: `version: 0.2\n\nenv:\n  variables:\n    GO_VERSION: \"1.21\"\n    SPT_VERSION: \"latest\"\n  parameter-store:\n    GITHUB_TOKEN: \"/spt/github-token\"  # Optional for private repos\n\nphases:\n  install:\n    runtime-versions:\n      golang: \\$GO_VERSION\n    commands:\n      - echo \"Installing SPT CLI...\"\n      - go install github.com/blockchain-spt/cmd/spt@\\$SPT_VERSION\n      - spt version\n\n  pre_build:\n    commands:\n      - echo \"Preparing security scan...\"\n      - mkdir -p reports\n      - echo \"Current directory contents:\"\n      - ls -la\n\n  build:\n    commands:\n      - echo \"Running SPT security scan...\"\n      - spt scan --format json --output reports/security-report.json ./\n      - spt audit --generate-report --report-path reports/audit-report.html ./\n      - echo \"Security scan completed\"\n\n  post_build:\n    commands:\n      - echo \"Processing security results...\"\n      - |\n        if [ -f \"reports/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' reports/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' reports/security-report.json)\n          MEDIUM_COUNT=\\$(jq '.summary.medium // 0' reports/security-report.json)\n\n          echo \"Security Summary:\"\n          echo \"  Critical: \\$CRITICAL_COUNT\"\n          echo \"  High: \\$HIGH_COUNT\"\n          echo \"  Medium: \\$MEDIUM_COUNT\"\n\n          # Fail build if critical issues found\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ]; then\n            echo \"Build failed due to critical security issues\"\n            exit 1\n          fi\n\n          # Warning for high issues\n          if [ \"\\$HIGH_COUNT\" -gt 10 ]; then\n            echo \"Warning: High number of high-severity issues (\\$HIGH_COUNT)\"\n          fi\n        else\n          echo \"Security report not found\"\n          exit 1\n        fi\n\nartifacts:\n  files:\n    - 'reports/**/*'\n  name: spt-security-reports\n\nreports:\n  spt-security:\n    files:\n      - 'reports/security-report.json'\n    file-format: 'JUNITXML'`,\n      notes: 'Store sensitive tokens in AWS Parameter Store or Secrets Manager'\n    },\n    {\n      platform: 'AWS CodePipeline',\n      description: 'Complete AWS CodePipeline with SPT integration',\n      icon: 'fab fa-aws',\n      filename: 'cloudformation-pipeline.yml',\n      config: `AWSTemplateFormatVersion: '2010-09-09'\nDescription: 'SPT Security Pipeline with CodePipeline'\n\nParameters:\n  GitHubRepo:\n    Type: String\n    Description: GitHub repository name\n  GitHubOwner:\n    Type: String\n    Description: GitHub repository owner\n  GitHubToken:\n    Type: String\n    NoEcho: true\n    Description: GitHub personal access token\n\nResources:\n  # S3 Bucket for artifacts\n  ArtifactsBucket:\n    Type: AWS::S3::Bucket\n    Properties:\n      BucketName: !Sub '\\${AWS::StackName}-spt-artifacts'\n      VersioningConfiguration:\n        Status: Enabled\n      PublicAccessBlockConfiguration:\n        BlockPublicAcls: true\n        BlockPublicPolicy: true\n        IgnorePublicAcls: true\n        RestrictPublicBuckets: true\n\n  # CodeBuild Project for SPT Security Scan\n  SPTSecurityProject:\n    Type: AWS::CodeBuild::Project\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-security'\n      ServiceRole: !GetAtt CodeBuildRole.Arn\n      Artifacts:\n        Type: CODEPIPELINE\n      Environment:\n        Type: LINUX_CONTAINER\n        ComputeType: BUILD_GENERAL1_MEDIUM\n        Image: aws/codebuild/amazonlinux2-x86_64-standard:3.0\n        EnvironmentVariables:\n          - Name: GITHUB_TOKEN\n            Value: !Ref GitHubToken\n            Type: PARAMETER_STORE\n      Source:\n        Type: CODEPIPELINE\n        BuildSpec: |\n          version: 0.2\n          phases:\n            install:\n              runtime-versions:\n                golang: 1.21\n              commands:\n                - go install github.com/blockchain-spt/cmd/spt@latest\n            build:\n              commands:\n                - mkdir -p reports\n                - spt scan --format json --output reports/security-report.json ./\n                - spt audit --generate-report --report-path reports/audit-report.html ./\n          artifacts:\n            files:\n              - 'reports/**/*'\n\n  # CodePipeline\n  SPTPipeline:\n    Type: AWS::CodePipeline::Pipeline\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-pipeline'\n      RoleArn: !GetAtt CodePipelineRole.Arn\n      ArtifactStore:\n        Type: S3\n        Location: !Ref ArtifactsBucket\n      Stages:\n        - Name: Source\n          Actions:\n            - Name: SourceAction\n              ActionTypeId:\n                Category: Source\n                Owner: ThirdParty\n                Provider: GitHub\n                Version: '1'\n              Configuration:\n                Owner: !Ref GitHubOwner\n                Repo: !Ref GitHubRepo\n                Branch: main\n                OAuthToken: !Ref GitHubToken\n              OutputArtifacts:\n                - Name: SourceOutput\n\n        - Name: SecurityScan\n          Actions:\n            - Name: SPTScan\n              ActionTypeId:\n                Category: Build\n                Owner: AWS\n                Provider: CodeBuild\n                Version: '1'\n              Configuration:\n                ProjectName: !Ref SPTSecurityProject\n              InputArtifacts:\n                - Name: SourceOutput\n              OutputArtifacts:\n                - Name: SecurityOutput\n\n  # IAM Roles (simplified - add specific permissions as needed)\n  CodeBuildRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codebuild.amazonaws.com\n            Action: sts:AssumeRole\n      ManagedPolicyArns:\n        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess\n      Policies:\n        - PolicyName: S3Access\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                Resource: !Sub '\\${ArtifactsBucket}/*'\n\n  CodePipelineRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codepipeline.amazonaws.com\n            Action: sts:AssumeRole\n      Policies:\n        - PolicyName: PipelinePolicy\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                  - s3:GetBucketVersioning\n                Resource:\n                  - !Sub '\\${ArtifactsBucket}'\n                  - !Sub '\\${ArtifactsBucket}/*'\n              - Effect: Allow\n                Action:\n                  - codebuild:BatchGetBuilds\n                  - codebuild:StartBuild\n                Resource: !GetAtt SPTSecurityProject.Arn`,\n      notes: 'Deploy using AWS CloudFormation. Customize IAM permissions based on your security requirements.'\n    },\n    {\n      platform: 'Docker',\n      description: 'Containerized SPT for consistent CI/CD environments',\n      icon: 'fab fa-docker',\n      filename: 'Dockerfile',\n      config: `# Multi-stage Dockerfile for SPT CLI\nFROM golang:1.21-alpine AS builder\n\n# Install dependencies\nRUN apk add --no-cache git ca-certificates\n\n# Set working directory\nWORKDIR /app\n\n# Install SPT CLI\nRUN go install github.com/blockchain-spt/cmd/spt@latest\n\n# Create final image\nFROM alpine:latest\n\n# Install runtime dependencies\nRUN apk add --no-cache ca-certificates jq curl\n\n# Copy SPT binary from builder\nCOPY --from=builder /go/bin/spt /usr/local/bin/spt\n\n# Create non-root user\nRUN addgroup -g 1001 spt && \\\\\n    adduser -D -u 1001 -G spt spt\n\n# Set working directory\nWORKDIR /workspace\n\n# Change ownership\nRUN chown -R spt:spt /workspace\n\n# Switch to non-root user\nUSER spt\n\n# Set entrypoint\nENTRYPOINT [\"spt\"]\nCMD [\"--help\"]\n\n# Usage examples:\n# docker build -t spt-cli .\n# docker run --rm -v \\$(pwd):/workspace spt-cli scan ./\n# docker run --rm -v \\$(pwd):/workspace spt-cli audit --generate-report ./`,\n      notes: 'Use this Docker image in any CI/CD system that supports containers'\n    },\n    {\n      platform: 'Jenkins',\n      description: 'Jenkins Pipeline with SPT integration',\n      icon: 'fab fa-jenkins',\n      filename: 'Jenkinsfile',\n      config: `pipeline {\n    agent any\n\n    environment {\n        GO_VERSION = '1.21'\n        SPT_VERSION = 'latest'\n        REPORTS_DIR = 'reports'\n    }\n\n    tools {\n        go 'go-1.21'  // Configure in Jenkins Global Tools\n    }\n\n    stages {\n        stage('Checkout') {\n            steps {\n                checkout scm\n                script {\n                    env.GIT_COMMIT_SHORT = sh(\n                        script: 'git rev-parse --short HEAD',\n                        returnStdout: true\n                    ).trim()\n                }\n            }\n        }\n\n        stage('Install SPT') {\n            steps {\n                sh '''\n                    echo \"Installing SPT CLI...\"\n                    go install github.com/blockchain-spt/cmd/spt@\\${SPT_VERSION}\n                    spt version\n                '''\n            }\n        }\n\n        stage('Security Scan') {\n            steps {\n                sh '''\n                    echo \"Creating reports directory...\"\n                    mkdir -p \\${REPORTS_DIR}\n\n                    echo \"Running SPT security scan...\"\n                    spt scan --format json --output \\${REPORTS_DIR}/security-report.json ./\n\n                    echo \"Generating audit report...\"\n                    spt audit --generate-report --report-path \\${REPORTS_DIR}/audit-report.html ./\n\n                    echo \"Security scan completed\"\n                '''\n            }\n            post {\n                always {\n                    // Archive artifacts\n                    archiveArtifacts artifacts: '\\${REPORTS_DIR}/**/*', fingerprint: true\n\n                    // Publish HTML reports\n                    publishHTML([\n                        allowMissing: false,\n                        alwaysLinkToLastBuild: true,\n                        keepAll: true,\n                        reportDir: '\\${REPORTS_DIR}',\n                        reportFiles: 'audit-report.html',\n                        reportName: 'SPT Security Report'\n                    ])\n                }\n            }\n        }\n\n        stage('Evaluate Results') {\n            steps {\n                script {\n                    if (fileExists(\"\\${REPORTS_DIR}/security-report.json\")) {\n                        def report = readJSON file: \"\\${REPORTS_DIR}/security-report.json\"\n                        def critical = report.summary?.critical ?: 0\n                        def high = report.summary?.high ?: 0\n                        def medium = report.summary?.medium ?: 0\n\n                        echo \"Security Summary:\"\n                        echo \"  Critical: \\${critical}\"\n                        echo \"  High: \\${high}\"\n                        echo \"  Medium: \\${medium}\"\n\n                        // Set build status based on results\n                        if (critical > 0) {\n                            currentBuild.result = 'FAILURE'\n                            error(\"Build failed due to \\${critical} critical security issues\")\n                        } else if (high > 10) {\n                            currentBuild.result = 'UNSTABLE'\n                            echo \"Build marked unstable due to \\${high} high-severity issues\"\n                        }\n\n                        // Add build description\n                        currentBuild.description = \"Critical: \\${critical}, High: \\${high}, Medium: \\${medium}\"\n                    } else {\n                        currentBuild.result = 'FAILURE'\n                        error(\"Security report not found\")\n                    }\n                }\n            }\n        }\n    }\n\n    post {\n        always {\n            // Clean workspace\n            cleanWs()\n        }\n        failure {\n            // Send notifications on failure\n            emailext (\n                subject: \"SPT Security Scan Failed: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan failed for commit \\${env.GIT_COMMIT_SHORT}. Check the build logs for details.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n        unstable {\n            // Send notifications on unstable builds\n            emailext (\n                subject: \"SPT Security Scan Unstable: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan completed with warnings for commit \\${env.GIT_COMMIT_SHORT}. Review the security report.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n    }\n}`,\n      notes: 'Configure Go tools and email notifications in Jenkins. Install required plugins: Pipeline, HTML Publisher, Email Extension.'\n    }\n  ];\n\n  cicdBestPractices = [\n    {\n      title: 'Fail Fast Strategy',\n      description: 'Configure your pipeline to fail immediately on critical security issues to prevent vulnerable code from progressing.',\n      icon: 'block',\n      color: '#f44336',\n      tips: [\n        'Set critical severity threshold to 0',\n        'Use exit codes to stop pipeline execution',\n        'Implement security gates at multiple stages',\n        'Configure notifications for security failures'\n      ]\n    },\n    {\n      title: 'Artifact Management',\n      description: 'Properly store and manage security reports and artifacts for compliance and tracking.',\n      icon: 'inventory',\n      color: '#2196f3',\n      tips: [\n        'Archive security reports for audit trails',\n        'Use versioned artifact storage',\n        'Implement retention policies',\n        'Enable artifact encryption for sensitive data'\n      ]\n    },\n    {\n      title: 'Parallel Execution',\n      description: 'Optimize scan performance by running security checks in parallel with other tests.',\n      icon: 'call_split',\n      color: '#4caf50',\n      tips: [\n        'Run security scans parallel to unit tests',\n        'Use matrix builds for multiple environments',\n        'Implement caching for faster scans',\n        'Configure resource limits appropriately'\n      ]\n    },\n    {\n      title: 'Security Thresholds',\n      description: 'Define appropriate security thresholds based on your project maturity and risk tolerance.',\n      icon: 'tune',\n      color: '#ff9800',\n      tips: [\n        'Start with strict thresholds for new projects',\n        'Gradually improve legacy project thresholds',\n        'Use different thresholds for different branches',\n        'Document threshold decisions and rationale'\n      ]\n    },\n    {\n      title: 'Integration Testing',\n      description: 'Test your CI/CD integration thoroughly before deploying to production pipelines.',\n      icon: 'integration_instructions',\n      color: '#9c27b0',\n      tips: [\n        'Test with sample vulnerable code',\n        'Verify artifact generation and storage',\n        'Test notification mechanisms',\n        'Validate security gate functionality'\n      ]\n    },\n    {\n      title: 'Monitoring & Alerting',\n      description: 'Implement comprehensive monitoring and alerting for your security pipeline.',\n      icon: 'monitoring',\n      color: '#607d8b',\n      tips: [\n        'Monitor pipeline execution times',\n        'Set up alerts for scan failures',\n        'Track security metrics over time',\n        'Implement dashboard for security trends'\n      ]\n    }\n  ];\n\n  environmentConfigs = [\n    {\n      name: 'Development Environment',\n      type: 'Environment Variables',\n      config: `# Development - More verbose, all severities\nexport SPT_SEVERITY_THRESHOLD=low\nexport SPT_OUTPUT_FORMAT=table\nexport SPT_COLORS=true\nexport SPT_VERBOSE=true\nexport SPT_PARALLEL_SCANS=2\nexport SPT_TIMEOUT=10m\nexport SPT_CACHE_ENABLED=true\nexport SPT_CACHE_DIR=~/.spt/cache`,\n      description: 'Development environment with verbose output and lower thresholds for learning and debugging.'\n    },\n    {\n      name: 'Staging Environment',\n      type: 'Environment Variables',\n      config: `# Staging - Production-like with medium threshold\nexport SPT_SEVERITY_THRESHOLD=medium\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=4\nexport SPT_TIMEOUT=15m\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_GENERATE_REPORTS=true`,\n      description: 'Staging environment that mirrors production settings with moderate security requirements.'\n    },\n    {\n      name: 'Production Environment',\n      type: 'Environment Variables',\n      config: `# Production - Strict security requirements\nexport SPT_SEVERITY_THRESHOLD=high\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=8\nexport SPT_TIMEOUT=30m\nexport SPT_FAIL_ON_CRITICAL=true\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_AUDIT_ENABLED=true\nexport SPT_COMPLIANCE_MODE=true`,\n      description: 'Production environment with strict security requirements and comprehensive auditing.'\n    },\n    {\n      name: 'Docker Configuration',\n      type: 'Docker Environment',\n      config: `# Docker container environment variables\nENV SPT_SEVERITY_THRESHOLD=medium\nENV SPT_OUTPUT_FORMAT=json\nENV SPT_PARALLEL_SCANS=4\nENV SPT_TIMEOUT=20m\nENV SPT_CACHE_ENABLED=false\nENV SPT_WORKSPACE=/workspace\nENV SPT_REPORTS_DIR=/reports\n\n# Volume mounts\n# docker run -v \\$(pwd):/workspace -v \\$(pwd)/reports:/reports spt-cli`,\n      description: 'Containerized environment configuration for consistent cross-platform execution.'\n    },\n    {\n      name: 'Cloud-Native Configuration',\n      type: 'Kubernetes ConfigMap',\n      config: `apiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: spt-config\n  namespace: security\ndata:\n  SPT_SEVERITY_THRESHOLD: \"medium\"\n  SPT_OUTPUT_FORMAT: \"json\"\n  SPT_PARALLEL_SCANS: \"6\"\n  SPT_TIMEOUT: \"25m\"\n  SPT_CLOUD_STORAGE: \"true\"\n  SPT_METRICS_ENABLED: \"true\"\n  SPT_DISTRIBUTED_SCAN: \"true\"\n---\napiVersion: v1\nkind: Secret\nmetadata:\n  name: spt-secrets\n  namespace: security\ntype: Opaque\nstringData:\n  github-token: \"your-github-token\"\n  api-key: \"your-api-key\"`,\n      description: 'Kubernetes-native configuration using ConfigMaps and Secrets for cloud deployments.'\n    }\n  ];\n\n  configExample = `{\n  \"scanning\": {\n    \"chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n    \"severity_threshold\": \"medium\",\n    \"max_file_size\": \"10MB\",\n    \"timeout\": \"5m\",\n    \"parallel_scans\": 4\n  },\n  \"output\": {\n    \"format\": \"table\",\n    \"colors\": true,\n    \"verbose\": false\n  },\n  \"rules\": {\n    \"ethereum\": {\n      \"check_reentrancy\": true,\n      \"check_overflow\": true,\n      \"check_access_control\": true\n    },\n    \"bitcoin\": {\n      \"check_key_management\": true,\n      \"check_transaction_validation\": true\n    }\n  },\n  \"integrations\": {\n    \"vscode\": {\n      \"enabled\": true,\n      \"server_url\": \"http://localhost:8080\"\n    }\n  }\n}`;\n\n  configOptions = [\n    {\n      key: 'scanning.chains',\n      description: 'Array of blockchain chains to analyze'\n    },\n    {\n      key: 'scanning.severity_threshold',\n      description: 'Minimum severity level to report'\n    },\n    {\n      key: 'output.format',\n      description: 'Default output format for scan results'\n    },\n    {\n      key: 'rules.ethereum',\n      description: 'Ethereum-specific security rules configuration'\n    },\n    {\n      key: 'rules.bitcoin',\n      description: 'Bitcoin-specific security rules configuration'\n    },\n    {\n      key: 'integrations',\n      description: 'Configuration for IDE and tool integrations'\n    }\n  ];\n\n  platformTips: { [key: string]: string[] } = {\n    'GitHub Actions': [\n      'Use GitHub Secrets for sensitive tokens',\n      'Enable branch protection rules with status checks',\n      'Configure matrix builds for multiple Go versions',\n      'Use actions/cache for faster builds'\n    ],\n    'GitLab CI': [\n      'Use GitLab CI/CD variables for configuration',\n      'Configure merge request pipelines',\n      'Use GitLab Container Registry for custom images',\n      'Enable pipeline schedules for regular scans'\n    ],\n    'Azure DevOps': [\n      'Store secrets in Azure Key Vault',\n      'Use variable groups for environment-specific configs',\n      'Configure branch policies with build validation',\n      'Enable Azure Artifacts for report storage'\n    ],\n    'AWS CodeBuild': [\n      'Use Parameter Store for secure configuration',\n      'Configure VPC settings for private repositories',\n      'Use CloudWatch for monitoring and alerting',\n      'Enable S3 artifact encryption'\n    ],\n    'AWS CodePipeline': [\n      'Use CloudFormation for infrastructure as code',\n      'Configure cross-region artifact replication',\n      'Implement approval gates for production',\n      'Use EventBridge for pipeline notifications'\n    ],\n    'Docker': [\n      'Use multi-stage builds for smaller images',\n      'Run containers as non-root user',\n      'Mount volumes for persistent reports',\n      'Use health checks for container monitoring'\n    ],\n    'Jenkins': [\n      'Use Jenkins Credentials for secure storage',\n      'Configure build triggers with webhooks',\n      'Use Pipeline as Code with Jenkinsfile',\n      'Enable Blue Ocean for better UI'\n    ]\n  };\n\n  copyToClipboard(text: string): void {\n    navigator.clipboard.writeText(text).then(() => {\n      // Could add a snackbar notification here\n      console.log('Configuration copied to clipboard');\n    }).catch(err => {\n      console.error('Failed to copy: ', err);\n    });\n  }\n\n  getPlatformTips(platform: string): string[] {\n    return this.platformTips[platform] || [];\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;IA6DxCC,EADF,CAAAC,cAAA,cAAyD,eACf;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnEH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAEhCF,EAFgC,CAAAG,YAAA,EAAI,EAC5B,EACF;;;;IALMH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,WAAA,UAAAC,UAAA,CAAAC,KAAA,CAA6B;IAACP,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAQ,iBAAA,CAAAF,UAAA,CAAAG,IAAA,CAAkB;IAEhDT,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAAF,UAAA,CAAAI,KAAA,CAAmB;IACxBV,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAQ,iBAAA,CAAAF,UAAA,CAAAK,WAAA,CAAyB;;;;;IA8B1BX,EADF,CAAAC,cAAA,cAA+C,eACnC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;;;;IADEH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAC,KAAA,CAAkB;;;;;IAd1Bb,EAFJ,CAAAC,cAAA,mBAAyE,sBACtD,kBACW;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACnDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAC7CF,EAD6C,CAAAG,YAAA,EAAoB,EAC/C;IAIZH,EAHN,CAAAC,cAAA,uBAAkB,cACQ,eACG,gBACb;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAChBF,EADgB,CAAAG,YAAA,EAAO,EACjB;IACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAClCF,EADkC,CAAAG,YAAA,EAAO,EAAM,EACzC;IACNH,EAAA,CAAAc,UAAA,KAAAC,6CAAA,kBAA+C;IAKnDf,EADE,CAAAG,YAAA,EAAmB,EACV;;;;IAjBmBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAH,IAAA,CAAiB;IAC3BT,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAF,KAAA,CAAkB;IACfV,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAD,WAAA,CAAwB;IAQ9BX,EAAA,CAAAI,SAAA,IAAqB;IAArBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAI,QAAA,CAAqB;IAEPhB,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAiB,UAAA,SAAAL,SAAA,CAAAC,KAAA,CAAkB;;;;;IAyCvCb,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAsC,WAC9B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC3B;;;;IADGH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAQ,iBAAA,CAAAU,SAAA,CAAAC,IAAA,CAAiB;;;;;IAIzBnB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAsC,eAC1B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC7BF,EAD6B,CAAAG,YAAA,EAAW,EACnC;;;;IADOH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAQ,iBAAA,CAAAY,SAAA,CAAAC,IAAA,CAAiB;;;;;IAI7BrB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACtDH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAc,SAAA,CAAAX,WAAA,CAAwB;;;;;IAG9DX,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EAAA,CAAAC,cAAA,WAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA3BH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAQ,iBAAA,CAAAe,SAAA,CAAAC,OAAA,CAAoB;;;;;IACjDxB,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFxCH,EAAA,CAAAC,cAAA,aAAsC;IAEpCD,EADA,CAAAc,UAAA,IAAAW,qEAAA,mBAA6B,IAAAC,qEAAA,mBACC;IAChC1B,EAAA,CAAAG,YAAA,EAAK;;;;IAFIH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAiB,UAAA,SAAAM,SAAA,CAAAC,OAAA,CAAoB;IACpBxB,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAiB,UAAA,UAAAM,SAAA,CAAAC,OAAA,CAAqB;;;;;IAGhCxB,EAAA,CAAA2B,SAAA,aAAyD;;;;;IACzD3B,EAAA,CAAA2B,SAAA,aAA+D;;;;;IA1BjE3B,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAA4B,uBAAA,OAAkC;IAEhC5B,EADA,CAAAc,UAAA,IAAAe,6DAAA,iBAAsC,IAAAC,6DAAA,iBACA;;IAIxC9B,EAAA,CAAA4B,uBAAA,OAAkC;IAEhC5B,EADA,CAAAc,UAAA,IAAAiB,6DAAA,iBAAsC,IAAAC,6DAAA,iBACA;;IAIxChC,EAAA,CAAA4B,uBAAA,QAAyC;IAEvC5B,EADA,CAAAc,UAAA,KAAAmB,8DAAA,iBAAsC,KAAAC,8DAAA,iBACA;;IAExClC,EAAA,CAAA4B,uBAAA,QAAqC;IAEnC5B,EADA,CAAAc,UAAA,KAAAqB,8DAAA,iBAAsC,KAAAC,8DAAA,iBACA;;IAMxCpC,EADA,CAAAc,UAAA,KAAAuB,8DAAA,iBAAoD,KAAAC,8DAAA,iBACM;IAE9DtC,EADE,CAAAG,YAAA,EAAQ,EACJ;;;;;IA3BaH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAiB,UAAA,eAAAsB,UAAA,CAAAC,OAAA,CAA8B;IAwBzBxC,EAAA,CAAAI,SAAA,IAA8B;IAA9BJ,EAAA,CAAAiB,UAAA,oBAAAwB,MAAA,CAAAC,aAAA,CAA8B;IACjB1C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAiB,UAAA,qBAAAwB,MAAA,CAAAC,aAAA,CAAuB;;;;;IAiBpD1C,EAFJ,CAAAC,cAAA,cAAiD,cACtB,eACb;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IACdF,EADc,CAAAG,YAAA,EAAO,EACf;IACDH,EAAL,CAAAC,cAAA,UAAK,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAAM,EACxC;;;;IADOH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,iBAAA,CAAAmC,UAAA,CAAAC,MAAA,CAAoB;;;;;IAbjC5C,EADF,CAAAC,cAAA,cAA8D,YAC7B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGxDH,EAFJ,CAAAC,cAAA,cAAwB,cACG,eACb;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,cAAO;IACfF,EADe,CAAAG,YAAA,EAAO,EAChB;IACDH,EAAL,CAAAC,cAAA,UAAK,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAClCF,EADkC,CAAAG,YAAA,EAAO,EAAM,EACzC;IACNH,EAAA,CAAAc,UAAA,KAAA+B,qEAAA,kBAAiD;IAOnD7C,EAAA,CAAAG,YAAA,EAAM;;;;IAf2BH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAQ,iBAAA,CAAAmC,UAAA,CAAAhC,WAAA,CAAyB;IAM3CX,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAQ,iBAAA,CAAAmC,UAAA,CAAAG,OAAA,CAAqB;IAEP9C,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAiB,UAAA,SAAA0B,UAAA,CAAAC,MAAA,CAAoB;;;;;IAVjD5C,EADF,CAAAC,cAAA,cAAkE,SAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAc,UAAA,IAAAiC,8DAAA,mBAA8D;IAiBhE/C,EAAA,CAAAG,YAAA,EAAM;;;;IAjBqCH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAiB,UAAA,YAAAsB,UAAA,CAAAS,QAAA,CAAmB;;;;;IAoB5DhD,EADF,CAAAC,cAAA,cAAiD,SAC3C;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEZH,EADF,CAAAC,cAAA,cAA2B,eACf;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAE1BF,EAF0B,CAAAG,YAAA,EAAI,EACtB,EACF;;;;IAFCH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAA+B,UAAA,CAAA1B,KAAA,CAAmB;;;;;IAvExBb,EALN,CAAAC,cAAA,8BAEwB,iCACM,sBACT,eACY;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACxC;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAIzBH,EAFJ,CAAAC,cAAA,cAA6B,cACA,SACrB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EADP,CAAAC,cAAA,eAAwB,WACjB,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAElCF,EAFkC,CAAAG,YAAA,EAAO,EAAM,EACvC,EACF;IAsDNH,EApDA,CAAAc,UAAA,KAAAmC,wDAAA,mBAAgE,KAAAC,wDAAA,kBA+BE,KAAAC,wDAAA,kBAqBjB;IAQrDnD,EADE,CAAAG,YAAA,EAAM,EACc;;;;IA3EWH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAoD,kBAAA,SAAAb,UAAA,CAAAc,IAAA,KAAsB;IAGjDrD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAAb,UAAA,CAAA5B,WAAA,MACF;IAOeX,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAA+B,UAAA,CAAAe,KAAA,CAAmB;IAIJtD,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAiB,UAAA,SAAAsB,UAAA,CAAAC,OAAA,CAAAe,MAAA,KAAgC;IA+B/BvD,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,SAAAsB,UAAA,CAAAS,QAAA,CAAAO,MAAA,KAAiC;IAqBpCvD,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAiB,UAAA,SAAAsB,UAAA,CAAA1B,KAAA,CAAmB;;;;;IA2D3Cb,EADF,CAAAC,cAAA,cAAyD,mBAC1B;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/CH,EADF,CAAAC,cAAA,cAA2B,aACjB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAE9BF,EAF8B,CAAAG,YAAA,EAAI,EAC1B,EACF;;;;IAFCH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAQ,iBAAA,CAAAgD,eAAA,CAAA3C,KAAA,CAAuB;;;;;IASxBb,EADF,CAAAC,cAAA,SAA8D,mBACjC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EACnB;;;;IADGH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAQ,iBAAA,CAAAiD,OAAA,CAAS;;;;;IAJnBzD,EADF,CAAAC,cAAA,cAAoF,SAC9E;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAc,UAAA,IAAA4C,6DAAA,iBAA8D;IAKlE1D,EADE,CAAAG,YAAA,EAAK,EACD;;;;;IALkBH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAiB,UAAA,YAAAwB,MAAA,CAAAkB,eAAA,CAAAH,eAAA,CAAAI,QAAA,EAAwC;;;;;;IA5ChE5D,EALJ,CAAAC,cAAA,8BAGuB,iCACO,sBACT;IACfD,EAAA,CAAA2B,SAAA,YAAuE;IACvE3B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAIzBH,EAFJ,CAAAC,cAAA,cAAiC,cACC,SAC1B;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,kBAEwC;IADhCD,EAAA,CAAA6D,UAAA,mBAAAC,2EAAA;MAAA,MAAAN,eAAA,GAAAxD,EAAA,CAAA+D,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAzC,EAAA,CAAAkE,aAAA;MAAA,OAAAlE,EAAA,CAAAmE,WAAA,CAAS1B,MAAA,CAAA2B,eAAA,CAAAZ,eAAA,CAAAa,MAAA,CAAmC;IAAA,EAAC;IAEnDrE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACL;IAIFH,EAFJ,CAAAC,cAAA,eAAwB,eACG,gBACb;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,kBAEuC;IAD/BD,EAAA,CAAA6D,UAAA,mBAAAS,2EAAA;MAAA,MAAAd,eAAA,GAAAxD,EAAA,CAAA+D,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAzC,EAAA,CAAAkE,aAAA;MAAA,OAAAlE,EAAA,CAAAmE,WAAA,CAAS1B,MAAA,CAAA2B,eAAA,CAAAZ,eAAA,CAAAa,MAAA,CAAmC;IAAA,EAAC;IAEnDrE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACL;IACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IACrCF,EADqC,CAAAG,YAAA,EAAO,EAAM,EAC5C;IAWNH,EATA,CAAAc,UAAA,KAAAyD,wDAAA,kBAAyD,KAAAC,wDAAA,kBAS2B;IAUxFxE,EADE,CAAAG,YAAA,EAAM,EACc;;;;;;IArDpBH,EAAA,CAAAiB,UAAA,aAAAwD,KAAA,OAAoB;IAGbzE,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAA0E,sBAAA,mBAAAlB,eAAA,CAAA/C,IAAA,KAA4C;IAC/CT,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAAI,eAAA,CAAAI,QAAA,MACF;IAEE5D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAAI,eAAA,CAAA7C,WAAA,MACF;IAKMX,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoD,kBAAA,yBAAAI,eAAA,CAAAmB,QAAA,KAA8C;IAW1C3E,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAgD,eAAA,CAAAmB,QAAA,CAA0B;IAOvB3E,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAgD,eAAA,CAAAa,MAAA,CAAwB;IAGLrE,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAiB,UAAA,SAAAuC,eAAA,CAAA3C,KAAA,CAAuB;IAS3Bb,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAiB,UAAA,SAAAwB,MAAA,CAAAkB,eAAA,CAAAH,eAAA,CAAAI,QAAA,EAAAL,MAAA,KAAsD;;;;;IAoC9EvD,EADF,CAAAC,cAAA,aAA2D,mBACxB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EACnB;;;;IADGH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAQ,iBAAA,CAAAoE,OAAA,CAAS;;;;;IAZjB5E,EALN,CAAAC,cAAA,8BAEyB,iCACK,sBACT,mBACgD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7FH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAG3BH,EADF,CAAAC,cAAA,cAA8B,aACF;IACxBD,EAAA,CAAAc,UAAA,KAAA+D,uDAAA,iBAA2D;IAMjE7E,EAFI,CAAAG,YAAA,EAAK,EACD,EACc;;;;IAhBgBH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,WAAA,UAAAyE,YAAA,CAAAvE,KAAA,CAA8B;IAACP,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAAsE,YAAA,CAAArE,IAAA,CAAmB;IAClFT,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAA0B,YAAA,CAAApE,KAAA,MACF;IAEEV,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAA0B,YAAA,CAAAnE,WAAA,MACF;IAKsBX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAiB,UAAA,YAAA6D,YAAA,CAAAC,IAAA,CAAgB;;;;;;IAsBpC/E,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGnBH,EAFJ,CAAAC,cAAA,cAAwB,cACG,eACb;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,iBAEwC;IADhCD,EAAA,CAAA6D,UAAA,mBAAAmB,0DAAA;MAAA,MAAAC,OAAA,GAAAjF,EAAA,CAAA+D,aAAA,CAAAmB,IAAA,EAAAjB,SAAA;MAAA,MAAAxB,MAAA,GAAAzC,EAAA,CAAAkE,aAAA;MAAA,OAAAlE,EAAA,CAAAmE,WAAA,CAAS1B,MAAA,CAAA2B,eAAA,CAAAa,OAAA,CAAAZ,MAAA,CAA2B;IAAA,EAAC;IAE3CrE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACL;IACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAC7BF,EAD6B,CAAAG,YAAA,EAAO,EAAM,EACpC;IACNH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAClDF,EADkD,CAAAG,YAAA,EAAI,EAChD;;;;IAdAH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAQ,iBAAA,CAAAyE,OAAA,CAAA5B,IAAA,CAAc;IAIRrD,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAQ,iBAAA,CAAAyE,OAAA,CAAA5D,IAAA,CAAc;IAOXrB,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAQ,iBAAA,CAAAyE,OAAA,CAAAZ,MAAA,CAAgB;IAEFrE,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAQ,iBAAA,CAAAyE,OAAA,CAAAtE,WAAA,CAAqB;;;;;IAiChDX,EADF,CAAAC,cAAA,cAAgE,aACtD;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAC7BF,EAD6B,CAAAG,YAAA,EAAI,EAC3B;;;;IAFIH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAQ,iBAAA,CAAA2E,UAAA,CAAAC,GAAA,CAAgB;IACrBpF,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAA2E,UAAA,CAAAxE,WAAA,CAAwB;;;AAilBjD,OAAM,MAAO0E,iBAAiB;EAz5B9BC,YAAA;IA05BE,KAAA5C,aAAa,GAAa,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,CAAC;IAEpE,KAAA6C,WAAW,GAAG,CACZ;MACE7E,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,6DAA6D;MAC1EF,IAAI,EAAE,UAAU;MAChBF,KAAK,EAAE;KACR,EACD;MACEG,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,8DAA8D;MAC3EF,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,EACD;MACEG,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,wDAAwD;MACrEF,IAAI,EAAE,0BAA0B;MAChCF,KAAK,EAAE;KACR,EACD;MACEG,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,2DAA2D;MACxEF,IAAI,EAAE,MAAM;MACZF,KAAK,EAAE;KACR,CACF;IAED,KAAAiF,mBAAmB,GAAG,CACpB;MACE9E,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE,wBAAwB;MACrCF,IAAI,EAAE,MAAM;MACZO,QAAQ,EAAE;;4BAEY;MACtBH,KAAK,EAAE;KACR,EACD;MACEH,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,sBAAsB;MACnCF,IAAI,EAAE,OAAO;MACbO,QAAQ,EAAE;;SAEP;MACHH,KAAK,EAAE;KACR,EACD;MACEH,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,0BAA0B;MACvCF,IAAI,EAAE,UAAU;MAChBO,QAAQ,EAAE,qDAAqD;MAC/DH,KAAK,EAAE;KACR,CACF;IAED,KAAA4E,WAAW,GAAiB,CAC1B;MACEpC,IAAI,EAAE,MAAM;MACZ1C,WAAW,EAAE,+CAA+C;MAC5D2C,KAAK,EAAE,yBAAyB;MAChCd,OAAO,EAAE,CACP;QAAErB,IAAI,EAAE,SAAS;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,6BAA6B;QAAEa,OAAO,EAAE;MAAK,CAAE,EAC/F;QAAEL,IAAI,EAAE,UAAU;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,wCAAwC;QAAEa,OAAO,EAAE;MAAO,CAAE,EAC7G;QAAEL,IAAI,EAAE,UAAU;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE;MAAkB,CAAE,EACrE;QAAEQ,IAAI,EAAE,YAAY;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,wBAAwB;QAAEa,OAAO,EAAE;MAAQ,CAAE,EAChG;QAAEL,IAAI,EAAE,aAAa;QAAEE,IAAI,EAAE,SAAS;QAAEV,WAAW,EAAE,8BAA8B;QAAEa,OAAO,EAAE;MAAM,CAAE,CACvG;MACDwB,QAAQ,EAAE,CACR;QACEF,OAAO,EAAE,sBAAsB;QAC/BnC,WAAW,EAAE,uCAAuC;QACpDiC,MAAM,EAAE;;;;;OAKT,EACD;QACEE,OAAO,EAAE,+CAA+C;QACxDnC,WAAW,EAAE;OACd,CACF;MACDE,KAAK,EAAE;KACR,EACD;MACEwC,IAAI,EAAE,OAAO;MACb1C,WAAW,EAAE,sCAAsC;MACnD2C,KAAK,EAAE,0BAA0B;MACjCd,OAAO,EAAE,CACP;QAAErB,IAAI,EAAE,mBAAmB;QAAEE,IAAI,EAAE,SAAS;QAAEV,WAAW,EAAE,0BAA0B;QAAEa,OAAO,EAAE;MAAO,CAAE,EACzG;QAAEL,IAAI,EAAE,eAAe;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,oBAAoB;QAAEa,OAAO,EAAE;MAAqB,CAAE,EAC5G;QAAEL,IAAI,EAAE,YAAY;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,iBAAiB;QAAEa,OAAO,EAAE;MAAU,CAAE,CAC5F;MACDwB,QAAQ,EAAE,CACR;QACEF,OAAO,EAAE,uCAAuC;QAChDnC,WAAW,EAAE;OACd;KAEJ,EACD;MACE0C,IAAI,EAAE,OAAO;MACb1C,WAAW,EAAE,8BAA8B;MAC3C2C,KAAK,EAAE,uCAAuC;MAC9Cd,OAAO,EAAE,CACP;QAAErB,IAAI,EAAE,OAAO;QAAEE,IAAI,EAAE,SAAS;QAAEV,WAAW,EAAE,qCAAqC;QAAEa,OAAO,EAAE;MAAO,CAAE,CACzG;MACDwB,QAAQ,EAAE,CACR;QACEF,OAAO,EAAE,sBAAsB;QAC/BnC,WAAW,EAAE;OACd,EACD;QACEmC,OAAO,EAAE,eAAe;QACxBnC,WAAW,EAAE;OACd;KAEJ,CACF;IAED,KAAA+E,mBAAmB,GAAG,CACpB;MACE9B,QAAQ,EAAE,gBAAgB;MAC1BjD,WAAW,EAAE,4CAA4C;MACzDF,IAAI,EAAE,eAAe;MACrBkE,QAAQ,EAAE,gCAAgC;MAC1CN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;qCAmBuB;MAC/BxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,WAAW;MACrBjD,WAAW,EAAE,0CAA0C;MACvDF,IAAI,EAAE,eAAe;MACrBkE,QAAQ,EAAE,gBAAgB;MAC1BN,MAAM,EAAE;;;;;;;;;;;;;;;WAeH;MACLxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,cAAc;MACxBjD,WAAW,EAAE,0CAA0C;MACvDF,IAAI,EAAE,kBAAkB;MACxBkE,QAAQ,EAAE,qBAAqB;MAC/BN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAkEY;MACpBxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,eAAe;MACzBjD,WAAW,EAAE,2CAA2C;MACxDF,IAAI,EAAE,YAAY;MAClBkE,QAAQ,EAAE,eAAe;MACzBN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAsEc;MACtBxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,kBAAkB;MAC5BjD,WAAW,EAAE,gDAAgD;MAC7DF,IAAI,EAAE,YAAY;MAClBkE,QAAQ,EAAE,6BAA6B;MACvCN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDA4J2C;MACnDxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,QAAQ;MAClBjD,WAAW,EAAE,qDAAqD;MAClEF,IAAI,EAAE,eAAe;MACrBkE,QAAQ,EAAE,YAAY;MACtBN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2EAyC6D;MACrExD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,SAAS;MACnBjD,WAAW,EAAE,uCAAuC;MACpDF,IAAI,EAAE,gBAAgB;MACtBkE,QAAQ,EAAE,aAAa;MACvBN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6HZ;MACIxD,KAAK,EAAE;KACR,CACF;IAED,KAAA8E,iBAAiB,GAAG,CAClB;MACEjF,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,sHAAsH;MACnIF,IAAI,EAAE,OAAO;MACbF,KAAK,EAAE,SAAS;MAChBwE,IAAI,EAAE,CACJ,sCAAsC,EACtC,2CAA2C,EAC3C,6CAA6C,EAC7C,+CAA+C;KAElD,EACD;MACErE,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,uFAAuF;MACpGF,IAAI,EAAE,WAAW;MACjBF,KAAK,EAAE,SAAS;MAChBwE,IAAI,EAAE,CACJ,2CAA2C,EAC3C,gCAAgC,EAChC,8BAA8B,EAC9B,+CAA+C;KAElD,EACD;MACErE,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,oFAAoF;MACjGF,IAAI,EAAE,YAAY;MAClBF,KAAK,EAAE,SAAS;MAChBwE,IAAI,EAAE,CACJ,2CAA2C,EAC3C,6CAA6C,EAC7C,oCAAoC,EACpC,yCAAyC;KAE5C,EACD;MACErE,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,2FAA2F;MACxGF,IAAI,EAAE,MAAM;MACZF,KAAK,EAAE,SAAS;MAChBwE,IAAI,EAAE,CACJ,+CAA+C,EAC/C,6CAA6C,EAC7C,iDAAiD,EACjD,4CAA4C;KAE/C,EACD;MACErE,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,kFAAkF;MAC/FF,IAAI,EAAE,0BAA0B;MAChCF,KAAK,EAAE,SAAS;MAChBwE,IAAI,EAAE,CACJ,kCAAkC,EAClC,wCAAwC,EACxC,8BAA8B,EAC9B,sCAAsC;KAEzC,EACD;MACErE,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,6EAA6E;MAC1FF,IAAI,EAAE,YAAY;MAClBF,KAAK,EAAE,SAAS;MAChBwE,IAAI,EAAE,CACJ,kCAAkC,EAClC,iCAAiC,EACjC,kCAAkC,EAClC,yCAAyC;KAE5C,CACF;IAED,KAAAa,kBAAkB,GAAG,CACnB;MACEvC,IAAI,EAAE,yBAAyB;MAC/BhC,IAAI,EAAE,uBAAuB;MAC7BgD,MAAM,EAAE;;;;;;;;kCAQoB;MAC5B1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,qBAAqB;MAC3BhC,IAAI,EAAE,uBAAuB;MAC7BgD,MAAM,EAAE;;;;;;;;iCAQmB;MAC3B1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,wBAAwB;MAC9BhC,IAAI,EAAE,uBAAuB;MAC7BgD,MAAM,EAAE;;;;;;;;;;gCAUkB;MAC1B1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,sBAAsB;MAC5BhC,IAAI,EAAE,oBAAoB;MAC1BgD,MAAM,EAAE;;;;;;;;;;uEAUyD;MACjE1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,4BAA4B;MAClChC,IAAI,EAAE,sBAAsB;MAC5BgD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAsBY;MACpB1D,WAAW,EAAE;KACd,CACF;IAED,KAAAkF,aAAa,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BhB;IAEA,KAAAC,aAAa,GAAG,CACd;MACEV,GAAG,EAAE,iBAAiB;MACtBzE,WAAW,EAAE;KACd,EACD;MACEyE,GAAG,EAAE,6BAA6B;MAClCzE,WAAW,EAAE;KACd,EACD;MACEyE,GAAG,EAAE,eAAe;MACpBzE,WAAW,EAAE;KACd,EACD;MACEyE,GAAG,EAAE,gBAAgB;MACrBzE,WAAW,EAAE;KACd,EACD;MACEyE,GAAG,EAAE,eAAe;MACpBzE,WAAW,EAAE;KACd,EACD;MACEyE,GAAG,EAAE,cAAc;MACnBzE,WAAW,EAAE;KACd,CACF;IAED,KAAAoF,YAAY,GAAgC;MAC1C,gBAAgB,EAAE,CAChB,yCAAyC,EACzC,mDAAmD,EACnD,kDAAkD,EAClD,qCAAqC,CACtC;MACD,WAAW,EAAE,CACX,8CAA8C,EAC9C,mCAAmC,EACnC,iDAAiD,EACjD,6CAA6C,CAC9C;MACD,cAAc,EAAE,CACd,kCAAkC,EAClC,sDAAsD,EACtD,iDAAiD,EACjD,2CAA2C,CAC5C;MACD,eAAe,EAAE,CACf,8CAA8C,EAC9C,iDAAiD,EACjD,4CAA4C,EAC5C,+BAA+B,CAChC;MACD,kBAAkB,EAAE,CAClB,+CAA+C,EAC/C,6CAA6C,EAC7C,yCAAyC,EACzC,4CAA4C,CAC7C;MACD,QAAQ,EAAE,CACR,2CAA2C,EAC3C,iCAAiC,EACjC,sCAAsC,EACtC,4CAA4C,CAC7C;MACD,SAAS,EAAE,CACT,4CAA4C,EAC5C,wCAAwC,EACxC,uCAAuC,EACvC,iCAAiC;KAEpC;;EAED3B,eAAeA,CAAC4B,IAAY;IAC1BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;MAC5C;MACAC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAClD,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAG;MACbH,OAAO,CAACI,KAAK,CAAC,kBAAkB,EAAED,GAAG,CAAC;IACxC,CAAC,CAAC;EACJ;EAEA7C,eAAeA,CAACC,QAAgB;IAC9B,OAAO,IAAI,CAACmC,YAAY,CAACnC,QAAQ,CAAC,IAAI,EAAE;EAC1C;;;uCA37BWyB,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAx4BpBhH,EAHN,CAAAC,cAAA,aAAiC,aACN,SACnB,eACQ;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAyB;UACvBD,EAAA,CAAAE,MAAA,yDACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAKAH,EAHN,CAAAC,cAAA,aAA0B,kBACQ,uBACb,mBACW;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzCH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,oDAA4C;UACjEF,EADiE,CAAAG,YAAA,EAAoB,EACnE;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAE,MAAA,qIAA6H;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpIH,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAAc,UAAA,KAAAoG,iCAAA,iBAAyD;UAUjElH,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;UAMAH,EAJN,CAAAC,cAAA,wBAA0D,kBAE1B,eACH,UACnB;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7BH,EAAA,CAAAC,cAAA,eAAkC;UAChCD,EAAA,CAAAc,UAAA,KAAAqG,sCAAA,wBAAyE;UAsB/EnH,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA0B,eACC,UACnB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sEAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAErEH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAc,UAAA,KAAAsG,iDAAA,mCAEwB;UAiF9BpH,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAAmC,eACR,UACnB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oFAA4E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGjFH,EADF,CAAAC,cAAA,eAAkC,yBACa;UAC3CD,EAAA,CAAAc,UAAA,KAAAuG,iDAAA,oCAGuB;UAuD3BrH,EADE,CAAAG,YAAA,EAAgB,EACZ;UAIJH,EADF,CAAAC,cAAA,eAAoC,UAC9B;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,oFAA4E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE/GH,EAAA,CAAAC,cAAA,yBAA2C;UACzCD,EAAA,CAAAc,UAAA,KAAAwG,iDAAA,mCAEyB;UAqB7BtH,EADE,CAAAG,YAAA,EAAgB,EACZ;UAIJH,EADF,CAAAC,cAAA,eAAiC,UAC3B;UAAAD,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGxCH,EAFJ,CAAAC,cAAA,oBAAkC,uBACf,mBACW;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1DH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACtDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,gDAAwC;UAC7DF,EAD6D,CAAAG,YAAA,EAAoB,EAC/D;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACU;UACxBD,EAAA,CAAAc,UAAA,KAAAyG,iCAAA,mBAAgE;UAqB5EvH,EALU,CAAAG,YAAA,EAAM,EACW,EACV,EACP,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA+B,eACJ,UACnB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2EAAmE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAItEH,EAFJ,CAAAC,cAAA,oBAA8B,uBACX,mBACW;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACnDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACpCF,EADoC,CAAAG,YAAA,EAAoB,EACtC;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACQ,eACG,gBACb;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;UACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAAM,EACvC;UAEJH,EADF,CAAAC,cAAA,eAAgC,UAC1B;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAc,UAAA,KAAA0G,iCAAA,kBAAgE;UAWhFxH,EAPc,CAAAG,YAAA,EAAM,EACF,EACW,EACV,EACP,EACE,EACI,EACZ;;;UA9S6CH,EAAA,CAAAI,SAAA,IAAc;UAAdJ,EAAA,CAAAiB,UAAA,YAAAgG,GAAA,CAAA1B,WAAA,CAAc;UAmBNvF,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,YAAAgG,GAAA,CAAAzB,mBAAA,CAAsB;UAgCjDxF,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAiB,UAAA,YAAAgG,GAAA,CAAAxB,WAAA,CAAc;UA6FRzF,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAiB,UAAA,YAAAgG,GAAA,CAAAvB,mBAAA,CAAwB;UAkE3B1F,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,YAAAgG,GAAA,CAAAtB,iBAAA,CAAsB;UAmCA3F,EAAA,CAAAI,SAAA,IAAqB;UAArBJ,EAAA,CAAAiB,UAAA,YAAAgG,GAAA,CAAArB,kBAAA,CAAqB;UAyCrD5F,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAQ,iBAAA,CAAAyG,GAAA,CAAApB,aAAA,CAAmB;UAKkB7F,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,YAAAgG,GAAA,CAAAnB,aAAA,CAAgB;;;qBAlU9EtG,YAAY,EAAAiI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZlI,YAAY,EACZC,aAAa,EAAAkI,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACbnI,aAAa,EAAAoI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACbzI,aAAa,EAAA0I,EAAA,CAAAC,OAAA,EACb1I,kBAAkB,EAAA2I,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,sBAAA,EAAAJ,EAAA,CAAAK,4BAAA,EAClB/I,cAAc,EAAAgJ,EAAA,CAAAC,OAAA,EACdhJ,cAAc,EAAAiJ,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}