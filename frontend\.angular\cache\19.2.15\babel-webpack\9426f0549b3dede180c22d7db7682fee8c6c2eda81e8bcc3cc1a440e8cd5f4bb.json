{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/checkbox\";\nimport * as i13 from \"@angular/material/toolbar\";\nfunction LoginComponent_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 31);\n  }\n}\nfunction LoginComponent_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  loginWithDemo() {\n    this.isLoading = true;\n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', {\n        duration: 3000\n      });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 69,\n      vars: 11,\n      consts: [[1, \"login-container\"], [1, \"login-toolbar\"], [1, \"toolbar-brand\"], [1, \"brand-icon\"], [1, \"brand-title\"], [1, \"spacer\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/doc\", 1, \"doc-btn\"], [1, \"login-content\"], [1, \"login-card-wrapper\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"logo-icon\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"form-options\"], [\"formControlName\", \"rememberMe\"], [\"href\", \"#\", 1, \"forgot-password\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"demo-button\", 3, \"click\", \"disabled\"], [1, \"card-actions\"], [\"routerLink\", \"/register\", 1, \"register-link\"], [1, \"features-info\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2)(3, \"mat-icon\", 3);\n          i0.ɵɵtext(4, \"shield\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 4);\n          i0.ɵɵtext(6, \"SPT - Blockchain Security Protocol Tool\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(7, \"span\", 5);\n          i0.ɵɵelementStart(8, \"button\", 6)(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Documentation \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 8)(14, \"mat-card\", 9)(15, \"mat-card-header\", 10)(16, \"div\", 11)(17, \"mat-icon\", 12);\n          i0.ɵɵtext(18, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"h1\");\n          i0.ɵɵtext(20, \"Welcome Back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"mat-card-subtitle\");\n          i0.ɵɵtext(22, \"Sign in to access your security dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"mat-card-content\")(24, \"form\", 13);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(25, \"div\", 14)(26, \"mat-form-field\", 15)(27, \"mat-label\");\n          i0.ɵɵtext(28, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 16);\n          i0.ɵɵelementStart(30, \"mat-icon\", 17);\n          i0.ɵɵtext(31, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, LoginComponent_mat_error_32_Template, 2, 0, \"mat-error\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-form-field\", 15)(34, \"mat-label\");\n          i0.ɵɵtext(35, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 19);\n          i0.ɵɵelementStart(37, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_37_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(38, \"mat-icon\");\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(40, LoginComponent_mat_error_40_Template, 2, 0, \"mat-error\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 21)(42, \"mat-checkbox\", 22);\n          i0.ɵɵtext(43, \" Remember me \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"a\", 23);\n          i0.ɵɵtext(45, \"Forgot password?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 24)(47, \"button\", 25);\n          i0.ɵɵtemplate(48, LoginComponent_mat_spinner_48_Template, 1, 0, \"mat-spinner\", 26)(49, LoginComponent_span_49_Template, 2, 0, \"span\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_50_listener() {\n            return ctx.loginWithDemo();\n          });\n          i0.ɵɵtext(51, \" Demo Login \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(52, \"mat-card-actions\", 28)(53, \"p\");\n          i0.ɵɵtext(54, \"Don't have an account? \");\n          i0.ɵɵelementStart(55, \"a\", 29);\n          i0.ɵɵtext(56, \"Sign up\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(57, \"div\", 30)(58, \"h3\");\n          i0.ɵɵtext(59, \"\\uD83D\\uDEE1\\uFE0F Security Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"ul\")(61, \"li\");\n          i0.ɵɵtext(62, \"\\u2705 Smart Contract Analysis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"li\");\n          i0.ɵɵtext(64, \"\\u2705 Vulnerability Detection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"li\");\n          i0.ɵɵtext(66, \"\\u2705 Real-time Monitoring\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"li\");\n          i0.ɵɵtext(68, \"\\u2705 Comprehensive Reports\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.hasError(\"required\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i12.MatCheckbox, MatToolbarModule, i13.MatToolbar],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);\\n}\\n\\n.login-toolbar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\\n  padding: 0 24px;\\n  min-height: 72px;\\n}\\n\\n.toolbar-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  width: 28px;\\n  height: 28px;\\n}\\n\\n.brand-title[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n  font-weight: 600;\\n  letter-spacing: -0.5px;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.doc-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.15);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: white;\\n  border-radius: 24px;\\n  padding: 8px 20px;\\n  font-weight: 500;\\n}\\n\\n.doc-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.25);\\n}\\n\\n.login-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n}\\n\\n.login-card-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 40px;\\n  align-items: center;\\n  max-width: 900px;\\n  width: 100%;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 400px;\\n  padding: 20px;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #667eea;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 300;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 14px;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.demo-button[_ngcontent-%COMP%] {\\n  height: 40px;\\n  color: #667eea;\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.register-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.features-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n  padding: 40px;\\n}\\n\\n.features-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 20px;\\n  font-weight: 300;\\n}\\n\\n.features-info[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n}\\n\\n.features-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n  font-size: 16px;\\n  opacity: 0.9;\\n}\\n\\n@media (max-width: 768px) {\\n  .login-card-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n  .features-info[_ngcontent-%COMP%] {\\n    order: -1;\\n    padding: 20px;\\n    text-align: center;\\n  }\\n  .login-card[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n}\\nmat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hdXRoL2xvZ2luLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsNkRBQUE7QUFBTjs7QUFHSTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLCtDQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBQU47O0FBR0k7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFBTjs7QUFHSTtFQUNFLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxzQkFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UscUNBQUE7RUFDQSwwQ0FBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLHFDQUFBO0FBQU47O0FBR0k7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsV0FBQTtBQUFOOztBQUdJO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtBQUFOOztBQUdJO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLFNBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0VBQ0EsZUFBQTtBQUFOOztBQUdJO0VBQ0UsMEJBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7QUFBTjs7QUFHSTtFQUNFLFlBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLFlBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsNkJBQUE7QUFBTjs7QUFHSTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSwwQkFBQTtBQUFOOztBQUdJO0VBQ0UsT0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0FBQU47O0FBR0k7RUFDRSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsZ0JBQUE7RUFDQSxVQUFBO0FBQU47O0FBR0k7RUFDRSxlQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUFBTjs7QUFHSTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSxTQUFBO0VBQU47RUFHSTtJQUNFLFNBQUE7SUFDQSxhQUFBO0lBQ0Esa0JBQUE7RUFETjtFQUlJO0lBQ0UsZUFBQTtFQUZOO0FBQ0Y7QUFLSTtFQUNFLGtCQUFBO0FBSE4iLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAubG9naW4tY29udGFpbmVyIHtcbiAgICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZmIDAlLCAjZjBmNGZmIDEwMCUpO1xuICAgIH1cblxuICAgIC5sb2dpbi10b29sYmFyIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKTtcbiAgICAgIHBhZGRpbmc6IDAgMjRweDtcbiAgICAgIG1pbi1oZWlnaHQ6IDcycHg7XG4gICAgfVxuXG4gICAgLnRvb2xiYXItYnJhbmQge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDEycHg7XG4gICAgfVxuXG4gICAgLmJyYW5kLWljb24ge1xuICAgICAgZm9udC1zaXplOiAyOHB4O1xuICAgICAgd2lkdGg6IDI4cHg7XG4gICAgICBoZWlnaHQ6IDI4cHg7XG4gICAgfVxuXG4gICAgLmJyYW5kLXRpdGxlIHtcbiAgICAgIGZvbnQtc2l6ZTogMS4yZW07XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgbGV0dGVyLXNwYWNpbmc6IC0wLjVweDtcbiAgICB9XG5cbiAgICAuc3BhY2VyIHtcbiAgICAgIGZsZXg6IDEgMSBhdXRvO1xuICAgIH1cblxuICAgIC5kb2MtYnRuIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAyNHB4O1xuICAgICAgcGFkZGluZzogOHB4IDIwcHg7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgIH1cblxuICAgIC5kb2MtYnRuOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yNSk7XG4gICAgfVxuXG4gICAgLmxvZ2luLWNvbnRlbnQge1xuICAgICAgZmxleDogMTtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBwYWRkaW5nOiA0MHB4IDIwcHg7XG4gICAgfVxuXG4gICAgLmxvZ2luLWNhcmQtd3JhcHBlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiA0MHB4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1heC13aWR0aDogOTAwcHg7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICB9XG5cbiAgICAubG9naW4tY2FyZCB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgbWF4LXdpZHRoOiA0MDBweDtcbiAgICAgIHBhZGRpbmc6IDIwcHg7XG4gICAgfVxuXG4gICAgLmxvZ2luLWhlYWRlciB7XG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xuICAgIH1cblxuICAgIC5sb2dvIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBnYXA6IDEwcHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICAgIH1cblxuICAgIC5sb2dvLWljb24ge1xuICAgICAgZm9udC1zaXplOiA0OHB4O1xuICAgICAgd2lkdGg6IDQ4cHg7XG4gICAgICBoZWlnaHQ6IDQ4cHg7XG4gICAgICBjb2xvcjogIzY2N2VlYTtcbiAgICB9XG5cbiAgICAubG9nbyBoMSB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICAgIGZvbnQtd2VpZ2h0OiAzMDA7XG4gICAgfVxuXG4gICAgLmZvcm0tZmllbGRzIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgZ2FwOiAyMHB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDtcbiAgICB9XG5cbiAgICAuZnVsbC13aWR0aCB7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICB9XG5cbiAgICAuZm9ybS1vcHRpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLXRvcDogMTBweDtcbiAgICB9XG5cbiAgICAuZm9yZ290LXBhc3N3b3JkIHtcbiAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgIH1cblxuICAgIC5mb3Jnb3QtcGFzc3dvcmQ6aG92ZXIge1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgfVxuXG4gICAgLmZvcm0tYWN0aW9ucyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogMTVweDtcbiAgICB9XG5cbiAgICAubG9naW4tYnV0dG9uIHtcbiAgICAgIGhlaWdodDogNDhweDtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuXG4gICAgLmRlbW8tYnV0dG9uIHtcbiAgICAgIGhlaWdodDogNDBweDtcbiAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgIH1cblxuICAgIC5jYXJkLWFjdGlvbnMge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgcGFkZGluZy10b3A6IDIwcHg7XG4gICAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2UwZTBlMDtcbiAgICB9XG5cbiAgICAucmVnaXN0ZXItbGluayB7XG4gICAgICBjb2xvcjogIzY2N2VlYTtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuXG4gICAgLnJlZ2lzdGVyLWxpbms6aG92ZXIge1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgfVxuXG4gICAgLmZlYXR1cmVzLWluZm8ge1xuICAgICAgZmxleDogMTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIHBhZGRpbmc6IDQwcHg7XG4gICAgfVxuXG4gICAgLmZlYXR1cmVzLWluZm8gaDMge1xuICAgICAgZm9udC1zaXplOiAyNHB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiAzMDA7XG4gICAgfVxuXG4gICAgLmZlYXR1cmVzLWluZm8gdWwge1xuICAgICAgbGlzdC1zdHlsZTogbm9uZTtcbiAgICAgIHBhZGRpbmc6IDA7XG4gICAgfVxuXG4gICAgLmZlYXR1cmVzLWluZm8gbGkge1xuICAgICAgcGFkZGluZzogMTBweCAwO1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgb3BhY2l0eTogMC45O1xuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgLmxvZ2luLWNhcmQtd3JhcHBlciB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogMjBweDtcbiAgICAgIH1cblxuICAgICAgLmZlYXR1cmVzLWluZm8ge1xuICAgICAgICBvcmRlcjogLTE7XG4gICAgICAgIHBhZGRpbmc6IDIwcHg7XG4gICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIH1cblxuICAgICAgLmxvZ2luLWNhcmQge1xuICAgICAgICBtYXgtd2lkdGg6IDEwMCU7XG4gICAgICB9XG4gICAgfVxuXG4gICAgbWF0LXNwaW5uZXIge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "MatToolbarModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "hidePassword", "isLoading", "returnUrl", "loginForm", "group", "username", "required", "password", "rememberMe", "ngOnInit", "snapshot", "queryParams", "onSubmit", "valid", "credentials", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "message", "loginWithDemo", "setTimeout", "mockLogin", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_24_listener", "ɵɵtemplate", "LoginComponent_mat_error_32_Template", "LoginComponent_Template_button_click_37_listener", "LoginComponent_mat_error_40_Template", "LoginComponent_mat_spinner_48_Template", "LoginComponent_span_49_Template", "LoginComponent_Template_button_click_50_listener", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "tmp_6_0", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatButton", "MatIconButton", "i10", "MatIcon", "i11", "MatProgressSpinner", "i12", "MatCheckbox", "i13", "MatToolbar", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { AuthService, LoginRequest } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule,\n    MatToolbarModule\n  ],\n  template: `\n    <div class=\"login-container\">\n      <!-- Top Navigation Bar -->\n      <mat-toolbar class=\"login-toolbar\">\n        <div class=\"toolbar-brand\">\n          <mat-icon class=\"brand-icon\">shield</mat-icon>\n          <span class=\"brand-title\">SPT - Blockchain Security Protocol Tool</span>\n        </div>\n        <span class=\"spacer\"></span>\n        <button mat-stroked-button routerLink=\"/doc\" class=\"doc-btn\">\n          <mat-icon>description</mat-icon>\n          Documentation\n        </button>\n      </mat-toolbar>\n\n      <div class=\"login-content\">\n        <div class=\"login-card-wrapper\">\n          <mat-card class=\"login-card\">\n            <mat-card-header class=\"login-header\">\n              <div class=\"logo\">\n                <mat-icon class=\"logo-icon\">security</mat-icon>\n                <h1>Welcome Back</h1>\n              </div>\n              <mat-card-subtitle>Sign in to access your security dashboard</mat-card-subtitle>\n            </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n              <div class=\"form-fields\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Username</mat-label>\n                  <input matInput formControlName=\"username\" autocomplete=\"username\">\n                  <mat-icon matSuffix>person</mat-icon>\n                  <mat-error *ngIf=\"loginForm.get('username')?.hasError('required')\">\n                    Username is required\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Password</mat-label>\n                  <input matInput \n                         [type]=\"hidePassword ? 'password' : 'text'\" \n                         formControlName=\"password\"\n                         autocomplete=\"current-password\">\n                  <button mat-icon-button matSuffix \n                          (click)=\"hidePassword = !hidePassword\" \n                          [attr.aria-label]=\"'Hide password'\" \n                          [attr.aria-pressed]=\"hidePassword\"\n                          type=\"button\">\n                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                  </button>\n                  <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                    Password is required\n                  </mat-error>\n                </mat-form-field>\n\n                <div class=\"form-options\">\n                  <mat-checkbox formControlName=\"rememberMe\">\n                    Remember me\n                  </mat-checkbox>\n                  <a href=\"#\" class=\"forgot-password\">Forgot password?</a>\n                </div>\n              </div>\n\n              <div class=\"form-actions\">\n                <button mat-raised-button \n                        color=\"primary\" \n                        type=\"submit\" \n                        class=\"login-button\"\n                        [disabled]=\"loginForm.invalid || isLoading\">\n                  <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n                  <span *ngIf=\"!isLoading\">Sign In</span>\n                </button>\n\n                <button mat-button \n                        type=\"button\" \n                        class=\"demo-button\"\n                        (click)=\"loginWithDemo()\"\n                        [disabled]=\"isLoading\">\n                  Demo Login\n                </button>\n              </div>\n            </form>\n          </mat-card-content>\n\n          <mat-card-actions class=\"card-actions\">\n            <p>Don't have an account? \n              <a routerLink=\"/register\" class=\"register-link\">Sign up</a>\n            </p>\n          </mat-card-actions>\n        </mat-card>\n\n        <div class=\"features-info\">\n          <h3>🛡️ Security Features</h3>\n          <ul>\n            <li>✅ Smart Contract Analysis</li>\n            <li>✅ Vulnerability Detection</li>\n            <li>✅ Real-time Monitoring</li>\n            <li>✅ Comprehensive Reports</li>\n          </ul>\n        </div>\n      </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .login-container {\n      min-height: 100vh;\n      display: flex;\n      flex-direction: column;\n      background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);\n    }\n\n    .login-toolbar {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n      padding: 0 24px;\n      min-height: 72px;\n    }\n\n    .toolbar-brand {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .brand-icon {\n      font-size: 28px;\n      width: 28px;\n      height: 28px;\n    }\n\n    .brand-title {\n      font-size: 1.2em;\n      font-weight: 600;\n      letter-spacing: -0.5px;\n    }\n\n    .spacer {\n      flex: 1 1 auto;\n    }\n\n    .doc-btn {\n      background: rgba(255, 255, 255, 0.15);\n      border: 1px solid rgba(255, 255, 255, 0.3);\n      color: white;\n      border-radius: 24px;\n      padding: 8px 20px;\n      font-weight: 500;\n    }\n\n    .doc-btn:hover {\n      background: rgba(255, 255, 255, 0.25);\n    }\n\n    .login-content {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 40px 20px;\n    }\n\n    .login-card-wrapper {\n      display: flex;\n      gap: 40px;\n      align-items: center;\n      max-width: 900px;\n      width: 100%;\n    }\n\n    .login-card {\n      flex: 1;\n      max-width: 400px;\n      padding: 20px;\n    }\n\n    .login-header {\n      text-align: center;\n      margin-bottom: 30px;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 10px;\n      margin-bottom: 10px;\n    }\n\n    .logo-icon {\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n      color: #667eea;\n    }\n\n    .logo h1 {\n      margin: 0;\n      color: #333;\n      font-weight: 300;\n    }\n\n    .form-fields {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n\n    .full-width {\n      width: 100%;\n    }\n\n    .form-options {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-top: 10px;\n    }\n\n    .forgot-password {\n      color: #667eea;\n      text-decoration: none;\n      font-size: 14px;\n    }\n\n    .forgot-password:hover {\n      text-decoration: underline;\n    }\n\n    .form-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 15px;\n    }\n\n    .login-button {\n      height: 48px;\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .demo-button {\n      height: 40px;\n      color: #667eea;\n    }\n\n    .card-actions {\n      text-align: center;\n      padding-top: 20px;\n      border-top: 1px solid #e0e0e0;\n    }\n\n    .register-link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .register-link:hover {\n      text-decoration: underline;\n    }\n\n    .features-info {\n      flex: 1;\n      color: white;\n      padding: 40px;\n    }\n\n    .features-info h3 {\n      font-size: 24px;\n      margin-bottom: 20px;\n      font-weight: 300;\n    }\n\n    .features-info ul {\n      list-style: none;\n      padding: 0;\n    }\n\n    .features-info li {\n      padding: 10px 0;\n      font-size: 16px;\n      opacity: 0.9;\n    }\n\n    @media (max-width: 768px) {\n      .login-card-wrapper {\n        flex-direction: column;\n        gap: 20px;\n      }\n\n      .features-info {\n        order: -1;\n        padding: 20px;\n        text-align: center;\n      }\n\n      .login-card {\n        max-width: 100%;\n      }\n    }\n\n    mat-spinner {\n      margin-right: 10px;\n    }\n  `]\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  hidePassword = true;\n  isLoading = false;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      \n      const credentials: LoginRequest = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n\n  loginWithDemo(): void {\n    this.isLoading = true;\n    \n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AAExF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;IAoD1CC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAiBZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AA+OzD,OAAM,MAAOE,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MAErB,MAAMa,WAAW,GAAiB;QAChCT,QAAQ,EAAE,IAAI,CAACF,SAAS,CAACY,KAAK,CAACV,QAAQ;QACvCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACY,KAAK,CAACR;OAChC;MAED,IAAI,CAACX,WAAW,CAACoB,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;QACxC,CAAC;QACDqB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;OACD,CAAC;IACJ;EACF;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACxB,SAAS,GAAG,IAAI;IAErB;IACAyB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9B,WAAW,CAAC+B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;MAC5C,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;IACxC,CAAC,EAAE,IAAI,CAAC;EACV;;;uCA1DWT,cAAc,EAAAL,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA/C,EAAA,CAAAwC,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd5C,cAAc;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjTjBxD,EAJN,CAAAC,cAAA,aAA6B,qBAEQ,aACN,kBACI;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,8CAAuC;UACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;UACNH,EAAA,CAAAI,SAAA,cAA4B;UAE1BJ,EADF,CAAAC,cAAA,gBAA6D,eACjD;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,uBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACG;UAOJH,EALV,CAAAC,cAAA,cAA2B,cACO,mBACD,2BACW,eAClB,oBACY;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAClBF,EADkB,CAAAG,YAAA,EAAK,EACjB;UACNH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,iDAAyC;UAC9DF,EAD8D,CAAAG,YAAA,EAAoB,EAChE;UAGlBH,EADF,CAAAC,cAAA,wBAAkB,gBACsC;UAAxBD,EAAA,CAAA0D,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAG/CxB,EAFJ,CAAAC,cAAA,eAAyB,0BACiC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAAmE;UACnEJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA4D,UAAA,KAAAC,oCAAA,wBAAmE;UAGrE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAGuC;UACvCJ,EAAA,CAAAC,cAAA,kBAIsB;UAHdD,EAAA,CAAA0D,UAAA,mBAAAI,iDAAA;YAAA,OAAAL,GAAA,CAAA7C,YAAA,IAAA6C,GAAA,CAAA7C,YAAA;UAAA,EAAsC;UAI5CZ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAA4D,UAAA,KAAAG,oCAAA,wBAAmE;UAGrE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,wBACmB;UACzCD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAExDF,EAFwD,CAAAG,YAAA,EAAI,EACpD,EACF;UAGJH,EADF,CAAAC,cAAA,eAA0B,kBAK4B;UAElDD,EADA,CAAA4D,UAAA,KAAAI,sCAAA,0BAA6C,KAAAC,+BAAA,mBACpB;UAC3BjE,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAI+B;UADvBD,EAAA,CAAA0D,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAE/BrC,EAAA,CAAAE,MAAA,oBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACU;UAGjBH,EADF,CAAAC,cAAA,4BAAuC,SAClC;UAAAD,EAAA,CAAAE,MAAA,+BACD;UAAAF,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAG7DF,EAH6D,CAAAG,YAAA,EAAI,EACzD,EACa,EACV;UAGTH,EADF,CAAAC,cAAA,eAA2B,UACrB;UAAAD,EAAA,CAAAE,MAAA,4CAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EADF,CAAAC,cAAA,UAAI,UACE;UAAAD,EAAA,CAAAE,MAAA,sCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,sCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,mCAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oCAAuB;UAKnCF,EALmC,CAAAG,YAAA,EAAK,EAC7B,EACD,EACF,EACA,EACF;;;;;UA5EQH,EAAA,CAAAmE,SAAA,IAAuB;UAAvBnE,EAAA,CAAAoE,UAAA,cAAAX,GAAA,CAAA1C,SAAA,CAAuB;UAMXf,EAAA,CAAAmE,SAAA,GAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAC,OAAA,GAAAZ,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAD,OAAA,CAAAE,QAAA,aAAqD;UAQ1DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA7C,YAAA,uBAA2C;UAK1CZ,EAAA,CAAAmE,SAAA,EAAmC;;UAG/BnE,EAAA,CAAAmE,SAAA,GAAkD;UAAlDnE,EAAA,CAAAwE,iBAAA,CAAAf,GAAA,CAAA7C,YAAA,mCAAkD;UAElDZ,EAAA,CAAAmE,SAAA,EAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAK,OAAA,GAAAhB,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAG,OAAA,CAAAF,QAAA,aAAqD;UAkB3DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA1C,SAAA,CAAA2D,OAAA,IAAAjB,GAAA,CAAA5C,SAAA,CAA2C;UACnCb,EAAA,CAAAmE,SAAA,EAAe;UAAfnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA5C,SAAA,CAAe;UACtBb,EAAA,CAAAmE,SAAA,EAAgB;UAAhBnE,EAAA,CAAAoE,UAAA,UAAAX,GAAA,CAAA5C,SAAA,CAAgB;UAOjBb,EAAA,CAAAmE,SAAA,EAAsB;UAAtBnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA5C,SAAA,CAAsB;;;qBA1F1CzB,YAAY,EAAAuF,EAAA,CAAAC,IAAA,EACZvF,mBAAmB,EAAAoD,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,kBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EACnB3F,aAAa,EAAA4F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EACbhG,kBAAkB,EAAAiG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBpG,cAAc,EAAAqG,EAAA,CAAAC,QAAA,EACdrG,eAAe,EAAAsG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfvG,aAAa,EAAAwG,GAAA,CAAAC,OAAA,EACbxG,wBAAwB,EAAAyG,GAAA,CAAAC,kBAAA,EACxBzG,iBAAiB,EACjBC,iBAAiB,EAAAyG,GAAA,CAAAC,WAAA,EACjBzG,gBAAgB,EAAA0G,GAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}