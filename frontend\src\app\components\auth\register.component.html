<div class="register-container">
  <!-- Background Pattern -->
  <div class="background-pattern"></div>

  <!-- Header -->
  <header class="app-header">
    <div class="header-content">
      <div class="brand">
        <mat-icon class="brand-icon">shield</mat-icon>
        <span class="brand-text">SPT</span>
      </div>
      <div class="header-actions">
        <button mat-button routerLink="/login" class="header-link">
          Sign In
        </button>
      </div>
    </div>
  </header>

  <div class="register-content">
    <div class="register-card-wrapper">
      <mat-card class="register-card">
        <mat-card-header class="register-header">
          <div class="logo">
            <div class="logo-icon-wrapper">
              <mat-icon class="logo-icon">security</mat-icon>
            </div>
            <div class="logo-text">
              <h1>Create Account</h1>
              <p class="subtitle">Join SPT Security Platform</p>
            </div>
          </div>
        </mat-card-header>

      <mat-card-content>
        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
          <div class="form-fields">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Email</mat-label>
              <input matInput type="email" formControlName="email" autocomplete="email">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                Please enter a valid email
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Username</mat-label>
              <input matInput formControlName="username" autocomplete="username">
              <mat-icon matSuffix>account_circle</mat-icon>
              <mat-error *ngIf="registerForm.get('username')?.hasError('required')">
                Username is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('username')?.hasError('minlength')">
                Username must be at least 3 characters
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Password</mat-label>
              <input matInput 
                     [type]="hidePassword ? 'password' : 'text'" 
                     formControlName="password"
                     autocomplete="new-password">
              <button mat-icon-button matSuffix 
                      (click)="hidePassword = !hidePassword" 
                      [attr.aria-label]="'Hide password'" 
                      [attr.aria-pressed]="hidePassword"
                      type="button">
                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                Password must be at least 8 characters
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Confirm Password</mat-label>
              <input matInput 
                     [type]="hideConfirmPassword ? 'password' : 'text'" 
                     formControlName="confirmPassword"
                     autocomplete="new-password">
              <button mat-icon-button matSuffix 
                      (click)="hideConfirmPassword = !hideConfirmPassword" 
                      [attr.aria-label]="'Hide password'" 
                      [attr.aria-pressed]="hideConfirmPassword"
                      type="button">
                <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                Please confirm your password
              </mat-error>
              <mat-error *ngIf="registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched">
                Passwords do not match
              </mat-error>
            </mat-form-field>

            <div class="form-options">
              <mat-checkbox formControlName="agreeToTerms">
                I agree to the <a href="#" class="terms-link">Terms of Service</a> and <a href="#" class="terms-link">Privacy Policy</a>
              </mat-checkbox>
            </div>
          </div>

          <div class="form-actions">
            <button mat-raised-button 
                    color="primary" 
                    type="submit" 
                    class="register-button"
                    [disabled]="registerForm.invalid || isLoading">
              <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
              <span *ngIf="!isLoading">Create Account</span>
            </button>
          </div>
        </form>
      </mat-card-content>

      <mat-card-actions class="card-actions">
        <p>Already have an account? 
          <a routerLink="/login" class="login-link">Sign in</a>
        </p>
      </mat-card-actions>
    </mat-card>

    <!-- Features Section -->
    <div class="features-section">
      <div class="features-header">
        <mat-icon class="features-icon">security</mat-icon>
        <h2>Security Features</h2>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <mat-icon>analytics</mat-icon>
        </div>
        <div class="feature-content">
          <h3>Smart Contract Analysis</h3>
          <p>Advanced static analysis for solidity contracts</p>
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <mat-icon>visibility</mat-icon>
        </div>
        <div class="feature-content">
          <h3>Real-time Monitoring</h3>
          <p>Continuous security monitoring</p>
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <mat-icon>assessment</mat-icon>
        </div>
        <div class="feature-content">
          <h3>Comprehensive Analytics</h3>
          <p>Detailed security reports and insights</p>
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <mat-icon>business</mat-icon>
        </div>
        <div class="feature-content">
          <h3>Enterprise Security</h3>
          <p>Professional-grade protection</p>
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <mat-icon>integration_instructions</mat-icon>
        </div>
        <div class="feature-content">
          <h3>Easy Integration</h3>
          <p>Seamless API and SDK integration</p>
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <mat-icon>devices</mat-icon>
        </div>
        <div class="feature-content">
          <h3>Multi-platform Support</h3>
          <p>Works across all major platforms</p>
        </div>
      </div>
    </div>
  </div>
</div>
