<div class="register-container">
  <div class="register-card-wrapper">
    <mat-card class="register-card">
      <mat-card-header class="register-header">
        <div class="logo">
          <mat-icon class="logo-icon">security</mat-icon>
          <h1>Create Account</h1>
        </div>
        <mat-card-subtitle>Join SPT Security Platform</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
          <div class="form-fields">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Full Name</mat-label>
              <input matInput formControlName="fullName" autocomplete="name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="registerForm.get('fullName')?.hasError('required')">
                Full name is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Email</mat-label>
              <input matInput type="email" formControlName="email" autocomplete="email">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                Please enter a valid email
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Username</mat-label>
              <input matInput formControlName="username" autocomplete="username">
              <mat-icon matSuffix>account_circle</mat-icon>
              <mat-error *ngIf="registerForm.get('username')?.hasError('required')">
                Username is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('username')?.hasError('minlength')">
                Username must be at least 3 characters
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Password</mat-label>
              <input matInput 
                     [type]="hidePassword ? 'password' : 'text'" 
                     formControlName="password"
                     autocomplete="new-password">
              <button mat-icon-button matSuffix 
                      (click)="hidePassword = !hidePassword" 
                      [attr.aria-label]="'Hide password'" 
                      [attr.aria-pressed]="hidePassword"
                      type="button">
                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                Password must be at least 8 characters
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Confirm Password</mat-label>
              <input matInput 
                     [type]="hideConfirmPassword ? 'password' : 'text'" 
                     formControlName="confirmPassword"
                     autocomplete="new-password">
              <button mat-icon-button matSuffix 
                      (click)="hideConfirmPassword = !hideConfirmPassword" 
                      [attr.aria-label]="'Hide password'" 
                      [attr.aria-pressed]="hideConfirmPassword"
                      type="button">
                <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                Please confirm your password
              </mat-error>
              <mat-error *ngIf="registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched">
                Passwords do not match
              </mat-error>
            </mat-form-field>

            <div class="form-options">
              <mat-checkbox formControlName="agreeToTerms">
                I agree to the <a href="#" class="terms-link">Terms of Service</a> and <a href="#" class="terms-link">Privacy Policy</a>
              </mat-checkbox>
            </div>
          </div>

          <div class="form-actions">
            <button mat-raised-button 
                    color="primary" 
                    type="submit" 
                    class="register-button"
                    [disabled]="registerForm.invalid || isLoading">
              <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
              <span *ngIf="!isLoading">Create Account</span>
            </button>
          </div>
        </form>
      </mat-card-content>

      <mat-card-actions class="card-actions">
        <p>Already have an account? 
          <a routerLink="/login" class="login-link">Sign in</a>
        </p>
      </mat-card-actions>
    </mat-card>

    <div class="benefits-info">
      <h3>🚀 Why Choose SPT?</h3>
      <ul>
        <li>🔍 Advanced vulnerability detection</li>
        <li>📊 Real-time security monitoring</li>
        <li>📈 Comprehensive analytics</li>
        <li>🛡️ Enterprise-grade security</li>
        <li>🔧 Easy integration</li>
        <li>📱 Multi-platform support</li>
      </ul>
    </div>
  </div>
</div>
