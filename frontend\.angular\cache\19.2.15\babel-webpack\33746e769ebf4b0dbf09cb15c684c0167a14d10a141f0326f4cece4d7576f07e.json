{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/expansion\";\nimport * as i6 from \"@angular/material/checkbox\";\nfunction SecurityPracticesComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const principle_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", principle_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(principle_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(principle_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(principle_r1.description);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h4\")(2, \"mat-icon\", 28);\n    i0.ɵɵtext(3, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Bad Practice \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const practice_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(practice_r2.badExample);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"h4\")(2, \"mat-icon\", 31);\n    i0.ɵɵtext(3, \"check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Good Practice \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const practice_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(practice_r2.goodExample);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, SecurityPracticesComponent_mat_expansion_panel_28_div_11_div_1_Template, 9, 1, \"div\", 25)(2, SecurityPracticesComponent_mat_expansion_panel_28_div_11_div_2_Template, 9, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const practice_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r2.badExample);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r2.goodExample);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_div_12_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tool_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tool_r3);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h4\");\n    i0.ɵɵtext(2, \"Recommended Tools\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, SecurityPracticesComponent_mat_expansion_panel_28_div_12_span_4_Template, 2, 1, \"span\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const practice_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", practice_r2.tools);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_div_13_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ref_r4);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"h4\");\n    i0.ɵɵtext(2, \"References\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 38);\n    i0.ɵɵtemplate(4, SecurityPracticesComponent_mat_expansion_panel_28_div_13_li_4_Template, 2, 1, \"li\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const practice_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", practice_r2.references);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 19)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SecurityPracticesComponent_mat_expansion_panel_28_div_11_Template, 3, 2, \"div\", 21)(12, SecurityPracticesComponent_mat_expansion_panel_28_div_12_Template, 5, 1, \"div\", 22)(13, SecurityPracticesComponent_mat_expansion_panel_28_div_13_Template, 5, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const practice_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"spt-badge spt-badge-\" + practice_r2.severity);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r2.severity.toUpperCase(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r2.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", practice_r2.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(practice_r2.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r2.badExample || practice_r2.goodExample);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r2.tools && practice_r2.tools.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r2.references && practice_r2.references.length > 0);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_36_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h4\");\n    i0.ɵɵtext(2, \"Implementation Example\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"pre\")(5, \"code\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const practice_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(practice_r5.codeExample);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_36_div_12_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tool_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tool_r6);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_36_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h4\");\n    i0.ɵɵtext(2, \"Recommended Tools\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, SecurityPracticesComponent_mat_expansion_panel_36_div_12_span_4_Template, 2, 1, \"span\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const practice_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", practice_r5.tools);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 19)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SecurityPracticesComponent_mat_expansion_panel_36_div_11_Template, 7, 1, \"div\", 21)(12, SecurityPracticesComponent_mat_expansion_panel_36_div_12_Template, 5, 1, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const practice_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"spt-badge spt-badge-\" + practice_r5.severity);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r5.severity.toUpperCase(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r5.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", practice_r5.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(practice_r5.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r5.codeExample);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r5.tools && practice_r5.tools.length > 0);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_44_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h4\");\n    i0.ɵɵtext(2, \"Example\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"pre\")(5, \"code\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const practice_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(practice_r7.codeExample);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_44_div_12_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tool_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tool_r8);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_44_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h4\");\n    i0.ɵɵtext(2, \"Recommended Tools\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, SecurityPracticesComponent_mat_expansion_panel_44_div_12_span_4_Template, 2, 1, \"span\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const practice_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", practice_r7.tools);\n  }\n}\nfunction SecurityPracticesComponent_mat_expansion_panel_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 19)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SecurityPracticesComponent_mat_expansion_panel_44_div_11_Template, 7, 1, \"div\", 21)(12, SecurityPracticesComponent_mat_expansion_panel_44_div_12_Template, 5, 1, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const practice_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"spt-badge spt-badge-\" + practice_r7.severity);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r7.severity.toUpperCase(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r7.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", practice_r7.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(practice_r7.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r7.codeExample);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", practice_r7.tools && practice_r7.tools.length > 0);\n  }\n}\nfunction SecurityPracticesComponent_mat_card_52_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"mat-checkbox\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9);\n  }\n}\nfunction SecurityPracticesComponent_mat_card_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 41)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 42);\n    i0.ɵɵtemplate(10, SecurityPracticesComponent_mat_card_52_div_10_Template, 3, 1, \"div\", 43);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const section_r10 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(section_r10.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r10.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", section_r10.items);\n  }\n}\nexport let SecurityPracticesComponent = /*#__PURE__*/(() => {\n  class SecurityPracticesComponent {\n    constructor() {\n      this.securityPrinciples = [{\n        title: 'Defense in Depth',\n        description: 'Implement multiple layers of security controls',\n        icon: 'layers',\n        color: '#1976d2'\n      }, {\n        title: 'Secure by Default',\n        description: 'Start with secure configurations and settings',\n        icon: 'lock',\n        color: '#4caf50'\n      }, {\n        title: 'Fail Securely',\n        description: 'Handle errors gracefully without exposing sensitive data',\n        icon: 'error_outline',\n        color: '#ff9800'\n      }, {\n        title: 'Least Privilege',\n        description: 'Grant minimum necessary permissions',\n        icon: 'person_remove',\n        color: '#9c27b0'\n      }];\n      this.ethereumPractices = [{\n        title: 'Reentrancy Protection',\n        description: 'Prevent reentrancy attacks using checks-effects-interactions pattern',\n        severity: 'critical',\n        category: 'Smart Contracts',\n        badExample: `// Vulnerable to reentrancy\nfunction withdraw(uint amount) public {\n    require(balances[msg.sender] >= amount);\n    msg.sender.call{value: amount}(\"\");\n    balances[msg.sender] -= amount;\n}`,\n        goodExample: `// Protected against reentrancy\nfunction withdraw(uint amount) public {\n    require(balances[msg.sender] >= amount);\n    balances[msg.sender] -= amount;\n    msg.sender.call{value: amount}(\"\");\n}`,\n        tools: ['Slither', 'Mythril', 'OpenZeppelin ReentrancyGuard'],\n        references: ['SWC-107: Reentrancy', 'OpenZeppelin Security Patterns']\n      }, {\n        title: 'Integer Overflow Protection',\n        description: 'Use SafeMath or Solidity 0.8+ for overflow protection',\n        severity: 'high',\n        category: 'Smart Contracts',\n        goodExample: `pragma solidity ^0.8.0; // Built-in overflow protection\n\n// Or use OpenZeppelin SafeMath\nimport \"@openzeppelin/contracts/utils/math/SafeMath.sol\";\n\ncontract SafeContract {\n    using SafeMath for uint256;\n    \n    function safeAdd(uint256 a, uint256 b) public pure returns (uint256) {\n        return a.add(b); // Will revert on overflow\n    }\n}`,\n        tools: ['OpenZeppelin SafeMath', 'Solidity 0.8+'],\n        references: ['SWC-101: Integer Overflow']\n      }];\n      this.bitcoinPractices = [{\n        title: 'Private Key Management',\n        description: 'Secure storage and handling of private keys',\n        severity: 'critical',\n        category: 'Wallet Security',\n        codeExample: `// Use environment variables or secure key management\nconst privateKey = process.env.BITCOIN_PRIVATE_KEY;\n\n// Never hardcode private keys\n// const privateKey = \"L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ\"; // BAD!\n\n// Use hardware wallets for production\nconst wallet = new HardwareWallet({\n    device: 'ledger',\n    derivationPath: \"m/44'/0'/0'/0/0\"\n});`,\n        tools: ['Hardware Wallets', 'HashiCorp Vault', 'AWS Secrets Manager']\n      }, {\n        title: 'Multisig Implementation',\n        description: 'Use multisig wallets for enhanced security',\n        severity: 'high',\n        category: 'Wallet Security',\n        codeExample: `// Use at least 2-of-3 multisig for significant amounts\nconst multisigAddress = bitcoin.payments.p2sh({\n    redeem: bitcoin.payments.p2ms({\n        m: 2, // Required signatures\n        pubkeys: [pubkey1, pubkey2, pubkey3]\n    })\n});`,\n        tools: ['Bitcoin Core', 'Electrum', 'BTCPay Server']\n      }];\n      this.generalPractices = [{\n        title: 'Environment Security',\n        description: 'Secure configuration and environment management',\n        severity: 'high',\n        category: 'Environment',\n        codeExample: `# Use environment variables for secrets\nexport PRIVATE_KEY=\"your-private-key\"\nexport API_KEY=\"your-api-key\"\nexport DATABASE_URL=\"your-database-url\"\n\n# .gitignore - Never commit sensitive files\n.env\n.env.local\n.env.production\n*.key\n*.pem\nwallet.dat`,\n        tools: ['dotenv', 'HashiCorp Vault', 'AWS Secrets Manager']\n      }, {\n        title: 'Dependency Management',\n        description: 'Regular security audits of dependencies',\n        severity: 'medium',\n        category: 'Dependencies',\n        codeExample: `{\n  \"scripts\": {\n    \"audit\": \"npm audit\",\n    \"audit-fix\": \"npm audit fix\",\n    \"security-check\": \"npm audit --audit-level moderate\"\n  }\n}\n\n# Run regular security checks\nnpm audit\ngo mod tidy\ncargo audit`,\n        tools: ['npm audit', 'Snyk', 'OWASP Dependency Check']\n      }];\n      this.checklistSections = [{\n        title: 'Smart Contract Security',\n        description: 'Essential checks for smart contracts',\n        icon: 'smart_toy',\n        items: ['Implement reentrancy protection', 'Use SafeMath or Solidity 0.8+', 'Validate all inputs', 'Implement proper access controls', 'Test with comprehensive test suite', 'Conduct security audit', 'Use established patterns and libraries']\n      }, {\n        title: 'Wallet Security',\n        description: 'Wallet and key management security',\n        icon: 'account_balance_wallet',\n        items: ['Use hardware wallets for production', 'Implement multisig for large amounts', 'Never hardcode private keys', 'Use secure key derivation', 'Implement proper backup procedures', 'Test recovery procedures', 'Monitor for suspicious activity']\n      }, {\n        title: 'Environment Security',\n        description: 'Development and deployment security',\n        icon: 'cloud_circle',\n        items: ['Use environment variables for secrets', 'Enable HTTPS/TLS everywhere', 'Implement proper logging', 'Set up monitoring and alerting', 'Regular security updates', 'Network segmentation', 'Incident response plan']\n      }, {\n        title: 'Code Security',\n        description: 'Secure coding practices',\n        icon: 'code',\n        items: ['Mandatory code reviews', 'Automated security scanning', 'Regular dependency updates', 'Input validation everywhere', 'Error handling without information leakage', 'Secure communication protocols', 'Regular penetration testing']\n      }];\n    }\n    static {\n      this.ɵfac = function SecurityPracticesComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SecurityPracticesComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SecurityPracticesComponent,\n        selectors: [[\"app-security-practices\"]],\n        decls: 53,\n        vars: 5,\n        consts: [[1, \"security-practices-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [1, \"practices-overview\"], [1, \"overview-card\"], [\"mat-card-avatar\", \"\"], [1, \"principles-grid\"], [\"class\", \"principle\", 4, \"ngFor\", \"ngForOf\"], [\"animationDuration\", \"300ms\", 1, \"practices-tabs\"], [\"label\", \"Ethereum Security\"], [1, \"tab-content\"], [1, \"practices-list\"], [\"class\", \"practice-panel\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Bitcoin Security\"], [\"label\", \"General Security\"], [\"label\", \"Security Checklist\"], [1, \"checklist-sections\"], [\"class\", \"checklist-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"principle\"], [1, \"practice-panel\"], [1, \"practice-content\"], [\"class\", \"code-examples\", 4, \"ngIf\"], [\"class\", \"tools-section\", 4, \"ngIf\"], [\"class\", \"references-section\", 4, \"ngIf\"], [1, \"code-examples\"], [\"class\", \"bad-example\", 4, \"ngIf\"], [\"class\", \"good-example\", 4, \"ngIf\"], [1, \"bad-example\"], [1, \"error-icon\"], [1, \"code-block\", \"bad\"], [1, \"good-example\"], [1, \"success-icon\"], [1, \"code-block\", \"good\"], [1, \"tools-section\"], [1, \"tools-chips\"], [\"class\", \"spt-badge spt-badge-info\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-badge\", \"spt-badge-info\"], [1, \"references-section\"], [1, \"references-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"code-block\"], [1, \"checklist-card\"], [1, \"checklist-items\"], [\"class\", \"checklist-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"checklist-item\"]],\n        template: function SecurityPracticesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(5, \" Security Best Practices \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 2);\n            i0.ɵɵtext(7, \" Comprehensive security guidelines for blockchain development \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-header\")(11, \"mat-icon\", 5);\n            i0.ɵɵtext(12, \"shield\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-title\");\n            i0.ɵɵtext(14, \"Security Principles\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"mat-card-subtitle\");\n            i0.ɵɵtext(16, \"Core principles for secure blockchain development\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"mat-card-content\")(18, \"div\", 6);\n            i0.ɵɵtemplate(19, SecurityPracticesComponent_div_19_Template, 8, 5, \"div\", 7);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(20, \"mat-tab-group\", 8)(21, \"mat-tab\", 9)(22, \"div\", 10)(23, \"h2\");\n            i0.ɵɵtext(24, \"Smart Contract Security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"p\");\n            i0.ɵɵtext(26, \"Essential security practices for Ethereum smart contract development.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 11);\n            i0.ɵɵtemplate(28, SecurityPracticesComponent_mat_expansion_panel_28_Template, 14, 9, \"mat-expansion-panel\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"mat-tab\", 13)(30, \"div\", 10)(31, \"h2\");\n            i0.ɵɵtext(32, \"Bitcoin Application Security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p\");\n            i0.ɵɵtext(34, \"Security best practices for Bitcoin application development.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 11);\n            i0.ɵɵtemplate(36, SecurityPracticesComponent_mat_expansion_panel_36_Template, 13, 8, \"mat-expansion-panel\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"mat-tab\", 14)(38, \"div\", 10)(39, \"h2\");\n            i0.ɵɵtext(40, \"General Development Security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"p\");\n            i0.ɵɵtext(42, \"Universal security practices for blockchain development environments.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 11);\n            i0.ɵɵtemplate(44, SecurityPracticesComponent_mat_expansion_panel_44_Template, 13, 8, \"mat-expansion-panel\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"mat-tab\", 15)(46, \"div\", 10)(47, \"h2\");\n            i0.ɵɵtext(48, \"Security Checklist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"p\");\n            i0.ɵɵtext(50, \"Use this checklist to ensure your blockchain application follows security best practices.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 16);\n            i0.ɵɵtemplate(52, SecurityPracticesComponent_mat_card_52_Template, 11, 4, \"mat-card\", 17);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"ngForOf\", ctx.securityPrinciples);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.ethereumPractices);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.bitcoinPractices);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.generalPractices);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.checklistSections);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatExpansionModule, i5.MatExpansionPanel, i5.MatExpansionPanelHeader, i5.MatExpansionPanelTitle, i5.MatExpansionPanelDescription, MatChipsModule, MatCheckboxModule, i6.MatCheckbox],\n        styles: [\".security-practices-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.page-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;color:#1976d2;margin:0 0 8px}.page-subtitle[_ngcontent-%COMP%]{color:#666;font-size:1.1em;margin:0}.practices-overview[_ngcontent-%COMP%]{margin-bottom:32px}.overview-card[_ngcontent-%COMP%]{border:1px solid #e0e0e0}.principles-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:16px;margin-top:16px}.principle[_ngcontent-%COMP%]{padding:16px;background:#f9f9f9;border-radius:8px}.practices-tabs[_ngcontent-%COMP%]{margin-bottom:32px}.tab-content[_ngcontent-%COMP%]{padding:24px 0}.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:8px}.practices-list[_ngcontent-%COMP%]{margin-top:24px}.practice-panel[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-3);border:1px solid var(--spt-gray-200);border-radius:var(--spt-radius-xl);box-shadow:var(--spt-shadow-sm);transition:all .2s ease}.practice-panel[_ngcontent-%COMP%]:hover{box-shadow:var(--spt-shadow-md);border-color:var(--spt-primary-300)}.practice-content[_ngcontent-%COMP%]{padding:16px 0}.code-examples[_ngcontent-%COMP%]{margin:24px 0}.bad-example[_ngcontent-%COMP%], .good-example[_ngcontent-%COMP%]{margin-bottom:16px}.bad-example[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .good-example[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 8px}.error-icon[_ngcontent-%COMP%]{color:#f44336}.success-icon[_ngcontent-%COMP%]{color:#4caf50}.code-block[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;overflow:hidden}.code-block.bad[_ngcontent-%COMP%]{border-color:#f44336;background:#ffebee}.code-block.good[_ngcontent-%COMP%]{border-color:#4caf50;background:#e8f5e8}.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{margin:0;padding:16px;overflow-x:auto}.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:.9em}.tools-section[_ngcontent-%COMP%], .references-section[_ngcontent-%COMP%]{margin-top:24px}.tools-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .references-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#1976d2}.tools-chips[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:var(--spt-space-2);margin-top:var(--spt-space-2)}.references-list[_ngcontent-%COMP%]{margin:0;padding-left:20px}.references-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:4px;color:#666}.checklist-sections[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:24px;margin-top:24px}.checklist-card[_ngcontent-%COMP%]{height:fit-content}.checklist-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.checklist-item[_ngcontent-%COMP%]{display:flex;align-items:center}@media (max-width: 768px){.principles-grid[_ngcontent-%COMP%], .checklist-sections[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return SecurityPracticesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}