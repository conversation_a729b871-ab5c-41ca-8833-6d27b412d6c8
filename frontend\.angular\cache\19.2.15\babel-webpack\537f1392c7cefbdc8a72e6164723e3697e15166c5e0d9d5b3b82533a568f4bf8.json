{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule, NavigationEnd } from '@angular/router';\nimport { filter, map, startWith } from 'rxjs/operators';\nimport { NavigationComponent } from './shared/navigation/navigation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction AppComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"app-navigation\");\n    i0.ɵɵelementStart(2, \"main\", 7);\n    i0.ɵɵelement(3, \"router-outlet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppComponent_div_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, AppComponent_div_2_div_1_Template, 4, 0, \"div\", 5);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵtemplate(3, AppComponent_div_2_ng_template_3_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const loginView_r1 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 2, ctx_r1.currentUser$))(\"ngIfElse\", loginView_r1);\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.title = 'SPT - Blockchain Security Protocol Tool';\n      this.currentUser$ = this.authService.currentUser$;\n      // Check if current route is documentation - include initial route\n      this.isDocumentationRoute$ = this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(event => event.url.startsWith('/doc')),\n      // Start with current URL check\n      startWith(this.router.url.startsWith('/doc')));\n    }\n    ngOnInit() {\n      // Initialize component\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        decls: 4,\n        vars: 6,\n        consts: [[\"loginView\", \"\"], [\"class\", \"doc-container\", 4, \"ngIf\"], [\"class\", \"app-container\", 4, \"ngIf\"], [1, \"doc-container\"], [1, \"app-container\"], [\"class\", \"authenticated-layout\", 4, \"ngIf\", \"ngIfElse\"], [1, \"authenticated-layout\"], [1, \"main-content\"], [1, \"login-layout\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AppComponent_div_0_Template, 2, 0, \"div\", 1);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵtemplate(2, AppComponent_div_2_Template, 5, 4, \"div\", 2);\n            i0.ɵɵpipe(3, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 2, ctx.isDocumentationRoute$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(3, 4, ctx.isDocumentationRoute$));\n          }\n        },\n        dependencies: [CommonModule, i3.NgIf, i3.AsyncPipe, RouterOutlet, RouterModule, NavigationComponent],\n        styles: [\".app-container[_ngcontent-%COMP%]{height:100vh;overflow:hidden;background:var(--spt-bg-primary)}.doc-container[_ngcontent-%COMP%], .login-layout[_ngcontent-%COMP%]{height:100vh;background:var(--spt-bg-primary)}.authenticated-layout[_ngcontent-%COMP%]{height:100vh;display:flex;flex-direction:column;background:var(--spt-bg-primary)}.main-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto;background:var(--spt-bg-secondary);min-height:0}@media (max-width: 768px){.authenticated-layout[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}