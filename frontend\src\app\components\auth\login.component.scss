// Clean, modern auth design
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;

    mat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      color: #667eea;
    }

    span {
      font-size: 24px;
      font-weight: 700;
      color: #1a202c;
    }
  }

  h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    color: #1a202c;
  }

  p {
    margin: 0;
    color: #64748b;
    font-size: 16px;
  }
}

.brand-text {
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.header-link {
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.header-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.login-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 60px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.login-card-wrapper {
  flex: 0 0 400px;
  max-width: 400px;
}

.login-card {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  padding: 32px 32px 0 32px;
  text-align: center;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.logo-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: white;
}

.logo-text h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
}

.subtitle {
  margin: 8px 0 0 0;
  color: #64748b;
  font-size: 16px;
  line-height: 1.4;
}

mat-card-content {
  padding: 32px;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.full-width {
  width: 100%;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.forgot-password:hover {
  text-decoration: underline;
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.login-button {
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.demo-button {
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  color: #667eea;
  border: 2px solid #667eea;
  transition: all 0.3s ease;
}

.demo-button:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

.card-actions {
  padding: 0 32px 32px 32px;
  text-align: center;
}

.card-actions p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

.register-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.register-link:hover {
  text-decoration: underline;
}

.features-info {
  flex: 1;
  max-width: 600px;
  color: white;
}

.features-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.features-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #fbbf24;
}

.features-header h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.feature-item mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #fbbf24;
  margin-top: 4px;
}

.feature-item h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.3;
}

.feature-item p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.9;
}

.stats {
  display: flex;
  justify-content: space-around;
  gap: 24px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: #fbbf24;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .login-content {
    flex-direction: column;
    gap: 40px;
  }

  .features-info {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 12px 16px;
  }

  .login-content {
    padding: 20px 16px;
  }

  .login-card-wrapper {
    flex: none;
    max-width: 100%;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stats {
    flex-direction: column;
    gap: 16px;
  }

  .logo-text h1 {
    font-size: 24px;
  }

  mat-card-content {
    padding: 24px;
  }

  .login-header {
    padding: 24px 24px 0 24px;
  }

  .card-actions {
    padding: 0 24px 24px 24px;
  }
}

// Responsive design
@media (max-width: 480px) {
  .auth-container {
    padding: 16px;
  }

  .auth-card {
    padding: 32px 24px;
  }

  .auth-header h1 {
    font-size: 24px;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
