{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { DocumentationLayoutComponent } from './components/documentation-layout/documentation-layout.component';\nimport { DocumentationNavComponent } from './components/documentation-nav/documentation-nav.component';\nimport { GettingStartedComponent } from './components/getting-started/getting-started.component';\nimport { ApiReferenceComponent } from './components/api-reference/api-reference.component';\nimport { SecurityPracticesComponent } from './components/security-practices/security-practices.component';\nimport { CliGuideComponent } from './components/cli-guide/cli-guide.component';\nimport { VscodeExtensionComponent } from './components/vscode-extension/vscode-extension.component';\nimport { ArchitectureComponent } from './components/architecture/architecture.component';\nimport { OverviewComponent } from './components/overview/overview.component';\nconst routes = [{\n  path: '',\n  component: DocumentationLayoutComponent,\n  children: [{\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: 'overview',\n    component: OverviewComponent\n  }, {\n    path: 'getting-started',\n    component: GettingStartedComponent\n  }, {\n    path: 'api-reference',\n    component: ApiReferenceComponent\n  }, {\n    path: 'security-practices',\n    component: SecurityPracticesComponent\n  }, {\n    path: 'cli-guide',\n    component: CliGuideComponent\n  }, {\n    path: 'vscode-extension',\n    component: VscodeExtensionComponent\n  }, {\n    path: 'architecture',\n    component: ArchitectureComponent\n  }]\n}];\nlet DocumentationModule = class DocumentationModule {};\nDocumentationModule = __decorate([NgModule({\n  declarations: [DocumentationLayoutComponent, DocumentationNavComponent, GettingStartedComponent, ApiReferenceComponent, SecurityPracticesComponent, CliGuideComponent, VscodeExtensionComponent, ArchitectureComponent, OverviewComponent],\n  imports: [CommonModule, RouterModule.forChild(routes), MatSidenavModule, MatToolbarModule, MatListModule, MatIconModule, MatButtonModule, MatCardModule, MatTabsModule, MatExpansionModule, MatChipsModule, MatDividerModule, MatTableModule, MatBadgeModule, MatTooltipModule]\n})], DocumentationModule);\nexport { DocumentationModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}