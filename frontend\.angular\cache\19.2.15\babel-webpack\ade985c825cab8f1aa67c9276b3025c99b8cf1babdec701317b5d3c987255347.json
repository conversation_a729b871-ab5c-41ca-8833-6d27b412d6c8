{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/expansion\";\nimport * as i6 from \"@angular/material/chips\";\nimport * as i7 from \"@angular/material/table\";\nfunction CliGuideComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", feature_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r1.description);\n  }\n}\nfunction CliGuideComponent_mat_card_28_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_card_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 36)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 30)(10, \"div\", 31)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Commands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"pre\")(16, \"code\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, CliGuideComponent_mat_card_28_div_18_Template, 5, 1, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(method_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.description);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(method_r2.commands);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Flag\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r3.flag);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r4.type);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r5.description);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Default\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"code\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtemplate(1, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template, 2, 1, \"code\", 58)(2, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", option_r6.default);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 59);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 60);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"h4\");\n    i0.ɵɵtext(2, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"table\", 47);\n    i0.ɵɵelementContainerStart(4, 48);\n    i0.ɵɵtemplate(5, CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template, 2, 0, \"th\", 49)(6, CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template, 3, 1, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 51);\n    i0.ɵɵtemplate(8, CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template, 2, 0, \"th\", 49)(9, CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template, 3, 1, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 52);\n    i0.ɵɵtemplate(11, CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template, 2, 0, \"th\", 49)(12, CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template, 2, 1, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 53);\n    i0.ɵɵtemplate(14, CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template, 2, 0, \"th\", 49)(15, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template, 3, 2, \"td\", 50);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(16, CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template, 1, 0, \"tr\", 54)(17, CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template, 1, 0, \"tr\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dataSource\", command_r7.options);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r7.optionColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r7.optionColumns);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 31)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Output\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const example_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"p\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"div\", 31)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Command\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"pre\")(10, \"code\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template, 9, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const example_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(example_r9.description);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(example_r9.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"h4\");\n    i0.ɵɵtext(2, \"Examples\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template, 13, 3, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", command_r7.examples);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"h4\");\n    i0.ɵɵtext(2, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 39)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"code\", 40);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-panel-description\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 41)(8, \"div\", 42)(9, \"h4\");\n    i0.ɵɵtext(10, \"Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 30)(12, \"pre\")(13, \"code\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, CliGuideComponent_mat_expansion_panel_36_div_15_Template, 18, 3, \"div\", 43)(16, CliGuideComponent_mat_expansion_panel_36_div_16_Template, 4, 1, \"div\", 44)(17, CliGuideComponent_mat_expansion_panel_36_div_17_Template, 8, 1, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"spt \", command_r7.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", command_r7.description, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(command_r7.usage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.options.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.examples.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"mat-icon\", 77);\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"strong\");\n    i0.ɵɵtext(5, \"Implementation Notes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const integration_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(integration_r11.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_24_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"mat-icon\", 81);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r12 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r12);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"h5\");\n    i0.ɵɵtext(2, \"Quick Tips:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 79);\n    i0.ɵɵtemplate(4, CliGuideComponent_mat_expansion_panel_45_div_24_li_4_Template, 5, 1, \"li\", 80);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.getPlatformTips(integration_r11.platform));\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 69)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵelement(3, \"i\", 70);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-panel-description\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 71)(8, \"div\", 72)(9, \"h4\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 30)(12, \"div\", 31)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function CliGuideComponent_mat_expansion_panel_45_Template_button_click_17_listener() {\n      const integration_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.copyToClipboard(integration_r11.config));\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"pre\")(21, \"code\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(23, CliGuideComponent_mat_expansion_panel_45_div_23_Template, 8, 1, \"div\", 74)(24, CliGuideComponent_mat_expansion_panel_45_div_24_Template, 5, 1, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r11 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"expanded\", i_r13 === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"platform-icon \", integration_r11.icon, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", integration_r11.platform, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", integration_r11.description, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Configuration File: \", integration_r11.filename, \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(integration_r11.filename);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(integration_r11.config);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", integration_r11.notes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.getPlatformTips(integration_r11.platform).length > 0);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_52_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 87)(1, \"mat-icon\", 88);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r14 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r14);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 82)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"mat-icon\", 83);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 84)(9, \"ul\", 85);\n    i0.ɵɵtemplate(10, CliGuideComponent_mat_expansion_panel_52_li_10_Template, 5, 1, \"li\", 86);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const practice_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", practice_r15.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(practice_r15.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", practice_r15.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", practice_r15.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", practice_r15.tips);\n  }\n}\nfunction CliGuideComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"h4\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"div\", 31)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function CliGuideComponent_div_66_Template_button_click_9_listener() {\n      const env_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.copyToClipboard(env_r17.config));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"pre\")(13, \"code\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"p\", 91);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const env_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(env_r17.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(env_r17.type);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(env_r17.config);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(env_r17.description);\n  }\n}\nfunction CliGuideComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r18 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r18.key);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r18.description);\n  }\n}\nexport let CliGuideComponent = /*#__PURE__*/(() => {\n  class CliGuideComponent {\n    constructor() {\n      this.optionColumns = ['flag', 'type', 'description', 'default'];\n      this.cliFeatures = [{\n        title: 'Security Scanning',\n        description: 'Comprehensive security analysis for blockchain applications',\n        icon: 'security',\n        color: '#1976d2'\n      }, {\n        title: 'Multiple Formats',\n        description: 'Output results in JSON, YAML, CSV, or human-readable formats',\n        icon: 'description',\n        color: '#4caf50'\n      }, {\n        title: 'CI/CD Integration',\n        description: 'Easy integration with continuous integration pipelines',\n        icon: 'integration_instructions',\n        color: '#ff9800'\n      }, {\n        title: 'Configurable',\n        description: 'Flexible configuration options for different environments',\n        icon: 'tune',\n        color: '#9c27b0'\n      }];\n      this.installationMethods = [{\n        title: 'From Source',\n        description: 'Build from source code',\n        icon: 'code',\n        commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\ngo build -o spt cmd/main.go`,\n        notes: 'Requires Go 1.21+ to be installed'\n      }, {\n        title: 'Using Make',\n        description: 'Build using Makefile',\n        icon: 'build',\n        commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\nmake cli`,\n        notes: 'Binary will be created in build/ directory'\n      }, {\n        title: 'Go Install',\n        description: 'Install directly with Go',\n        icon: 'download',\n        commands: `go install github.com/blockchain-spt/cmd/spt@latest`,\n        notes: 'Installs to $GOPATH/bin'\n      }];\n      this.cliCommands = [{\n        name: 'scan',\n        description: 'Perform security scan on files or directories',\n        usage: 'spt scan [flags] [path]',\n        options: [{\n          flag: '--chain',\n          type: 'string',\n          description: 'Blockchain chain to analyze',\n          default: 'all'\n        }, {\n          flag: '--format',\n          type: 'string',\n          description: 'Output format (json, yaml, csv, table)',\n          default: 'table'\n        }, {\n          flag: '--output',\n          type: 'string',\n          description: 'Output file path'\n        }, {\n          flag: '--severity',\n          type: 'string',\n          description: 'Minimum severity level',\n          default: 'medium'\n        }, {\n          flag: '--recursive',\n          type: 'boolean',\n          description: 'Scan directories recursively',\n          default: 'true'\n        }],\n        examples: [{\n          command: 'spt scan ./contracts',\n          description: 'Scan all files in contracts directory',\n          output: `Scanning ./contracts...\nFound 3 issues:\n  HIGH: Potential reentrancy in contract.sol:42\n  MEDIUM: Unchecked return value in token.sol:15\n  MEDIUM: Gas optimization opportunity in utils.sol:8`\n        }, {\n          command: 'spt scan --chain ethereum --format json ./src',\n          description: 'Scan for Ethereum-specific issues and output as JSON'\n        }],\n        notes: 'Use --help flag with any command to see detailed usage information'\n      }, {\n        name: 'audit',\n        description: 'Perform comprehensive security audit',\n        usage: 'spt audit [flags] [path]',\n        options: [{\n          flag: '--generate-report',\n          type: 'boolean',\n          description: 'Generate detailed report',\n          default: 'false'\n        }, {\n          flag: '--report-path',\n          type: 'string',\n          description: 'Report output path',\n          default: './audit-report.html'\n        }, {\n          flag: '--template',\n          type: 'string',\n          description: 'Report template',\n          default: 'standard'\n        }],\n        examples: [{\n          command: 'spt audit --generate-report ./project',\n          description: 'Perform audit and generate HTML report'\n        }]\n      }, {\n        name: 'check',\n        description: 'Run specific security checks',\n        usage: 'spt check [subcommand] [flags] [path]',\n        options: [{\n          flag: '--fix',\n          type: 'boolean',\n          description: 'Attempt to fix issues automatically',\n          default: 'false'\n        }],\n        examples: [{\n          command: 'spt check deps --fix',\n          description: 'Check dependencies and fix known vulnerabilities'\n        }, {\n          command: 'spt check env',\n          description: 'Check environment configuration for security issues'\n        }]\n      }];\n      this.integrationExamples = [{\n        platform: 'GitHub Actions',\n        description: 'Integrate SPT into GitHub Actions workflow',\n        icon: 'fab fa-github',\n        filename: '.github/workflows/security.yml',\n        config: `name: Security Scan\non: [push, pull_request]\n\njobs:\n  security:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - uses: actions/setup-go@v3\n        with:\n          go-version: '1.21'\n      - name: Install SPT\n        run: go install github.com/blockchain-spt/cmd/spt@latest\n      - name: Run Security Scan\n        run: spt scan --format json --output security-report.json ./\n      - name: Upload Results\n        uses: actions/upload-artifact@v3\n        with:\n          name: security-report\n          path: security-report.json`,\n        notes: 'Add GITHUB_TOKEN to secrets for private repositories'\n      }, {\n        platform: 'GitLab CI',\n        description: 'Integrate SPT into GitLab CI/CD pipeline',\n        icon: 'fab fa-gitlab',\n        filename: '.gitlab-ci.yml',\n        config: `security_scan:\n  stage: test\n  image: golang:1.21\n  script:\n    - go install github.com/blockchain-spt/cmd/spt@latest\n    - spt scan --format json --output security-report.json ./\n    - spt audit --generate-report --report-path audit-report.html ./\n  artifacts:\n    reports:\n      junit: security-report.json\n    paths:\n      - audit-report.html\n    expire_in: 1 week\n  only:\n    - merge_requests\n    - main`,\n        notes: 'Configure artifact storage for report persistence'\n      }, {\n        platform: 'Azure DevOps',\n        description: 'Integrate SPT into Azure DevOps Pipeline',\n        icon: 'fab fa-microsoft',\n        filename: 'azure-pipelines.yml',\n        config: `trigger:\n  branches:\n    include:\n      - main\n      - develop\n  paths:\n    include:\n      - contracts/*\n      - src/*\n\npool:\n  vmImage: 'ubuntu-latest'\n\nvariables:\n  GO_VERSION: '1.21'\n  SPT_VERSION: 'latest'\n\nstages:\n- stage: SecurityScan\n  displayName: 'Security Analysis'\n  jobs:\n  - job: SPTScan\n    displayName: 'SPT Security Scan'\n    steps:\n    - task: GoTool@0\n      displayName: 'Install Go'\n      inputs:\n        version: '\\$(GO_VERSION)'\n\n    - script: |\n        go install github.com/blockchain-spt/cmd/spt@\\$(SPT_VERSION)\n        echo \"SPT installed successfully\"\n      displayName: 'Install SPT CLI'\n\n    - script: |\n        spt scan --format json --output \\$(Agent.TempDirectory)/security-report.json ./\n        spt audit --generate-report --report-path \\$(Agent.TempDirectory)/audit-report.html ./\n      displayName: 'Run Security Scan'\n      continueOnError: true\n\n    - task: PublishTestResults@2\n      displayName: 'Publish Security Results'\n      inputs:\n        testResultsFormat: 'JUnit'\n        testResultsFiles: '\\$(Agent.TempDirectory)/security-report.json'\n        testRunTitle: 'SPT Security Scan Results'\n      condition: always()\n\n    - task: PublishBuildArtifacts@1\n      displayName: 'Publish Security Reports'\n      inputs:\n        pathToPublish: '\\$(Agent.TempDirectory)'\n        artifactName: 'security-reports'\n        publishLocation: 'Container'\n      condition: always()\n\n    - script: |\n        if [ -f \"\\$(Agent.TempDirectory)/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' \\$(Agent.TempDirectory)/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' \\$(Agent.TempDirectory)/security-report.json)\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ] || [ \"\\$HIGH_COUNT\" -gt 5 ]; then\n            echo \"##vso[task.logissue type=error]Critical security issues found: \\$CRITICAL_COUNT critical, \\$HIGH_COUNT high\"\n            exit 1\n          fi\n        fi\n      displayName: 'Evaluate Security Results'\n      condition: always()`,\n        notes: 'Configure service connections for private repositories and adjust thresholds as needed'\n      }, {\n        platform: 'AWS CodeBuild',\n        description: 'Integrate SPT into AWS CodeBuild pipeline',\n        icon: 'fab fa-aws',\n        filename: 'buildspec.yml',\n        config: `version: 0.2\n\nenv:\n  variables:\n    GO_VERSION: \"1.21\"\n    SPT_VERSION: \"latest\"\n  parameter-store:\n    GITHUB_TOKEN: \"/spt/github-token\"  # Optional for private repos\n\nphases:\n  install:\n    runtime-versions:\n      golang: \\$GO_VERSION\n    commands:\n      - echo \"Installing SPT CLI...\"\n      - go install github.com/blockchain-spt/cmd/spt@\\$SPT_VERSION\n      - spt version\n\n  pre_build:\n    commands:\n      - echo \"Preparing security scan...\"\n      - mkdir -p reports\n      - echo \"Current directory contents:\"\n      - ls -la\n\n  build:\n    commands:\n      - echo \"Running SPT security scan...\"\n      - spt scan --format json --output reports/security-report.json ./\n      - spt audit --generate-report --report-path reports/audit-report.html ./\n      - echo \"Security scan completed\"\n\n  post_build:\n    commands:\n      - echo \"Processing security results...\"\n      - |\n        if [ -f \"reports/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' reports/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' reports/security-report.json)\n          MEDIUM_COUNT=\\$(jq '.summary.medium // 0' reports/security-report.json)\n\n          echo \"Security Summary:\"\n          echo \"  Critical: \\$CRITICAL_COUNT\"\n          echo \"  High: \\$HIGH_COUNT\"\n          echo \"  Medium: \\$MEDIUM_COUNT\"\n\n          # Fail build if critical issues found\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ]; then\n            echo \"Build failed due to critical security issues\"\n            exit 1\n          fi\n\n          # Warning for high issues\n          if [ \"\\$HIGH_COUNT\" -gt 10 ]; then\n            echo \"Warning: High number of high-severity issues (\\$HIGH_COUNT)\"\n          fi\n        else\n          echo \"Security report not found\"\n          exit 1\n        fi\n\nartifacts:\n  files:\n    - 'reports/**/*'\n  name: spt-security-reports\n\nreports:\n  spt-security:\n    files:\n      - 'reports/security-report.json'\n    file-format: 'JUNITXML'`,\n        notes: 'Store sensitive tokens in AWS Parameter Store or Secrets Manager'\n      }, {\n        platform: 'AWS CodePipeline',\n        description: 'Complete AWS CodePipeline with SPT integration',\n        icon: 'fab fa-aws',\n        filename: 'cloudformation-pipeline.yml',\n        config: `AWSTemplateFormatVersion: '2010-09-09'\nDescription: 'SPT Security Pipeline with CodePipeline'\n\nParameters:\n  GitHubRepo:\n    Type: String\n    Description: GitHub repository name\n  GitHubOwner:\n    Type: String\n    Description: GitHub repository owner\n  GitHubToken:\n    Type: String\n    NoEcho: true\n    Description: GitHub personal access token\n\nResources:\n  # S3 Bucket for artifacts\n  ArtifactsBucket:\n    Type: AWS::S3::Bucket\n    Properties:\n      BucketName: !Sub '\\${AWS::StackName}-spt-artifacts'\n      VersioningConfiguration:\n        Status: Enabled\n      PublicAccessBlockConfiguration:\n        BlockPublicAcls: true\n        BlockPublicPolicy: true\n        IgnorePublicAcls: true\n        RestrictPublicBuckets: true\n\n  # CodeBuild Project for SPT Security Scan\n  SPTSecurityProject:\n    Type: AWS::CodeBuild::Project\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-security'\n      ServiceRole: !GetAtt CodeBuildRole.Arn\n      Artifacts:\n        Type: CODEPIPELINE\n      Environment:\n        Type: LINUX_CONTAINER\n        ComputeType: BUILD_GENERAL1_MEDIUM\n        Image: aws/codebuild/amazonlinux2-x86_64-standard:3.0\n        EnvironmentVariables:\n          - Name: GITHUB_TOKEN\n            Value: !Ref GitHubToken\n            Type: PARAMETER_STORE\n      Source:\n        Type: CODEPIPELINE\n        BuildSpec: |\n          version: 0.2\n          phases:\n            install:\n              runtime-versions:\n                golang: 1.21\n              commands:\n                - go install github.com/blockchain-spt/cmd/spt@latest\n            build:\n              commands:\n                - mkdir -p reports\n                - spt scan --format json --output reports/security-report.json ./\n                - spt audit --generate-report --report-path reports/audit-report.html ./\n          artifacts:\n            files:\n              - 'reports/**/*'\n\n  # CodePipeline\n  SPTPipeline:\n    Type: AWS::CodePipeline::Pipeline\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-pipeline'\n      RoleArn: !GetAtt CodePipelineRole.Arn\n      ArtifactStore:\n        Type: S3\n        Location: !Ref ArtifactsBucket\n      Stages:\n        - Name: Source\n          Actions:\n            - Name: SourceAction\n              ActionTypeId:\n                Category: Source\n                Owner: ThirdParty\n                Provider: GitHub\n                Version: '1'\n              Configuration:\n                Owner: !Ref GitHubOwner\n                Repo: !Ref GitHubRepo\n                Branch: main\n                OAuthToken: !Ref GitHubToken\n              OutputArtifacts:\n                - Name: SourceOutput\n\n        - Name: SecurityScan\n          Actions:\n            - Name: SPTScan\n              ActionTypeId:\n                Category: Build\n                Owner: AWS\n                Provider: CodeBuild\n                Version: '1'\n              Configuration:\n                ProjectName: !Ref SPTSecurityProject\n              InputArtifacts:\n                - Name: SourceOutput\n              OutputArtifacts:\n                - Name: SecurityOutput\n\n  # IAM Roles (simplified - add specific permissions as needed)\n  CodeBuildRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codebuild.amazonaws.com\n            Action: sts:AssumeRole\n      ManagedPolicyArns:\n        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess\n      Policies:\n        - PolicyName: S3Access\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                Resource: !Sub '\\${ArtifactsBucket}/*'\n\n  CodePipelineRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codepipeline.amazonaws.com\n            Action: sts:AssumeRole\n      Policies:\n        - PolicyName: PipelinePolicy\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                  - s3:GetBucketVersioning\n                Resource:\n                  - !Sub '\\${ArtifactsBucket}'\n                  - !Sub '\\${ArtifactsBucket}/*'\n              - Effect: Allow\n                Action:\n                  - codebuild:BatchGetBuilds\n                  - codebuild:StartBuild\n                Resource: !GetAtt SPTSecurityProject.Arn`,\n        notes: 'Deploy using AWS CloudFormation. Customize IAM permissions based on your security requirements.'\n      }, {\n        platform: 'Docker',\n        description: 'Containerized SPT for consistent CI/CD environments',\n        icon: 'fab fa-docker',\n        filename: 'Dockerfile',\n        config: `# Multi-stage Dockerfile for SPT CLI\nFROM golang:1.21-alpine AS builder\n\n# Install dependencies\nRUN apk add --no-cache git ca-certificates\n\n# Set working directory\nWORKDIR /app\n\n# Install SPT CLI\nRUN go install github.com/blockchain-spt/cmd/spt@latest\n\n# Create final image\nFROM alpine:latest\n\n# Install runtime dependencies\nRUN apk add --no-cache ca-certificates jq curl\n\n# Copy SPT binary from builder\nCOPY --from=builder /go/bin/spt /usr/local/bin/spt\n\n# Create non-root user\nRUN addgroup -g 1001 spt && \\\\\n    adduser -D -u 1001 -G spt spt\n\n# Set working directory\nWORKDIR /workspace\n\n# Change ownership\nRUN chown -R spt:spt /workspace\n\n# Switch to non-root user\nUSER spt\n\n# Set entrypoint\nENTRYPOINT [\"spt\"]\nCMD [\"--help\"]\n\n# Usage examples:\n# docker build -t spt-cli .\n# docker run --rm -v \\$(pwd):/workspace spt-cli scan ./\n# docker run --rm -v \\$(pwd):/workspace spt-cli audit --generate-report ./`,\n        notes: 'Use this Docker image in any CI/CD system that supports containers'\n      }, {\n        platform: 'Jenkins',\n        description: 'Jenkins Pipeline with SPT integration',\n        icon: 'fab fa-jenkins',\n        filename: 'Jenkinsfile',\n        config: `pipeline {\n    agent any\n\n    environment {\n        GO_VERSION = '1.21'\n        SPT_VERSION = 'latest'\n        REPORTS_DIR = 'reports'\n    }\n\n    tools {\n        go 'go-1.21'  // Configure in Jenkins Global Tools\n    }\n\n    stages {\n        stage('Checkout') {\n            steps {\n                checkout scm\n                script {\n                    env.GIT_COMMIT_SHORT = sh(\n                        script: 'git rev-parse --short HEAD',\n                        returnStdout: true\n                    ).trim()\n                }\n            }\n        }\n\n        stage('Install SPT') {\n            steps {\n                sh '''\n                    echo \"Installing SPT CLI...\"\n                    go install github.com/blockchain-spt/cmd/spt@\\${SPT_VERSION}\n                    spt version\n                '''\n            }\n        }\n\n        stage('Security Scan') {\n            steps {\n                sh '''\n                    echo \"Creating reports directory...\"\n                    mkdir -p \\${REPORTS_DIR}\n\n                    echo \"Running SPT security scan...\"\n                    spt scan --format json --output \\${REPORTS_DIR}/security-report.json ./\n\n                    echo \"Generating audit report...\"\n                    spt audit --generate-report --report-path \\${REPORTS_DIR}/audit-report.html ./\n\n                    echo \"Security scan completed\"\n                '''\n            }\n            post {\n                always {\n                    // Archive artifacts\n                    archiveArtifacts artifacts: '\\${REPORTS_DIR}/**/*', fingerprint: true\n\n                    // Publish HTML reports\n                    publishHTML([\n                        allowMissing: false,\n                        alwaysLinkToLastBuild: true,\n                        keepAll: true,\n                        reportDir: '\\${REPORTS_DIR}',\n                        reportFiles: 'audit-report.html',\n                        reportName: 'SPT Security Report'\n                    ])\n                }\n            }\n        }\n\n        stage('Evaluate Results') {\n            steps {\n                script {\n                    if (fileExists(\"\\${REPORTS_DIR}/security-report.json\")) {\n                        def report = readJSON file: \"\\${REPORTS_DIR}/security-report.json\"\n                        def critical = report.summary?.critical ?: 0\n                        def high = report.summary?.high ?: 0\n                        def medium = report.summary?.medium ?: 0\n\n                        echo \"Security Summary:\"\n                        echo \"  Critical: \\${critical}\"\n                        echo \"  High: \\${high}\"\n                        echo \"  Medium: \\${medium}\"\n\n                        // Set build status based on results\n                        if (critical > 0) {\n                            currentBuild.result = 'FAILURE'\n                            error(\"Build failed due to \\${critical} critical security issues\")\n                        } else if (high > 10) {\n                            currentBuild.result = 'UNSTABLE'\n                            echo \"Build marked unstable due to \\${high} high-severity issues\"\n                        }\n\n                        // Add build description\n                        currentBuild.description = \"Critical: \\${critical}, High: \\${high}, Medium: \\${medium}\"\n                    } else {\n                        currentBuild.result = 'FAILURE'\n                        error(\"Security report not found\")\n                    }\n                }\n            }\n        }\n    }\n\n    post {\n        always {\n            // Clean workspace\n            cleanWs()\n        }\n        failure {\n            // Send notifications on failure\n            emailext (\n                subject: \"SPT Security Scan Failed: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan failed for commit \\${env.GIT_COMMIT_SHORT}. Check the build logs for details.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n        unstable {\n            // Send notifications on unstable builds\n            emailext (\n                subject: \"SPT Security Scan Unstable: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan completed with warnings for commit \\${env.GIT_COMMIT_SHORT}. Review the security report.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n    }\n}`,\n        notes: 'Configure Go tools and email notifications in Jenkins. Install required plugins: Pipeline, HTML Publisher, Email Extension.'\n      }];\n      this.cicdBestPractices = [{\n        title: 'Fail Fast Strategy',\n        description: 'Configure your pipeline to fail immediately on critical security issues to prevent vulnerable code from progressing.',\n        icon: 'block',\n        color: '#f44336',\n        tips: ['Set critical severity threshold to 0', 'Use exit codes to stop pipeline execution', 'Implement security gates at multiple stages', 'Configure notifications for security failures']\n      }, {\n        title: 'Artifact Management',\n        description: 'Properly store and manage security reports and artifacts for compliance and tracking.',\n        icon: 'inventory',\n        color: '#2196f3',\n        tips: ['Archive security reports for audit trails', 'Use versioned artifact storage', 'Implement retention policies', 'Enable artifact encryption for sensitive data']\n      }, {\n        title: 'Parallel Execution',\n        description: 'Optimize scan performance by running security checks in parallel with other tests.',\n        icon: 'call_split',\n        color: '#4caf50',\n        tips: ['Run security scans parallel to unit tests', 'Use matrix builds for multiple environments', 'Implement caching for faster scans', 'Configure resource limits appropriately']\n      }, {\n        title: 'Security Thresholds',\n        description: 'Define appropriate security thresholds based on your project maturity and risk tolerance.',\n        icon: 'security',\n        color: '#ff9800',\n        tips: ['Start with strict thresholds for new projects', 'Gradually improve legacy project thresholds', 'Use different thresholds for different branches', 'Document threshold decisions and rationale']\n      }, {\n        title: 'Integration Testing',\n        description: 'Test your CI/CD integration thoroughly before deploying to production pipelines.',\n        icon: 'verified',\n        color: '#9c27b0',\n        tips: ['Test with sample vulnerable code', 'Verify artifact generation and storage', 'Test notification mechanisms', 'Validate security gate functionality']\n      }, {\n        title: 'Monitoring & Alerting',\n        description: 'Implement comprehensive monitoring and alerting for your security pipeline.',\n        icon: 'notifications_active',\n        color: '#607d8b',\n        tips: ['Monitor pipeline execution times', 'Set up alerts for scan failures', 'Track security metrics over time', 'Implement dashboard for security trends']\n      }];\n      this.environmentConfigs = [{\n        name: 'Development Environment',\n        type: 'Environment Variables',\n        config: `# Development - More verbose, all severities\nexport SPT_SEVERITY_THRESHOLD=low\nexport SPT_OUTPUT_FORMAT=table\nexport SPT_COLORS=true\nexport SPT_VERBOSE=true\nexport SPT_PARALLEL_SCANS=2\nexport SPT_TIMEOUT=10m\nexport SPT_CACHE_ENABLED=true\nexport SPT_CACHE_DIR=~/.spt/cache`,\n        description: 'Development environment with verbose output and lower thresholds for learning and debugging.'\n      }, {\n        name: 'Staging Environment',\n        type: 'Environment Variables',\n        config: `# Staging - Production-like with medium threshold\nexport SPT_SEVERITY_THRESHOLD=medium\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=4\nexport SPT_TIMEOUT=15m\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_GENERATE_REPORTS=true`,\n        description: 'Staging environment that mirrors production settings with moderate security requirements.'\n      }, {\n        name: 'Production Environment',\n        type: 'Environment Variables',\n        config: `# Production - Strict security requirements\nexport SPT_SEVERITY_THRESHOLD=high\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=8\nexport SPT_TIMEOUT=30m\nexport SPT_FAIL_ON_CRITICAL=true\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_AUDIT_ENABLED=true\nexport SPT_COMPLIANCE_MODE=true`,\n        description: 'Production environment with strict security requirements and comprehensive auditing.'\n      }, {\n        name: 'Docker Configuration',\n        type: 'Docker Environment',\n        config: `# Docker container environment variables\nENV SPT_SEVERITY_THRESHOLD=medium\nENV SPT_OUTPUT_FORMAT=json\nENV SPT_PARALLEL_SCANS=4\nENV SPT_TIMEOUT=20m\nENV SPT_CACHE_ENABLED=false\nENV SPT_WORKSPACE=/workspace\nENV SPT_REPORTS_DIR=/reports\n\n# Volume mounts\n# docker run -v \\$(pwd):/workspace -v \\$(pwd)/reports:/reports spt-cli`,\n        description: 'Containerized environment configuration for consistent cross-platform execution.'\n      }, {\n        name: 'Cloud-Native Configuration',\n        type: 'Kubernetes ConfigMap',\n        config: `apiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: spt-config\n  namespace: security\ndata:\n  SPT_SEVERITY_THRESHOLD: \"medium\"\n  SPT_OUTPUT_FORMAT: \"json\"\n  SPT_PARALLEL_SCANS: \"6\"\n  SPT_TIMEOUT: \"25m\"\n  SPT_CLOUD_STORAGE: \"true\"\n  SPT_METRICS_ENABLED: \"true\"\n  SPT_DISTRIBUTED_SCAN: \"true\"\n---\napiVersion: v1\nkind: Secret\nmetadata:\n  name: spt-secrets\n  namespace: security\ntype: Opaque\nstringData:\n  github-token: \"your-github-token\"\n  api-key: \"your-api-key\"`,\n        description: 'Kubernetes-native configuration using ConfigMaps and Secrets for cloud deployments.'\n      }];\n      this.configExample = `{\n  \"scanning\": {\n    \"chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n    \"severity_threshold\": \"medium\",\n    \"max_file_size\": \"10MB\",\n    \"timeout\": \"5m\",\n    \"parallel_scans\": 4\n  },\n  \"output\": {\n    \"format\": \"table\",\n    \"colors\": true,\n    \"verbose\": false\n  },\n  \"rules\": {\n    \"ethereum\": {\n      \"check_reentrancy\": true,\n      \"check_overflow\": true,\n      \"check_access_control\": true\n    },\n    \"bitcoin\": {\n      \"check_key_management\": true,\n      \"check_transaction_validation\": true\n    }\n  },\n  \"integrations\": {\n    \"vscode\": {\n      \"enabled\": true,\n      \"server_url\": \"http://localhost:8080\"\n    }\n  }\n}`;\n      this.configOptions = [{\n        key: 'scanning.chains',\n        description: 'Array of blockchain chains to analyze'\n      }, {\n        key: 'scanning.severity_threshold',\n        description: 'Minimum severity level to report'\n      }, {\n        key: 'output.format',\n        description: 'Default output format for scan results'\n      }, {\n        key: 'rules.ethereum',\n        description: 'Ethereum-specific security rules configuration'\n      }, {\n        key: 'rules.bitcoin',\n        description: 'Bitcoin-specific security rules configuration'\n      }, {\n        key: 'integrations',\n        description: 'Configuration for IDE and tool integrations'\n      }];\n      this.platformTips = {\n        'GitHub Actions': ['Use GitHub Secrets for sensitive tokens', 'Enable branch protection rules with status checks', 'Configure matrix builds for multiple Go versions', 'Use actions/cache for faster builds'],\n        'GitLab CI': ['Use GitLab CI/CD variables for configuration', 'Configure merge request pipelines', 'Use GitLab Container Registry for custom images', 'Enable pipeline schedules for regular scans'],\n        'Azure DevOps': ['Store secrets in Azure Key Vault', 'Use variable groups for environment-specific configs', 'Configure branch policies with build validation', 'Enable Azure Artifacts for report storage'],\n        'AWS CodeBuild': ['Use Parameter Store for secure configuration', 'Configure VPC settings for private repositories', 'Use CloudWatch for monitoring and alerting', 'Enable S3 artifact encryption'],\n        'AWS CodePipeline': ['Use CloudFormation for infrastructure as code', 'Configure cross-region artifact replication', 'Implement approval gates for production', 'Use EventBridge for pipeline notifications'],\n        'Docker': ['Use multi-stage builds for smaller images', 'Run containers as non-root user', 'Mount volumes for persistent reports', 'Use health checks for container monitoring'],\n        'Jenkins': ['Use Jenkins Credentials for secure storage', 'Configure build triggers with webhooks', 'Use Pipeline as Code with Jenkinsfile', 'Enable Blue Ocean for better UI']\n      };\n    }\n    copyToClipboard(text) {\n      navigator.clipboard.writeText(text).then(() => {\n        // Could add a snackbar notification here\n        console.log('Configuration copied to clipboard');\n      }).catch(err => {\n        console.error('Failed to copy: ', err);\n      });\n    }\n    getPlatformTips(platform) {\n      return this.platformTips[platform] || [];\n    }\n    static {\n      this.ɵfac = function CliGuideComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CliGuideComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CliGuideComponent,\n        selectors: [[\"app-cli-guide\"]],\n        decls: 96,\n        vars: 8,\n        consts: [[1, \"cli-guide-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [1, \"cli-overview\"], [1, \"overview-card\"], [\"mat-card-avatar\", \"\"], [1, \"cli-features\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [\"animationDuration\", \"300ms\", 1, \"cli-tabs\"], [\"label\", \"Installation\"], [1, \"tab-content\"], [1, \"installation-methods\"], [\"class\", \"method-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Commands\"], [1, \"commands-list\"], [\"class\", \"command-panel\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"CI/CD Integration\"], [1, \"integration-examples\"], [1, \"integration-accordion\"], [\"class\", \"integration-panel\", 3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"best-practices-section\"], [1, \"section-description\"], [1, \"practices-accordion\"], [\"class\", \"practice-panel\", 4, \"ngFor\", \"ngForOf\"], [1, \"environment-configs\"], [1, \"env-config-card\"], [1, \"env-examples\"], [\"class\", \"env-example\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-card\"], [1, \"code-block\"], [1, \"code-header\"], [1, \"config-description\"], [1, \"config-options\"], [\"class\", \"config-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature\"], [1, \"method-card\"], [\"class\", \"method-notes\", 4, \"ngIf\"], [1, \"method-notes\"], [1, \"command-panel\"], [1, \"command-name\"], [1, \"command-details\"], [1, \"usage-section\"], [\"class\", \"options-section\", 4, \"ngIf\"], [\"class\", \"examples-section\", 4, \"ngIf\"], [\"class\", \"notes-section\", 4, \"ngIf\"], [1, \"options-section\"], [\"mat-table\", \"\", 1, \"options-table\", 3, \"dataSource\"], [\"matColumnDef\", \"flag\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"default\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [4, \"ngIf\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"examples-section\"], [\"class\", \"example\", 4, \"ngFor\", \"ngForOf\"], [1, \"example\"], [1, \"example-description\"], [\"class\", \"output-block\", 4, \"ngIf\"], [1, \"output-block\"], [1, \"notes-section\"], [1, \"notes-content\"], [1, \"integration-panel\", 3, \"expanded\"], [\"aria-hidden\", \"true\"], [1, \"integration-content\"], [1, \"integration-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy to clipboard\", 1, \"copy-btn\", 3, \"click\"], [\"class\", \"integration-notes\", 4, \"ngIf\"], [\"class\", \"platform-tips\", 4, \"ngIf\"], [1, \"integration-notes\"], [1, \"notes-icon\"], [1, \"platform-tips\"], [1, \"tips-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"tip-icon\"], [1, \"practice-panel\"], [1, \"practice-icon\"], [1, \"practice-content\"], [1, \"practice-tips\"], [\"class\", \"practice-tip\", 4, \"ngFor\", \"ngForOf\"], [1, \"practice-tip\"], [1, \"tip-check-icon\"], [1, \"env-example\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy configuration\", 1, \"copy-btn\", 3, \"click\"], [1, \"env-description\"], [1, \"config-option\"]],\n        template: function CliGuideComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"terminal\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(5, \" CLI Guide \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 2);\n            i0.ɵɵtext(7, \" Complete guide to the SPT command-line interface \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-header\")(11, \"mat-icon\", 5);\n            i0.ɵɵtext(12, \"info\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-title\");\n            i0.ɵɵtext(14, \"SPT CLI Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"mat-card-subtitle\");\n            i0.ɵɵtext(16, \"Powerful command-line security scanning tool\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"mat-card-content\")(18, \"p\");\n            i0.ɵɵtext(19, \"The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 6);\n            i0.ɵɵtemplate(21, CliGuideComponent_div_21_Template, 8, 5, \"div\", 7);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(22, \"mat-tab-group\", 8)(23, \"mat-tab\", 9)(24, \"div\", 10)(25, \"h2\");\n            i0.ɵɵtext(26, \"Installation Methods\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 11);\n            i0.ɵɵtemplate(28, CliGuideComponent_mat_card_28_Template, 19, 5, \"mat-card\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"mat-tab\", 13)(30, \"div\", 10)(31, \"h2\");\n            i0.ɵɵtext(32, \"Available Commands\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p\");\n            i0.ɵɵtext(34, \"Complete reference for all SPT CLI commands and their options.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 14);\n            i0.ɵɵtemplate(36, CliGuideComponent_mat_expansion_panel_36_Template, 18, 6, \"mat-expansion-panel\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"mat-tab\", 16)(38, \"div\", 10)(39, \"h2\");\n            i0.ɵɵtext(40, \"CI/CD Integration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"p\");\n            i0.ɵɵtext(42, \"Integrate SPT CLI into your continuous integration and deployment pipelines.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 17)(44, \"mat-accordion\", 18);\n            i0.ɵɵtemplate(45, CliGuideComponent_mat_expansion_panel_45_Template, 25, 11, \"mat-expansion-panel\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 20)(47, \"h3\");\n            i0.ɵɵtext(48, \"CI/CD Best Practices\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"p\", 21);\n            i0.ɵɵtext(50, \"Essential practices for implementing SPT in your CI/CD pipeline effectively.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"mat-accordion\", 22);\n            i0.ɵɵtemplate(52, CliGuideComponent_mat_expansion_panel_52_Template, 11, 6, \"mat-expansion-panel\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"div\", 24)(54, \"h3\");\n            i0.ɵɵtext(55, \"Environment-Specific Configurations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"mat-card\", 25)(57, \"mat-card-header\")(58, \"mat-icon\", 5);\n            i0.ɵɵtext(59, \"settings_applications\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"mat-card-title\");\n            i0.ɵɵtext(61, \"Environment Variables\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"mat-card-subtitle\");\n            i0.ɵɵtext(63, \"Configure SPT for different environments\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(64, \"mat-card-content\")(65, \"div\", 26);\n            i0.ɵɵtemplate(66, CliGuideComponent_div_66_Template, 17, 4, \"div\", 27);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(67, \"mat-tab\", 28)(68, \"div\", 10)(69, \"h2\");\n            i0.ɵɵtext(70, \"Configuration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"p\");\n            i0.ɵɵtext(72, \"Configure SPT CLI for your development environment and preferences.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"mat-card\", 29)(74, \"mat-card-header\")(75, \"mat-icon\", 5);\n            i0.ɵɵtext(76, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"mat-card-title\");\n            i0.ɵɵtext(78, \"Configuration File\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"mat-card-subtitle\");\n            i0.ɵɵtext(80, \"spt.config.json\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(81, \"mat-card-content\")(82, \"div\", 30)(83, \"div\", 31)(84, \"mat-icon\");\n            i0.ɵɵtext(85, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"span\");\n            i0.ɵɵtext(87, \"JSON Configuration\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(88, \"pre\")(89, \"code\");\n            i0.ɵɵtext(90);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(91, \"div\", 32)(92, \"h4\");\n            i0.ɵɵtext(93, \"Configuration Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"div\", 33);\n            i0.ɵɵtemplate(95, CliGuideComponent_div_95_Template, 5, 2, \"div\", 34);\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngForOf\", ctx.cliFeatures);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngForOf\", ctx.installationMethods);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.cliCommands);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.integrationExamples);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngForOf\", ctx.cicdBestPractices);\n            i0.ɵɵadvance(14);\n            i0.ɵɵproperty(\"ngForOf\", ctx.environmentConfigs);\n            i0.ɵɵadvance(24);\n            i0.ɵɵtextInterpolate(ctx.configExample);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.configOptions);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatExpansionModule, i5.MatAccordion, i5.MatExpansionPanel, i5.MatExpansionPanelHeader, i5.MatExpansionPanelTitle, i5.MatExpansionPanelDescription, MatChipsModule, i6.MatChip, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow],\n        styles: [\".cli-guide-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.page-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;color:#1976d2;margin:0 0 8px}.page-subtitle[_ngcontent-%COMP%]{color:#666;font-size:1.1em;margin:0}.cli-overview[_ngcontent-%COMP%]{margin-bottom:32px}.overview-card[_ngcontent-%COMP%]{border:1px solid #e0e0e0}.cli-features[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:16px;margin-top:16px}.feature[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;padding:12px;background:#f5f5f5;border-radius:8px}.feature[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:2px}.feature[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:4px}.feature[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.9em}.cli-tabs[_ngcontent-%COMP%]{margin-bottom:32px}.tab-content[_ngcontent-%COMP%]{padding:24px 0}.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:8px}.installation-methods[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:24px;margin-top:24px}.method-card[_ngcontent-%COMP%]{height:fit-content}.method-notes[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-top:12px;padding:8px 12px;background:#e3f2fd;border-radius:4px;color:#1976d2}.commands-list[_ngcontent-%COMP%]{margin-top:24px}.command-panel[_ngcontent-%COMP%]{margin-bottom:8px;border:1px solid #e0e0e0;border-radius:8px}.command-name[_ngcontent-%COMP%]{background:#f5f5f5;padding:4px 8px;border-radius:4px;font-family:Courier New,monospace;font-weight:500}.command-details[_ngcontent-%COMP%]{padding:16px 0}.usage-section[_ngcontent-%COMP%], .options-section[_ngcontent-%COMP%], .examples-section[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]{margin-bottom:24px}.usage-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .options-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .examples-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#1976d2}.code-block[_ngcontent-%COMP%]{border:1px solid var(--spt-gray-200);border-radius:var(--spt-radius-xl);overflow:hidden;margin-bottom:var(--spt-space-4);box-shadow:var(--spt-shadow-sm);background:#fff}.code-header[_ngcontent-%COMP%]{background:var(--spt-gray-50);padding:var(--spt-space-3) var(--spt-space-4);display:flex;align-items:center;gap:var(--spt-space-2);font-weight:var(--spt-font-medium);border-bottom:1px solid var(--spt-gray-200);justify-content:space-between}.code-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-primary-600);font-size:18px;width:18px;height:18px}.code-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--spt-gray-700);font-size:var(--spt-text-sm);font-weight:var(--spt-font-medium);flex:1}.copy-btn[_ngcontent-%COMP%]{color:var(--spt-primary-600)!important;background:var(--spt-primary-100)!important;border-radius:var(--spt-radius-md)!important;transition:all .2s ease!important}.copy-btn[_ngcontent-%COMP%]:hover{background:var(--spt-primary-200)!important;transform:scale(1.05)}.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{margin:0;padding:var(--spt-space-4);background:var(--spt-gray-50);overflow-x:auto;font-family:JetBrains Mono,Fira Code,Courier New,monospace;font-size:var(--spt-text-sm);line-height:1.5;color:var(--spt-gray-800)}.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{font-family:inherit;font-size:inherit;color:inherit}.output-block[_ngcontent-%COMP%]{border:1px solid #4caf50;border-radius:8px;overflow:hidden;margin-top:8px}.output-block[_ngcontent-%COMP%]   .code-header[_ngcontent-%COMP%]{background:#e8f5e8;border-bottom-color:#4caf50}.output-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background:#f1f8e9}.options-table[_ngcontent-%COMP%]{width:100%;border:1px solid #e0e0e0;border-radius:8px}.options-table[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:#f5f5f5;padding:2px 6px;border-radius:4px;font-size:.9em}.example[_ngcontent-%COMP%]{margin-bottom:24px;padding:16px;background:#f9f9f9;border-radius:8px}.example-description[_ngcontent-%COMP%]{margin:0 0 12px;font-weight:500;color:#1976d2}.notes-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:8px;padding:12px;background:#e3f2fd;border-radius:8px;color:#1976d2}.notes-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.integration-examples[_ngcontent-%COMP%]{margin-top:var(--spt-space-6)}.integration-accordion[_ngcontent-%COMP%]{box-shadow:var(--spt-shadow-sm);border-radius:var(--spt-radius-xl);overflow:hidden;border:1px solid var(--spt-gray-200)}.integration-panel[_ngcontent-%COMP%]{border:none!important;box-shadow:none!important;border-bottom:1px solid var(--spt-gray-200)!important}.integration-panel[_ngcontent-%COMP%]:last-child{border-bottom:none!important}.integration-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{padding:var(--spt-space-4) var(--spt-space-6)!important;background:var(--spt-gray-50)!important;transition:all .2s ease!important}.integration-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover{background:var(--spt-primary-50)!important}.integration-panel.mat-expanded[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{background:var(--spt-primary-100)!important;border-bottom:1px solid var(--spt-primary-200)!important}.platform-icon[_ngcontent-%COMP%]{color:var(--spt-primary-600);margin-right:var(--spt-space-3);font-size:18px;width:18px;display:inline-flex;align-items:center;justify-content:center}.platform-icon.fa-github[_ngcontent-%COMP%]{color:#24292e}.platform-icon.fa-gitlab[_ngcontent-%COMP%]{color:#fc6d26}.platform-icon.fa-microsoft[_ngcontent-%COMP%]{color:#0078d4}.platform-icon.fa-aws[_ngcontent-%COMP%]{color:#f90}.platform-icon.fa-docker[_ngcontent-%COMP%]{color:#2496ed}.platform-icon.fa-jenkins[_ngcontent-%COMP%]{color:#d33833}.integration-content[_ngcontent-%COMP%]{padding:var(--spt-space-6);background:#fff}.integration-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:var(--spt-space-4)}.integration-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:var(--spt-gray-900);font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold)}.copy-config-btn[_ngcontent-%COMP%]{color:var(--spt-primary-600)!important;background:var(--spt-primary-100)!important;border-radius:var(--spt-radius-md)!important}.copy-config-btn[_ngcontent-%COMP%]:hover{background:var(--spt-primary-200)!important}.integration-notes[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:var(--spt-space-3);margin-top:var(--spt-space-4);padding:var(--spt-space-4);background:var(--spt-info-50);border-radius:var(--spt-radius-lg);border:1px solid var(--spt-info-200)}.notes-icon[_ngcontent-%COMP%]{color:var(--spt-info-600);margin-top:var(--spt-space-1)}.notes-content[_ngcontent-%COMP%]{flex:1}.notes-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--spt-info-800);font-weight:var(--spt-font-semibold)}.notes-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:var(--spt-space-1) 0 0 0;color:var(--spt-info-700);line-height:1.5}.platform-tips[_ngcontent-%COMP%]{margin-top:var(--spt-space-4);padding:var(--spt-space-4);background:var(--spt-success-50);border-radius:var(--spt-radius-lg);border:1px solid var(--spt-success-200)}.platform-tips[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-3) 0;color:var(--spt-success-800);font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold)}.tips-list[_ngcontent-%COMP%]{list-style:none;margin:0;padding:0}.tips-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:var(--spt-space-2);margin-bottom:var(--spt-space-2);color:var(--spt-success-700);font-size:var(--spt-text-sm);line-height:1.4}.tip-icon[_ngcontent-%COMP%]{color:var(--spt-success-600);font-size:16px;width:16px;height:16px;margin-top:2px}.best-practices-section[_ngcontent-%COMP%]{margin-top:var(--spt-space-8);margin-bottom:var(--spt-space-8)}.best-practices-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--spt-gray-900);font-size:var(--spt-text-2xl);font-weight:var(--spt-font-bold);margin-bottom:var(--spt-space-2)}.section-description[_ngcontent-%COMP%]{color:var(--spt-gray-600);font-size:var(--spt-text-base);margin-bottom:var(--spt-space-6);line-height:1.6}.practices-accordion[_ngcontent-%COMP%]{box-shadow:var(--spt-shadow-sm);border-radius:var(--spt-radius-xl);overflow:hidden;border:1px solid var(--spt-gray-200)}.practice-panel[_ngcontent-%COMP%]{border:none!important;box-shadow:none!important;border-bottom:1px solid var(--spt-gray-200)!important}.practice-panel[_ngcontent-%COMP%]:last-child{border-bottom:none!important}.practice-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{padding:var(--spt-space-4) var(--spt-space-6)!important;background:var(--spt-gray-50)!important;transition:all .2s ease!important}.practice-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover{background:var(--spt-secondary-50)!important}.practice-panel.mat-expanded[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{background:var(--spt-secondary-100)!important;border-bottom:1px solid var(--spt-secondary-200)!important}.practice-icon[_ngcontent-%COMP%]{margin-right:var(--spt-space-3)}.practice-content[_ngcontent-%COMP%]{padding:var(--spt-space-6);background:#fff}.practice-tips[_ngcontent-%COMP%]{list-style:none;margin:0;padding:0}.practice-tip[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:var(--spt-space-2);margin-bottom:var(--spt-space-3);color:var(--spt-gray-700);font-size:var(--spt-text-sm);line-height:1.5}.tip-check-icon[_ngcontent-%COMP%]{color:var(--spt-success-600);font-size:16px;width:16px;height:16px;margin-top:2px}.config-card[_ngcontent-%COMP%], .config-description[_ngcontent-%COMP%]{margin-top:24px}.config-description[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;color:#1976d2}.config-options[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:16px}.config-option[_ngcontent-%COMP%]{padding:12px;background:#f5f5f5;border-radius:8px}.config-option[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:4px;color:#1976d2}.config-option[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.9em}@media (max-width: 768px){.cli-features[_ngcontent-%COMP%], .installation-methods[_ngcontent-%COMP%]{grid-template-columns:1fr}.integration-content[_ngcontent-%COMP%]{padding:var(--spt-space-4)}.integration-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:var(--spt-space-2)}.integration-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:var(--spt-text-base)}.platform-tips[_ngcontent-%COMP%], .integration-notes[_ngcontent-%COMP%]{padding:var(--spt-space-3)}.config-options[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return CliGuideComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}