{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'http://localhost:8080/api/v1/auth';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.tokenSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.token$ = this.tokenSubject.asObservable();\n    // Check for existing token on service initialization\n    const token = localStorage.getItem('auth_token');\n    const user = localStorage.getItem('current_user');\n    console.log('AuthService constructor - Token:', token);\n    console.log('AuthService constructor - User:', user);\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        console.log('AuthService constructor - Parsed user:', parsedUser);\n        this.tokenSubject.next(token);\n        this.currentUserSubject.next(parsedUser);\n        console.log('AuthService constructor - User state initialized');\n      } catch (error) {\n        console.error('AuthService constructor - Error parsing user:', error);\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('current_user');\n      }\n    } else {\n      console.log('AuthService constructor - No existing auth data found');\n    }\n  }\n  // Authentication methods\n  login(credentials) {\n    return this.http.post(`${this.baseUrl}/login`, credentials).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(this.handleError));\n  }\n  register(userData) {\n    return this.http.post(`${this.baseUrl}/register`, userData).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(this.handleError));\n  }\n  logout() {\n    // Call logout endpoint to invalidate token on server\n    this.http.post(`${this.baseUrl}/logout`, {}).subscribe({\n      complete: () => {\n        this.clearAuthData();\n      },\n      error: () => {\n        // Clear local data even if server call fails\n        this.clearAuthData();\n      }\n    });\n  }\n  refreshToken() {\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n    return this.http.post(`${this.baseUrl}/refresh`, {\n      refresh_token: refreshToken\n    }).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(error => {\n      this.clearAuthData();\n      return throwError(() => error);\n    }));\n  }\n  // Utility methods\n  isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    console.log('isAuthenticated - Token:', token);\n    console.log('isAuthenticated - User:', user);\n    if (!token || !user) {\n      console.log('isAuthenticated - No token or user, returning false');\n      return false;\n    }\n    // Check if token is expired\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      const isValid = payload.exp > currentTime;\n      console.log('isAuthenticated - Token valid:', isValid, 'Expires:', payload.exp, 'Current:', currentTime);\n      return isValid;\n    } catch (error) {\n      console.log('isAuthenticated - Token parsing error:', error);\n      return false;\n    }\n  }\n  getToken() {\n    return localStorage.getItem('auth_token');\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  hasRole(role) {\n    const user = this.getCurrentUser();\n    return user ? user.role === role : false;\n  }\n  hasAnyRole(roles) {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n  // HTTP headers with authentication\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n  // Private methods\n  setAuthData(response) {\n    localStorage.setItem('auth_token', response.token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    this.tokenSubject.next(response.token);\n    this.currentUserSubject.next(response.user);\n    // Set token expiration timer\n    if (response.expires_in) {\n      setTimeout(() => {\n        this.refreshToken().subscribe({\n          error: () => this.logout()\n        });\n      }, (response.expires_in - 300) * 1000); // Refresh 5 minutes before expiry\n    }\n  }\n  clearAuthData() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('current_user');\n    localStorage.removeItem('refresh_token');\n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  // Mock authentication for development\n  mockLogin(username = 'admin', role = 'admin') {\n    const mockUser = {\n      id: '1',\n      username: username,\n      email: `${username}@spt.local`,\n      role: role,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    const mockToken = this.generateMockToken(mockUser);\n    const mockResponse = {\n      token: mockToken,\n      user: mockUser,\n      expires_in: 3600\n    };\n    this.setAuthData(mockResponse);\n  }\n  generateMockToken(user) {\n    const header = btoa(JSON.stringify({\n      alg: 'HS256',\n      typ: 'JWT'\n    }));\n    const payload = btoa(JSON.stringify({\n      sub: user.id,\n      username: user.username,\n      role: user.role,\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + 3600\n    }));\n    const signature = btoa('mock-signature');\n    return `${header}.${payload}.${signature}`;\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "map", "catchError", "AuthService", "constructor", "http", "router", "baseUrl", "currentUserSubject", "tokenSubject", "currentUser$", "asObservable", "token$", "token", "localStorage", "getItem", "user", "console", "log", "parsedUser", "JSON", "parse", "next", "error", "removeItem", "login", "credentials", "post", "pipe", "response", "setAuthData", "handleError", "register", "userData", "logout", "subscribe", "complete", "clearAuthData", "refreshToken", "Error", "refresh_token", "isAuthenticated", "getToken", "getCurrentUser", "payload", "atob", "split", "currentTime", "Math", "floor", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "exp", "value", "hasRole", "role", "hasAnyRole", "roles", "includes", "getAuthHeaders", "setItem", "stringify", "expires_in", "setTimeout", "navigate", "errorMessage", "message", "mockLogin", "username", "mockUser", "id", "email", "created_at", "toISOString", "last_login", "mockToken", "generateMockToken", "mockResponse", "header", "btoa", "alg", "typ", "sub", "iat", "signature", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { Router } from '@angular/router';\n\nexport interface User {\n  id: string;\n  username: string;\n  email: string;\n  role: string;\n  created_at: string;\n  last_login?: string;\n}\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n}\n\nexport interface AuthResponse {\n  token: string;\n  user: User;\n  expires_in: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly baseUrl = 'http://localhost:8080/api/v1/auth';\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private tokenSubject = new BehaviorSubject<string | null>(null);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public token$ = this.tokenSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    // Check for existing token on service initialization\n    const token = localStorage.getItem('auth_token');\n    const user = localStorage.getItem('current_user');\n\n    console.log('AuthService constructor - Token:', token);\n    console.log('AuthService constructor - User:', user);\n\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        console.log('AuthService constructor - Parsed user:', parsedUser);\n        this.tokenSubject.next(token);\n        this.currentUserSubject.next(parsedUser);\n        console.log('AuthService constructor - User state initialized');\n      } catch (error) {\n        console.error('AuthService constructor - Error parsing user:', error);\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('current_user');\n      }\n    } else {\n      console.log('AuthService constructor - No existing auth data found');\n    }\n  }\n\n  // Authentication methods\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.baseUrl}/login`, credentials)\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.baseUrl}/register`, userData)\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  logout(): void {\n    // Call logout endpoint to invalidate token on server\n    this.http.post(`${this.baseUrl}/logout`, {}).subscribe({\n      complete: () => {\n        this.clearAuthData();\n      },\n      error: () => {\n        // Clear local data even if server call fails\n        this.clearAuthData();\n      }\n    });\n  }\n\n  refreshToken(): Observable<AuthResponse> {\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n\n    return this.http.post<AuthResponse>(`${this.baseUrl}/refresh`, { refresh_token: refreshToken })\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(error => {\n          this.clearAuthData();\n          return throwError(() => error);\n        })\n      );\n  }\n\n  // Utility methods\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n\n    console.log('isAuthenticated - Token:', token);\n    console.log('isAuthenticated - User:', user);\n\n    if (!token || !user) {\n      console.log('isAuthenticated - No token or user, returning false');\n      return false;\n    }\n\n    // Check if token is expired\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      const isValid = payload.exp > currentTime;\n      console.log('isAuthenticated - Token valid:', isValid, 'Expires:', payload.exp, 'Current:', currentTime);\n      return isValid;\n    } catch (error) {\n      console.log('isAuthenticated - Token parsing error:', error);\n      return false;\n    }\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  hasRole(role: string): boolean {\n    const user = this.getCurrentUser();\n    return user ? user.role === role : false;\n  }\n\n  hasAnyRole(roles: string[]): boolean {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n\n  // HTTP headers with authentication\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n\n  // Private methods\n  private setAuthData(response: AuthResponse): void {\n    localStorage.setItem('auth_token', response.token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    \n    this.tokenSubject.next(response.token);\n    this.currentUserSubject.next(response.user);\n\n    // Set token expiration timer\n    if (response.expires_in) {\n      setTimeout(() => {\n        this.refreshToken().subscribe({\n          error: () => this.logout()\n        });\n      }, (response.expires_in - 300) * 1000); // Refresh 5 minutes before expiry\n    }\n  }\n\n  private clearAuthData(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('current_user');\n    localStorage.removeItem('refresh_token');\n    \n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    \n    this.router.navigate(['/login']);\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An error occurred';\n    \n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    \n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n\n  // Mock authentication for development\n  mockLogin(username: string = 'admin', role: string = 'admin'): void {\n    const mockUser: User = {\n      id: '1',\n      username: username,\n      email: `${username}@spt.local`,\n      role: role,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n\n    const mockToken = this.generateMockToken(mockUser);\n    \n    const mockResponse: AuthResponse = {\n      token: mockToken,\n      user: mockUser,\n      expires_in: 3600\n    };\n\n    this.setAuthData(mockResponse);\n  }\n\n  private generateMockToken(user: User): string {\n    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));\n    const payload = btoa(JSON.stringify({\n      sub: user.id,\n      username: user.username,\n      role: user.role,\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + 3600\n    }));\n    const signature = btoa('mock-signature');\n    \n    return `${header}.${payload}.${signature}`;\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;;AAiChD,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAG,mCAAmC;IACtD,KAAAC,kBAAkB,GAAG,IAAIT,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAU,YAAY,GAAG,IAAIV,eAAe,CAAgB,IAAI,CAAC;IAExD,KAAAW,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,MAAM,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAM9C;IACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAEjDE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEL,KAAK,CAAC;IACtDI,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,IAAI,CAAC;IAEpD,IAAIH,KAAK,IAAIG,IAAI,EAAE;MACjB,IAAI;QACF,MAAMG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC;QACnCC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,UAAU,CAAC;QACjE,IAAI,CAACV,YAAY,CAACa,IAAI,CAACT,KAAK,CAAC;QAC7B,IAAI,CAACL,kBAAkB,CAACc,IAAI,CAACH,UAAU,CAAC;QACxCF,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MACjE,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrET,YAAY,CAACU,UAAU,CAAC,YAAY,CAAC;QACrCV,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;MACzC;IACF,CAAC,MAAM;MACLP,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACtE;EACF;EAEA;EACAO,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACrB,IAAI,CAACsB,IAAI,CAAe,GAAG,IAAI,CAACpB,OAAO,QAAQ,EAAEmB,WAAW,CAAC,CACtEE,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF3B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAC,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAAC5B,IAAI,CAACsB,IAAI,CAAe,GAAG,IAAI,CAACpB,OAAO,WAAW,EAAE0B,QAAQ,CAAC,CACtEL,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF3B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAG,MAAMA,CAAA;IACJ;IACA,IAAI,CAAC7B,IAAI,CAACsB,IAAI,CAAC,GAAG,IAAI,CAACpB,OAAO,SAAS,EAAE,EAAE,CAAC,CAAC4B,SAAS,CAAC;MACrDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC;MACDd,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACc,aAAa,EAAE;MACtB;KACD,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,MAAMA,YAAY,GAAGxB,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC1D,IAAI,CAACuB,YAAY,EAAE;MACjB,OAAOtC,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE;IAEA,OAAO,IAAI,CAAClC,IAAI,CAACsB,IAAI,CAAe,GAAG,IAAI,CAACpB,OAAO,UAAU,EAAE;MAAEiC,aAAa,EAAEF;IAAY,CAAE,CAAC,CAC5FV,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF3B,UAAU,CAACqB,KAAK,IAAG;MACjB,IAAI,CAACc,aAAa,EAAE;MACpB,OAAOrC,UAAU,CAAC,MAAMuB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;EACAkB,eAAeA,CAAA;IACb,MAAM5B,KAAK,GAAG,IAAI,CAAC6B,QAAQ,EAAE;IAC7B,MAAM1B,IAAI,GAAG,IAAI,CAAC2B,cAAc,EAAE;IAElC1B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEL,KAAK,CAAC;IAC9CI,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,IAAI,CAAC;IAE5C,IAAI,CAACH,KAAK,IAAI,CAACG,IAAI,EAAE;MACnBC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE,OAAO,KAAK;IACd;IAEA;IACA,IAAI;MACF,MAAM0B,OAAO,GAAGxB,IAAI,CAACC,KAAK,CAACwB,IAAI,CAAChC,KAAK,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MACjD,MAAMC,OAAO,GAAGR,OAAO,CAACS,GAAG,GAAGN,WAAW;MACzC9B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkC,OAAO,EAAE,UAAU,EAAER,OAAO,CAACS,GAAG,EAAE,UAAU,EAAEN,WAAW,CAAC;MACxG,OAAOK,OAAO;IAChB,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdN,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEK,KAAK,CAAC;MAC5D,OAAO,KAAK;IACd;EACF;EAEAmB,QAAQA,CAAA;IACN,OAAO5B,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAC3C;EAEA4B,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACnC,kBAAkB,CAAC8C,KAAK;EACtC;EAEAC,OAAOA,CAACC,IAAY;IAClB,MAAMxC,IAAI,GAAG,IAAI,CAAC2B,cAAc,EAAE;IAClC,OAAO3B,IAAI,GAAGA,IAAI,CAACwC,IAAI,KAAKA,IAAI,GAAG,KAAK;EAC1C;EAEAC,UAAUA,CAACC,KAAe;IACxB,MAAM1C,IAAI,GAAG,IAAI,CAAC2B,cAAc,EAAE;IAClC,OAAO3B,IAAI,GAAG0C,KAAK,CAACC,QAAQ,CAAC3C,IAAI,CAACwC,IAAI,CAAC,GAAG,KAAK;EACjD;EAEA;EACAI,cAAcA,CAAA;IACZ,MAAM/C,KAAK,GAAG,IAAI,CAAC6B,QAAQ,EAAE;IAC7B,OAAO,IAAI5C,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAEe,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;KAC9C,CAAC;EACJ;EAEA;EACQiB,WAAWA,CAACD,QAAsB;IACxCf,YAAY,CAAC+C,OAAO,CAAC,YAAY,EAAEhC,QAAQ,CAAChB,KAAK,CAAC;IAClDC,YAAY,CAAC+C,OAAO,CAAC,cAAc,EAAEzC,IAAI,CAAC0C,SAAS,CAACjC,QAAQ,CAACb,IAAI,CAAC,CAAC;IAEnE,IAAI,CAACP,YAAY,CAACa,IAAI,CAACO,QAAQ,CAAChB,KAAK,CAAC;IACtC,IAAI,CAACL,kBAAkB,CAACc,IAAI,CAACO,QAAQ,CAACb,IAAI,CAAC;IAE3C;IACA,IAAIa,QAAQ,CAACkC,UAAU,EAAE;MACvBC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1B,YAAY,EAAE,CAACH,SAAS,CAAC;UAC5BZ,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACW,MAAM;SACzB,CAAC;MACJ,CAAC,EAAE,CAACL,QAAQ,CAACkC,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IAC1C;EACF;EAEQ1B,aAAaA,CAAA;IACnBvB,YAAY,CAACU,UAAU,CAAC,YAAY,CAAC;IACrCV,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;IACvCV,YAAY,CAACU,UAAU,CAAC,eAAe,CAAC;IAExC,IAAI,CAACf,YAAY,CAACa,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACd,kBAAkB,CAACc,IAAI,CAAC,IAAI,CAAC;IAElC,IAAI,CAAChB,MAAM,CAAC2D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEQlC,WAAWA,CAACR,KAAU;IAC5B,IAAI2C,YAAY,GAAG,mBAAmB;IAEtC,IAAI3C,KAAK,CAACA,KAAK,EAAE4C,OAAO,EAAE;MACxBD,YAAY,GAAG3C,KAAK,CAACA,KAAK,CAAC4C,OAAO;IACpC,CAAC,MAAM,IAAI5C,KAAK,CAAC4C,OAAO,EAAE;MACxBD,YAAY,GAAG3C,KAAK,CAAC4C,OAAO;IAC9B;IAEAlD,OAAO,CAACM,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,OAAOvB,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAAC2B,YAAY,CAAC,CAAC;EAClD;EAEA;EACAE,SAASA,CAACC,QAAA,GAAmB,OAAO,EAAEb,IAAA,GAAe,OAAO;IAC1D,MAAMc,QAAQ,GAAS;MACrBC,EAAE,EAAE,GAAG;MACPF,QAAQ,EAAEA,QAAQ;MAClBG,KAAK,EAAE,GAAGH,QAAQ,YAAY;MAC9Bb,IAAI,EAAEA,IAAI;MACViB,UAAU,EAAE,IAAIvB,IAAI,EAAE,CAACwB,WAAW,EAAE;MACpCC,UAAU,EAAE,IAAIzB,IAAI,EAAE,CAACwB,WAAW;KACnC;IAED,MAAME,SAAS,GAAG,IAAI,CAACC,iBAAiB,CAACP,QAAQ,CAAC;IAElD,MAAMQ,YAAY,GAAiB;MACjCjE,KAAK,EAAE+D,SAAS;MAChB5D,IAAI,EAAEsD,QAAQ;MACdP,UAAU,EAAE;KACb;IAED,IAAI,CAACjC,WAAW,CAACgD,YAAY,CAAC;EAChC;EAEQD,iBAAiBA,CAAC7D,IAAU;IAClC,MAAM+D,MAAM,GAAGC,IAAI,CAAC5D,IAAI,CAAC0C,SAAS,CAAC;MAAEmB,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAK,CAAE,CAAC,CAAC;IACjE,MAAMtC,OAAO,GAAGoC,IAAI,CAAC5D,IAAI,CAAC0C,SAAS,CAAC;MAClCqB,GAAG,EAAEnE,IAAI,CAACuD,EAAE;MACZF,QAAQ,EAAErD,IAAI,CAACqD,QAAQ;MACvBb,IAAI,EAAExC,IAAI,CAACwC,IAAI;MACf4B,GAAG,EAAEpC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MAClCE,GAAG,EAAEL,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG;KACtC,CAAC,CAAC;IACH,MAAMkC,SAAS,GAAGL,IAAI,CAAC,gBAAgB,CAAC;IAExC,OAAO,GAAGD,MAAM,IAAInC,OAAO,IAAIyC,SAAS,EAAE;EAC5C;;;uCA5NWlF,WAAW,EAAAmF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXxF,WAAW;MAAAyF,OAAA,EAAXzF,WAAW,CAAA0F,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}