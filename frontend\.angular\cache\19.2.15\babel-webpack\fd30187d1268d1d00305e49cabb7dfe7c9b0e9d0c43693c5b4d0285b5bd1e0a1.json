{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n  _attachedHost;\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    let host = this._attachedHost;\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n  /** The type of the component that will be instantiated for attachment. */\n  component;\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef;\n  /** Injector used for the instantiation of the component. */\n  injector;\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  componentFactoryResolver;\n  /**\n   * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n   */\n  projectableNodes;\n  constructor(component, viewContainerRef, injector,\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  _componentFactoryResolver, projectableNodes) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.projectableNodes = projectableNodes;\n  }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n  templateRef;\n  viewContainerRef;\n  context;\n  injector;\n  constructor(/** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef, /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef, /** Contextual data to be passed in to the embedded view. */\n  context, /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n  /** DOM node hosting the portal's content. */\n  element;\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n  /** The portal currently attached to the host. */\n  _attachedPortal;\n  /** A function that will permanently dispose this host. */\n  _disposeFn;\n  /** Whether this host has already been permanently disposed. */\n  _isDisposed = false;\n  /** Whether this host has an attached portal. */\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n  attachDomPortal = null;\n  /** Detaches a previously attached portal. */\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n  outletElement;\n  _appRef;\n  _defaultInjector;\n  _document;\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _unusedComponentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor(/** Element into which the content is projected. */\n  outletElement,\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  _unusedComponentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    this.outletElement = outletElement;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      const injector = portal.injector || portal.viewContainerRef.injector;\n      const ngModuleRef = injector.get(NgModuleRef, null, {\n        optional: true\n      }) || undefined;\n      componentRef = portal.viewContainerRef.createComponent(portal.component, {\n        index: portal.viewContainerRef.length,\n        injector,\n        ngModuleRef,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      const appRef = this._appRef;\n      const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n      const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n      componentRef = createComponent(portal.component, {\n        elementInjector,\n        environmentInjector,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (appRef.viewCount > 0) {\n          appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal;\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n  /**\n   * Attaches a DOM portal by transferring its content into the outlet.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n    element.parentNode.insertBefore(anchorNode, element);\n    this.outletElement.appendChild(element);\n    this._attachedPortal = portal;\n    super.setDisposeFn(() => {\n      // We can't use `replaceWith` here because IE doesn't support it.\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n  constructor() {\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    super(templateRef, viewContainerRef);\n  }\n  static ɵfac = function CdkPortal_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkPortal)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortal,\n    selectors: [[\"\", \"cdkPortal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal'\n    }]\n  }], () => [], null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTemplatePortalDirective_BaseFactory;\n    return function TemplatePortalDirective_Factory(__ngFactoryType__) {\n      return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(__ngFactoryType__ || TemplatePortalDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TemplatePortalDirective,\n    selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortal,\n      useExisting: TemplatePortalDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n  _moduleRef = inject(NgModuleRef, {\n    optional: true\n  });\n  _document = inject(DOCUMENT);\n  _viewContainerRef = inject(ViewContainerRef);\n  /** Whether the portal component is initialized. */\n  _isInitialized = false;\n  /** Reference to the currently-attached component/view ref. */\n  _attachedRef;\n  constructor() {\n    super();\n  }\n  /** Portal associated with the Portal outlet. */\n  get portal() {\n    return this._attachedPortal;\n  }\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n    if (this.hasAttached()) {\n      super.detach();\n    }\n    if (portal) {\n      super.attach(portal);\n    }\n    this._attachedPortal = portal || null;\n  }\n  /** Emits when a portal is attached to the outlet. */\n  attached = new EventEmitter();\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef() {\n    return this._attachedRef;\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this);\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const ref = viewContainerRef.createComponent(portal.component, {\n      index: viewContainerRef.length,\n      injector: portal.injector || viewContainerRef.injector,\n      projectableNodes: portal.projectableNodes || undefined,\n      ngModuleRef: this._moduleRef || undefined\n    });\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /**\n   * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n    portal.setAttachedHost(this);\n    element.parentNode.insertBefore(anchorNode, element);\n    this._getRootNode().appendChild(element);\n    this._attachedPortal = portal;\n    super.setDisposeFn(() => {\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n  /** Gets the root node of the portal outlet. */\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement;\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n  static ɵfac = function CdkPortalOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkPortalOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortalOutlet,\n    selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n    inputs: {\n      portal: [0, \"cdkPortalOutlet\", \"portal\"]\n    },\n    outputs: {\n      attached: \"attached\"\n    },\n    exportAs: [\"cdkPortalOutlet\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet'\n    }]\n  }], () => [], {\n    portal: [{\n      type: Input,\n      args: ['cdkPortalOutlet']\n    }],\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵPortalHostDirective_BaseFactory;\n    return function PortalHostDirective_Factory(__ngFactoryType__) {\n      return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(__ngFactoryType__ || PortalHostDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PortalHostDirective,\n    selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n    inputs: {\n      portal: [0, \"cdkPortalHost\", \"portal\"]\n    },\n    exportAs: [\"cdkPortalHost\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortalOutlet,\n      useExisting: PortalHostDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: [{\n        name: 'portal',\n        alias: 'cdkPortalHost'\n      }],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]\n    }]\n  }], null, null);\n})();\nclass PortalModule {\n  static ɵfac = function PortalModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PortalModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PortalModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\nexport { BasePortalOutlet as B, ComponentPortal as C, DomPortal as D, Portal as P, TemplatePortal as T, BasePortalHost as a, DomPortalOutlet as b, DomPortalHost as c, CdkPortal as d, TemplatePortalDirective as e, CdkPortalOutlet as f, PortalHostDirective as g, PortalModule as h };", "map": {"version": 3, "names": ["i0", "ElementRef", "NgModuleRef", "EnvironmentInjector", "createComponent", "Injector", "inject", "TemplateRef", "ViewContainerRef", "Directive", "EventEmitter", "Input", "Output", "NgModule", "DOCUMENT", "throwNullPortalError", "Error", "throwPortalAlreadyAttachedError", "throwPortalOutletAlreadyDisposedError", "throwUnknownPortalTypeError", "throwNullPortalOutletError", "throwNoPortalAttachedError", "Portal", "_attachedHost", "attach", "host", "ngDevMode", "has<PERSON>tta<PERSON>", "detach", "isAttached", "setAttachedHost", "ComponentPortal", "component", "viewContainerRef", "injector", "componentFactoryResolver", "projectableNodes", "constructor", "_componentFactoryResolver", "TemplatePortal", "templateRef", "context", "origin", "elementRef", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "element", "nativeElement", "BasePortalOutlet", "_attachedPortal", "_disposeFn", "_isDisposed", "portal", "attachComponentPortal", "attachTemplatePortal", "attachDomPortal", "_invokeDisposeFn", "dispose", "setDisposeFn", "fn", "BasePortalHost", "DomPortalOutlet", "outletElement", "_appRef", "_defaultInjector", "_document", "_unusedComponentFactoryResolver", "componentRef", "ngModuleRef", "get", "optional", "index", "length", "destroy", "appRef", "elementInjector", "NULL", "environmentInjector", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "viewCount", "detach<PERSON>iew", "append<PERSON><PERSON><PERSON>", "_getComponentRootNode", "viewContainer", "viewRef", "createEmbeddedView", "rootNodes", "for<PERSON>ach", "rootNode", "detectChanges", "indexOf", "remove", "parentNode", "anchorNode", "createComment", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "DomPortalHost", "CdkPortal", "ɵfac", "CdkPortal_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "exportAs", "features", "ɵɵInheritDefinitionFeature", "ɵsetClassMetadata", "args", "selector", "TemplatePortalDirective", "ɵTemplatePortalDirective_BaseFactory", "TemplatePortalDirective_Factory", "ɵɵgetInheritedFactory", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "CdkPortalOutlet", "_moduleRef", "_viewContainerRef", "_isInitialized", "_attachedRef", "attached", "attachedRef", "ngOnInit", "ngOnDestroy", "ref", "_getRootNode", "emit", "clear", "nodeType", "ELEMENT_NODE", "CdkPortalOutlet_Factory", "inputs", "outputs", "PortalHostDirective", "ɵPortalHostDirective_BaseFactory", "PortalHostDirective_Factory", "name", "alias", "PortalModule", "PortalModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "B", "C", "D", "P", "T", "a", "b", "c", "d", "e", "f", "g", "h"], "sources": ["D:/TGI/Blockchain.SPT/frontend/node_modules/@angular/cdk/fesm2022/portal-directives-Bw5woq8I.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n    throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n    throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n    throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n    throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n        'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n    throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n    throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n    _attachedHost;\n    /** Attach this portal to a host. */\n    attach(host) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (host == null) {\n                throwNullPortalOutletError();\n            }\n            if (host.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n        }\n        this._attachedHost = host;\n        return host.attach(this);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        let host = this._attachedHost;\n        if (host != null) {\n            this._attachedHost = null;\n            host.detach();\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwNoPortalAttachedError();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n     * the PortalOutlet when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n    /** The type of the component that will be instantiated for attachment. */\n    component;\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This is different from where the component *renders*, which is determined by the PortalOutlet.\n     * The origin is necessary when the host is outside of the Angular application context.\n     */\n    viewContainerRef;\n    /** Injector used for the instantiation of the component. */\n    injector;\n    /**\n     * @deprecated No longer in use. To be removed.\n     * @breaking-change 18.0.0\n     */\n    componentFactoryResolver;\n    /**\n     * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n     */\n    projectableNodes;\n    constructor(component, viewContainerRef, injector, \n    /**\n     * @deprecated No longer in use. To be removed.\n     * @breaking-change 18.0.0\n     */\n    _componentFactoryResolver, projectableNodes) {\n        super();\n        this.component = component;\n        this.viewContainerRef = viewContainerRef;\n        this.injector = injector;\n        this.projectableNodes = projectableNodes;\n    }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n    templateRef;\n    viewContainerRef;\n    context;\n    injector;\n    constructor(\n    /** The embedded template that will be used to instantiate an embedded View in the host. */\n    templateRef, \n    /** Reference to the ViewContainer into which the template will be stamped out. */\n    viewContainerRef, \n    /** Contextual data to be passed in to the embedded view. */\n    context, \n    /** The injector to use for the embedded view. */\n    injector) {\n        super();\n        this.templateRef = templateRef;\n        this.viewContainerRef = viewContainerRef;\n        this.context = context;\n        this.injector = injector;\n    }\n    get origin() {\n        return this.templateRef.elementRef;\n    }\n    /**\n     * Attach the portal to the provided `PortalOutlet`.\n     * When a context is provided it will override the `context` property of the `TemplatePortal`\n     * instance.\n     */\n    attach(host, context = this.context) {\n        this.context = context;\n        return super.attach(host);\n    }\n    detach() {\n        this.context = undefined;\n        return super.detach();\n    }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n    /** DOM node hosting the portal's content. */\n    element;\n    constructor(element) {\n        super();\n        this.element = element instanceof ElementRef ? element.nativeElement : element;\n    }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n    /** The portal currently attached to the host. */\n    _attachedPortal;\n    /** A function that will permanently dispose this host. */\n    _disposeFn;\n    /** Whether this host has already been permanently disposed. */\n    _isDisposed = false;\n    /** Whether this host has an attached portal. */\n    hasAttached() {\n        return !!this._attachedPortal;\n    }\n    /** Attaches a portal. */\n    attach(portal) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!portal) {\n                throwNullPortalError();\n            }\n            if (this.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n            if (this._isDisposed) {\n                throwPortalOutletAlreadyDisposedError();\n            }\n        }\n        if (portal instanceof ComponentPortal) {\n            this._attachedPortal = portal;\n            return this.attachComponentPortal(portal);\n        }\n        else if (portal instanceof TemplatePortal) {\n            this._attachedPortal = portal;\n            return this.attachTemplatePortal(portal);\n            // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n        }\n        else if (this.attachDomPortal && portal instanceof DomPortal) {\n            this._attachedPortal = portal;\n            return this.attachDomPortal(portal);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwUnknownPortalTypeError();\n        }\n    }\n    // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n    attachDomPortal = null;\n    /** Detaches a previously attached portal. */\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost(null);\n            this._attachedPortal = null;\n        }\n        this._invokeDisposeFn();\n    }\n    /** Permanently dispose of this portal host. */\n    dispose() {\n        if (this.hasAttached()) {\n            this.detach();\n        }\n        this._invokeDisposeFn();\n        this._isDisposed = true;\n    }\n    /** @docs-private */\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n    _invokeDisposeFn() {\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = null;\n        }\n    }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {\n}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n    outletElement;\n    _appRef;\n    _defaultInjector;\n    _document;\n    /**\n     * @param outletElement Element into which the content is projected.\n     * @param _unusedComponentFactoryResolver Used to resolve the component factory.\n     *   Only required when attaching component portals.\n     * @param _appRef Reference to the application. Only used in component portals when there\n     *   is no `ViewContainerRef` available.\n     * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n     *   have one. Only used for component portals.\n     * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n     *   become a required parameter.\n     */\n    constructor(\n    /** Element into which the content is projected. */\n    outletElement, \n    /**\n     * @deprecated No longer in use. To be removed.\n     * @breaking-change 18.0.0\n     */\n    _unusedComponentFactoryResolver, _appRef, _defaultInjector, \n    /**\n     * @deprecated `_document` Parameter to be made required.\n     * @breaking-change 10.0.0\n     */\n    _document) {\n        super();\n        this.outletElement = outletElement;\n        this._appRef = _appRef;\n        this._defaultInjector = _defaultInjector;\n        this._document = _document;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element.\n     * @param portal Portal to be attached\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the view to the application.\n        if (portal.viewContainerRef) {\n            const injector = portal.injector || portal.viewContainerRef.injector;\n            const ngModuleRef = injector.get(NgModuleRef, null, { optional: true }) || undefined;\n            componentRef = portal.viewContainerRef.createComponent(portal.component, {\n                index: portal.viewContainerRef.length,\n                injector,\n                ngModuleRef,\n                projectableNodes: portal.projectableNodes || undefined,\n            });\n            this.setDisposeFn(() => componentRef.destroy());\n        }\n        else {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n                throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n            }\n            const appRef = this._appRef;\n            const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n            const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n            componentRef = createComponent(portal.component, {\n                elementInjector,\n                environmentInjector,\n                projectableNodes: portal.projectableNodes || undefined,\n            });\n            appRef.attachView(componentRef.hostView);\n            this.setDisposeFn(() => {\n                // Verify that the ApplicationRef has registered views before trying to detach a host view.\n                // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n                if (appRef.viewCount > 0) {\n                    appRef.detachView(componentRef.hostView);\n                }\n                componentRef.destroy();\n            });\n        }\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n        this._attachedPortal = portal;\n        return componentRef;\n    }\n    /**\n     * Attaches a template portal to the DOM as an embedded view.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        let viewContainer = portal.viewContainerRef;\n        let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n        // But for the DomPortalOutlet the view can be added everywhere in the DOM\n        // (e.g Overlay Container) To move the view to the specified host element. We just\n        // re-append the existing root nodes.\n        viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n        // Note that we want to detect changes after the nodes have been moved so that\n        // any directives inside the portal that are looking at the DOM inside a lifecycle\n        // hook won't be invoked too early.\n        viewRef.detectChanges();\n        this.setDisposeFn(() => {\n            let index = viewContainer.indexOf(viewRef);\n            if (index !== -1) {\n                viewContainer.remove(index);\n            }\n        });\n        this._attachedPortal = portal;\n        // TODO(jelbourn): Return locals from view.\n        return viewRef;\n    }\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        const element = portal.element;\n        if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('DOM portal content must be attached to a parent node.');\n        }\n        // Anchor used to save the element's previous position so\n        // that we can restore it when the portal is detached.\n        const anchorNode = this._document.createComment('dom-portal');\n        element.parentNode.insertBefore(anchorNode, element);\n        this.outletElement.appendChild(element);\n        this._attachedPortal = portal;\n        super.setDisposeFn(() => {\n            // We can't use `replaceWith` here because IE doesn't support it.\n            if (anchorNode.parentNode) {\n                anchorNode.parentNode.replaceChild(element, anchorNode);\n            }\n        });\n    };\n    /**\n     * Clears out a portal from the DOM.\n     */\n    dispose() {\n        super.dispose();\n        this.outletElement.remove();\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {\n}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n    constructor() {\n        const templateRef = inject(TemplateRef);\n        const viewContainerRef = inject(ViewContainerRef);\n        super(templateRef, viewContainerRef);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortal, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkPortal, isStandalone: true, selector: \"[cdkPortal]\", exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortal]',\n                    exportAs: 'cdkPortal',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TemplatePortalDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: TemplatePortalDirective, isStandalone: true, selector: \"[cdk-portal], [portal]\", providers: [\n            {\n                provide: CdkPortal,\n                useExisting: TemplatePortalDirective,\n            },\n        ], exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TemplatePortalDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-portal], [portal]',\n                    exportAs: 'cdkPortal',\n                    providers: [\n                        {\n                            provide: CdkPortal,\n                            useExisting: TemplatePortalDirective,\n                        },\n                    ],\n                }]\n        }] });\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n    _moduleRef = inject(NgModuleRef, { optional: true });\n    _document = inject(DOCUMENT);\n    _viewContainerRef = inject(ViewContainerRef);\n    /** Whether the portal component is initialized. */\n    _isInitialized = false;\n    /** Reference to the currently-attached component/view ref. */\n    _attachedRef;\n    constructor() {\n        super();\n    }\n    /** Portal associated with the Portal outlet. */\n    get portal() {\n        return this._attachedPortal;\n    }\n    set portal(portal) {\n        // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n        // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n        // and attach a portal programmatically in the parent component. When Angular does the first CD\n        // round, it will fire the setter with empty string, causing the user's content to be cleared.\n        if (this.hasAttached() && !portal && !this._isInitialized) {\n            return;\n        }\n        if (this.hasAttached()) {\n            super.detach();\n        }\n        if (portal) {\n            super.attach(portal);\n        }\n        this._attachedPortal = portal || null;\n    }\n    /** Emits when a portal is attached to the outlet. */\n    attached = new EventEmitter();\n    /** Component or view reference that is attached to the portal. */\n    get attachedRef() {\n        return this._attachedRef;\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        super.dispose();\n        this._attachedRef = this._attachedPortal = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        portal.setAttachedHost(this);\n        // If the portal specifies an origin, use that as the logical location of the component\n        // in the application tree. Otherwise use the location of this PortalOutlet.\n        const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n        const ref = viewContainerRef.createComponent(portal.component, {\n            index: viewContainerRef.length,\n            injector: portal.injector || viewContainerRef.injector,\n            projectableNodes: portal.projectableNodes || undefined,\n            ngModuleRef: this._moduleRef || undefined,\n        });\n        // If we're using a view container that's different from the injected one (e.g. when the portal\n        // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n        // inside of the alternate view container.\n        if (viewContainerRef !== this._viewContainerRef) {\n            this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n        }\n        super.setDisposeFn(() => ref.destroy());\n        this._attachedPortal = portal;\n        this._attachedRef = ref;\n        this.attached.emit(ref);\n        return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        portal.setAttachedHost(this);\n        const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        super.setDisposeFn(() => this._viewContainerRef.clear());\n        this._attachedPortal = portal;\n        this._attachedRef = viewRef;\n        this.attached.emit(viewRef);\n        return viewRef;\n    }\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        const element = portal.element;\n        if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('DOM portal content must be attached to a parent node.');\n        }\n        // Anchor used to save the element's previous position so\n        // that we can restore it when the portal is detached.\n        const anchorNode = this._document.createComment('dom-portal');\n        portal.setAttachedHost(this);\n        element.parentNode.insertBefore(anchorNode, element);\n        this._getRootNode().appendChild(element);\n        this._attachedPortal = portal;\n        super.setDisposeFn(() => {\n            if (anchorNode.parentNode) {\n                anchorNode.parentNode.replaceChild(element, anchorNode);\n            }\n        });\n    };\n    /** Gets the root node of the portal outlet. */\n    _getRootNode() {\n        const nativeElement = this._viewContainerRef.element.nativeElement;\n        // The directive could be set on a template which will result in a comment\n        // node being the root. Use the comment's parent node if that is the case.\n        return (nativeElement.nodeType === nativeElement.ELEMENT_NODE\n            ? nativeElement\n            : nativeElement.parentNode);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortalOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkPortalOutlet, isStandalone: true, selector: \"[cdkPortalOutlet]\", inputs: { portal: [\"cdkPortalOutlet\", \"portal\"] }, outputs: { attached: \"attached\" }, exportAs: [\"cdkPortalOutlet\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortalOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalOutlet]',\n                    exportAs: 'cdkPortalOutlet',\n                }]\n        }], ctorParameters: () => [], propDecorators: { portal: [{\n                type: Input,\n                args: ['cdkPortalOutlet']\n            }], attached: [{\n                type: Output\n            }] } });\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalHostDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: PortalHostDirective, isStandalone: true, selector: \"[cdkPortalHost], [portalHost]\", inputs: { portal: [\"cdkPortalHost\", \"portal\"] }, providers: [\n            {\n                provide: CdkPortalOutlet,\n                useExisting: PortalHostDirective,\n            },\n        ], exportAs: [\"cdkPortalHost\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalHostDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalHost], [portalHost]',\n                    exportAs: 'cdkPortalHost',\n                    inputs: [{ name: 'portal', alias: 'cdkPortalHost' }],\n                    providers: [\n                        {\n                            provide: CdkPortalOutlet,\n                            useExisting: PortalHostDirective,\n                        },\n                    ],\n                }]\n        }] });\nclass PortalModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule, imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective], exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                }]\n        }] });\n\nexport { BasePortalOutlet as B, ComponentPortal as C, DomPortal as D, Portal as P, TemplatePortal as T, BasePortalHost as a, DomPortalOutlet as b, DomPortalHost as c, CdkPortal as d, TemplatePortalDirective as e, CdkPortalOutlet as f, PortalHostDirective as g, PortalModule as h };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAChM,SAASC,QAAQ,QAAQ,iBAAiB;;AAE1C;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,KAAK,CAAC,iCAAiC,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC,MAAMD,KAAK,CAAC,oCAAoC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASE,qCAAqCA,CAAA,EAAG;EAC7C,MAAMF,KAAK,CAAC,6CAA6C,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,MAAMH,KAAK,CAAC,+EAA+E,GACvF,wCAAwC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASI,0BAA0BA,CAAA,EAAG;EAClC,MAAMJ,KAAK,CAAC,sDAAsD,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAASK,0BAA0BA,CAAA,EAAG;EAClC,MAAML,KAAK,CAAC,8DAA8D,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA,MAAMM,MAAM,CAAC;EACTC,aAAa;EACb;EACAC,MAAMA,CAACC,IAAI,EAAE;IACT,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAID,IAAI,IAAI,IAAI,EAAE;QACdL,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAIK,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;QACpBV,+BAA+B,CAAC,CAAC;MACrC;IACJ;IACA,IAAI,CAACM,aAAa,GAAGE,IAAI;IACzB,OAAOA,IAAI,CAACD,MAAM,CAAC,IAAI,CAAC;EAC5B;EACA;EACAI,MAAMA,CAAA,EAAG;IACL,IAAIH,IAAI,GAAG,IAAI,CAACF,aAAa;IAC7B,IAAIE,IAAI,IAAI,IAAI,EAAE;MACd,IAAI,CAACF,aAAa,GAAG,IAAI;MACzBE,IAAI,CAACG,MAAM,CAAC,CAAC;IACjB,CAAC,MACI,IAAI,OAAOF,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpDL,0BAA0B,CAAC,CAAC;IAChC;EACJ;EACA;EACA,IAAIQ,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACN,aAAa,IAAI,IAAI;EACrC;EACA;AACJ;AACA;AACA;EACIO,eAAeA,CAACL,IAAI,EAAE;IAClB,IAAI,CAACF,aAAa,GAAGE,IAAI;EAC7B;AACJ;AACA;AACA;AACA;AACA,MAAMM,eAAe,SAAST,MAAM,CAAC;EACjC;EACAU,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,gBAAgB;EAChB;EACAC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,wBAAwB;EACxB;AACJ;AACA;EACIC,gBAAgB;EAChBC,WAAWA,CAACL,SAAS,EAAEC,gBAAgB,EAAEC,QAAQ;EACjD;AACJ;AACA;AACA;EACII,yBAAyB,EAAEF,gBAAgB,EAAE;IACzC,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB;EAC5C;AACJ;AACA;AACA;AACA;AACA,MAAMG,cAAc,SAASjB,MAAM,CAAC;EAChCkB,WAAW;EACXP,gBAAgB;EAChBQ,OAAO;EACPP,QAAQ;EACRG,WAAWA,CACX;EACAG,WAAW,EACX;EACAP,gBAAgB,EAChB;EACAQ,OAAO,EACP;EACAP,QAAQ,EAAE;IACN,KAAK,CAAC,CAAC;IACP,IAAI,CAACM,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACP,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACQ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACP,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIQ,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,WAAW,CAACG,UAAU;EACtC;EACA;AACJ;AACA;AACA;AACA;EACInB,MAAMA,CAACC,IAAI,EAAEgB,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IACjC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,KAAK,CAACjB,MAAM,CAACC,IAAI,CAAC;EAC7B;EACAG,MAAMA,CAAA,EAAG;IACL,IAAI,CAACa,OAAO,GAAGG,SAAS;IACxB,OAAO,KAAK,CAAChB,MAAM,CAAC,CAAC;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,SAAS,SAASvB,MAAM,CAAC;EAC3B;EACAwB,OAAO;EACPT,WAAWA,CAACS,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO,YAAY7C,UAAU,GAAG6C,OAAO,CAACC,aAAa,GAAGD,OAAO;EAClF;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,CAAC;EACnB;EACAC,eAAe;EACf;EACAC,UAAU;EACV;EACAC,WAAW,GAAG,KAAK;EACnB;EACAxB,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACsB,eAAe;EACjC;EACA;EACAzB,MAAMA,CAAC4B,MAAM,EAAE;IACX,IAAI,OAAO1B,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC0B,MAAM,EAAE;QACTrC,oBAAoB,CAAC,CAAC;MAC1B;MACA,IAAI,IAAI,CAACY,WAAW,CAAC,CAAC,EAAE;QACpBV,+BAA+B,CAAC,CAAC;MACrC;MACA,IAAI,IAAI,CAACkC,WAAW,EAAE;QAClBjC,qCAAqC,CAAC,CAAC;MAC3C;IACJ;IACA,IAAIkC,MAAM,YAAYrB,eAAe,EAAE;MACnC,IAAI,CAACkB,eAAe,GAAGG,MAAM;MAC7B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;IAC7C,CAAC,MACI,IAAIA,MAAM,YAAYb,cAAc,EAAE;MACvC,IAAI,CAACU,eAAe,GAAGG,MAAM;MAC7B,OAAO,IAAI,CAACE,oBAAoB,CAACF,MAAM,CAAC;MACxC;IACJ,CAAC,MACI,IAAI,IAAI,CAACG,eAAe,IAAIH,MAAM,YAAYP,SAAS,EAAE;MAC1D,IAAI,CAACI,eAAe,GAAGG,MAAM;MAC7B,OAAO,IAAI,CAACG,eAAe,CAACH,MAAM,CAAC;IACvC;IACA,IAAI,OAAO1B,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CP,2BAA2B,CAAC,CAAC;IACjC;EACJ;EACA;EACAoC,eAAe,GAAG,IAAI;EACtB;EACA3B,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACqB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACnB,eAAe,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACmB,eAAe,GAAG,IAAI;IAC/B;IACA,IAAI,CAACO,gBAAgB,CAAC,CAAC;EAC3B;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC9B,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAAC4B,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACL,WAAW,GAAG,IAAI;EAC3B;EACA;EACAO,YAAYA,CAACC,EAAE,EAAE;IACb,IAAI,CAACT,UAAU,GAAGS,EAAE;EACxB;EACAH,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACN,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC,CAAC;MACjB,IAAI,CAACA,UAAU,GAAG,IAAI;IAC1B;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMU,cAAc,SAASZ,gBAAgB,CAAC;;AAG9C;AACA;AACA;AACA;AACA,MAAMa,eAAe,SAASb,gBAAgB,CAAC;EAC3Cc,aAAa;EACbC,OAAO;EACPC,gBAAgB;EAChBC,SAAS;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5B,WAAWA,CACX;EACAyB,aAAa;EACb;AACJ;AACA;AACA;EACII,+BAA+B,EAAEH,OAAO,EAAEC,gBAAgB;EAC1D;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIZ,qBAAqBA,CAACD,MAAM,EAAE;IAC1B,IAAIe,YAAY;IAChB;IACA;IACA;IACA;IACA,IAAIf,MAAM,CAACnB,gBAAgB,EAAE;MACzB,MAAMC,QAAQ,GAAGkB,MAAM,CAAClB,QAAQ,IAAIkB,MAAM,CAACnB,gBAAgB,CAACC,QAAQ;MACpE,MAAMkC,WAAW,GAAGlC,QAAQ,CAACmC,GAAG,CAACnE,WAAW,EAAE,IAAI,EAAE;QAAEoE,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAI1B,SAAS;MACpFuB,YAAY,GAAGf,MAAM,CAACnB,gBAAgB,CAAC7B,eAAe,CAACgD,MAAM,CAACpB,SAAS,EAAE;QACrEuC,KAAK,EAAEnB,MAAM,CAACnB,gBAAgB,CAACuC,MAAM;QACrCtC,QAAQ;QACRkC,WAAW;QACXhC,gBAAgB,EAAEgB,MAAM,CAAChB,gBAAgB,IAAIQ;MACjD,CAAC,CAAC;MACF,IAAI,CAACc,YAAY,CAAC,MAAMS,YAAY,CAACM,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAC,OAAO/C,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAAC,IAAI,CAACqC,OAAO,EAAE;QAClE,MAAM/C,KAAK,CAAC,qEAAqE,CAAC;MACtF;MACA,MAAM0D,MAAM,GAAG,IAAI,CAACX,OAAO;MAC3B,MAAMY,eAAe,GAAGvB,MAAM,CAAClB,QAAQ,IAAI,IAAI,CAAC8B,gBAAgB,IAAI3D,QAAQ,CAACuE,IAAI;MACjF,MAAMC,mBAAmB,GAAGF,eAAe,CAACN,GAAG,CAAClE,mBAAmB,EAAEuE,MAAM,CAACxC,QAAQ,CAAC;MACrFiC,YAAY,GAAG/D,eAAe,CAACgD,MAAM,CAACpB,SAAS,EAAE;QAC7C2C,eAAe;QACfE,mBAAmB;QACnBzC,gBAAgB,EAAEgB,MAAM,CAAChB,gBAAgB,IAAIQ;MACjD,CAAC,CAAC;MACF8B,MAAM,CAACI,UAAU,CAACX,YAAY,CAACY,QAAQ,CAAC;MACxC,IAAI,CAACrB,YAAY,CAAC,MAAM;QACpB;QACA;QACA,IAAIgB,MAAM,CAACM,SAAS,GAAG,CAAC,EAAE;UACtBN,MAAM,CAACO,UAAU,CAACd,YAAY,CAACY,QAAQ,CAAC;QAC5C;QACAZ,YAAY,CAACM,OAAO,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAACX,aAAa,CAACoB,WAAW,CAAC,IAAI,CAACC,qBAAqB,CAAChB,YAAY,CAAC,CAAC;IACxE,IAAI,CAAClB,eAAe,GAAGG,MAAM;IAC7B,OAAOe,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIb,oBAAoBA,CAACF,MAAM,EAAE;IACzB,IAAIgC,aAAa,GAAGhC,MAAM,CAACnB,gBAAgB;IAC3C,IAAIoD,OAAO,GAAGD,aAAa,CAACE,kBAAkB,CAAClC,MAAM,CAACZ,WAAW,EAAEY,MAAM,CAACX,OAAO,EAAE;MAC/EP,QAAQ,EAAEkB,MAAM,CAAClB;IACrB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACAmD,OAAO,CAACE,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI,IAAI,CAAC3B,aAAa,CAACoB,WAAW,CAACO,QAAQ,CAAC,CAAC;IAC/E;IACA;IACA;IACAJ,OAAO,CAACK,aAAa,CAAC,CAAC;IACvB,IAAI,CAAChC,YAAY,CAAC,MAAM;MACpB,IAAIa,KAAK,GAAGa,aAAa,CAACO,OAAO,CAACN,OAAO,CAAC;MAC1C,IAAId,KAAK,KAAK,CAAC,CAAC,EAAE;QACda,aAAa,CAACQ,MAAM,CAACrB,KAAK,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,IAAI,CAACtB,eAAe,GAAGG,MAAM;IAC7B;IACA,OAAOiC,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9B,eAAe,GAAIH,MAAM,IAAK;IAC1B,MAAMN,OAAO,GAAGM,MAAM,CAACN,OAAO;IAC9B,IAAI,CAACA,OAAO,CAAC+C,UAAU,KAAK,OAAOnE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACxE,MAAMV,KAAK,CAAC,uDAAuD,CAAC;IACxE;IACA;IACA;IACA,MAAM8E,UAAU,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,aAAa,CAAC,YAAY,CAAC;IAC7DjD,OAAO,CAAC+C,UAAU,CAACG,YAAY,CAACF,UAAU,EAAEhD,OAAO,CAAC;IACpD,IAAI,CAACgB,aAAa,CAACoB,WAAW,CAACpC,OAAO,CAAC;IACvC,IAAI,CAACG,eAAe,GAAGG,MAAM;IAC7B,KAAK,CAACM,YAAY,CAAC,MAAM;MACrB;MACA,IAAIoC,UAAU,CAACD,UAAU,EAAE;QACvBC,UAAU,CAACD,UAAU,CAACI,YAAY,CAACnD,OAAO,EAAEgD,UAAU,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACIrC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACK,aAAa,CAAC8B,MAAM,CAAC,CAAC;EAC/B;EACA;EACAT,qBAAqBA,CAAChB,YAAY,EAAE;IAChC,OAAOA,YAAY,CAACY,QAAQ,CAACQ,SAAS,CAAC,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMW,aAAa,SAASrC,eAAe,CAAC;;AAG5C;AACA;AACA;AACA;AACA,MAAMsC,SAAS,SAAS5D,cAAc,CAAC;EACnCF,WAAWA,CAAA,EAAG;IACV,MAAMG,WAAW,GAAGlC,MAAM,CAACC,WAAW,CAAC;IACvC,MAAM0B,gBAAgB,GAAG3B,MAAM,CAACE,gBAAgB,CAAC;IACjD,KAAK,CAACgC,WAAW,EAAEP,gBAAgB,CAAC;EACxC;EACA,OAAOmE,IAAI,YAAAC,kBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,SAAS;EAAA;EAC5G,OAAOI,IAAI,kBAD8EvG,EAAE,CAAAwG,iBAAA;IAAAC,IAAA,EACJN,SAAS;IAAAO,SAAA;IAAAC,QAAA;IAAAC,QAAA,GADP5G,EAAE,CAAA6G,0BAAA;EAAA;AAE/F;AACA;EAAA,QAAAnF,SAAA,oBAAAA,SAAA,KAH6F1B,EAAE,CAAA8G,iBAAA,CAGJX,SAAS,EAAc,CAAC;IACvGM,IAAI,EAAEhG,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBL,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMM,uBAAuB,SAASd,SAAS,CAAC;EAC5C,OAAOC,IAAI;IAAA,IAAAc,oCAAA;IAAA,gBAAAC,gCAAAb,iBAAA;MAAA,QAAAY,oCAAA,KAAAA,oCAAA,GAf8ElH,EAAE,CAAAoH,qBAAA,CAeQH,uBAAuB,IAAAX,iBAAA,IAAvBW,uBAAuB;IAAA;EAAA;EAC1H,OAAOV,IAAI,kBAhB8EvG,EAAE,CAAAwG,iBAAA;IAAAC,IAAA,EAgBJQ,uBAAuB;IAAAP,SAAA;IAAAC,QAAA;IAAAC,QAAA,GAhBrB5G,EAAE,CAAAqH,kBAAA,CAgBwF,CAC3K;MACIC,OAAO,EAAEnB,SAAS;MAClBoB,WAAW,EAAEN;IACjB,CAAC,CACJ,GArBoFjH,EAAE,CAAA6G,0BAAA;EAAA;AAsB/F;AACA;EAAA,QAAAnF,SAAA,oBAAAA,SAAA,KAvB6F1B,EAAE,CAAA8G,iBAAA,CAuBJG,uBAAuB,EAAc,CAAC;IACrHR,IAAI,EAAEhG,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCL,QAAQ,EAAE,WAAW;MACrBa,SAAS,EAAE,CACP;QACIF,OAAO,EAAEnB,SAAS;QAClBoB,WAAW,EAAEN;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,eAAe,SAASzE,gBAAgB,CAAC;EAC3C0E,UAAU,GAAGpH,MAAM,CAACJ,WAAW,EAAE;IAAEoE,QAAQ,EAAE;EAAK,CAAC,CAAC;EACpDL,SAAS,GAAG3D,MAAM,CAACQ,QAAQ,CAAC;EAC5B6G,iBAAiB,GAAGrH,MAAM,CAACE,gBAAgB,CAAC;EAC5C;EACAoH,cAAc,GAAG,KAAK;EACtB;EACAC,YAAY;EACZxF,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACA;EACA,IAAIe,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACH,eAAe;EAC/B;EACA,IAAIG,MAAMA,CAACA,MAAM,EAAE;IACf;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACzB,WAAW,CAAC,CAAC,IAAI,CAACyB,MAAM,IAAI,CAAC,IAAI,CAACwE,cAAc,EAAE;MACvD;IACJ;IACA,IAAI,IAAI,CAACjG,WAAW,CAAC,CAAC,EAAE;MACpB,KAAK,CAACC,MAAM,CAAC,CAAC;IAClB;IACA,IAAIwB,MAAM,EAAE;MACR,KAAK,CAAC5B,MAAM,CAAC4B,MAAM,CAAC;IACxB;IACA,IAAI,CAACH,eAAe,GAAGG,MAAM,IAAI,IAAI;EACzC;EACA;EACA0E,QAAQ,GAAG,IAAIpH,YAAY,CAAC,CAAC;EAC7B;EACA,IAAIqH,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,YAAY;EAC5B;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACJ,cAAc,GAAG,IAAI;EAC9B;EACAK,WAAWA,CAAA,EAAG;IACV,KAAK,CAACxE,OAAO,CAAC,CAAC;IACf,IAAI,CAACoE,YAAY,GAAG,IAAI,CAAC5E,eAAe,GAAG,IAAI;EACnD;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,qBAAqBA,CAACD,MAAM,EAAE;IAC1BA,MAAM,CAACtB,eAAe,CAAC,IAAI,CAAC;IAC5B;IACA;IACA,MAAMG,gBAAgB,GAAGmB,MAAM,CAACnB,gBAAgB,IAAI,IAAI,GAAGmB,MAAM,CAACnB,gBAAgB,GAAG,IAAI,CAAC0F,iBAAiB;IAC3G,MAAMO,GAAG,GAAGjG,gBAAgB,CAAC7B,eAAe,CAACgD,MAAM,CAACpB,SAAS,EAAE;MAC3DuC,KAAK,EAAEtC,gBAAgB,CAACuC,MAAM;MAC9BtC,QAAQ,EAAEkB,MAAM,CAAClB,QAAQ,IAAID,gBAAgB,CAACC,QAAQ;MACtDE,gBAAgB,EAAEgB,MAAM,CAAChB,gBAAgB,IAAIQ,SAAS;MACtDwB,WAAW,EAAE,IAAI,CAACsD,UAAU,IAAI9E;IACpC,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAIX,gBAAgB,KAAK,IAAI,CAAC0F,iBAAiB,EAAE;MAC7C,IAAI,CAACQ,YAAY,CAAC,CAAC,CAACjD,WAAW,CAACgD,GAAG,CAACnD,QAAQ,CAACQ,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9D;IACA,KAAK,CAAC7B,YAAY,CAAC,MAAMwE,GAAG,CAACzD,OAAO,CAAC,CAAC,CAAC;IACvC,IAAI,CAACxB,eAAe,GAAGG,MAAM;IAC7B,IAAI,CAACyE,YAAY,GAAGK,GAAG;IACvB,IAAI,CAACJ,QAAQ,CAACM,IAAI,CAACF,GAAG,CAAC;IACvB,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;EACI5E,oBAAoBA,CAACF,MAAM,EAAE;IACzBA,MAAM,CAACtB,eAAe,CAAC,IAAI,CAAC;IAC5B,MAAMuD,OAAO,GAAG,IAAI,CAACsC,iBAAiB,CAACrC,kBAAkB,CAAClC,MAAM,CAACZ,WAAW,EAAEY,MAAM,CAACX,OAAO,EAAE;MAC1FP,QAAQ,EAAEkB,MAAM,CAAClB;IACrB,CAAC,CAAC;IACF,KAAK,CAACwB,YAAY,CAAC,MAAM,IAAI,CAACiE,iBAAiB,CAACU,KAAK,CAAC,CAAC,CAAC;IACxD,IAAI,CAACpF,eAAe,GAAGG,MAAM;IAC7B,IAAI,CAACyE,YAAY,GAAGxC,OAAO;IAC3B,IAAI,CAACyC,QAAQ,CAACM,IAAI,CAAC/C,OAAO,CAAC;IAC3B,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9B,eAAe,GAAIH,MAAM,IAAK;IAC1B,MAAMN,OAAO,GAAGM,MAAM,CAACN,OAAO;IAC9B,IAAI,CAACA,OAAO,CAAC+C,UAAU,KAAK,OAAOnE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACxE,MAAMV,KAAK,CAAC,uDAAuD,CAAC;IACxE;IACA;IACA;IACA,MAAM8E,UAAU,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,aAAa,CAAC,YAAY,CAAC;IAC7D3C,MAAM,CAACtB,eAAe,CAAC,IAAI,CAAC;IAC5BgB,OAAO,CAAC+C,UAAU,CAACG,YAAY,CAACF,UAAU,EAAEhD,OAAO,CAAC;IACpD,IAAI,CAACqF,YAAY,CAAC,CAAC,CAACjD,WAAW,CAACpC,OAAO,CAAC;IACxC,IAAI,CAACG,eAAe,GAAGG,MAAM;IAC7B,KAAK,CAACM,YAAY,CAAC,MAAM;MACrB,IAAIoC,UAAU,CAACD,UAAU,EAAE;QACvBC,UAAU,CAACD,UAAU,CAACI,YAAY,CAACnD,OAAO,EAAEgD,UAAU,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EACD;EACAqC,YAAYA,CAAA,EAAG;IACX,MAAMpF,aAAa,GAAG,IAAI,CAAC4E,iBAAiB,CAAC7E,OAAO,CAACC,aAAa;IAClE;IACA;IACA,OAAQA,aAAa,CAACuF,QAAQ,KAAKvF,aAAa,CAACwF,YAAY,GACvDxF,aAAa,GACbA,aAAa,CAAC8C,UAAU;EAClC;EACA,OAAOO,IAAI,YAAAoC,wBAAAlC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmB,eAAe;EAAA;EAClH,OAAOlB,IAAI,kBAtK8EvG,EAAE,CAAAwG,iBAAA;IAAAC,IAAA,EAsKJgB,eAAe;IAAAf,SAAA;IAAA+B,MAAA;MAAArF,MAAA;IAAA;IAAAsF,OAAA;MAAAZ,QAAA;IAAA;IAAAnB,QAAA;IAAAC,QAAA,GAtKb5G,EAAE,CAAA6G,0BAAA;EAAA;AAuK/F;AACA;EAAA,QAAAnF,SAAA,oBAAAA,SAAA,KAxK6F1B,EAAE,CAAA8G,iBAAA,CAwKJW,eAAe,EAAc,CAAC;IAC7GhB,IAAI,EAAEhG,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BL,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEvD,MAAM,EAAE,CAAC;MACjDqD,IAAI,EAAE9F,KAAK;MACXoG,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEe,QAAQ,EAAE,CAAC;MACXrB,IAAI,EAAE7F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM+H,mBAAmB,SAASlB,eAAe,CAAC;EAC9C,OAAOrB,IAAI;IAAA,IAAAwC,gCAAA;IAAA,gBAAAC,4BAAAvC,iBAAA;MAAA,QAAAsC,gCAAA,KAAAA,gCAAA,GAzL8E5I,EAAE,CAAAoH,qBAAA,CAyLQuB,mBAAmB,IAAArC,iBAAA,IAAnBqC,mBAAmB;IAAA;EAAA;EACtH,OAAOpC,IAAI,kBA1L8EvG,EAAE,CAAAwG,iBAAA;IAAAC,IAAA,EA0LJkC,mBAAmB;IAAAjC,SAAA;IAAA+B,MAAA;MAAArF,MAAA;IAAA;IAAAuD,QAAA;IAAAC,QAAA,GA1LjB5G,EAAE,CAAAqH,kBAAA,CA0L4I,CAC/N;MACIC,OAAO,EAAEG,eAAe;MACxBF,WAAW,EAAEoB;IACjB,CAAC,CACJ,GA/LoF3I,EAAE,CAAA6G,0BAAA;EAAA;AAgM/F;AACA;EAAA,QAAAnF,SAAA,oBAAAA,SAAA,KAjM6F1B,EAAE,CAAA8G,iBAAA,CAiMJ6B,mBAAmB,EAAc,CAAC;IACjHlC,IAAI,EAAEhG,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzCL,QAAQ,EAAE,eAAe;MACzB8B,MAAM,EAAE,CAAC;QAAEK,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAgB,CAAC,CAAC;MACpDvB,SAAS,EAAE,CACP;QACIF,OAAO,EAAEG,eAAe;QACxBF,WAAW,EAAEoB;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMK,YAAY,CAAC;EACf,OAAO5C,IAAI,YAAA6C,qBAAA3C,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0C,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBAjN8ElJ,EAAE,CAAAmJ,gBAAA;IAAA1C,IAAA,EAiNSuC;EAAY;EAChH,OAAOI,IAAI,kBAlN8EpJ,EAAE,CAAAqJ,gBAAA;AAmN/F;AACA;EAAA,QAAA3H,SAAA,oBAAAA,SAAA,KApN6F1B,EAAE,CAAA8G,iBAAA,CAoNJkC,YAAY,EAAc,CAAC;IAC1GvC,IAAI,EAAE5F,QAAQ;IACdkG,IAAI,EAAE,CAAC;MACCuC,OAAO,EAAE,CAACnD,SAAS,EAAEsB,eAAe,EAAER,uBAAuB,EAAE0B,mBAAmB,CAAC;MACnFY,OAAO,EAAE,CAACpD,SAAS,EAAEsB,eAAe,EAAER,uBAAuB,EAAE0B,mBAAmB;IACtF,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAAS3F,gBAAgB,IAAIwG,CAAC,EAAEzH,eAAe,IAAI0H,CAAC,EAAE5G,SAAS,IAAI6G,CAAC,EAAEpI,MAAM,IAAIqI,CAAC,EAAEpH,cAAc,IAAIqH,CAAC,EAAEhG,cAAc,IAAIiG,CAAC,EAAEhG,eAAe,IAAIiG,CAAC,EAAE5D,aAAa,IAAI6D,CAAC,EAAE5D,SAAS,IAAI6D,CAAC,EAAE/C,uBAAuB,IAAIgD,CAAC,EAAExC,eAAe,IAAIyC,CAAC,EAAEvB,mBAAmB,IAAIwB,CAAC,EAAEnB,YAAY,IAAIoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}