{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-ChV6uQgD.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-CAX2sutq.mjs';\nlet MatOptionModule = /*#__PURE__*/(() => {\n  class MatOptionModule {\n    static ɵfac = function MatOptionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOptionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatOptionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule]\n    });\n  }\n  return MatOptionModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatOptionModule as M };\n//# sourceMappingURL=index-DOxJc1m4.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}