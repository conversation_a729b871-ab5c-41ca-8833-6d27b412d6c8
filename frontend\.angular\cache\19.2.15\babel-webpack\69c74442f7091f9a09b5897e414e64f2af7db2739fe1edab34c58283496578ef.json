{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ThemeService {\n  constructor() {\n    this.THEME_KEY = 'spt-theme';\n    this.themeSubject = new BehaviorSubject('light');\n    this.isDarkSubject = new BehaviorSubject(false);\n    this.theme$ = this.themeSubject.asObservable();\n    this.isDark$ = this.isDarkSubject.asObservable();\n    this.initializeTheme();\n    this.setupMediaQueryListener();\n  }\n  initializeTheme() {\n    const savedTheme = localStorage.getItem(this.THEME_KEY);\n    const theme = savedTheme || 'light';\n    this.setTheme(theme);\n  }\n  setupMediaQueryListener() {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    mediaQuery.addEventListener('change', () => {\n      if (this.themeSubject.value === 'auto') {\n        this.updateDarkMode();\n      }\n    });\n  }\n  setTheme(theme) {\n    this.themeSubject.next(theme);\n    localStorage.setItem(this.THEME_KEY, theme);\n    this.updateDarkMode();\n  }\n  toggleTheme() {\n    const currentTheme = this.themeSubject.value;\n    const newTheme = currentTheme === 'light' ? 'dark' : 'light';\n    this.setTheme(newTheme);\n  }\n  updateDarkMode() {\n    const theme = this.themeSubject.value;\n    let isDark = false;\n    if (theme === 'dark') {\n      isDark = true;\n    } else if (theme === 'auto') {\n      isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    }\n    this.isDarkSubject.next(isDark);\n    this.applyTheme(isDark);\n  }\n  applyTheme(isDark) {\n    const root = document.documentElement;\n    if (isDark) {\n      root.setAttribute('data-theme', 'dark');\n      root.classList.add('dark');\n    } else {\n      root.setAttribute('data-theme', 'light');\n      root.classList.remove('dark');\n    }\n  }\n  getCurrentTheme() {\n    return this.themeSubject.value;\n  }\n  isDarkMode() {\n    return this.isDarkSubject.value;\n  }\n  static {\n    this.ɵfac = function ThemeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThemeService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ThemeService,\n      factory: ThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ThemeService", "constructor", "THEME_KEY", "themeSubject", "isDarkSubject", "theme$", "asObservable", "isDark$", "initializeTheme", "setupMediaQueryListener", "savedTheme", "localStorage", "getItem", "theme", "setTheme", "mediaQuery", "window", "matchMedia", "addEventListener", "value", "updateDarkMode", "next", "setItem", "toggleTheme", "currentTheme", "newTheme", "isDark", "matches", "applyTheme", "root", "document", "documentElement", "setAttribute", "classList", "add", "remove", "getCurrentTheme", "isDarkMode", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\services\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\n\nexport type Theme = 'light' | 'dark' | 'auto';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ThemeService {\n  private readonly THEME_KEY = 'spt-theme';\n  private themeSubject = new BehaviorSubject<Theme>('light');\n  private isDarkSubject = new BehaviorSubject<boolean>(false);\n\n  public theme$ = this.themeSubject.asObservable();\n  public isDark$ = this.isDarkSubject.asObservable();\n\n  constructor() {\n    this.initializeTheme();\n    this.setupMediaQueryListener();\n  }\n\n  private initializeTheme(): void {\n    const savedTheme = localStorage.getItem(this.THEME_KEY) as Theme;\n    const theme = savedTheme || 'light';\n    this.setTheme(theme);\n  }\n\n  private setupMediaQueryListener(): void {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    mediaQuery.addEventListener('change', () => {\n      if (this.themeSubject.value === 'auto') {\n        this.updateDarkMode();\n      }\n    });\n  }\n\n  setTheme(theme: Theme): void {\n    this.themeSubject.next(theme);\n    localStorage.setItem(this.THEME_KEY, theme);\n    this.updateDarkMode();\n  }\n\n  toggleTheme(): void {\n    const currentTheme = this.themeSubject.value;\n    const newTheme = currentTheme === 'light' ? 'dark' : 'light';\n    this.setTheme(newTheme);\n  }\n\n  private updateDarkMode(): void {\n    const theme = this.themeSubject.value;\n    let isDark = false;\n\n    if (theme === 'dark') {\n      isDark = true;\n    } else if (theme === 'auto') {\n      isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    }\n\n    this.isDarkSubject.next(isDark);\n    this.applyTheme(isDark);\n  }\n\n  private applyTheme(isDark: boolean): void {\n    const root = document.documentElement;\n    \n    if (isDark) {\n      root.setAttribute('data-theme', 'dark');\n      root.classList.add('dark');\n    } else {\n      root.setAttribute('data-theme', 'light');\n      root.classList.remove('dark');\n    }\n  }\n\n  getCurrentTheme(): Theme {\n    return this.themeSubject.value;\n  }\n\n  isDarkMode(): boolean {\n    return this.isDarkSubject.value;\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;;AAOlD,OAAM,MAAOC,YAAY;EAQvBC,YAAA;IAPiB,KAAAC,SAAS,GAAG,WAAW;IAChC,KAAAC,YAAY,GAAG,IAAIJ,eAAe,CAAQ,OAAO,CAAC;IAClD,KAAAK,aAAa,GAAG,IAAIL,eAAe,CAAU,KAAK,CAAC;IAEpD,KAAAM,MAAM,GAAG,IAAI,CAACF,YAAY,CAACG,YAAY,EAAE;IACzC,KAAAC,OAAO,GAAG,IAAI,CAACH,aAAa,CAACE,YAAY,EAAE;IAGhD,IAAI,CAACE,eAAe,EAAE;IACtB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEQD,eAAeA,CAAA;IACrB,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACV,SAAS,CAAU;IAChE,MAAMW,KAAK,GAAGH,UAAU,IAAI,OAAO;IACnC,IAAI,CAACI,QAAQ,CAACD,KAAK,CAAC;EACtB;EAEQJ,uBAAuBA,CAAA;IAC7B,MAAMM,UAAU,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;IACpEF,UAAU,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACzC,IAAI,IAAI,CAACf,YAAY,CAACgB,KAAK,KAAK,MAAM,EAAE;QACtC,IAAI,CAACC,cAAc,EAAE;MACvB;IACF,CAAC,CAAC;EACJ;EAEAN,QAAQA,CAACD,KAAY;IACnB,IAAI,CAACV,YAAY,CAACkB,IAAI,CAACR,KAAK,CAAC;IAC7BF,YAAY,CAACW,OAAO,CAAC,IAAI,CAACpB,SAAS,EAAEW,KAAK,CAAC;IAC3C,IAAI,CAACO,cAAc,EAAE;EACvB;EAEAG,WAAWA,CAAA;IACT,MAAMC,YAAY,GAAG,IAAI,CAACrB,YAAY,CAACgB,KAAK;IAC5C,MAAMM,QAAQ,GAAGD,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC5D,IAAI,CAACV,QAAQ,CAACW,QAAQ,CAAC;EACzB;EAEQL,cAAcA,CAAA;IACpB,MAAMP,KAAK,GAAG,IAAI,CAACV,YAAY,CAACgB,KAAK;IACrC,IAAIO,MAAM,GAAG,KAAK;IAElB,IAAIb,KAAK,KAAK,MAAM,EAAE;MACpBa,MAAM,GAAG,IAAI;IACf,CAAC,MAAM,IAAIb,KAAK,KAAK,MAAM,EAAE;MAC3Ba,MAAM,GAAGV,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACU,OAAO;IACpE;IAEA,IAAI,CAACvB,aAAa,CAACiB,IAAI,CAACK,MAAM,CAAC;IAC/B,IAAI,CAACE,UAAU,CAACF,MAAM,CAAC;EACzB;EAEQE,UAAUA,CAACF,MAAe;IAChC,MAAMG,IAAI,GAAGC,QAAQ,CAACC,eAAe;IAErC,IAAIL,MAAM,EAAE;MACVG,IAAI,CAACG,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;MACvCH,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAC5B,CAAC,MAAM;MACLL,IAAI,CAACG,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;MACxCH,IAAI,CAACI,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;IAC/B;EACF;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACjC,YAAY,CAACgB,KAAK;EAChC;EAEAkB,UAAUA,CAAA;IACR,OAAO,IAAI,CAACjC,aAAa,CAACe,KAAK;EACjC;;;uCAxEWnB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAsC,OAAA,EAAZtC,YAAY,CAAAuC,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}