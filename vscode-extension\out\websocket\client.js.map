{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/websocket/client.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,4CAA2B;AA2B3B,MAAa,eAAe;IAYxB,YAAoB,aAAmC;QAAnC,kBAAa,GAAb,aAAa,CAAsB;QAX/C,OAAE,GAAqB,IAAI,CAAC;QAC5B,mBAAc,GAA0B,IAAI,CAAC;QAC7C,iBAAY,GAAY,KAAK,CAAC;QAC9B,sBAAiB,GAAW,CAAC,CAAC;QAC9B,yBAAoB,GAAW,CAAC,CAAC;QACjC,mBAAc,GAAW,IAAI,CAAC,CAAC,YAAY;QAG3C,iBAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAAoB,CAAC;QACnD,cAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;QAGhD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAClD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,EAAE,CACL,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,0BAA0B,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAE1B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,OAAO;QACX,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,EAAE,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE;YAC7D,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEnC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QACpD,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;QAEvD,IAAI;YACA,IAAI,CAAC,EAAE,GAAG,IAAI,YAAS,CAAC,KAAK,CAAC,CAAC;YAE/B,IAAI,IAAI,CAAC,EAAE,EAAE;gBACT,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACxB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;oBACvC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBAC3B,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;oBAElC,uBAAuB;oBACvB,IAAI,CAAC,IAAI,CAAC;wBACN,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE;4BACF,WAAW,EAAE,kBAAkB;4BAC/B,OAAO,EAAE,OAAO;yBACnB;wBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACtC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;gBAEC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAoB,EAAE,EAAE;oBAC3C,IAAI;wBACA,MAAM,OAAO,GAAqB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;wBAC9D,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;qBAC/B;oBAAC,OAAO,KAAK,EAAE;wBACZ,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;qBAC9D;gBACL,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,MAAc,EAAE,EAAE;oBACjD,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,MAAM,MAAM,EAAE,CAAC,CAAC;oBACzD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;oBACf,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;oBACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC7B,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;oBACjC,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;oBAC7C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC7B,CAAC,CAAC,CAAC;aACN;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;IACL,CAAC;IAEO,aAAa,CAAC,OAAyB;QAC3C,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzD,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,SAAS;gBACV,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;gBAC3E,MAAM;YAEV,KAAK,eAAe;gBAChB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAA2B,CAAC,CAAC;gBAC7D,MAAM;YAEV,KAAK,aAAa;gBACd,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAyB,CAAC,CAAC;gBACzD,MAAM;YAEV,KAAK,cAAc;gBACf,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM;YAEV,KAAK,MAAM;gBACP,kCAAkC;gBAClC,MAAM;YAEV;gBACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;SAC1D;QAED,4CAA4C;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEO,kBAAkB,CAAC,QAA6B;QACpD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC;QAE5D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YACvB,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;YAC9C,KAAK,EAAE,aAAa,QAAQ,CAAC,OAAO,EAAE;YACtC,WAAW,EAAE,KAAK;SACrB,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE;YAC1B,gBAAgB,CAAC,MAAM,CAAC;gBACpB,SAAS,EAAE,eAAe;gBAC1B,OAAO,EAAE,GAAG,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,WAAW,WAAW,QAAQ,CAAC,YAAY,gBAAgB;aAClI,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,qBAAqB,eAAe,GAAG,CAAC;QAClE,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,yBAAyB,QAAQ,CAAC,YAAY,IAAI,eAAe,EAAE,CAAC;IACrG,CAAC;IAEO,gBAAgB,CAAC,MAAyB;QAC9C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;QACxC,MAAM,OAAO,GAAG,UAAU,GAAG,CAAC;YAC1B,CAAC,CAAC,mBAAmB,UAAU,wBAAwB;YACvD,CAAC,CAAC,0CAA0C,CAAC;QAEjD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YAC3E,IAAI,SAAS,KAAK,cAAc,EAAE;gBAC9B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;aAC5D;QACL,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAEO,iBAAiB,CAAC,KAAU;QAChC,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC;QAC7C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;QAEzD,QAAQ,SAAS,EAAE;YACf,KAAK,OAAO;gBACR,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,cAAc,OAAO,EAAE,CAAC,CAAC;gBACxD,MAAM;YACV,KAAK,SAAS;gBACV,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,cAAc,OAAO,EAAE,CAAC,CAAC;gBAC1D,MAAM;YACV;gBACI,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,OAAO,EAAE,CAAC,CAAC;SACrE;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACrC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACrD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAClC,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE3D,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,iBAAiB,OAAO,KAAK,IAAI,CAAC,CAAC;QAEpF,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,EAAE,KAAK,CAAC,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE/B,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACjD,qDAAqD,EACrD;YACI,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,+DAA+D;SAC1E,EACD;YACI,KAAK,EAAE,uBAAuB;YAC9B,MAAM,EAAE,+DAA+D;SAC1E,EACD;YACI,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,gCAAgC;SAC3C,EACD;YACI,KAAK,EAAE,mBAAmB;YAC1B,MAAM,EAAE,yDAAyD;SACpE,CACJ,CAAC;QAEF,QAAQ,MAAM,EAAE,KAAK,EAAE;YACnB,KAAK,uBAAuB;gBACxB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACjC,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM;YACV,KAAK,mBAAmB,CAAC;YACzB;gBACI,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,iIAAiI,EACjI,eAAe,CAClB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACf,IAAI,SAAS,KAAK,eAAe,EAAE;wBAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,mBAAmB,CAAC,CAAC;qBACxF;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;SACb;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,2CAA2C;QAC3C,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEhH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wFAAwF,CAAC,CAAC;QAC/H,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,aAAa;QACjB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEnC,MAAM,gBAAgB,GAAG,GAAG,EAAE;YAC1B,IAAI,IAAI,CAAC,EAAE,EAAE,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE;gBACxC,OAAO,CAAC,oBAAoB;aAC/B;YAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;YAEf,+CAA+C;YAC/C,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;gBAClC,IAAI,IAAI,CAAC,EAAE,EAAE,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE;oBACxC,gBAAgB,EAAE,CAAC;iBACtB;YACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,uBAAuB;QACtC,CAAC,CAAC;QAEF,gBAAgB,EAAE,CAAC;IACvB,CAAC;IAEO,eAAe,CAAC,MAAwE;QAC5F,MAAM,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC3D,MAAM,sBAAsB,GAAG,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAErE,QAAQ,MAAM,EAAE;YACZ,KAAK,YAAY;gBACb,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,kBAAkB,CAAC;gBAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,8BAA8B,sBAAsB,EAAE,CAAC;gBACpF,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;gBAC/C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC;gBACvC,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,0BAA0B,sBAAsB,EAAE,CAAC;gBAChF,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;gBAC/C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC;gBACvC,MAAM;YACV,KAAK,cAAc;gBACf,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,yBAAyB,CAAC;gBACpD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,+BAA+B,sBAAsB,uBAAuB,CAAC;gBAC1G,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;gBAC9F,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,wBAAwB,CAAC;gBACtD,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,cAAc,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,uBAAuB,sBAAsB,uBAAuB,CAAC;gBAClG,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;gBAC5F,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,wBAAwB,CAAC;gBACtD,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,UAAU,CAAC;gBACrC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,oBAAoB;oBAC7C,CAAC,CAAC,2BAA2B,sBAAsB,sBAAsB;oBACzE,CAAC,CAAC,qEAAqE,CAAC;gBAC5E,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;gBAC5F,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,wBAAwB,CAAC;gBACzG,MAAM;SACb;IACL,CAAC;IAEM,IAAI,CAAC,OAAyB;QACjC,IAAI,IAAI,CAAC,EAAE,EAAE,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE;YACxC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;SACzC;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,OAAO,CAAC,CAAC;SACvE;IACL,CAAC;IAEM,SAAS;QACZ,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,sBAAsB;QAC1B,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IAChF,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE;YAC/B,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;YAC3E,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;aAAM;YACH,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACvC;IACL,CAAC;IAEM,UAAU;QACb,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;SAClB;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAEM,WAAW;QACd,OAAO,IAAI,CAAC,EAAE,EAAE,UAAU,KAAK,YAAS,CAAC,IAAI,CAAC;IAClD,CAAC;IAEM,mBAAmB;QACtB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACV,OAAO,cAAc,CAAC;SACzB;QAED,QAAQ,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE;YACxB,KAAK,YAAS,CAAC,UAAU;gBACrB,OAAO,YAAY,CAAC;YACxB,KAAK,YAAS,CAAC,IAAI;gBACf,OAAO,WAAW,CAAC;YACvB,KAAK,YAAS,CAAC,OAAO;gBAClB,OAAO,SAAS,CAAC;YACrB,KAAK,YAAS,CAAC,MAAM;gBACjB,OAAO,QAAQ,CAAC;YACpB;gBACI,OAAO,SAAS,CAAC;SACxB;IACL,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;CACJ;AAhYD,0CAgYC"}