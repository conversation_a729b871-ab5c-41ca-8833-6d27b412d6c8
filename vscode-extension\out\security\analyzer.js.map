{"version": 3, "file": "analyzer.js", "sourceRoot": "", "sources": ["../../src/security/analyzer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAuCjC,MAAa,gBAAgB;IAMzB,YACY,SAAuB,EACvB,aAAmC;QADnC,cAAS,GAAT,SAAS,CAAc;QACvB,kBAAa,GAAb,aAAa,CAAsB;QANvC,kBAAa,GAAiC,IAAI,GAAG,EAAE,CAAC;QACxD,eAAU,GAAY,KAAK,CAAC;QAOhC,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC5F,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,sBAAsB,CAAC;QACpD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,WAAmB;QACjC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,yBAAyB,CAAC,CAAC;QAE5D,IAAI;YACA,gBAAgB;YAChB,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBACpC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,yCAAyC;gBAChD,WAAW,EAAE,IAAI;aACpB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACzB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;gBAE/D,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAa,CAAC;gBAElE,IAAI;oBACA,6BAA6B;oBAC7B,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;oBAC7E,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAEhC,uBAAuB;oBACvB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;oBAChE,OAAO,CAAC,GAAG,CAAC,8BAA8B,WAAW,aAAa,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAEvF,iDAAiD;oBACjD,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC;wBACxC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC;wBAC/B,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAE7D,IAAI,YAAY,EAAE;wBACd,OAAO,CAAC,GAAG,CAAC,sEAAsE,CAAC,CAAC;wBACpF,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,kDAAkD,EAAE,CAAC,CAAC;qBACnG;oBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;wBAC1C,YAAY,EAAE,WAAW;wBACzB,MAAM,EAAE,MAAM;wBACd,SAAS,EAAE,MAAM;qBACpB,CAAC,CAAC;oBAEH,IAAI,KAAK,CAAC,uBAAuB,EAAE;wBAC/B,OAAO,IAAI,CAAC;qBACf;oBAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;oBACvD,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC,CAAC;oBAEvF,mBAAmB;oBACnB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;oBAErF,IAAI,UAAU,EAAE;wBACZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;wBAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,mBAAmB,UAAU,CAAC,MAAM,CAAC,MAAM,oBAAoB,UAAU,CAAC,YAAY,QAAQ,CACjG,CAAC;qBACL;oBAED,OAAO,UAAU,CAAC;iBACrB;gBAAC,OAAO,KAAK,EAAE;oBACZ,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;oBACrC,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC5E,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,gBAAgB,YAAY,EAAE,CAAC,CAAC;oBAC9D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,YAAY,EAAE,CAAC,CAAC;oBAC/D,OAAO,IAAI,CAAC;iBACf;YACL,CAAC,CAAC,CAAC;SACN;gBAAS;YACN,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC;SACxD;IACL,CAAC;IAEO,eAAe,CAAC,KAAoC,EAAE,OAAe;QACzE,QAAQ,KAAK,EAAE;YACX,KAAK,UAAU;gBACX,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,kBAAkB,CAAC;gBAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;gBACrC,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,cAAc,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;gBACrC,MAAM;YACV,KAAK,MAAM,CAAC;YACZ;gBACI,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC;gBAC1C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;gBACrC,MAAM;SACb;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,IAAI;YACA,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;YAEpD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;aAC/D;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,sFAAsF,KAAK,EAAE,CAAC,CAAC;SAClH;IACL,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAC3B,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAa,CAAC;YAElE,qCAAqC;YACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE/D,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;gBACzB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACtD,OAAO,MAAM,CAAC,MAAM,CAAC;aACxB;YAED,OAAO,EAAE,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAC/B,MAAc,EACd,QAAmE,EACnE,KAA+B;QAE/B,MAAM,WAAW,GAAG,GAAG,CAAC,CAAC,4CAA4C;QACrE,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,UAAU,GAAG,SAAS,CAAC;QAC3B,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,MAAM,oBAAoB,GAAG,CAAC,CAAC;QAE/B,OAAO,CAAC,GAAG,CAAC,8CAA8C,MAAM,EAAE,CAAC,CAAC;QAEpE,OAAO,QAAQ,GAAG,WAAW,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;YAC7D,IAAI;gBACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC1D,iBAAiB,GAAG,CAAC,CAAC,CAAC,4CAA4C;gBACnE,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;gBAE3B,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,YAAY,MAAM,CAAC,MAAM,cAAc,QAAQ,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC,CAAC;gBAEhG,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE;oBAC/B,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC,CAAC;oBAChE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,yBAAyB,CAAC,CAAC;oBACrD,OAAO,MAAM,CAAC;iBACjB;qBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;oBACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,IAAI,oCAAoC,CAAC;oBACtE,OAAO,CAAC,KAAK,CAAC,QAAQ,MAAM,YAAY,QAAQ,EAAE,CAAC,CAAC;oBACpD,MAAM,IAAI,KAAK,CAAC,gBAAgB,QAAQ,EAAE,CAAC,CAAC;iBAC/C;qBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE;oBACtC,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,gBAAgB,CAAC,CAAC;oBAC5C,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;iBACzC;gBAED,kCAAkC;gBAClC,IAAI,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;gBAClE,IAAI,aAAa,GAAG,gBAAgB,MAAM,CAAC,MAAM,GAAG,CAAC;gBAErD,iDAAiD;gBACjD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;oBAC7B,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;oBACnE,aAAa,GAAG,sBAAsB,QAAQ,GAAG,CAAC,YAAY,CAAC;iBAClE;qBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;oBACpC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;oBAC9D,aAAa,GAAG,2BAA2B,QAAQ,GAAG,CAAC,YAAY,CAAC;iBACvE;gBAED,QAAQ,CAAC,MAAM,CAAC;oBACZ,SAAS,EAAE,eAAe,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrF,OAAO,EAAE,aAAa;iBACzB,CAAC,CAAC;gBAEH,mCAAmC;gBACnC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,QAAQ,EAAE,CAAC;aACd;YAAC,OAAO,KAAK,EAAE;gBACZ,iBAAiB,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,uCAAuC,QAAQ,GAAG,CAAC,yBAAyB,iBAAiB,IAAI,EAAE,KAAK,CAAC,CAAC;gBAExH,oDAAoD;gBACpD,IAAI,iBAAiB,IAAI,oBAAoB,EAAE;oBAC3C,MAAM,QAAQ,GAAG,qCAAqC,oBAAoB,sCAAsC,KAAK,EAAE,CAAC;oBACxH,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACxB,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;iBAC7B;gBAED,6CAA6C;gBAC7C,QAAQ,CAAC,MAAM,CAAC;oBACZ,OAAO,EAAE,kCAAkC,iBAAiB,IAAI,oBAAoB,GAAG;iBAC1F,CAAC,CAAC;gBAEH,QAAQ,EAAE,CAAC;gBACX,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;aAC3D;SACJ;QAED,IAAI,KAAK,CAAC,uBAAuB,EAAE;YAC/B,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,wBAAwB,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,UAAU,GAAG,sBAAsB,WAAW,GAAG,CAAC,0BAA0B,UAAU,sCAAsC,CAAC;QACnI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAsB;QACnD,6BAA6B;QAC7B,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAE3B,uBAAuB;QACvB,MAAM,YAAY,GAAG,IAAI,GAAG,EAA2B,CAAC;QAExD,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE;YACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC7B,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;aAClC;YACD,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;QAED,oBAAoB;QACpB,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,YAAY,EAAE;YAC3C,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SAClD;QAED,8BAA8B;QAC9B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,uBAAuB,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxG,CAAC;IAIO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,MAAuB;QACrE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEzC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;YAC9C,MAAM,WAAW,GAAwB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CACjC,CAAC;gBAEF,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,KAAK,EACL,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,EAAE,EACxE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,CACxC,CAAC;gBAEF,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;gBAC1B,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;gBAE7B,OAAO,UAAU,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;SACnD;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE;YACvD,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SAClD;IACL,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,QAAQ,QAAQ,EAAE;YACd,KAAK,UAAU,CAAC;YAChB,KAAK,MAAM;gBACP,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC3C,KAAK,QAAQ;gBACT,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7C,KAAK,KAAK;gBACN,OAAO,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC;YACjD,KAAK,MAAM;gBACP,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAC1C;gBACI,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;SAChD;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,MAAuB;QACrE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CACnD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,CACpD,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,mDAAmD;YACnD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAElD,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAsC,CAAC;YAExE,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBACxB,MAAM,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACvD,IAAI,CAAC,cAAc,EAAE;oBACjB,SAAS;iBACZ;gBAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;oBACxC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;iBAC7C;gBAED,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CACjC,CAAC;gBAEF,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC;oBACxC,KAAK;oBACL,YAAY,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,KAAK,CAAC,WAAW,oBAAoB,KAAK,CAAC,UAAU,GAAG;iBAClG,CAAC,CAAC;aACN;YAED,oBAAoB;YACpB,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,iBAAiB,EAAE;gBACrD,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;gBACjD,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;aACtD;SACJ;IACL,CAAC;IAEO,kBAAkB;QACtB,OAAO;YACH,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBACnD,eAAe,EAAE,sBAAsB;gBACvC,MAAM,EAAE,eAAe;gBACvB,YAAY,EAAE,KAAK;aACtB,CAAC;YACF,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBAC/C,eAAe,EAAE,wBAAwB;gBACzC,MAAM,EAAE,kBAAkB;gBAC1B,YAAY,EAAE,KAAK;aACtB,CAAC;YACF,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBACjD,eAAe,EAAE,wBAAwB;gBACzC,MAAM,EAAE,kBAAkB;gBAC1B,YAAY,EAAE,KAAK;aACtB,CAAC;YACF,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBAC9C,eAAe,EAAE,sBAAsB;gBACvC,MAAM,EAAE,iBAAiB;gBACzB,YAAY,EAAE,KAAK;aACtB,CAAC;SACL,CAAC;IACN,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,KAAoB;QAC/B,sCAAsC;QACtC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sBAAsB,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;IACnG,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAoB;QAClC,2BAA2B;QAC3B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,KAAK,CAAC,KAAK,mBAAmB,CAAC,CAAC;IAClF,CAAC;IAED,gBAAgB,CAAC,QAAgB;QAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AA1YD,4CA0YC"}