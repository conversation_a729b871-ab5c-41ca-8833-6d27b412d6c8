{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/checkbox\";\nfunction RegisterComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username must be at least 3 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_spinner_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction RegisterComponent_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control) {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return {\n      'passwordMismatch': true\n    };\n  }\n  return null;\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.isLoading = false;\n    this.registerForm = this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: passwordMatchValidator\n    });\n  }\n  onSubmit() {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      const userData = {\n        username: this.registerForm.value.username,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword\n      };\n      this.authService.register(userData).subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate(['/dashboard']);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Registration failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 58,\n      vars: 16,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"logo\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"formControlName\", \"agreeToTerms\", 1, \"terms-checkbox\"], [\"href\", \"#\", 1, \"terms-link\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"auth-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"auth-footer\"], [\"routerLink\", \"/login\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7, \"SPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"h1\");\n          i0.ɵɵtext(9, \"Create Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\");\n          i0.ɵɵtext(11, \"Join the SPT Security Platform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(13, \"mat-form-field\", 5)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 6);\n          i0.ɵɵtemplate(17, RegisterComponent_mat_error_17_Template, 2, 0, \"mat-error\", 7)(18, RegisterComponent_mat_error_18_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 5)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 8);\n          i0.ɵɵtemplate(23, RegisterComponent_mat_error_23_Template, 2, 0, \"mat-error\", 7)(24, RegisterComponent_mat_error_24_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-form-field\", 5)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 9);\n          i0.ɵɵelementStart(29, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_29_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(30, \"mat-icon\");\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(32, RegisterComponent_mat_error_32_Template, 2, 0, \"mat-error\", 7)(33, RegisterComponent_mat_error_33_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-form-field\", 5)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 11);\n          i0.ɵɵelementStart(38, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_38_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(39, \"mat-icon\");\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(41, RegisterComponent_mat_error_41_Template, 2, 0, \"mat-error\", 7)(42, RegisterComponent_mat_error_42_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"mat-checkbox\", 12);\n          i0.ɵɵtext(44, \" I agree to the \");\n          i0.ɵɵelementStart(45, \"a\", 13);\n          i0.ɵɵtext(46, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \" and \");\n          i0.ɵɵelementStart(48, \"a\", 13);\n          i0.ɵɵtext(49, \"Privacy Policy\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"button\", 14);\n          i0.ɵɵtemplate(51, RegisterComponent_mat_spinner_51_Template, 1, 0, \"mat-spinner\", 15)(52, RegisterComponent_span_52_Template, 2, 0, \"span\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 16)(54, \"p\");\n          i0.ɵɵtext(55, \"Already have an account? \");\n          i0.ɵɵelementStart(56, \"a\", 17);\n          i0.ɵɵtext(57, \"Sign in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_11_0;\n          let tmp_12_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_2_0.hasError(\"email\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_3_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_4_0.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_7_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_11_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.registerForm.hasError(\"passwordMismatch\") && ((tmp_12_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatSuffix, MatInputModule, i7.MatInput, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatProgressSpinnerModule, i10.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i11.MatCheckbox],\n      styles: [\".auth-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  padding: 40px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.auth-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.auth-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.auth-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: #667eea;\\n}\\n.auth-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a202c;\\n}\\n.auth-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 28px;\\n  font-weight: 700;\\n  color: #1a202c;\\n}\\n.auth-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 16px;\\n}\\n\\n.auth-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n.auth-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 0;\\n}\\n.auth-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n}\\n.auth-form[_ngcontent-%COMP%]   .terms-checkbox[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 14px;\\n}\\n.auth-form[_ngcontent-%COMP%]   .terms-checkbox[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n}\\n.auth-form[_ngcontent-%COMP%]   .terms-checkbox[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.auth-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-top: 8px;\\n  border-radius: 8px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  color: white;\\n  transition: all 0.2s ease;\\n}\\n.auth-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\\n}\\n.auth-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.auth-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.auth-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 24px;\\n  padding-top: 24px;\\n  border-top: 1px solid #e2e8f0;\\n}\\n.auth-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 14px;\\n}\\n.auth-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 600;\\n}\\n.auth-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.register-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.card-actions[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 14px;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 600;\\n}\\n\\n.login-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.features-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n  padding: 20px;\\n}\\n\\n.features-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 30px;\\n}\\n.features-header[_ngcontent-%COMP%]   .features-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.features-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 20px;\\n  margin-bottom: 16px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateY(-2px);\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 48px;\\n  height: 48px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 10px;\\n  flex-shrink: 0;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  color: white;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: white;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  color: rgba(255, 255, 255, 0.8);\\n  line-height: 1.4;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 30px;\\n    padding: 1rem;\\n  }\\n  .register-card-wrapper[_ngcontent-%COMP%] {\\n    flex: none;\\n    max-width: 100%;\\n  }\\n  .register-card[_ngcontent-%COMP%] {\\n    padding: 30px 20px;\\n  }\\n  .features-section[_ngcontent-%COMP%] {\\n    order: -1;\\n    text-align: center;\\n  }\\n  .logo[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .logo-text[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .brand[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n}\\nmat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\nmat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\nmat-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hdXRoL3JlZ2lzdGVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0UsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLDZEQUFBO0VBQ0EsYUFBQTtBQUFGOztBQUdBO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLDBDQUFBO0VBQ0EsYUFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFBRjtBQUVFO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsV0FBQTtFQUNBLDREQUFBO0FBQUo7O0FBSUE7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0FBREY7QUFHRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsUUFBQTtFQUNBLG1CQUFBO0FBREo7QUFHSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7QUFETjtBQUlJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQUZOO0FBTUU7RUFDRSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFKSjtBQU9FO0VBQ0UsU0FBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBTEo7O0FBU0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBTkY7QUFRRTtFQUNFLFdBQUE7RUFDQSxTQUFBO0FBTko7QUFRSTtFQUNFLGVBQUE7QUFOTjtBQVVFO0VBQ0UsYUFBQTtFQUNBLGVBQUE7QUFSSjtBQVVJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBUk47QUFVTTtFQUNFLDBCQUFBO0FBUlI7O0FBY0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtBQVhGO0FBYUU7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBWEo7QUFjRTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtBQVpKO0FBZUU7RUFDRSxpQkFBQTtBQWJKOztBQWlCQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLDZCQUFBO0FBZEY7QUFnQkU7RUFDRSxTQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QUFkSjtBQWdCSTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0FBZE47QUFnQk07RUFDRSwwQkFBQTtBQWRSOztBQW9CQTtFQUNFLDJCQUFBO0VBQ0EsK0NBQUE7QUFqQkY7O0FBb0JBO0VBQ0Usa0JBQUE7RUFDQSxpQkFBQTtFQUNBLDZCQUFBO0FBakJGOztBQW9CQTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtBQWpCRjs7QUFvQkE7RUFDRSxjQUFBO0VBQ0EscUJBQUE7RUFDQSxnQkFBQTtBQWpCRjs7QUFvQkE7RUFDRSwwQkFBQTtBQWpCRjs7QUFvQkE7RUFDRSxPQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7QUFqQkY7O0FBb0JBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBakJGO0FBbUJFO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsK0JBQUE7QUFqQko7QUFvQkU7RUFDRSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUFsQko7O0FBc0JBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsU0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG9DQUFBO0VBQ0EsbUJBQUE7RUFDQSwwQ0FBQTtFQUNBLHlCQUFBO0FBbkJGO0FBcUJFO0VBQ0UscUNBQUE7RUFDQSwyQkFBQTtBQW5CSjtBQXNCRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxvQ0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtBQXBCSjtBQXNCSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7QUFwQk47QUF3QkU7RUFDRSxPQUFBO0FBdEJKO0FBd0JJO0VBQ0UsaUJBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtBQXRCTjtBQXlCSTtFQUNFLFNBQUE7RUFDQSxpQkFBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7QUF2Qk47O0FBNEJBLHNCQUFBO0FBQ0E7RUFDRTtJQUNFLHNCQUFBO0lBQ0EsU0FBQTtJQUNBLGFBQUE7RUF6QkY7RUE0QkE7SUFDRSxVQUFBO0lBQ0EsZUFBQTtFQTFCRjtFQTZCQTtJQUNFLGtCQUFBO0VBM0JGO0VBOEJBO0lBQ0UsU0FBQTtJQUNBLGtCQUFBO0VBNUJGO0VBK0JBO0lBQ0Usc0JBQUE7SUFDQSxrQkFBQTtFQTdCRjtFQWdDQTtJQUNFLGtCQUFBO0VBOUJGO0VBaUNBO0lBQ0UsaUJBQUE7RUEvQkY7RUFrQ0E7SUFDRSxhQUFBO0VBaENGO0VBbUNBO0lBQ0UsaUJBQUE7RUFqQ0Y7QUFDRjtBQW9DQTtFQUNFLG1CQUFBO0FBbENGOztBQXFDQTtFQUNFLGtCQUFBO0FBbENGOztBQXFDQTtFQUNFLGVBQUE7RUFDQSxlQUFBO0FBbENGIiwic291cmNlc0NvbnRlbnQiOlsiLy8gQ2xlYW4sIG1vZGVybiBhdXRoIGRlc2lnblxuLmF1dGgtY29udGFpbmVyIHtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpO1xuICBwYWRkaW5nOiAyMHB4O1xufVxuXG4uYXV0aC1jYXJkIHtcbiAgd2lkdGg6IDEwMCU7XG4gIG1heC13aWR0aDogNDAwcHg7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHBhZGRpbmc6IDQwcHg7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcblxuICAmOjpiZWZvcmUge1xuICAgIGNvbnRlbnQ6ICcnO1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBoZWlnaHQ6IDRweDtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XG4gIH1cbn1cblxuLmF1dGgtaGVhZGVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAzMnB4O1xuXG4gIC5sb2dvIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgZ2FwOiA4cHg7XG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcblxuICAgIG1hdC1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogMzJweDtcbiAgICAgIHdpZHRoOiAzMnB4O1xuICAgICAgaGVpZ2h0OiAzMnB4O1xuICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgfVxuXG4gICAgc3BhbiB7XG4gICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgY29sb3I6ICMxYTIwMmM7XG4gICAgfVxuICB9XG5cbiAgaDEge1xuICAgIG1hcmdpbjogMCAwIDhweCAwO1xuICAgIGZvbnQtc2l6ZTogMjhweDtcbiAgICBmb250LXdlaWdodDogNzAwO1xuICAgIGNvbG9yOiAjMWEyMDJjO1xuICB9XG5cbiAgcCB7XG4gICAgbWFyZ2luOiAwO1xuICAgIGNvbG9yOiAjNjQ3NDhiO1xuICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgfVxufVxuXG4uYXV0aC1mb3JtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAyMHB4O1xuXG4gIG1hdC1mb3JtLWZpZWxkIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBtYXJnaW46IDA7XG5cbiAgICAubWF0LW1kYy1mb3JtLWZpZWxkLXN1YnNjcmlwdC13cmFwcGVyIHtcbiAgICAgIG1hcmdpbi10b3A6IDRweDtcbiAgICB9XG4gIH1cblxuICAudGVybXMtY2hlY2tib3gge1xuICAgIG1hcmdpbjogOHB4IDA7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuXG4gICAgLnRlcm1zLWxpbmsge1xuICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLmF1dGgtYnV0dG9uIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogNDhweDtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBmb250LXdlaWdodDogNjAwO1xuICBtYXJnaW4tdG9wOiA4cHg7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgYm9yZGVyOiBub25lO1xuICBjb2xvcjogd2hpdGU7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG5cbiAgJjpob3Zlcjpub3QoOmRpc2FibGVkKSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpO1xuICB9XG5cbiAgJjpkaXNhYmxlZCB7XG4gICAgb3BhY2l0eTogMC42O1xuICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gIH1cblxuICBtYXQtc3Bpbm5lciB7XG4gICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gIH1cbn1cblxuLmF1dGgtZm9vdGVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tdG9wOiAyNHB4O1xuICBwYWRkaW5nLXRvcDogMjRweDtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlMmU4ZjA7XG5cbiAgcCB7XG4gICAgbWFyZ2luOiAwO1xuICAgIGNvbG9yOiAjNjQ3NDhiO1xuICAgIGZvbnQtc2l6ZTogMTRweDtcblxuICAgIGEge1xuICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi5yZWdpc3Rlci1idXR0b246aG92ZXI6bm90KDpkaXNhYmxlZCkge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpO1xufVxuXG4uY2FyZC1hY3Rpb25zIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwYWRkaW5nLXRvcDogMjBweDtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlMGUwZTA7XG59XG5cbi5jYXJkLWFjdGlvbnMgcCB7XG4gIG1hcmdpbjogMDtcbiAgY29sb3I6ICM2NDc0OGI7XG4gIGZvbnQtc2l6ZTogMTRweDtcbn1cblxuLmxvZ2luLWxpbmsge1xuICBjb2xvcjogIzY2N2VlYTtcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4ubG9naW4tbGluazpob3ZlciB7XG4gIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xufVxuXG4uZmVhdHVyZXMtc2VjdGlvbiB7XG4gIGZsZXg6IDE7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgcGFkZGluZzogMjBweDtcbn1cblxuLmZlYXR1cmVzLWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcblxuICAuZmVhdHVyZXMtaWNvbiB7XG4gICAgZm9udC1zaXplOiAycmVtO1xuICAgIHdpZHRoOiAycmVtO1xuICAgIGhlaWdodDogMnJlbTtcbiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xuICB9XG5cbiAgaDIge1xuICAgIG1hcmdpbjogMDtcbiAgICBmb250LXNpemU6IDEuNXJlbTtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgfVxufVxuXG4uZmVhdHVyZS1jYXJkIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gIGdhcDogMTZweDtcbiAgcGFkZGluZzogMjBweDtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgJjpob3ZlciB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gIH1cblxuICAuZmVhdHVyZS1pY29uIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgd2lkdGg6IDQ4cHg7XG4gICAgaGVpZ2h0OiA0OHB4O1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgIGZsZXgtc2hyaW5rOiAwO1xuXG4gICAgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiAyNHB4O1xuICAgICAgd2lkdGg6IDI0cHg7XG4gICAgICBoZWlnaHQ6IDI0cHg7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgfVxuICB9XG5cbiAgLmZlYXR1cmUtY29udGVudCB7XG4gICAgZmxleDogMTtcblxuICAgIGgzIHtcbiAgICAgIG1hcmdpbjogMCAwIDhweCAwO1xuICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgIH1cblxuICAgIHAge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgICB9XG4gIH1cbn1cblxuLyogUmVzcG9uc2l2ZSBEZXNpZ24gKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAucmVnaXN0ZXItY29udGVudCB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDMwcHg7XG4gICAgcGFkZGluZzogMXJlbTtcbiAgfVxuXG4gIC5yZWdpc3Rlci1jYXJkLXdyYXBwZXIge1xuICAgIGZsZXg6IG5vbmU7XG4gICAgbWF4LXdpZHRoOiAxMDAlO1xuICB9XG5cbiAgLnJlZ2lzdGVyLWNhcmQge1xuICAgIHBhZGRpbmc6IDMwcHggMjBweDtcbiAgfVxuXG4gIC5mZWF0dXJlcy1zZWN0aW9uIHtcbiAgICBvcmRlcjogLTE7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG5cbiAgLmxvZ28ge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG5cbiAgLmxvZ28tdGV4dCB7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG5cbiAgLmxvZ28tdGV4dCBoMSB7XG4gICAgZm9udC1zaXplOiAxLjVyZW07XG4gIH1cblxuICAuaGVhZGVyLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6IDFyZW07XG4gIH1cblxuICAuYnJhbmQge1xuICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xuICB9XG59XG5cbm1hdC1mb3JtLWZpZWxkIHtcbiAgbWFyZ2luLWJvdHRvbTogMTVweDtcbn1cblxubWF0LXNwaW5uZXIge1xuICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG59XG5cbm1hdC1lcnJvciB7XG4gIGZvbnQtc2l6ZTogMTJweDtcbiAgbWFyZ2luLXRvcDogNXB4O1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "passwordMatchValidator", "control", "password", "get", "confirmPassword", "value", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "snackBar", "hidePassword", "hideConfirmPassword", "isLoading", "registerForm", "group", "username", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "agreeToTerms", "requiredTrue", "validators", "onSubmit", "valid", "userData", "register", "subscribe", "next", "open", "duration", "navigate", "error", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_12_listener", "ɵɵtemplate", "RegisterComponent_mat_error_17_Template", "RegisterComponent_mat_error_18_Template", "RegisterComponent_mat_error_23_Template", "RegisterComponent_mat_error_24_Template", "RegisterComponent_Template_button_click_29_listener", "RegisterComponent_mat_error_32_Template", "RegisterComponent_mat_error_33_Template", "RegisterComponent_Template_button_click_38_listener", "RegisterComponent_mat_error_41_Template", "RegisterComponent_mat_error_42_Template", "RegisterComponent_mat_spinner_51_Template", "RegisterComponent_span_52_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "tmp_3_0", "tmp_4_0", "ɵɵtextInterpolate", "tmp_7_0", "tmp_8_0", "tmp_11_0", "tmp_12_0", "touched", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i7", "MatInput", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatProgressSpinner", "i11", "MatCheckbox", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\register.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\register.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { AuthService, RegisterRequest } from '../../services/auth.service';\n\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control: AbstractControl): {[key: string]: any} | null {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  \n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return { 'passwordMismatch': true };\n  }\n  return null;\n}\n\n@Component({\n  selector: 'app-register',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.scss']\n})\nexport class RegisterComponent {\n  registerForm: FormGroup;\n  hidePassword = true;\n  hideConfirmPassword = true;\n  isLoading = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, { validators: passwordMatchValidator });\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n\n      const userData: RegisterRequest = {\n        username: this.registerForm.value.username,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword\n      };\n\n      this.authService.register(userData).subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', { duration: 3000 });\n          this.router.navigate(['/dashboard']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Registration failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card\">\n    <div class=\"auth-header\">\n      <div class=\"logo\">\n        <mat-icon>security</mat-icon>\n        <span>SPT</span>\n      </div>\n      <h1>Create Account</h1>\n      <p>Join the SPT Security Platform</p>\n    </div>\n\n    <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Email</mat-label>\n        <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n        <mat-error *ngIf=\"registerForm.get('email')?.hasError('required')\">\n          Email is required\n        </mat-error>\n        <mat-error *ngIf=\"registerForm.get('email')?.hasError('email')\">\n          Please enter a valid email\n        </mat-error>\n      </mat-form-field>\n\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Username</mat-label>\n        <input matInput formControlName=\"username\" autocomplete=\"username\">\n        <mat-error *ngIf=\"registerForm.get('username')?.hasError('required')\">\n          Username is required\n        </mat-error>\n        <mat-error *ngIf=\"registerForm.get('username')?.hasError('minlength')\">\n          Username must be at least 3 characters\n        </mat-error>\n      </mat-form-field>\n\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Password</mat-label>\n        <input matInput\n               [type]=\"hidePassword ? 'password' : 'text'\"\n               formControlName=\"password\"\n               autocomplete=\"new-password\">\n        <button mat-icon-button matSuffix\n                (click)=\"hidePassword = !hidePassword\"\n                type=\"button\">\n          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n        </button>\n        <mat-error *ngIf=\"registerForm.get('password')?.hasError('required')\">\n          Password is required\n        </mat-error>\n        <mat-error *ngIf=\"registerForm.get('password')?.hasError('minlength')\">\n          Password must be at least 8 characters\n        </mat-error>\n      </mat-form-field>\n\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Confirm Password</mat-label>\n        <input matInput\n               [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n               formControlName=\"confirmPassword\"\n               autocomplete=\"new-password\">\n        <button mat-icon-button matSuffix\n                (click)=\"hideConfirmPassword = !hideConfirmPassword\"\n                type=\"button\">\n          <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n        </button>\n        <mat-error *ngIf=\"registerForm.get('confirmPassword')?.hasError('required')\">\n          Please confirm your password\n        </mat-error>\n        <mat-error *ngIf=\"registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched\">\n          Passwords do not match\n        </mat-error>\n      </mat-form-field>\n\n      <mat-checkbox formControlName=\"agreeToTerms\" class=\"terms-checkbox\">\n        I agree to the <a href=\"#\" class=\"terms-link\">Terms of Service</a> and <a href=\"#\" class=\"terms-link\">Privacy Policy</a>\n      </mat-checkbox>\n\n      <button mat-raised-button\n              color=\"primary\"\n              type=\"submit\"\n              class=\"auth-button\"\n              [disabled]=\"registerForm.invalid || isLoading\">\n        <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n        <span *ngIf=\"!isLoading\">Create Account</span>\n      </button>\n    </form>\n\n    <div class=\"auth-footer\">\n      <p>Already have an account? <a routerLink=\"/login\">Sign in</a></p>\n    </div>\n  </div>\n\n\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAyB,gBAAgB;AAEzG,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;ICItDC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAcZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAcZH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA6G;IAC3GD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAYZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADpEtD;AACA,SAASE,sBAAsBA,CAACC,OAAwB;EACtD,MAAMC,QAAQ,GAAGD,OAAO,CAACE,GAAG,CAAC,UAAU,CAAC;EACxC,MAAMC,eAAe,GAAGH,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC;EAEtD,IAAID,QAAQ,IAAIE,eAAe,IAAIF,QAAQ,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;IAC3E,OAAO;MAAE,kBAAkB,EAAE;IAAI,CAAE;EACrC;EACA,OAAO,IAAI;AACb;AAoBA,OAAM,MAAOC,iBAAiB;EAM5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,SAAS,GAAG,KAAK;IAQf,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACzCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACkC,KAAK,CAAC,CAAC;MACpDlB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9Df,eAAe,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACgC,QAAQ,CAAC,CAAC;MAC5CG,YAAY,EAAE,CAAC,KAAK,EAAE,CAACnC,UAAU,CAACoC,YAAY,CAAC;KAChD,EAAE;MAAEC,UAAU,EAAEvB;IAAsB,CAAE,CAAC;EAC5C;EAEAwB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,YAAY,CAACU,KAAK,EAAE;MAC3B,IAAI,CAACX,SAAS,GAAG,IAAI;MAErB,MAAMY,QAAQ,GAAoB;QAChCT,QAAQ,EAAE,IAAI,CAACF,YAAY,CAACV,KAAK,CAACY,QAAQ;QAC1CG,KAAK,EAAE,IAAI,CAACL,YAAY,CAACV,KAAK,CAACe,KAAK;QACpClB,QAAQ,EAAE,IAAI,CAACa,YAAY,CAACV,KAAK,CAACH,QAAQ;QAC1CE,eAAe,EAAE,IAAI,CAACW,YAAY,CAACV,KAAK,CAACD;OAC1C;MAED,IAAI,CAACK,WAAW,CAACkB,QAAQ,CAACD,QAAQ,CAAC,CAACE,SAAS,CAAC;QAC5CC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACf,SAAS,GAAG,KAAK;UACtB,IAAI,CAACH,QAAQ,CAACmB,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAC3F,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACH,QAAQ,CAACmB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,qBAAqB,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QACzF;OACD,CAAC;IACJ;EACF;;;uCA5CWzB,iBAAiB,EAAAX,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAAwC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBrC,iBAAiB;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvCtBvD,EAJR,CAAAC,cAAA,aAA4B,aACH,aACI,aACL,eACN;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,UAAG;UACXF,EADW,CAAAG,YAAA,EAAO,EACZ;UACNH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sCAA8B;UACnCF,EADmC,CAAAG,YAAA,EAAI,EACjC;UAENH,EAAA,CAAAC,cAAA,eAA2E;UAA1CD,EAAA,CAAAyD,UAAA,sBAAAC,qDAAA;YAAA,OAAYF,GAAA,CAAA3B,QAAA,EAAU;UAAA,EAAC;UAEpD7B,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,gBAA0E;UAI1EJ,EAHA,CAAA2D,UAAA,KAAAC,uCAAA,uBAAmE,KAAAC,uCAAA,uBAGH;UAGlE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAAmE;UAInEJ,EAHA,CAAA2D,UAAA,KAAAG,uCAAA,uBAAsE,KAAAC,uCAAA,uBAGC;UAGzE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAGmC;UACnCJ,EAAA,CAAAC,cAAA,kBAEsB;UADdD,EAAA,CAAAyD,UAAA,mBAAAO,oDAAA;YAAA,OAAAR,GAAA,CAAAvC,YAAA,IAAAuC,GAAA,CAAAvC,YAAA;UAAA,EAAsC;UAE5CjB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UAITH,EAHA,CAAA2D,UAAA,KAAAM,uCAAA,uBAAsE,KAAAC,uCAAA,uBAGC;UAGzElE,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAI,SAAA,iBAGmC;UACnCJ,EAAA,CAAAC,cAAA,kBAEsB;UADdD,EAAA,CAAAyD,UAAA,mBAAAU,oDAAA;YAAA,OAAAX,GAAA,CAAAtC,mBAAA,IAAAsC,GAAA,CAAAtC,mBAAA;UAAA,EAAoD;UAE1DlB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAyD;UACrEF,EADqE,CAAAG,YAAA,EAAW,EACvE;UAITH,EAHA,CAAA2D,UAAA,KAAAS,uCAAA,uBAA6E,KAAAC,uCAAA,uBAGgC;UAG/GrE,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,wBAAoE;UAClED,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UACtHF,EADsH,CAAAG,YAAA,EAAI,EAC3G;UAEfH,EAAA,CAAAC,cAAA,kBAIuD;UAErDD,EADA,CAAA2D,UAAA,KAAAW,yCAAA,0BAA6C,KAAAC,kCAAA,kBACpB;UAE7BvE,EADE,CAAAG,YAAA,EAAS,EACJ;UAGLH,EADF,CAAAC,cAAA,eAAyB,SACpB;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAAI,EAC9D,EACF,EAzFoB;;;;;;;;;;;UAWlBH,EAAA,CAAAwE,SAAA,IAA0B;UAA1BxE,EAAA,CAAAyE,UAAA,cAAAjB,GAAA,CAAApC,YAAA,CAA0B;UAIhBpB,EAAA,CAAAwE,SAAA,GAAqD;UAArDxE,EAAA,CAAAyE,UAAA,UAAAC,OAAA,GAAAlB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,4BAAAkE,OAAA,CAAAC,QAAA,aAAqD;UAGrD3E,EAAA,CAAAwE,SAAA,EAAkD;UAAlDxE,EAAA,CAAAyE,UAAA,UAAAG,OAAA,GAAApB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,4BAAAoE,OAAA,CAAAD,QAAA,UAAkD;UAQlD3E,EAAA,CAAAwE,SAAA,GAAwD;UAAxDxE,EAAA,CAAAyE,UAAA,UAAAI,OAAA,GAAArB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAqE,OAAA,CAAAF,QAAA,aAAwD;UAGxD3E,EAAA,CAAAwE,SAAA,EAAyD;UAAzDxE,EAAA,CAAAyE,UAAA,UAAAK,OAAA,GAAAtB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAsE,OAAA,CAAAH,QAAA,cAAyD;UAQ9D3E,EAAA,CAAAwE,SAAA,GAA2C;UAA3CxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAAvC,YAAA,uBAA2C;UAMtCjB,EAAA,CAAAwE,SAAA,GAAkD;UAAlDxE,EAAA,CAAA+E,iBAAA,CAAAvB,GAAA,CAAAvC,YAAA,mCAAkD;UAElDjB,EAAA,CAAAwE,SAAA,EAAwD;UAAxDxE,EAAA,CAAAyE,UAAA,UAAAO,OAAA,GAAAxB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAwE,OAAA,CAAAL,QAAA,aAAwD;UAGxD3E,EAAA,CAAAwE,SAAA,EAAyD;UAAzDxE,EAAA,CAAAyE,UAAA,UAAAQ,OAAA,GAAAzB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAyE,OAAA,CAAAN,QAAA,cAAyD;UAQ9D3E,EAAA,CAAAwE,SAAA,GAAkD;UAAlDxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAAtC,mBAAA,uBAAkD;UAM7ClB,EAAA,CAAAwE,SAAA,GAAyD;UAAzDxE,EAAA,CAAA+E,iBAAA,CAAAvB,GAAA,CAAAtC,mBAAA,mCAAyD;UAEzDlB,EAAA,CAAAwE,SAAA,EAA+D;UAA/DxE,EAAA,CAAAyE,UAAA,UAAAS,QAAA,GAAA1B,GAAA,CAAApC,YAAA,CAAAZ,GAAA,sCAAA0E,QAAA,CAAAP,QAAA,aAA+D;UAG/D3E,EAAA,CAAAwE,SAAA,EAA+F;UAA/FxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAApC,YAAA,CAAAuD,QAAA,0BAAAQ,QAAA,GAAA3B,GAAA,CAAApC,YAAA,CAAAZ,GAAA,sCAAA2E,QAAA,CAAAC,OAAA,EAA+F;UAarGpF,EAAA,CAAAwE,SAAA,GAA8C;UAA9CxE,EAAA,CAAAyE,UAAA,aAAAjB,GAAA,CAAApC,YAAA,CAAAiE,OAAA,IAAA7B,GAAA,CAAArC,SAAA,CAA8C;UACtCnB,EAAA,CAAAwE,SAAA,EAAe;UAAfxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAArC,SAAA,CAAe;UACtBnB,EAAA,CAAAwE,SAAA,EAAgB;UAAhBxE,EAAA,CAAAyE,UAAA,UAAAjB,GAAA,CAAArC,SAAA,CAAgB;;;qBDrD3B9B,YAAY,EAAAiG,EAAA,CAAAC,IAAA,EACZjG,mBAAmB,EAAAmD,EAAA,CAAA+C,aAAA,EAAA/C,EAAA,CAAAgD,oBAAA,EAAAhD,EAAA,CAAAiD,eAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAlD,EAAA,CAAAmD,kBAAA,EAAAnD,EAAA,CAAAoD,eAAA,EACnBrG,aAAa,EACbC,kBAAkB,EAAAqG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBxG,cAAc,EAAAyG,EAAA,CAAAC,QAAA,EACdzG,eAAe,EAAA0G,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf3G,aAAa,EAAA4G,EAAA,CAAAC,OAAA,EACb5G,wBAAwB,EAAA6G,GAAA,CAAAC,kBAAA,EACxB7G,iBAAiB,EACjBC,iBAAiB,EAAA6G,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}