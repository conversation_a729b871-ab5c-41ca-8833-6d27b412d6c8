{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { Chart, registerables } from 'chart.js';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nconst _c0 = [\"chartCanvas\"];\nfunction SecurityMetricsChartComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Total Issues: \", ctx_r0.getTotalValue(), \" \");\n  }\n}\nfunction SecurityMetricsChartComponent_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const range_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", range_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", range_r2.label, \" \");\n  }\n}\nfunction SecurityMetricsChartComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"mat-icon\", 24);\n    i0.ɵɵtext(3, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Loading chart data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityMetricsChartComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"mat-icon\", 26);\n    i0.ɵɵtext(2, \"assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No security metrics data to display for the selected time range.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SecurityMetricsChartComponent_div_22_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.refreshData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityMetricsChartComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"canvas\", 29, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.title + \" chart\");\n  }\n}\nfunction SecurityMetricsChartComponent_div_24_div_7_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41)(1, \"mat-icon\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"positive\", item_r4.trend > 0)(\"negative\", item_r4.trend < 0)(\"neutral\", item_r4.trend === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r4.trend > 0 ? \"trending_up\" : item_r4.trend < 0 ? \"trending_down\" : \"trending_flat\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.Math.abs(item_r4.trend), \"%\");\n  }\n}\nfunction SecurityMetricsChartComponent_div_24_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"div\", 35);\n    i0.ɵɵelementStart(2, \"div\", 36)(3, \"span\", 37);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 38)(6, \"span\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SecurityMetricsChartComponent_div_24_div_7_span_8_Template, 5, 8, \"span\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"severity-\" + item_r4.severity);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r0.severityColors[item_r4.severity]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.label);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.trend !== undefined);\n  }\n}\nfunction SecurityMetricsChartComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Trends\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 32);\n    i0.ɵɵtemplate(7, SecurityMetricsChartComponent_div_24_div_7_Template, 9, 7, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.data);\n  }\n}\nfunction SecurityMetricsChartComponent_div_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 53);\n    i0.ɵɵtext(6, \"Critical\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.getCriticalCount());\n  }\n}\nfunction SecurityMetricsChartComponent_div_25_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 53);\n    i0.ɵɵtext(6, \"High\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.getHighCount());\n  }\n}\nfunction SecurityMetricsChartComponent_div_25_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 53);\n    i0.ɵɵtext(6, \"Medium\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.getMediumCount());\n  }\n}\nfunction SecurityMetricsChartComponent_div_25_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 53);\n    i0.ɵɵtext(6, \"Low\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.getLowCount());\n  }\n}\nfunction SecurityMetricsChartComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44);\n    i0.ɵɵtemplate(2, SecurityMetricsChartComponent_div_25_div_2_Template, 7, 1, \"div\", 45)(3, SecurityMetricsChartComponent_div_25_div_3_Template, 7, 1, \"div\", 46)(4, SecurityMetricsChartComponent_div_25_div_4_Template, 7, 1, \"div\", 47)(5, SecurityMetricsChartComponent_div_25_div_5_Template, 7, 1, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 49)(7, \"button\", 50)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" View Details \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getCriticalCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getHighCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getMediumCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getLowCount() > 0);\n  }\n}\n// Register Chart.js components\nChart.register(...registerables);\nexport let SecurityMetricsChartComponent = /*#__PURE__*/(() => {\n  class SecurityMetricsChartComponent {\n    constructor() {\n      this.title = 'Security Metrics';\n      this.chartType = 'doughnut';\n      this.data = [];\n      this.height = 300;\n      this.showLegend = true;\n      this.showTrends = true;\n      this.animated = true;\n      this.chart = null;\n      this.selectedTimeRange = '7d';\n      this.isLoading = false;\n      // Make Math available in template\n      this.Math = Math;\n      this.timeRanges = [{\n        value: '24h',\n        label: 'Last 24 Hours'\n      }, {\n        value: '7d',\n        label: 'Last 7 Days'\n      }, {\n        value: '30d',\n        label: 'Last 30 Days'\n      }, {\n        value: '90d',\n        label: 'Last 90 Days'\n      }];\n      this.severityColors = {\n        critical: '#dc2626',\n        high: '#ea580c',\n        medium: '#d97706',\n        low: '#65a30d',\n        info: '#2563eb'\n      };\n    }\n    ngOnInit() {\n      // Component initialization\n    }\n    ngAfterViewInit() {\n      this.createChart();\n    }\n    ngOnDestroy() {\n      if (this.chart) {\n        this.chart.destroy();\n      }\n    }\n    createChart() {\n      if (!this.chartCanvas?.nativeElement) {\n        return;\n      }\n      const ctx = this.chartCanvas.nativeElement.getContext('2d');\n      if (!ctx) {\n        return;\n      }\n      // Destroy existing chart\n      if (this.chart) {\n        this.chart.destroy();\n      }\n      const chartData = this.prepareChartData();\n      const config = this.getChartConfiguration(chartData);\n      this.chart = new Chart(ctx, config);\n    }\n    prepareChartData() {\n      const labels = this.data.map(item => item.label);\n      const values = this.data.map(item => item.value);\n      const colors = this.data.map(item => item.color || this.severityColors[item.severity] || '#6b7280');\n      return {\n        labels,\n        datasets: [{\n          data: values,\n          backgroundColor: colors,\n          borderColor: colors.map(color => this.adjustColorOpacity(color, 1)),\n          borderWidth: 2,\n          hoverBorderWidth: 3,\n          hoverOffset: 4\n        }]\n      };\n    }\n    getChartConfiguration(chartData) {\n      const baseConfig = {\n        type: this.chartType,\n        data: chartData,\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          plugins: {\n            legend: {\n              display: this.showLegend,\n              position: 'bottom',\n              labels: {\n                padding: 20,\n                usePointStyle: true,\n                font: {\n                  family: 'Inter, sans-serif',\n                  size: 12,\n                  weight: 500\n                }\n              }\n            },\n            tooltip: {\n              backgroundColor: 'rgba(0, 0, 0, 0.8)',\n              titleColor: '#ffffff',\n              bodyColor: '#ffffff',\n              borderColor: '#374151',\n              borderWidth: 1,\n              cornerRadius: 8,\n              displayColors: true,\n              callbacks: {\n                label: context => {\n                  const dataItem = this.data[context.dataIndex];\n                  const percentage = (context.parsed / this.getTotalValue() * 100).toFixed(1);\n                  let label = `${context.label}: ${context.parsed}`;\n                  if (this.showTrends && dataItem.trend !== undefined) {\n                    const trendIcon = dataItem.trend > 0 ? '↗' : dataItem.trend < 0 ? '↘' : '→';\n                    label += ` (${trendIcon} ${Math.abs(dataItem.trend)}%)`;\n                  }\n                  return `${label} (${percentage}%)`;\n                }\n              }\n            }\n          },\n          animation: {\n            duration: this.animated ? 1000 : 0,\n            easing: 'easeInOutQuart'\n          }\n        }\n      };\n      // Chart type specific configurations\n      if (this.chartType === 'doughnut' || this.chartType === 'pie') {\n        baseConfig.options.cutout = this.chartType === 'doughnut' ? '60%' : 0;\n      } else if (this.chartType === 'bar' || this.chartType === 'line') {\n        baseConfig.options.scales = {\n          y: {\n            beginAtZero: true,\n            grid: {\n              color: '#f3f4f6'\n            },\n            ticks: {\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          },\n          x: {\n            grid: {\n              display: false\n            },\n            ticks: {\n              font: {\n                family: 'Inter, sans-serif'\n              }\n            }\n          }\n        };\n      }\n      return baseConfig;\n    }\n    adjustColorOpacity(color, opacity) {\n      // Convert hex to rgba\n      const hex = color.replace('#', '');\n      const r = parseInt(hex.substring(0, 2), 16);\n      const g = parseInt(hex.substring(2, 4), 16);\n      const b = parseInt(hex.substring(4, 6), 16);\n      return `rgba(${r}, ${g}, ${b}, ${opacity})`;\n    }\n    onTimeRangeChange(range) {\n      this.selectedTimeRange = range;\n      this.refreshData();\n    }\n    refreshData() {\n      this.isLoading = true;\n      // Simulate data refresh\n      setTimeout(() => {\n        this.createChart();\n        this.isLoading = false;\n      }, 500);\n    }\n    exportChart() {\n      if (this.chart) {\n        const url = this.chart.toBase64Image();\n        const link = document.createElement('a');\n        link.download = `${this.title.toLowerCase().replace(/\\s+/g, '-')}-chart.png`;\n        link.href = url;\n        link.click();\n      }\n    }\n    toggleChartType() {\n      const types = ['doughnut', 'bar', 'line', 'pie'];\n      const currentIndex = types.indexOf(this.chartType);\n      this.chartType = types[(currentIndex + 1) % types.length];\n      this.createChart();\n    }\n    // Template helper methods\n    getTotalValue() {\n      return this.data.reduce((sum, item) => sum + item.value, 0);\n    }\n    getCriticalCount() {\n      return this.data.filter(item => item.severity === 'critical').reduce((sum, item) => sum + item.value, 0);\n    }\n    getHighCount() {\n      return this.data.filter(item => item.severity === 'high').reduce((sum, item) => sum + item.value, 0);\n    }\n    getMediumCount() {\n      return this.data.filter(item => item.severity === 'medium').reduce((sum, item) => sum + item.value, 0);\n    }\n    getLowCount() {\n      return this.data.filter(item => item.severity === 'low').reduce((sum, item) => sum + item.value, 0);\n    }\n    static {\n      this.ɵfac = function SecurityMetricsChartComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SecurityMetricsChartComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SecurityMetricsChartComponent,\n        selectors: [[\"app-security-metrics-chart\"]],\n        viewQuery: function SecurityMetricsChartComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartCanvas = _t.first);\n          }\n        },\n        inputs: {\n          title: \"title\",\n          chartType: \"chartType\",\n          data: \"data\",\n          height: \"height\",\n          showLegend: \"showLegend\",\n          showTrends: \"showTrends\",\n          animated: \"animated\"\n        },\n        decls: 26,\n        vars: 14,\n        consts: [[\"chartCanvas\", \"\"], [1, \"chart-container\"], [1, \"chart-header\"], [1, \"chart-title-section\"], [1, \"chart-title\"], [\"class\", \"chart-subtitle\", 4, \"ngIf\"], [1, \"chart-controls\"], [\"appearance\", \"outline\", 1, \"time-range-select\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Change Chart Type\", 1, \"action-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 1, \"action-btn\", 3, \"click\", \"disabled\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export Chart\", 1, \"action-btn\", 3, \"click\"], [1, \"chart-content\"], [\"class\", \"chart-loading\", 4, \"ngIf\"], [\"class\", \"chart-empty\", 4, \"ngIf\"], [\"class\", \"chart-wrapper\", 4, \"ngIf\"], [\"class\", \"chart-trends\", 4, \"ngIf\"], [\"class\", \"chart-summary\", 4, \"ngIf\"], [1, \"chart-subtitle\"], [3, \"value\"], [1, \"chart-loading\"], [1, \"loading-spinner\"], [1, \"spinning\"], [1, \"chart-empty\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"chart-wrapper\"], [1, \"chart-canvas\"], [1, \"chart-trends\"], [1, \"trends-header\"], [1, \"trends-list\"], [\"class\", \"trend-item\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"trend-item\"], [1, \"trend-indicator\"], [1, \"trend-content\"], [1, \"trend-label\"], [1, \"trend-value\"], [1, \"value\"], [\"class\", \"trend-change\", 3, \"positive\", \"negative\", \"neutral\", 4, \"ngIf\"], [1, \"trend-change\"], [1, \"trend-icon\"], [1, \"chart-summary\"], [1, \"summary-stats\"], [\"class\", \"summary-item critical\", 4, \"ngIf\"], [\"class\", \"summary-item high\", 4, \"ngIf\"], [\"class\", \"summary-item medium\", 4, \"ngIf\"], [\"class\", \"summary-item low\", 4, \"ngIf\"], [1, \"summary-actions\"], [\"mat-button\", \"\", \"color\", \"primary\", 1, \"view-details-btn\"], [1, \"summary-item\", \"critical\"], [1, \"count\"], [1, \"label\"], [1, \"summary-item\", \"high\"], [1, \"summary-item\", \"medium\"], [1, \"summary-item\", \"low\"]],\n        template: function SecurityMetricsChartComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h3\", 4);\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, SecurityMetricsChartComponent_div_5_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"mat-form-field\", 7)(8, \"mat-select\", 8);\n            i0.ɵɵtwoWayListener(\"valueChange\", function SecurityMetricsChartComponent_Template_mat_select_valueChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedTimeRange, $event) || (ctx.selectedTimeRange = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function SecurityMetricsChartComponent_Template_mat_select_selectionChange_8_listener($event) {\n              return ctx.onTimeRangeChange($event.value);\n            });\n            i0.ɵɵtemplate(9, SecurityMetricsChartComponent_mat_option_9_Template, 2, 2, \"mat-option\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 10)(11, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function SecurityMetricsChartComponent_Template_button_click_11_listener() {\n              return ctx.toggleChartType();\n            });\n            i0.ɵɵelementStart(12, \"mat-icon\");\n            i0.ɵɵtext(13, \"bar_chart\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function SecurityMetricsChartComponent_Template_button_click_14_listener() {\n              return ctx.refreshData();\n            });\n            i0.ɵɵelementStart(15, \"mat-icon\");\n            i0.ɵɵtext(16, \"refresh\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function SecurityMetricsChartComponent_Template_button_click_17_listener() {\n              return ctx.exportChart();\n            });\n            i0.ɵɵelementStart(18, \"mat-icon\");\n            i0.ɵɵtext(19, \"download\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(20, \"div\", 14);\n            i0.ɵɵtemplate(21, SecurityMetricsChartComponent_div_21_Template, 6, 0, \"div\", 15)(22, SecurityMetricsChartComponent_div_22_Template, 11, 0, \"div\", 16)(23, SecurityMetricsChartComponent_div_23_Template, 3, 1, \"div\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(24, SecurityMetricsChartComponent_div_24_Template, 8, 1, \"div\", 18)(25, SecurityMetricsChartComponent_div_25_Template, 11, 4, \"div\", 19);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.title);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.data.length > 0);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedTimeRange);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.timeRanges);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"spinning\", ctx.isLoading);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleProp(\"height\", ctx.height, \"px\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.data.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.data.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showTrends && ctx.data.length > 0 && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.data.length > 0 && !ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatSelectModule, i4.MatFormField, i5.MatSelect, i5.MatOption, MatFormFieldModule],\n        styles: [\".chart-container[_ngcontent-%COMP%]{background:var(--spt-surface);border-radius:var(--spt-radius-xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm);overflow:hidden;transition:all .3s ease}.chart-container[_ngcontent-%COMP%]:hover{box-shadow:var(--spt-shadow-md);transform:translateY(-1px)}.chart-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:var(--spt-space-6);border-bottom:1px solid var(--spt-border-light);background:var(--spt-bg-secondary)}.chart-title-section[_ngcontent-%COMP%]{flex:1}.chart-title[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-1) 0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary);line-height:var(--spt-leading-tight)}.chart-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-text-secondary);font-weight:var(--spt-font-medium)}.chart-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3)}.time-range-select[_ngcontent-%COMP%]{min-width:140px}.time-range-select[_ngcontent-%COMP%]     .mat-mdc-form-field-wrapper{padding-bottom:0}.time-range-select[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper{height:40px;border-radius:var(--spt-radius-lg)}.chart-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-1)}.action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:var(--spt-radius-lg);color:var(--spt-text-secondary);transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{background:var(--spt-primary-50);color:var(--spt-primary-600);transform:scale(1.05)}.action-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.chart-content[_ngcontent-%COMP%]{position:relative;padding:var(--spt-space-6);min-height:300px}.chart-wrapper[_ngcontent-%COMP%]{position:relative;height:100%;width:100%}.chart-canvas[_ngcontent-%COMP%]{max-width:100%;max-height:100%}.chart-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;color:var(--spt-text-secondary)}.loading-spinner[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-4)}.loading-spinner[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:var(--spt-primary-500)}.chart-empty[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;text-align:center;color:var(--spt-text-secondary)}.empty-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:var(--spt-text-tertiary);margin-bottom:var(--spt-space-4)}.chart-empty[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-2) 0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.chart-empty[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-6) 0;font-size:var(--spt-text-sm);max-width:300px;line-height:var(--spt-leading-relaxed)}.chart-trends[_ngcontent-%COMP%]{padding:var(--spt-space-6);border-top:1px solid var(--spt-border-light);background:var(--spt-bg-secondary)}.trends-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);margin-bottom:var(--spt-space-4);font-size:var(--spt-text-sm);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary);text-transform:uppercase;letter-spacing:.05em}.trends-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:var(--spt-primary-600)}.trends-list[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:var(--spt-space-3)}.trend-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);padding:var(--spt-space-3);border-radius:var(--spt-radius-lg);background:var(--spt-surface);border:1px solid var(--spt-border-light);transition:all .2s ease}.trend-item[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:var(--spt-shadow-sm)}.trend-indicator[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:var(--spt-radius-full);flex-shrink:0}.trend-content[_ngcontent-%COMP%]{flex:1;min-width:0}.trend-label[_ngcontent-%COMP%]{display:block;font-size:var(--spt-text-sm);font-weight:var(--spt-font-medium);color:var(--spt-text-primary);margin-bottom:var(--spt-space-1);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.trend-value[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2)}.value[_ngcontent-%COMP%]{font-size:var(--spt-text-lg);font-weight:var(--spt-font-bold);color:var(--spt-text-primary)}.trend-change[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-1);font-size:var(--spt-text-xs);font-weight:var(--spt-font-semibold);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-md)}.trend-change.positive[_ngcontent-%COMP%]{background:var(--spt-error-100);color:var(--spt-error-700)}.trend-change.negative[_ngcontent-%COMP%]{background:var(--spt-success-100);color:var(--spt-success-700)}.trend-change.neutral[_ngcontent-%COMP%]{background:var(--spt-gray-100);color:var(--spt-text-secondary)}.trend-icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px}.chart-summary[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:var(--spt-space-6);border-top:1px solid var(--spt-border-light);background:var(--spt-bg-secondary)}.summary-stats[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-6)}.summary-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);padding:var(--spt-space-2) var(--spt-space-3);border-radius:var(--spt-radius-lg);background:var(--spt-surface);border:1px solid var(--spt-border-light)}.summary-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.summary-item.critical[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-error-600)}.summary-item.high[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.summary-item.medium[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-info-600)}.summary-item.low[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-success-600)}.count[_ngcontent-%COMP%]{font-size:var(--spt-text-lg);font-weight:var(--spt-font-bold);color:var(--spt-text-primary)}.label[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);font-weight:var(--spt-font-medium);color:var(--spt-text-secondary)}.summary-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-2)}.view-details-btn[_ngcontent-%COMP%]{border-radius:var(--spt-radius-lg);font-weight:var(--spt-font-medium)}.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.chart-header[_ngcontent-%COMP%]{flex-direction:column;gap:var(--spt-space-4);align-items:stretch}.chart-controls[_ngcontent-%COMP%]{justify-content:space-between}.trends-list[_ngcontent-%COMP%]{grid-template-columns:1fr}.chart-summary[_ngcontent-%COMP%]{flex-direction:column;gap:var(--spt-space-4);align-items:stretch}.summary-stats[_ngcontent-%COMP%]{justify-content:space-around;flex-wrap:wrap}}\"]\n      });\n    }\n  }\n  return SecurityMetricsChartComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}