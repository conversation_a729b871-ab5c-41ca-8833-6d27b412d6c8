{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { Chart, registerables } from 'chart.js';\nimport { Subject, interval, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/progress-bar\";\nconst _c0 = [\"progressCanvas\"];\nfunction ScanProgressChartComponent_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ScanProgressChartComponent_div_0_div_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.pauseScan());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"pause\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ScanProgressChartComponent_div_0_div_16_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.cancelScan());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"stop\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getEstimatedTimeRemaining(), \" remaining \");\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"h4\");\n    i0.ɵɵtext(3, \"Real-time Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 38)(5, \"div\", 39);\n    i0.ɵɵelement(6, \"div\", 40);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Progress\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵelement(10, \"div\", 41);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Issues Found\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 42);\n    i0.ɵɵelement(14, \"canvas\", 43, 0);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(14);\n    i0.ɵɵattribute(\"aria-label\", \"Real-time progress chart for \" + ctx_r1.scanProgress.projectName);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 57);\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 58);\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_mat_progress_bar_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-progress-bar\", 59);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵtemplate(2, ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_2_Template, 2, 0, \"mat-icon\", 49)(3, ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_3_Template, 2, 0, \"mat-icon\", 50)(4, ScanProgressChartComponent_div_0_div_80_div_4_span_4_Template, 2, 1, \"span\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 51)(6, \"div\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 53);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 54);\n    i0.ɵɵtemplate(11, ScanProgressChartComponent_div_0_div_80_div_4_mat_icon_11_Template, 2, 0, \"mat-icon\", 55)(12, ScanProgressChartComponent_div_0_div_80_div_4_mat_progress_bar_12_Template, 1, 0, \"mat-progress-bar\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"completed\", i_r3 < ctx_r1.scanProgress.completedSteps)(\"current\", i_r3 === ctx_r1.scanProgress.completedSteps)(\"pending\", i_r3 > ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r3 < ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r3 === ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r3 > ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r3 < ctx_r1.scanProgress.completedSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r3 === ctx_r1.scanProgress.completedSteps);\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"h4\");\n    i0.ɵɵtext(2, \"Scan Steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtemplate(4, ScanProgressChartComponent_div_0_div_80_div_4_Template, 13, 13, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getScanSteps());\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_81_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 70)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" View Report \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_81_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 71)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Retry Scan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ScanProgressChartComponent_div_0_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 63)(8, \"div\", 64)(9, \"span\", 65);\n    i0.ɵɵtext(10, \"Total Files Scanned:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 66);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 64)(14, \"span\", 65);\n    i0.ɵɵtext(15, \"Issues Found:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 66);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 64)(19, \"span\", 65);\n    i0.ɵɵtext(20, \"Total Time:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 66);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 67);\n    i0.ɵɵtemplate(24, ScanProgressChartComponent_div_0_div_81_button_24_Template, 4, 0, \"button\", 68)(25, ScanProgressChartComponent_div_0_div_81_button_25_Template, 4, 0, \"button\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusIcon(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Scan \", i0.ɵɵpipeBind1(6, 9, ctx_r1.scanProgress.status), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.filesScanned);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.issuesFound);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getElapsedTime());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"completed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"failed\");\n  }\n}\nfunction ScanProgressChartComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"mat-icon\", 7);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 8);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"span\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 11);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, ScanProgressChartComponent_div_0_div_16_Template, 7, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 13)(18, \"div\", 14)(19, \"span\", 15);\n    i0.ɵɵtext(20, \"Overall Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 16);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(24, \"mat-progress-bar\", 17);\n    i0.ɵɵelementStart(25, \"div\", 18)(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 19)(32, \"div\", 20)(33, \"div\", 21)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"description\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 22)(37, \"div\", 23);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 24);\n    i0.ɵɵtext(41, \"Files Scanned\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 25);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 26)(45, \"div\", 21)(46, \"mat-icon\");\n    i0.ɵɵtext(47, \"bug_report\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 22)(49, \"div\", 23);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 24);\n    i0.ɵɵtext(53, \"Issues Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 25);\n    i0.ɵɵtext(55);\n    i0.ɵɵpipe(56, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 27)(58, \"div\", 21)(59, \"mat-icon\");\n    i0.ɵɵtext(60, \"schedule\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 22)(62, \"div\", 23);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 24);\n    i0.ɵɵtext(65, \"Elapsed Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(66, ScanProgressChartComponent_div_0_div_66_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 29)(68, \"div\", 21)(69, \"mat-icon\");\n    i0.ɵɵtext(70, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 22)(72, \"div\", 23);\n    i0.ɵɵtext(73);\n    i0.ɵɵpipe(74, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 24);\n    i0.ɵɵtext(76, \"Files/Min\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 25);\n    i0.ɵɵtext(78, \"Scan Speed\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, ScanProgressChartComponent_div_0_div_79_Template, 16, 1, \"div\", 30)(80, ScanProgressChartComponent_div_0_div_80_Template, 5, 1, \"div\", 31)(81, ScanProgressChartComponent_div_0_div_81_Template, 26, 11, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusIcon(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.projectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", ctx_r1.scanProgress.scanId, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + ctx_r1.scanProgress.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 26, ctx_r1.scanProgress.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.scanProgress.currentStep);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"running\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(23, 28, ctx_r1.animatedProgress, \"1.1-1\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.animatedProgress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"Step \", ctx_r1.scanProgress.completedSteps, \" of \", ctx_r1.scanProgress.totalSteps, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(30, 31, ctx_r1.animatedFilesScanned, \"1.0-0\"), \" / \", ctx_r1.scanProgress.totalFiles, \" files\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(39, 34, ctx_r1.animatedFilesScanned, \"1.0-0\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"of \", ctx_r1.scanProgress.totalFiles, \" total\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(51, 37, ctx_r1.animatedIssuesFound, \"1.0-0\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(56, 40, ctx_r1.animatedIssuesFound / ctx_r1.Math.max(ctx_r1.animatedFilesScanned, 1) * 100, \"1.1-1\"), \"% of files\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getElapsedTime());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"running\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(74, 43, ctx_r1.animatedFilesScanned / ctx_r1.Math.max(ctx_r1.getElapsedTimeInMinutes(), 1), \"1.1-1\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showRealTimeUpdates && ctx_r1.progressHistory.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"running\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scanProgress.status === \"completed\" || ctx_r1.scanProgress.status === \"failed\");\n  }\n}\nfunction ScanProgressChartComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"mat-icon\", 73);\n    i0.ɵɵtext(2, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Active Scan\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start a new security scan to see real-time progress here.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 70)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Start New Scan \");\n    i0.ɵɵelementEnd()();\n  }\n}\n// Register Chart.js components\nChart.register(...registerables);\nexport let ScanProgressChartComponent = /*#__PURE__*/(() => {\n  class ScanProgressChartComponent {\n    constructor() {\n      this.scanProgress = null;\n      this.showRealTimeUpdates = true;\n      this.updateInterval = 2000; // milliseconds\n      this.destroy$ = new Subject();\n      this.chart = null;\n      this.progressHistory = [];\n      // Animation properties\n      this.animatedProgress = 0;\n      this.animatedFilesScanned = 0;\n      this.animatedIssuesFound = 0;\n    }\n    ngOnInit() {\n      if (this.showRealTimeUpdates && this.scanProgress?.status === 'running') {\n        this.startRealTimeUpdates();\n      }\n    }\n    ngAfterViewInit() {\n      this.createProgressChart();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      if (this.chart) {\n        this.chart.destroy();\n      }\n    }\n    startRealTimeUpdates() {\n      interval(this.updateInterval).pipe(takeUntil(this.destroy$)).subscribe(() => {\n        if (this.scanProgress?.status === 'running') {\n          this.updateProgressData();\n          this.animateCounters();\n        }\n      });\n    }\n    updateProgressData() {\n      if (!this.scanProgress) return;\n      const dataPoint = {\n        timestamp: new Date(),\n        progress: this.scanProgress.progress,\n        filesScanned: this.scanProgress.filesScanned,\n        issuesFound: this.scanProgress.issuesFound\n      };\n      this.progressHistory.push(dataPoint);\n      // Keep only last 50 data points for performance\n      if (this.progressHistory.length > 50) {\n        this.progressHistory.shift();\n      }\n      this.updateChart();\n    }\n    animateCounters() {\n      if (!this.scanProgress) return;\n      // Animate progress\n      const progressDiff = this.scanProgress.progress - this.animatedProgress;\n      this.animatedProgress += progressDiff * 0.1;\n      // Animate files scanned\n      const filesDiff = this.scanProgress.filesScanned - this.animatedFilesScanned;\n      this.animatedFilesScanned += filesDiff * 0.1;\n      // Animate issues found\n      const issuesDiff = this.scanProgress.issuesFound - this.animatedIssuesFound;\n      this.animatedIssuesFound += issuesDiff * 0.1;\n    }\n    createProgressChart() {\n      if (!this.progressCanvas?.nativeElement) {\n        return;\n      }\n      const ctx = this.progressCanvas.nativeElement.getContext('2d');\n      if (!ctx) {\n        return;\n      }\n      // Destroy existing chart\n      if (this.chart) {\n        this.chart.destroy();\n      }\n      const config = {\n        type: 'line',\n        data: {\n          labels: [],\n          datasets: [{\n            label: 'Progress %',\n            data: [],\n            borderColor: '#3b82f6',\n            backgroundColor: 'rgba(59, 130, 246, 0.1)',\n            borderWidth: 2,\n            fill: true,\n            tension: 0.4,\n            yAxisID: 'y'\n          }, {\n            label: 'Issues Found',\n            data: [],\n            borderColor: '#ef4444',\n            backgroundColor: 'rgba(239, 68, 68, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4,\n            yAxisID: 'y1'\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          interaction: {\n            mode: 'index',\n            intersect: false\n          },\n          plugins: {\n            legend: {\n              display: true,\n              position: 'top',\n              labels: {\n                usePointStyle: true,\n                font: {\n                  family: 'Inter, sans-serif',\n                  size: 12,\n                  weight: 500\n                }\n              }\n            },\n            tooltip: {\n              backgroundColor: 'rgba(0, 0, 0, 0.8)',\n              titleColor: '#ffffff',\n              bodyColor: '#ffffff',\n              borderColor: '#374151',\n              borderWidth: 1,\n              cornerRadius: 8,\n              callbacks: {\n                title: context => {\n                  const timestamp = new Date(context[0].label);\n                  return timestamp.toLocaleTimeString();\n                }\n              }\n            }\n          },\n          scales: {\n            x: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Time',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              ticks: {\n                callback: function (value, index) {\n                  const timestamp = this.getLabelForValue(value);\n                  return new Date(timestamp).toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                  });\n                },\n                font: {\n                  family: 'Inter, sans-serif'\n                }\n              }\n            },\n            y: {\n              type: 'linear',\n              display: true,\n              position: 'left',\n              title: {\n                display: true,\n                text: 'Progress (%)',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              min: 0,\n              max: 100,\n              ticks: {\n                font: {\n                  family: 'Inter, sans-serif'\n                }\n              }\n            },\n            y1: {\n              type: 'linear',\n              display: true,\n              position: 'right',\n              title: {\n                display: true,\n                text: 'Issues Found',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              min: 0,\n              grid: {\n                drawOnChartArea: false\n              },\n              ticks: {\n                font: {\n                  family: 'Inter, sans-serif'\n                }\n              }\n            }\n          },\n          animation: {\n            duration: 750,\n            easing: 'easeInOutQuart'\n          }\n        }\n      };\n      this.chart = new Chart(ctx, config);\n      this.updateChart();\n    }\n    updateChart() {\n      if (!this.chart || this.progressHistory.length === 0) {\n        return;\n      }\n      const labels = this.progressHistory.map(point => point.timestamp.toISOString());\n      const progressData = this.progressHistory.map(point => point.progress);\n      const issuesData = this.progressHistory.map(point => point.issuesFound);\n      this.chart.data.labels = labels;\n      this.chart.data.datasets[0].data = progressData;\n      this.chart.data.datasets[1].data = issuesData;\n      this.chart.update('none');\n    }\n    getStatusIcon() {\n      if (!this.scanProgress) return 'help';\n      switch (this.scanProgress.status) {\n        case 'pending':\n          return 'schedule';\n        case 'running':\n          return 'hourglass_empty';\n        case 'completed':\n          return 'check_circle';\n        case 'failed':\n          return 'error';\n        default:\n          return 'help';\n      }\n    }\n    getStatusColor() {\n      if (!this.scanProgress) return 'var(--spt-text-secondary)';\n      switch (this.scanProgress.status) {\n        case 'pending':\n          return 'var(--spt-warning-600)';\n        case 'running':\n          return 'var(--spt-info-600)';\n        case 'completed':\n          return 'var(--spt-success-600)';\n        case 'failed':\n          return 'var(--spt-error-600)';\n        default:\n          return 'var(--spt-text-secondary)';\n      }\n    }\n    getEstimatedTimeRemaining() {\n      if (!this.scanProgress || !this.scanProgress.estimatedCompletion) {\n        return 'Calculating...';\n      }\n      const now = new Date();\n      const remaining = this.scanProgress.estimatedCompletion.getTime() - now.getTime();\n      if (remaining <= 0) {\n        return 'Almost done';\n      }\n      const minutes = Math.floor(remaining / 60000);\n      const seconds = Math.floor(remaining % 60000 / 1000);\n      if (minutes > 0) {\n        return `${minutes}m ${seconds}s`;\n      } else {\n        return `${seconds}s`;\n      }\n    }\n    getElapsedTime() {\n      if (!this.scanProgress) return '0s';\n      const now = new Date();\n      const elapsed = now.getTime() - this.scanProgress.startTime.getTime();\n      const minutes = Math.floor(elapsed / 60000);\n      const seconds = Math.floor(elapsed % 60000 / 1000);\n      if (minutes > 0) {\n        return `${minutes}m ${seconds}s`;\n      } else {\n        return `${seconds}s`;\n      }\n    }\n    cancelScan() {\n      // Emit cancel event or call service\n      console.log('Cancel scan requested for:', this.scanProgress?.scanId);\n    }\n    pauseScan() {\n      // Emit pause event or call service\n      console.log('Pause scan requested for:', this.scanProgress?.scanId);\n    }\n    getElapsedTimeInMinutes() {\n      if (!this.scanProgress) return 0;\n      const now = new Date();\n      const elapsed = now.getTime() - this.scanProgress.startTime.getTime();\n      return elapsed / 60000; // Convert to minutes\n    }\n    getScanSteps() {\n      return [{\n        name: 'Initialize',\n        description: 'Setting up scan environment'\n      }, {\n        name: 'File Discovery',\n        description: 'Finding smart contract files'\n      }, {\n        name: 'Static Analysis',\n        description: 'Analyzing code structure'\n      }, {\n        name: 'Security Checks',\n        description: 'Running security rules'\n      }, {\n        name: 'Vulnerability Detection',\n        description: 'Identifying security issues'\n      }, {\n        name: 'Report Generation',\n        description: 'Compiling results'\n      }];\n    }\n    static {\n      this.ɵfac = function ScanProgressChartComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ScanProgressChartComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ScanProgressChartComponent,\n        selectors: [[\"app-scan-progress-chart\"]],\n        viewQuery: function ScanProgressChartComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progressCanvas = _t.first);\n          }\n        },\n        inputs: {\n          scanProgress: \"scanProgress\",\n          showRealTimeUpdates: \"showRealTimeUpdates\",\n          updateInterval: \"updateInterval\"\n        },\n        decls: 2,\n        vars: 2,\n        consts: [[\"progressCanvas\", \"\"], [\"class\", \"progress-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"progress-container\"], [1, \"progress-header\"], [1, \"scan-info\"], [1, \"scan-title\"], [1, \"status-icon\"], [1, \"scan-id\"], [1, \"scan-status\"], [1, \"status-text\"], [1, \"current-step\"], [\"class\", \"progress-actions\", 4, \"ngIf\"], [1, \"main-progress\"], [1, \"progress-info\"], [1, \"progress-label\"], [1, \"progress-percentage\"], [\"mode\", \"determinate\", 1, \"main-progress-bar\", 3, \"value\"], [1, \"progress-details\"], [1, \"progress-stats\"], [1, \"stat-card\", \"files\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"stat-detail\"], [1, \"stat-card\", \"issues\"], [1, \"stat-card\", \"time\"], [\"class\", \"stat-detail\", 4, \"ngIf\"], [1, \"stat-card\", \"speed\"], [\"class\", \"progress-chart\", 4, \"ngIf\"], [\"class\", \"step-progress\", 4, \"ngIf\"], [\"class\", \"completion-summary\", 4, \"ngIf\"], [1, \"progress-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Pause Scan\", 1, \"action-btn\", \"pause\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Cancel Scan\", 1, \"action-btn\", \"cancel\", 3, \"click\"], [1, \"progress-chart\"], [1, \"chart-header\"], [1, \"chart-legend\"], [1, \"legend-item\"], [1, \"legend-color\", \"progress\"], [1, \"legend-color\", \"issues\"], [1, \"chart-content\"], [1, \"progress-canvas\"], [1, \"step-progress\"], [1, \"steps-list\"], [\"class\", \"step-item\", 3, \"completed\", \"current\", \"pending\", 4, \"ngFor\", \"ngForOf\"], [1, \"step-item\"], [1, \"step-indicator\"], [4, \"ngIf\"], [\"class\", \"spinning\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"step-name\"], [1, \"step-description\"], [1, \"step-status\"], [\"class\", \"completed-icon\", 4, \"ngIf\"], [\"mode\", \"indeterminate\", \"class\", \"step-progress-bar\", 4, \"ngIf\"], [1, \"spinning\"], [1, \"completed-icon\"], [\"mode\", \"indeterminate\", 1, \"step-progress-bar\"], [1, \"completion-summary\"], [1, \"summary-header\"], [1, \"summary-icon\"], [1, \"summary-stats\"], [1, \"summary-item\"], [1, \"label\"], [1, \"value\"], [1, \"summary-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"], [\"mat-stroked-button\", \"\"], [1, \"empty-state\"], [1, \"empty-icon\"]],\n        template: function ScanProgressChartComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ScanProgressChartComponent_div_0_Template, 82, 46, \"div\", 1)(1, ScanProgressChartComponent_div_1_Template, 11, 0, \"div\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.scanProgress);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.scanProgress);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.DecimalPipe, i1.TitleCasePipe, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatProgressBarModule, i4.MatProgressBar],\n        styles: [\".progress-container[_ngcontent-%COMP%]{background:var(--spt-surface);border-radius:var(--spt-radius-xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm);overflow:hidden}.progress-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:var(--spt-space-6);border-bottom:1px solid var(--spt-border-light);background:var(--spt-bg-secondary)}.scan-info[_ngcontent-%COMP%]{flex:1}.scan-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);margin-bottom:var(--spt-space-2)}.status-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.scan-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.scan-id[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-tertiary);background:var(--spt-gray-100);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-md);font-family:var(--spt-font-mono)}.scan-status[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-1)}.status-text[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);font-weight:var(--spt-font-semibold);text-transform:uppercase;letter-spacing:.05em}.scan-status.status-pending[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.scan-status.status-running[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:var(--spt-info-600)}.scan-status.status-completed[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:var(--spt-success-600)}.scan-status.status-failed[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:var(--spt-error-600)}.current-step[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-secondary)}.progress-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-2)}.action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:var(--spt-radius-lg);transition:all .2s ease}.action-btn.pause[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.action-btn.pause[_ngcontent-%COMP%]:hover{background:var(--spt-warning-50);color:var(--spt-warning-700)}.action-btn.cancel[_ngcontent-%COMP%]{color:var(--spt-error-600)}.action-btn.cancel[_ngcontent-%COMP%]:hover{background:var(--spt-error-50);color:var(--spt-error-700)}.main-progress[_ngcontent-%COMP%]{padding:var(--spt-space-6);border-bottom:1px solid var(--spt-border-light)}.progress-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--spt-space-3)}.progress-label[_ngcontent-%COMP%]{font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.progress-percentage[_ngcontent-%COMP%]{font-size:var(--spt-text-2xl);font-weight:var(--spt-font-bold);color:var(--spt-primary-600)}.main-progress-bar[_ngcontent-%COMP%]{height:12px;border-radius:var(--spt-radius-lg);margin-bottom:var(--spt-space-3)}.main-progress-bar[_ngcontent-%COMP%]     .mat-mdc-progress-bar-fill:after{border-radius:var(--spt-radius-lg)}.main-progress-bar[_ngcontent-%COMP%]     .mat-mdc-progress-bar-buffer{border-radius:var(--spt-radius-lg)}.progress-details[_ngcontent-%COMP%]{display:flex;justify-content:space-between;font-size:var(--spt-text-sm);color:var(--spt-text-secondary)}.progress-stats[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:var(--spt-space-4);padding:var(--spt-space-6);border-bottom:1px solid var(--spt-border-light)}.stat-card[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);padding:var(--spt-space-4);background:var(--spt-bg-secondary);border-radius:var(--spt-radius-lg);border:1px solid var(--spt-border-light);transition:all .2s ease}.stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:var(--spt-shadow-sm)}.stat-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:var(--spt-radius-xl);display:flex;align-items:center;justify-content:center;flex-shrink:0}.stat-card.files[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{background:var(--spt-info-100);color:var(--spt-info-600)}.stat-card.issues[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{background:var(--spt-error-100);color:var(--spt-error-600)}.stat-card.time[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{background:var(--spt-warning-100);color:var(--spt-warning-600)}.stat-card.speed[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{background:var(--spt-success-100);color:var(--spt-success-600)}.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.stat-content[_ngcontent-%COMP%]{flex:1;min-width:0}.stat-value[_ngcontent-%COMP%]{font-size:var(--spt-text-xl);font-weight:var(--spt-font-bold);color:var(--spt-text-primary);line-height:var(--spt-leading-tight)}.stat-label[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary);margin:var(--spt-space-1) 0}.stat-detail[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-secondary)}.progress-chart[_ngcontent-%COMP%]{padding:var(--spt-space-6);border-bottom:1px solid var(--spt-border-light)}.chart-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--spt-space-4)}.chart-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.chart-legend[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-4)}.legend-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);font-size:var(--spt-text-sm);color:var(--spt-text-secondary)}.legend-color[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:var(--spt-radius-sm)}.legend-color.progress[_ngcontent-%COMP%]{background:#3b82f6}.legend-color.issues[_ngcontent-%COMP%]{background:#ef4444}.chart-content[_ngcontent-%COMP%]{height:200px;position:relative}.progress-canvas[_ngcontent-%COMP%]{max-width:100%;max-height:100%}.step-progress[_ngcontent-%COMP%]{padding:var(--spt-space-6);border-bottom:1px solid var(--spt-border-light)}.step-progress[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-4) 0;font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.steps-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-3)}.step-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-4);padding:var(--spt-space-3);border-radius:var(--spt-radius-lg);transition:all .2s ease}.step-item.completed[_ngcontent-%COMP%]{background:var(--spt-success-50);border:1px solid var(--spt-success-200)}.step-item.current[_ngcontent-%COMP%]{background:var(--spt-info-50);border:1px solid var(--spt-info-200)}.step-item.pending[_ngcontent-%COMP%]{background:var(--spt-gray-50);border:1px solid var(--spt-border-light)}.step-indicator[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:var(--spt-radius-full);display:flex;align-items:center;justify-content:center;font-weight:var(--spt-font-semibold);font-size:var(--spt-text-sm);flex-shrink:0}.step-item.completed[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%]{background:var(--spt-success-600);color:#fff}.step-item.current[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%]{background:var(--spt-info-600);color:#fff}.step-item.pending[_ngcontent-%COMP%]   .step-indicator[_ngcontent-%COMP%]{background:var(--spt-gray-200);color:var(--spt-text-secondary)}.step-content[_ngcontent-%COMP%]{flex:1;min-width:0}.step-name[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary);margin-bottom:var(--spt-space-1)}.step-description[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);color:var(--spt-text-secondary)}.step-status[_ngcontent-%COMP%]{width:100px;display:flex;justify-content:flex-end}.completed-icon[_ngcontent-%COMP%]{color:var(--spt-success-600);font-size:20px;width:20px;height:20px}.step-progress-bar[_ngcontent-%COMP%]{width:80px;height:4px}.completion-summary[_ngcontent-%COMP%]{padding:var(--spt-space-6)}.summary-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);margin-bottom:var(--spt-space-4)}.summary-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px}.summary-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.summary-stats[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:var(--spt-space-4);margin-bottom:var(--spt-space-6)}.summary-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:var(--spt-space-3);background:var(--spt-bg-secondary);border-radius:var(--spt-radius-lg);border:1px solid var(--spt-border-light)}.summary-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-text-secondary)}.summary-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.summary-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-3);justify-content:center}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--spt-space-12);text-align:center;color:var(--spt-text-secondary)}.empty-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:var(--spt-text-tertiary);margin-bottom:var(--spt-space-4)}.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-2) 0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-6) 0;font-size:var(--spt-text-sm);max-width:300px;line-height:var(--spt-leading-relaxed)}.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.progress-header[_ngcontent-%COMP%]{flex-direction:column;gap:var(--spt-space-4);align-items:stretch}.progress-stats[_ngcontent-%COMP%]{grid-template-columns:1fr}.chart-header[_ngcontent-%COMP%]{flex-direction:column;gap:var(--spt-space-2);align-items:stretch}.summary-stats[_ngcontent-%COMP%]{grid-template-columns:1fr}.summary-actions[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return ScanProgressChartComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}