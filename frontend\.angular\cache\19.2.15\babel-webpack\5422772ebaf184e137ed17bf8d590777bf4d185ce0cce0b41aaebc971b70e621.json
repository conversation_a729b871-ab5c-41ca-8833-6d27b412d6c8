{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/checkbox\";\nfunction LoginComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 44);\n  }\n}\nfunction LoginComponent_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, authService, router, route, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.hidePassword = true;\n      this.isLoading = false;\n      this.returnUrl = '/dashboard';\n      this.loginForm = this.formBuilder.group({\n        username: ['', [Validators.required]],\n        password: ['', [Validators.required]],\n        rememberMe: [false]\n      });\n    }\n    ngOnInit() {\n      // Get return URL from route parameters or default to dashboard\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    }\n    onSubmit() {\n      if (this.loginForm.valid) {\n        this.isLoading = true;\n        const credentials = {\n          username: this.loginForm.value.username,\n          password: this.loginForm.value.password\n        };\n        this.authService.login(credentials).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.snackBar.open('Login successful!', 'Close', {\n              duration: 3000\n            });\n            this.router.navigate([this.returnUrl]);\n          },\n          error: error => {\n            this.isLoading = false;\n            this.snackBar.open(error.message || 'Login failed', 'Close', {\n              duration: 5000\n            });\n          }\n        });\n      }\n    }\n    loginWithDemo() {\n      this.isLoading = true;\n      // Use mock login for development\n      setTimeout(() => {\n        this.authService.mockLogin('admin', 'admin');\n        this.isLoading = false;\n        this.snackBar.open('Demo login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n      }, 1000);\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 114,\n        vars: 11,\n        consts: [[1, \"login-container\"], [1, \"background-pattern\"], [1, \"app-header\"], [1, \"header-content\"], [1, \"brand\"], [1, \"brand-icon\"], [1, \"brand-text\"], [1, \"header-actions\"], [\"mat-button\", \"\", \"routerLink\", \"/register\", 1, \"header-link\"], [1, \"login-content\"], [1, \"login-card-wrapper\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"logo-icon-wrapper\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"subtitle\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"form-options\"], [\"formControlName\", \"rememberMe\"], [\"href\", \"#\", 1, \"forgot-password\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"demo-button\", 3, \"click\", \"disabled\"], [1, \"card-actions\"], [\"routerLink\", \"/register\", 1, \"register-link\"], [1, \"features-info\"], [1, \"features-header\"], [1, \"features-icon\"], [1, \"features-grid\"], [1, \"feature-item\"], [1, \"stats\"], [1, \"stat-item\"], [1, \"stat-number\"], [1, \"stat-label\"], [\"diameter\", \"20\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"div\", 1);\n            i0.ɵɵelementStart(2, \"header\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n            i0.ɵɵtext(6, \"shield\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 6);\n            i0.ɵɵtext(8, \"SPT\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n            i0.ɵɵtext(11, \" Create Account \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"mat-card\", 11)(15, \"mat-card-header\", 12)(16, \"div\", 13)(17, \"div\", 14)(18, \"mat-icon\", 15);\n            i0.ɵɵtext(19, \"security\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 16)(21, \"h1\");\n            i0.ɵɵtext(22, \"Welcome Back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"p\", 17);\n            i0.ɵɵtext(24, \"Sign in to your security dashboard\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"mat-card-content\")(26, \"form\", 18);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_26_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(27, \"div\", 19)(28, \"mat-form-field\", 20)(29, \"mat-label\");\n            i0.ɵɵtext(30, \"Username\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(31, \"input\", 21);\n            i0.ɵɵelementStart(32, \"mat-icon\", 22);\n            i0.ɵɵtext(33, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(34, LoginComponent_mat_error_34_Template, 2, 0, \"mat-error\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"mat-form-field\", 20)(36, \"mat-label\");\n            i0.ɵɵtext(37, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(38, \"input\", 24);\n            i0.ɵɵelementStart(39, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_39_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelementStart(40, \"mat-icon\");\n            i0.ɵɵtext(41);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(42, LoginComponent_mat_error_42_Template, 2, 0, \"mat-error\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 26)(44, \"mat-checkbox\", 27);\n            i0.ɵɵtext(45, \" Remember me \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"a\", 28);\n            i0.ɵɵtext(47, \"Forgot password?\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"div\", 29)(49, \"button\", 30);\n            i0.ɵɵtemplate(50, LoginComponent_mat_spinner_50_Template, 1, 0, \"mat-spinner\", 31)(51, LoginComponent_span_51_Template, 2, 0, \"span\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_52_listener() {\n              return ctx.loginWithDemo();\n            });\n            i0.ɵɵtext(53, \" Demo Login \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(54, \"mat-card-actions\", 33)(55, \"p\");\n            i0.ɵɵtext(56, \"Don't have an account? \");\n            i0.ɵɵelementStart(57, \"a\", 34);\n            i0.ɵɵtext(58, \"Sign up\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(59, \"div\", 35)(60, \"div\", 36)(61, \"mat-icon\", 37);\n            i0.ɵɵtext(62, \"verified_user\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"h3\");\n            i0.ɵɵtext(64, \"Security Features\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(65, \"div\", 38)(66, \"div\", 39)(67, \"mat-icon\");\n            i0.ɵɵtext(68, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"div\")(70, \"h4\");\n            i0.ɵɵtext(71, \"Smart Contract Analysis\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"p\");\n            i0.ɵɵtext(73, \"Advanced static analysis for Solidity contracts\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(74, \"div\", 39)(75, \"mat-icon\");\n            i0.ɵɵtext(76, \"bug_report\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"div\")(78, \"h4\");\n            i0.ɵɵtext(79, \"Vulnerability Detection\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"p\");\n            i0.ɵɵtext(81, \"Identify security flaws and potential exploits\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(82, \"div\", 39)(83, \"mat-icon\");\n            i0.ɵɵtext(84, \"monitor\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"div\")(86, \"h4\");\n            i0.ɵɵtext(87, \"Real-time Monitoring\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"p\");\n            i0.ɵɵtext(89, \"Continuous security monitoring and alerts\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(90, \"div\", 39)(91, \"mat-icon\");\n            i0.ɵɵtext(92, \"assessment\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"div\")(94, \"h4\");\n            i0.ɵɵtext(95, \"Comprehensive Reports\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"p\");\n            i0.ɵɵtext(97, \"Detailed security reports and recommendations\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(98, \"div\", 40)(99, \"div\", 41)(100, \"span\", 42);\n            i0.ɵɵtext(101, \"10K+\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"span\", 43);\n            i0.ɵɵtext(103, \"Contracts Analyzed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(104, \"div\", 41)(105, \"span\", 42);\n            i0.ɵɵtext(106, \"500+\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"span\", 43);\n            i0.ɵɵtext(108, \"Vulnerabilities Found\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(109, \"div\", 41)(110, \"span\", 42);\n            i0.ɵɵtext(111, \"99.9%\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(112, \"span\", 43);\n            i0.ɵɵtext(113, \"Accuracy Rate\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_6_0;\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance();\n            i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.hasError(\"required\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i12.MatCheckbox],\n        styles: [\".login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;flex-direction:column;background:linear-gradient(135deg,#667eea,#764ba2);position:relative;overflow:hidden}.background-pattern[_ngcontent-%COMP%]{position:absolute;inset:0;background-image:radial-gradient(circle at 25% 25%,rgba(255,255,255,.1) 0%,transparent 50%),radial-gradient(circle at 75% 75%,rgba(255,255,255,.05) 0%,transparent 50%);pointer-events:none}.app-header[_ngcontent-%COMP%]{position:relative;z-index:10;background:#ffffff1a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-bottom:1px solid rgba(255,255,255,.1)}.header-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:16px 24px;display:flex;justify-content:space-between;align-items:center}.brand[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;color:#fff}.brand-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px}.brand-text[_ngcontent-%COMP%]{font-size:24px;font-weight:700;letter-spacing:-.5px}.header-link[_ngcontent-%COMP%]{color:#fff;border:1px solid rgba(255,255,255,.3);border-radius:8px;padding:8px 16px;transition:all .2s ease}.header-link[_ngcontent-%COMP%]:hover{background:#ffffff1a;border-color:#ffffff80}.login-content[_ngcontent-%COMP%]{flex:1;display:flex;align-items:center;justify-content:center;padding:40px 20px;position:relative;z-index:1}.login-card-wrapper[_ngcontent-%COMP%]{display:flex;gap:60px;align-items:center;max-width:1200px;width:100%}.login-card[_ngcontent-%COMP%]{flex:0 0 480px;padding:48px;background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:24px;box-shadow:0 20px 25px -5px #0000001a,0 10px 10px -5px #0000000a;border:1px solid rgba(255,255,255,.2);animation:_ngcontent-%COMP%_slideInUp .6s ease-out}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.login-header[_ngcontent-%COMP%]{margin-bottom:40px}.logo[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;margin-bottom:8px}.logo-icon-wrapper[_ngcontent-%COMP%]{width:64px;height:64px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;display:flex;align-items:center;justify-content:center;box-shadow:0 8px 16px #667eea4d}.logo-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px;color:#fff}.logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;color:#1a202c;font-weight:700;font-size:28px;letter-spacing:-.5px;line-height:1.2}.subtitle[_ngcontent-%COMP%]{margin:4px 0 0;color:#64748b;font-size:16px;font-weight:400}.form-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;margin-bottom:30px}.full-width[_ngcontent-%COMP%]{width:100%}.form-options[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:10px}.forgot-password[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-size:14px}.forgot-password[_ngcontent-%COMP%]:hover{text-decoration:underline}.form-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.login-button[_ngcontent-%COMP%]{height:56px;font-size:16px;font-weight:600;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;box-shadow:0 8px 20px #667eea4d;transition:all .3s ease;position:relative;overflow:hidden}.login-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s ease}.login-button[_ngcontent-%COMP%]:hover:before{left:100%}.login-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 12px 24px #667eea66}.login-button[_ngcontent-%COMP%]:active{transform:translateY(0)}.demo-button[_ngcontent-%COMP%]{height:48px;color:#667eea;border:2px solid rgba(102,126,234,.2);border-radius:12px;font-weight:500;background:#667eea0d;transition:all .3s ease}.demo-button[_ngcontent-%COMP%]:hover{background:#667eea1a;border-color:#667eea4d;transform:translateY(-1px)}.card-actions[_ngcontent-%COMP%]{text-align:center;padding-top:20px;border-top:1px solid #e0e0e0}.register-link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-weight:500}.register-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.features-info[_ngcontent-%COMP%]{flex:1;background:#ffffff1a;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);color:#fff;padding:48px;border-radius:24px;border:1px solid rgba(255,255,255,.2);animation:_ngcontent-%COMP%_slideInRight .6s ease-out .2s both}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}.features-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;margin-bottom:32px}.features-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px}.features-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:28px;margin:0;font-weight:700}.features-grid[_ngcontent-%COMP%]{display:grid;gap:24px;margin-bottom:40px}.feature-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:16px;padding:20px;background:#ffffff1a;border-radius:16px;border:1px solid rgba(255,255,255,.1);transition:all .3s ease}.feature-item[_ngcontent-%COMP%]:hover{background:#ffffff26;transform:translateY(-2px)}.feature-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;margin-top:2px}.feature-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600}.feature-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;opacity:.9;line-height:1.4}.stats[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:24px;padding-top:32px;border-top:1px solid rgba(255,255,255,.2)}.stat-item[_ngcontent-%COMP%]{text-align:center}.stat-number[_ngcontent-%COMP%]{display:block;font-size:24px;font-weight:700;margin-bottom:4px}.stat-label[_ngcontent-%COMP%]{font-size:12px;opacity:.8;text-transform:uppercase;letter-spacing:.5px}@media (max-width: 1024px){.login-card-wrapper[_ngcontent-%COMP%]{flex-direction:column;gap:40px;max-width:600px}.login-card[_ngcontent-%COMP%]{flex:none;max-width:100%}.features-info[_ngcontent-%COMP%]{order:-1}.features-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.stats[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr)}}@media (max-width: 768px){.header-content[_ngcontent-%COMP%]{padding:12px 16px}.brand-text[_ngcontent-%COMP%]{font-size:20px}.login-content[_ngcontent-%COMP%]{padding:20px 16px}.login-card[_ngcontent-%COMP%], .features-info[_ngcontent-%COMP%]{padding:32px 24px}.features-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.feature-item[_ngcontent-%COMP%]{padding:16px}.stats[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.logo[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:12px}.logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px}}mat-spinner[_ngcontent-%COMP%]{margin-right:10px}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}