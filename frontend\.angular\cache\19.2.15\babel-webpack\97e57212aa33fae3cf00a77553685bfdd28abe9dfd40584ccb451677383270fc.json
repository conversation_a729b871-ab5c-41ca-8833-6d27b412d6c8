{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nexport let WebSocketService = /*#__PURE__*/(() => {\n  class WebSocketService {\n    constructor(authService) {\n      this.authService = authService;\n      this.socket = null;\n      this.reconnectAttempts = 0;\n      this.maxReconnectAttempts = 5;\n      this.reconnectInterval = 5000;\n      // Connection state\n      this.connectionStateSubject = new BehaviorSubject('disconnected');\n      this.connectionState$ = this.connectionStateSubject.asObservable();\n      // Scan progress tracking\n      this.scanProgressSubject = new BehaviorSubject(null);\n      this.scanProgress$ = this.scanProgressSubject.asObservable();\n      // Notifications\n      this.notificationsSubject = new BehaviorSubject([]);\n      this.notifications$ = this.notificationsSubject.asObservable();\n      // Real-time messages\n      this.messagesSubject = new Subject();\n      this.messages$ = this.messagesSubject.asObservable();\n      // Auto-connect when user is authenticated\n      this.authService.currentUser$.subscribe(user => {\n        if (user) {\n          this.connect();\n        } else {\n          this.disconnect();\n        }\n      });\n    }\n    connect() {\n      if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n        return; // Already connected\n      }\n      this.connectionStateSubject.next('connecting');\n      const token = this.authService.getToken();\n      const wsUrl = `ws://localhost:8080/ws${token ? '?token=' + token : ''}`;\n      try {\n        this.socket = new WebSocket(wsUrl);\n        this.setupEventHandlers();\n      } catch (error) {\n        console.error('WebSocket connection error:', error);\n        this.connectionStateSubject.next('error');\n        this.scheduleReconnect();\n      }\n    }\n    disconnect() {\n      if (this.reconnectTimer) {\n        clearTimeout(this.reconnectTimer);\n        this.reconnectTimer = null;\n      }\n      if (this.socket) {\n        this.socket.close();\n        this.socket = null;\n      }\n      this.connectionStateSubject.next('disconnected');\n      this.reconnectAttempts = 0;\n    }\n    sendMessage(message) {\n      if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n        this.socket.send(JSON.stringify(message));\n      } else {\n        console.warn('WebSocket is not connected. Message not sent:', message);\n      }\n    }\n    getCurrentConnectionState() {\n      return this.connectionStateSubject.value;\n    }\n    // Scan-specific methods\n    subscribeScanProgress(scanId) {\n      this.sendMessage({\n        type: 'subscribe_scan',\n        scanId: scanId\n      });\n    }\n    unsubscribeScanProgress(scanId) {\n      this.sendMessage({\n        type: 'unsubscribe_scan',\n        scanId: scanId\n      });\n    }\n    // Notification methods\n    markNotificationAsRead(notificationId) {\n      const notifications = this.notificationsSubject.value;\n      const updatedNotifications = notifications.map(n => n.id === notificationId ? {\n        ...n,\n        read: true\n      } : n);\n      this.notificationsSubject.next(updatedNotifications);\n    }\n    clearAllNotifications() {\n      this.notificationsSubject.next([]);\n    }\n    getUnreadNotificationCount() {\n      return this.notificationsSubject.value.filter(n => !n.read).length;\n    }\n    setupEventHandlers() {\n      if (!this.socket) return;\n      this.socket.onopen = () => {\n        console.log('WebSocket connected');\n        this.connectionStateSubject.next('connected');\n        this.reconnectAttempts = 0;\n        // Send authentication if needed\n        const token = this.authService.getToken();\n        if (token) {\n          this.sendMessage({\n            type: 'authenticate',\n            token: token\n          });\n        }\n      };\n      this.socket.onmessage = event => {\n        try {\n          const message = JSON.parse(event.data);\n          this.handleMessage(message);\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      this.socket.onclose = event => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        this.connectionStateSubject.next('disconnected');\n        // Attempt to reconnect if not a normal closure\n        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {\n          this.scheduleReconnect();\n        }\n      };\n      this.socket.onerror = error => {\n        console.error('WebSocket error:', error);\n        this.connectionStateSubject.next('error');\n      };\n    }\n    handleMessage(message) {\n      this.messagesSubject.next(message);\n      switch (message.type) {\n        case 'scan_progress':\n          this.handleScanProgress(message.data);\n          break;\n        case 'scan_notification':\n          this.handleScanNotification(message.data);\n          break;\n        case 'system_notification':\n          this.handleSystemNotification(message.data);\n          break;\n        case 'authentication_success':\n          console.log('WebSocket authentication successful');\n          break;\n        case 'authentication_failed':\n          console.error('WebSocket authentication failed');\n          this.authService.logout();\n          break;\n        default:\n          console.log('Unknown message type:', message.type);\n      }\n    }\n    handleScanProgress(data) {\n      this.scanProgressSubject.next(data);\n      // Create notification for scan completion\n      if (data.status === 'completed' || data.status === 'failed') {\n        const notification = {\n          id: `scan_${data.scanId}_${Date.now()}`,\n          type: data.status === 'completed' ? 'scan_completed' : 'scan_failed',\n          title: data.status === 'completed' ? 'Scan Completed' : 'Scan Failed',\n          message: data.status === 'completed' ? `Scan completed with ${data.issuesFound} issues found` : data.message || 'Scan failed due to an error',\n          severity: data.status === 'completed' ? 'success' : 'error',\n          timestamp: new Date().toISOString(),\n          read: false,\n          data: data\n        };\n        this.addNotification(notification);\n      }\n    }\n    handleScanNotification(data) {\n      const notification = {\n        id: data.id || `notification_${Date.now()}`,\n        type: data.type || 'system_alert',\n        title: data.title,\n        message: data.message,\n        severity: data.severity || 'info',\n        timestamp: data.timestamp || new Date().toISOString(),\n        read: false,\n        data: data.data\n      };\n      this.addNotification(notification);\n    }\n    handleSystemNotification(data) {\n      const notification = {\n        id: data.id || `system_${Date.now()}`,\n        type: 'system_alert',\n        title: data.title || 'System Notification',\n        message: data.message,\n        severity: data.severity || 'info',\n        timestamp: data.timestamp || new Date().toISOString(),\n        read: false,\n        data: data.data\n      };\n      this.addNotification(notification);\n    }\n    addNotification(notification) {\n      const notifications = this.notificationsSubject.value;\n      const updatedNotifications = [notification, ...notifications].slice(0, 50); // Keep only last 50\n      this.notificationsSubject.next(updatedNotifications);\n    }\n    scheduleReconnect() {\n      if (this.reconnectTimer) {\n        clearTimeout(this.reconnectTimer);\n      }\n      this.reconnectAttempts++;\n      const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff\n      console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);\n      this.reconnectTimer = setTimeout(() => {\n        if (this.authService.isAuthenticated()) {\n          this.connect();\n        }\n      }, delay);\n    }\n    // Mock methods for development\n    mockScanProgress(scanId) {\n      const steps = [{\n        status: 'starting',\n        progress: 0,\n        message: 'Initializing scan...'\n      }, {\n        status: 'scanning',\n        progress: 25,\n        message: 'Scanning smart contracts...'\n      }, {\n        status: 'scanning',\n        progress: 50,\n        message: 'Analyzing dependencies...'\n      }, {\n        status: 'analyzing',\n        progress: 75,\n        message: 'Generating report...'\n      }, {\n        status: 'completed',\n        progress: 100,\n        message: 'Scan completed successfully'\n      }];\n      let stepIndex = 0;\n      const interval = setInterval(() => {\n        if (stepIndex < steps.length) {\n          const step = steps[stepIndex];\n          const progress = {\n            scanId: scanId,\n            status: step.status,\n            progress: step.progress,\n            currentFile: stepIndex > 0 ? `contract_${stepIndex}.sol` : undefined,\n            filesScanned: stepIndex * 5,\n            totalFiles: 20,\n            issuesFound: stepIndex * 2,\n            message: step.message\n          };\n          this.scanProgressSubject.next(progress);\n          stepIndex++;\n        } else {\n          clearInterval(interval);\n        }\n      }, 2000);\n    }\n    static {\n      this.ɵfac = function WebSocketService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || WebSocketService)(i0.ɵɵinject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: WebSocketService,\n        factory: WebSocketService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return WebSocketService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}