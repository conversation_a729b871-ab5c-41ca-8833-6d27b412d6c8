{"ast": null, "code": "import { map, take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.checkAuth(state.url);\n  }\n  canActivateChild(route, state) {\n    return this.checkAuth(state.url);\n  }\n  checkAuth(url) {\n    return this.authService.currentUser$.pipe(take(1), map(user => {\n      if (user && this.authService.isAuthenticated()) {\n        return true;\n      } else {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: url\n          }\n        });\n        return false;\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class RoleGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    const requiredRoles = route.data['roles'];\n    if (!requiredRoles || requiredRoles.length === 0) {\n      return true;\n    }\n    return this.authService.currentUser$.pipe(take(1), map(user => {\n      if (!user || !this.authService.isAuthenticated()) {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: state.url\n          }\n        });\n        return false;\n      }\n      if (this.authService.hasAnyRole(requiredRoles)) {\n        return true;\n      } else {\n        this.router.navigate(['/unauthorized']);\n        return false;\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function RoleGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoleGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleGuard,\n      factory: RoleGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class GuestGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate() {\n    return this.authService.currentUser$.pipe(take(1), map(user => {\n      console.log('GuestGuard - Current user:', user);\n      console.log('GuestGuard - Is authenticated:', this.authService.isAuthenticated());\n      console.log('GuestGuard - Token:', this.authService.getToken());\n      if (user && this.authService.isAuthenticated()) {\n        console.log('GuestGuard - Redirecting to dashboard');\n        this.router.navigate(['/dashboard']);\n        return false;\n      }\n      console.log('GuestGuard - Allowing access to auth page');\n      return true;\n    }));\n  }\n  static {\n    this.ɵfac = function GuestGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GuestGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GuestGuard,\n      factory: GuestGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "take", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "checkAuth", "url", "canActivateChild", "currentUser$", "pipe", "user", "isAuthenticated", "navigate", "queryParams", "returnUrl", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn", "<PERSON><PERSON><PERSON>", "requiredRoles", "data", "length", "hasAnyRole", "<PERSON><PERSON><PERSON>", "console", "log", "getToken"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate, CanActivateChild {\n  \n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    return this.checkAuth(state.url);\n  }\n\n  canActivateChild(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    return this.checkAuth(state.url);\n  }\n\n  private checkAuth(url: string): Observable<boolean> {\n    return this.authService.currentUser$.pipe(\n      take(1),\n      map(user => {\n        if (user && this.authService.isAuthenticated()) {\n          return true;\n        } else {\n          this.router.navigate(['/login'], { queryParams: { returnUrl: url } });\n          return false;\n        }\n      })\n    );\n  }\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RoleGuard implements CanActivate {\n  \n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    const requiredRoles = route.data['roles'] as string[];\n    \n    if (!requiredRoles || requiredRoles.length === 0) {\n      return true;\n    }\n\n    return this.authService.currentUser$.pipe(\n      take(1),\n      map(user => {\n        if (!user || !this.authService.isAuthenticated()) {\n          this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });\n          return false;\n        }\n\n        if (this.authService.hasAnyRole(requiredRoles)) {\n          return true;\n        } else {\n          this.router.navigate(['/unauthorized']);\n          return false;\n        }\n      })\n    );\n  }\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GuestGuard implements CanActivate {\n  \n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(): Observable<boolean> | Promise<boolean> | boolean {\n    return this.authService.currentUser$.pipe(\n      take(1),\n      map(user => {\n        console.log('GuestGuard - Current user:', user);\n        console.log('GuestGuard - Is authenticated:', this.authService.isAuthenticated());\n        console.log('GuestGuard - Token:', this.authService.getToken());\n\n        if (user && this.authService.isAuthenticated()) {\n          console.log('GuestGuard - Redirecting to dashboard');\n          this.router.navigate(['/dashboard']);\n          return false;\n        }\n        console.log('GuestGuard - Allowing access to auth page');\n        return true;\n      })\n    );\n  }\n}\n"], "mappings": "AAGA,SAASA,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;;;;AAM1C,OAAM,MAAOC,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACC,SAAS,CAACD,KAAK,CAACE,GAAG,CAAC;EAClC;EAEAC,gBAAgBA,CACdJ,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACC,SAAS,CAACD,KAAK,CAACE,GAAG,CAAC;EAClC;EAEQD,SAASA,CAACC,GAAW;IAC3B,OAAO,IAAI,CAACN,WAAW,CAACQ,YAAY,CAACC,IAAI,CACvCZ,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACc,IAAI,IAAG;MACT,IAAIA,IAAI,IAAI,IAAI,CAACV,WAAW,CAACW,eAAe,EAAE,EAAE;QAC9C,OAAO,IAAI;MACb,CAAC,MAAM;QACL,IAAI,CAACV,MAAM,CAACW,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAAEC,WAAW,EAAE;YAAEC,SAAS,EAAER;UAAG;QAAE,CAAE,CAAC;QACrE,OAAO,KAAK;MACd;IACF,CAAC,CAAC,CACH;EACH;;;uCAjCWR,SAAS,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATtB,SAAS;MAAAuB,OAAA,EAATvB,SAAS,CAAAwB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;AAyCpB,OAAM,MAAOC,SAAS;EAEpBzB,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,MAAMqB,aAAa,GAAGtB,KAAK,CAACuB,IAAI,CAAC,OAAO,CAAa;IAErD,IAAI,CAACD,aAAa,IAAIA,aAAa,CAACE,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,IAAI;IACb;IAEA,OAAO,IAAI,CAAC3B,WAAW,CAACQ,YAAY,CAACC,IAAI,CACvCZ,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACc,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAACV,WAAW,CAACW,eAAe,EAAE,EAAE;QAChD,IAAI,CAACV,MAAM,CAACW,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAAEC,WAAW,EAAE;YAAEC,SAAS,EAAEV,KAAK,CAACE;UAAG;QAAE,CAAE,CAAC;QAC3E,OAAO,KAAK;MACd;MAEA,IAAI,IAAI,CAACN,WAAW,CAAC4B,UAAU,CAACH,aAAa,CAAC,EAAE;QAC9C,OAAO,IAAI;MACb,CAAC,MAAM;QACL,IAAI,CAACxB,MAAM,CAACW,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC,OAAO,KAAK;MACd;IACF,CAAC,CAAC,CACH;EACH;;;uCAjCWY,SAAS,EAAAT,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATI,SAAS;MAAAH,OAAA,EAATG,SAAS,CAAAF,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;AAyCpB,OAAM,MAAOM,UAAU;EAErB9B,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACF,WAAW,CAACQ,YAAY,CAACC,IAAI,CACvCZ,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACc,IAAI,IAAG;MACToB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAErB,IAAI,CAAC;MAC/CoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC/B,WAAW,CAACW,eAAe,EAAE,CAAC;MACjFmB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC/B,WAAW,CAACgC,QAAQ,EAAE,CAAC;MAE/D,IAAItB,IAAI,IAAI,IAAI,CAACV,WAAW,CAACW,eAAe,EAAE,EAAE;QAC9CmB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAAC9B,MAAM,CAACW,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACpC,OAAO,KAAK;MACd;MACAkB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;;;uCAxBWF,UAAU,EAAAd,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAVS,UAAU;MAAAR,OAAA,EAAVQ,UAAU,CAAAP,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}