{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/dialog\";\nfunction ProjectDialogComponent_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"name\"), \" \");\n  }\n}\nfunction ProjectDialogComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"description\"), \" \");\n  }\n}\nfunction ProjectDialogComponent_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", type_r2.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getBlockchainIcon(type_r2.value));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r2.label, \" \");\n  }\n}\nfunction ProjectDialogComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"repository_url\"), \" \");\n  }\n}\nfunction ProjectDialogComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"After creating the project, you can start scanning for security vulnerabilities.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ProjectDialogComponent = /*#__PURE__*/(() => {\n  class ProjectDialogComponent {\n    constructor(fb, dialogRef, data) {\n      this.fb = fb;\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.blockchainTypes = [{\n        value: 'ethereum',\n        label: 'Ethereum'\n      }, {\n        value: 'bitcoin',\n        label: 'Bitcoin'\n      }, {\n        value: 'general',\n        label: 'General'\n      }];\n      this.isEditMode = data.mode === 'edit';\n      this.projectForm = this.createForm();\n    }\n    ngOnInit() {\n      if (this.isEditMode && this.data.project) {\n        this.projectForm.patchValue({\n          name: this.data.project.name,\n          description: this.data.project.description,\n          type: this.data.project.type,\n          repository_url: this.data.project.repository_url\n        });\n      }\n    }\n    createForm() {\n      return this.fb.group({\n        name: ['', [Validators.required, Validators.minLength(3)]],\n        description: ['', [Validators.required, Validators.minLength(10)]],\n        type: ['ethereum', Validators.required],\n        repository_url: ['', [Validators.pattern(/^https?:\\/\\/.+/)]]\n      });\n    }\n    onSubmit() {\n      if (this.projectForm.valid) {\n        const formValue = this.projectForm.value;\n        const project = {\n          name: formValue.name,\n          description: formValue.description,\n          type: formValue.type,\n          repository_url: formValue.repository_url || undefined,\n          status: 'active',\n          owner: 'current-user',\n          // This should come from auth service\n          collaborators: []\n        };\n        if (this.isEditMode && this.data.project) {\n          project.id = this.data.project.id;\n          project.created_at = this.data.project.created_at;\n          project.updated_at = new Date().toISOString();\n        } else {\n          project.id = this.generateId();\n          project.created_at = new Date().toISOString();\n          project.updated_at = new Date().toISOString();\n          project.scan_count = 0;\n          project.issue_count = 0;\n        }\n        this.dialogRef.close(project);\n      }\n    }\n    onCancel() {\n      this.dialogRef.close();\n    }\n    generateId() {\n      return Math.random().toString(36).substr(2, 9);\n    }\n    getErrorMessage(fieldName) {\n      const field = this.projectForm.get(fieldName);\n      if (field?.hasError('required')) {\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n      }\n      if (field?.hasError('minlength')) {\n        const minLength = field.errors?.['minlength']?.requiredLength;\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${minLength} characters`;\n      }\n      if (field?.hasError('pattern')) {\n        return 'Please enter a valid URL (http:// or https://)';\n      }\n      return '';\n    }\n    getBlockchainIcon(type) {\n      switch (type) {\n        case 'ethereum':\n          return 'currency_exchange';\n        case 'bitcoin':\n          return 'currency_bitcoin';\n        case 'general':\n          return 'code';\n        default:\n          return 'account_tree';\n      }\n    }\n    static {\n      this.ɵfac = function ProjectDialogComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ProjectDialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectDialogComponent,\n        selectors: [[\"app-project-dialog\"]],\n        decls: 45,\n        vars: 11,\n        consts: [[1, \"project-dialog\"], [\"mat-dialog-title\", \"\"], [1, \"project-form\", 3, \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Enter project name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"placeholder\", \"Describe your project\", \"rows\", \"3\"], [\"formControlName\", \"type\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"repository_url\", \"placeholder\", \"https://github.com/username/repo\"], [\"class\", \"project-info\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"click\", \"disabled\"], [3, \"value\"], [1, \"project-info\"]],\n        template: function ProjectDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1)(2, \"mat-icon\");\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"mat-dialog-content\")(6, \"form\", 2)(7, \"mat-form-field\", 3)(8, \"mat-label\");\n            i0.ɵɵtext(9, \"Project Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 4);\n            i0.ɵɵelementStart(11, \"mat-icon\", 5);\n            i0.ɵɵtext(12, \"label\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(13, ProjectDialogComponent_mat_error_13_Template, 2, 1, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"mat-form-field\", 3)(15, \"mat-label\");\n            i0.ɵɵtext(16, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"textarea\", 7);\n            i0.ɵɵelementStart(18, \"mat-icon\", 5);\n            i0.ɵɵtext(19, \"description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(20, ProjectDialogComponent_mat_error_20_Template, 2, 1, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"mat-form-field\", 3)(22, \"mat-label\");\n            i0.ɵɵtext(23, \"Blockchain Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"mat-select\", 8);\n            i0.ɵɵtemplate(25, ProjectDialogComponent_mat_option_25_Template, 4, 3, \"mat-option\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"mat-icon\", 5);\n            i0.ɵɵtext(27, \"account_tree\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"mat-form-field\", 3)(29, \"mat-label\");\n            i0.ɵɵtext(30, \"Repository URL (Optional)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(31, \"input\", 10);\n            i0.ɵɵelementStart(32, \"mat-icon\", 5);\n            i0.ɵɵtext(33, \"link\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(34, ProjectDialogComponent_mat_error_34_Template, 2, 1, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(35, ProjectDialogComponent_div_35_Template, 5, 0, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"mat-dialog-actions\", 12)(37, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function ProjectDialogComponent_Template_button_click_37_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelementStart(38, \"mat-icon\");\n            i0.ɵɵtext(39, \"cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(40, \" Cancel \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function ProjectDialogComponent_Template_button_click_41_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(42, \"mat-icon\");\n            i0.ɵɵtext(43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_6_0;\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add\");\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Edit Project\" : \"Create New Project\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.projectForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.projectForm.get(\"name\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.projectForm.get(\"name\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.projectForm.get(\"description\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.projectForm.get(\"description\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.blockchainTypes);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.projectForm.get(\"repository_url\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.projectForm.get(\"repository_url\")) == null ? null : tmp_6_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.projectForm.invalid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"save\" : \"add\");\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          }\n        },\n        styles: [\".project-dialog[_ngcontent-%COMP%]{min-width:500px;max-width:600px}.project-dialog[_ngcontent-%COMP%]   h2[mat-dialog-title][_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;color:#1976d2}.project-dialog[_ngcontent-%COMP%]   h2[mat-dialog-title][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.project-dialog[_ngcontent-%COMP%]   .project-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;padding:8px 0}.project-dialog[_ngcontent-%COMP%]   .project-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%}.project-dialog[_ngcontent-%COMP%]   .project-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-icon-suffix[_ngcontent-%COMP%]{color:#0000008a}.project-dialog[_ngcontent-%COMP%]   .project-form[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:12px;background-color:#e3f2fd;border-radius:4px;color:#1976d2;font-size:14px}.project-dialog[_ngcontent-%COMP%]   .project-form[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.project-dialog[_ngcontent-%COMP%]   mat-dialog-actions[_ngcontent-%COMP%]{padding:16px 0 8px;gap:8px}.project-dialog[_ngcontent-%COMP%]   mat-dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.project-dialog[_ngcontent-%COMP%]   mat-dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.project-dialog[_ngcontent-%COMP%]   mat-dialog-actions[_ngcontent-%COMP%]   button[mat-raised-button][_ngcontent-%COMP%]{min-width:100px}@media (max-width: 600px){.project-dialog[_ngcontent-%COMP%]{min-width:90vw;max-width:90vw}}.dark-theme[_ngcontent-%COMP%]   .project-dialog[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]{background-color:#1e3a5f;color:#90caf9}.project-dialog[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translateY(-20px)}to{opacity:1;transform:translateY(0)}}\"]\n      });\n    }\n  }\n  return ProjectDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}