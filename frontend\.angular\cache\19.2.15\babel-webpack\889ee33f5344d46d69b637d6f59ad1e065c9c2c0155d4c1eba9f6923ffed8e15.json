{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule, NavigationEnd } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport { filter, map, startWith } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/cdk/layout\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/sidenav\";\nimport * as i7 from \"@angular/material/list\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/menu\";\nimport * as i11 from \"@angular/material/badge\";\nfunction AppComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_2_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext();\n      const drawer_r3 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(drawer_r3.close());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"close\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppComponent_div_2_ng_template_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-sidenav-container\", 8)(3, \"mat-sidenav\", 9, 0)(5, \"div\", 10)(6, \"div\", 11)(7, \"mat-icon\", 12);\n    i0.ɵɵtext(8, \"shield\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 13);\n    i0.ɵɵtext(10, \"SPT\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, AppComponent_div_2_button_11_Template, 3, 0, \"button\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-nav-list\", 15)(13, \"a\", 16);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const drawer_r3 = i0.ɵɵreference(4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.isMobile && drawer_r3.close());\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\", 17);\n    i0.ɵɵtext(15, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 18);\n    i0.ɵɵtext(17, \"Dashboard\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_a_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const drawer_r3 = i0.ɵɵreference(4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.isMobile && drawer_r3.close());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\", 17);\n    i0.ɵɵtext(20, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 18);\n    i0.ɵɵtext(22, \"Security Scan\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"a\", 20);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_a_click_23_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const drawer_r3 = i0.ɵɵreference(4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.isMobile && drawer_r3.close());\n    });\n    i0.ɵɵelementStart(24, \"mat-icon\", 17);\n    i0.ɵɵtext(25, \"checklist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 18);\n    i0.ɵɵtext(27, \"Security Checklist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_a_click_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const drawer_r3 = i0.ɵɵreference(4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.isMobile && drawer_r3.close());\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\", 17);\n    i0.ɵɵtext(30, \"assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 18);\n    i0.ɵɵtext(32, \"Reports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_a_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const drawer_r3 = i0.ɵɵreference(4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.isMobile && drawer_r3.close());\n    });\n    i0.ɵɵelementStart(34, \"mat-icon\", 17);\n    i0.ɵɵtext(35, \"folder\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 18);\n    i0.ɵɵtext(37, \"Projects\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_a_click_38_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const drawer_r3 = i0.ɵɵreference(4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.isMobile && drawer_r3.close());\n    });\n    i0.ɵɵelementStart(39, \"mat-icon\", 17);\n    i0.ɵɵtext(40, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"span\", 18);\n    i0.ɵɵtext(42, \"Settings\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 24)(44, \"div\", 25)(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 26)(48, \"div\", 27);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 28);\n    i0.ɵɵtext(51);\n    i0.ɵɵpipe(52, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(53, \"mat-sidenav-content\", 29)(54, \"mat-toolbar\", 30)(55, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const drawer_r3 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(drawer_r3.toggle());\n    });\n    i0.ɵɵelementStart(56, \"mat-icon\");\n    i0.ɵɵtext(57, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"span\", 32);\n    i0.ɵɵtext(59, \"\\uD83D\\uDEE1\\uFE0F Blockchain Security Protocol Tool\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(60, \"span\", 33);\n    i0.ɵɵelementStart(61, \"button\", 34)(62, \"mat-icon\", 35);\n    i0.ɵɵtext(63, \"notifications\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"button\", 36)(65, \"mat-icon\");\n    i0.ɵɵtext(66, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"mat-menu\", null, 1)(69, \"button\", 37)(70, \"mat-icon\");\n    i0.ɵɵtext(71, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"span\");\n    i0.ɵɵtext(73, \"Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"button\", 37)(75, \"mat-icon\");\n    i0.ɵɵtext(76, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"span\");\n    i0.ɵɵtext(78, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(79, \"mat-divider\");\n    i0.ɵɵelementStart(80, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_Template_button_click_80_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.logout());\n    });\n    i0.ɵɵelementStart(81, \"mat-icon\");\n    i0.ɵɵtext(82, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"span\");\n    i0.ɵɵtext(84, \"Logout\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(85, \"div\", 39);\n    i0.ɵɵelement(86, \"router-outlet\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(87, AppComponent_div_2_ng_template_87_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const userMenu_r5 = i0.ɵɵreference(68);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"mode\", ctx_r3.isMobile ? \"over\" : \"side\")(\"opened\", !ctx_r3.isMobile)(\"disableClose\", !ctx_r3.isMobile);\n    i0.ɵɵattribute(\"role\", \"navigation\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isMobile);\n    i0.ɵɵadvance(38);\n    i0.ɵɵtextInterpolate(ctx_r3.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(52, 8, ctx_r3.user.role));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n  }\n}\nexport class AppComponent {\n  constructor(authService, router, breakpointObserver) {\n    this.authService = authService;\n    this.router = router;\n    this.breakpointObserver = breakpointObserver;\n    this.title = 'SPT - Blockchain Security Protocol Tool';\n    this.isMobile = false;\n    this.currentUser$ = this.authService.currentUser$;\n    // Check if current route is documentation - include initial route\n    this.isDocumentationRoute$ = this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(event => event.url.startsWith('/doc')),\n    // Start with current URL check\n    startWith(this.router.url.startsWith('/doc')));\n    // Check for mobile breakpoint\n    this.breakpointObserver.observe([Breakpoints.Handset]).subscribe(result => {\n      this.isMobile = result.matches;\n    });\n  }\n  ngOnInit() {\n    // Debug authentication state\n    this.currentUser$.subscribe(user => {\n      console.log('App Component - Current user changed:', user);\n    });\n    console.log('App Component - Initial auth state:', this.authService.isAuthenticated());\n    console.log('App Component - Initial user:', this.authService.getCurrentUser());\n  }\n  hasRole(roles) {\n    return this.authService.hasAnyRole(roles);\n  }\n  logout() {\n    this.authService.logout();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.BreakpointObserver));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 4,\n      vars: 6,\n      consts: [[\"drawer\", \"\"], [\"userMenu\", \"matMenu\"], [\"loginView\", \"\"], [\"class\", \"doc-container\", 4, \"ngIf\"], [\"class\", \"app-container\", 4, \"ngIf\"], [1, \"doc-container\"], [1, \"app-container\"], [1, \"authenticated-layout\"], [1, \"sidenav-container\"], [\"fixedInViewport\", \"\", 1, \"sidenav\", 3, \"mode\", \"opened\", \"disableClose\"], [1, \"sidenav-header\"], [1, \"brand\"], [1, \"brand-icon\"], [1, \"brand-text\"], [\"mat-icon-button\", \"\", \"class\", \"close-button\", 3, \"click\", 4, \"ngIf\"], [1, \"nav-list\"], [\"mat-list-item\", \"\", \"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"click\"], [\"matListItemIcon\", \"\", 1, \"nav-icon\"], [\"matListItemTitle\", \"\", 1, \"nav-text\"], [\"mat-list-item\", \"\", \"routerLink\", \"/scan\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/checklist\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/reports\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/projects\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/settings\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"click\"], [1, \"user-info\"], [1, \"user-avatar\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-role\"], [1, \"main-content\"], [\"color\", \"primary\", 1, \"top-toolbar\"], [\"type\", \"button\", \"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"app-title\"], [1, \"spacer\"], [\"mat-icon-button\", \"\", 1, \"toolbar-button\"], [\"matBadge\", \"3\", \"matBadgeColor\", \"warn\"], [\"mat-icon-button\", \"\", 1, \"toolbar-button\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"page-content\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [1, \"login-layout\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppComponent_div_0_Template, 2, 0, \"div\", 3);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵtemplate(2, AppComponent_div_2_Template, 89, 10, \"div\", 4);\n          i0.ɵɵpipe(3, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 2, ctx.isDocumentationRoute$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(3, 4, ctx.isDocumentationRoute$));\n        }\n      },\n      dependencies: [CommonModule, i4.NgIf, i4.AsyncPipe, i4.TitleCasePipe, RouterOutlet, RouterModule, i2.RouterLink, i2.RouterLinkActive, MatToolbarModule, i5.MatToolbar, MatSidenavModule, i6.MatSidenav, i6.MatSidenavContainer, i6.MatSidenavContent, MatListModule, i7.MatNavList, i7.MatListItem, i7.MatListItemIcon, i7.MatDivider, i7.MatListItemTitle, MatIconModule, i8.MatIcon, MatButtonModule, i9.MatIconButton, MatMenuModule, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, MatBadgeModule, i11.MatBadge, MatDividerModule],\n      styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n\\n.doc-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n}\\n\\n.login-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n}\\n\\n.authenticated-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n}\\n\\n\\n\\n.sidenav-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n}\\n\\n.sidenav[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: #ffffff;\\n  border-right: 1px solid #e0e0e0;\\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.sidenav-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid #e0e0e0;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  letter-spacing: -0.5px;\\n}\\n\\n.close-button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n\\n\\n.nav-list[_ngcontent-%COMP%] {\\n  padding: 16px 0;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  margin: 4px 16px;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  color: #64748b;\\n  text-decoration: none;\\n  min-height: 48px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(102, 126, 234, 0.08);\\n  color: #667eea;\\n}\\n\\n.nav-item.active[_ngcontent-%COMP%] {\\n  background-color: rgba(102, 126, 234, 0.12);\\n  color: #667eea;\\n  font-weight: 600;\\n}\\n\\n.nav-icon[_ngcontent-%COMP%] {\\n  color: inherit;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.nav-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.user-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 20px 24px;\\n  border-top: 1px solid #e0e0e0;\\n  background: #f8fafc;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  width: 40px;\\n  height: 40px;\\n  color: #667eea;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: #1e293b;\\n  margin-bottom: 2px;\\n}\\n\\n.user-role[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #64748b;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n}\\n\\n.top-toolbar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 100;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.menu-button[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n}\\n\\n.app-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.toolbar-button[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.page-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  background: #f8fafc;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .sidenav[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 320px;\\n  }\\n  .app-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .menu-button[_ngcontent-%COMP%] {\\n    margin-right: 8px;\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n}\\n\\n\\n.mat-mdc-list-item[_ngcontent-%COMP%] {\\n  --mdc-list-list-item-container-height: 48px;\\n}\\n\\n.mat-mdc-list-item[_ngcontent-%COMP%]   .mdc-list-item__content[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n}\\n\\n.mat-toolbar[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n}\\n\\n.mat-toolbar.mat-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n\\n\\n.mat-mdc-menu-panel[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "RouterModule", "NavigationEnd", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatIconModule", "MatButtonModule", "MatMenuModule", "MatBadgeModule", "MatDividerModule", "Breakpoints", "filter", "map", "startWith", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "AppComponent_div_2_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "drawer_r3", "ɵɵreference", "ɵɵresetView", "close", "ɵɵtext", "ɵɵtemplate", "AppComponent_div_2_button_11_Template", "AppComponent_div_2_Template_a_click_13_listener", "_r1", "ctx_r3", "isMobile", "AppComponent_div_2_Template_a_click_18_listener", "AppComponent_div_2_Template_a_click_23_listener", "AppComponent_div_2_Template_a_click_28_listener", "AppComponent_div_2_Template_a_click_33_listener", "AppComponent_div_2_Template_a_click_38_listener", "AppComponent_div_2_Template_button_click_55_listener", "toggle", "AppComponent_div_2_Template_button_click_80_listener", "logout", "AppComponent_div_2_ng_template_87_Template", "ɵɵtemplateRefExtractor", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "user", "username", "ɵɵpipeBind1", "role", "userMenu_r5", "AppComponent", "constructor", "authService", "router", "breakpointObserver", "title", "currentUser$", "isDocumentationRoute$", "events", "pipe", "event", "url", "startsWith", "observe", "Handset", "subscribe", "result", "matches", "ngOnInit", "console", "log", "isAuthenticated", "getCurrentUser", "hasRole", "roles", "hasAnyRole", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "BreakpointObserver", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_div_0_Template", "AppComponent_div_2_Template", "i4", "NgIf", "AsyncPipe", "TitleCasePipe", "RouterLink", "RouterLinkActive", "i5", "MatToolbar", "i6", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i7", "MatNavList", "MatListItem", "MatListItemIcon", "<PERSON><PERSON><PERSON><PERSON>", "MatListItemTitle", "i8", "MatIcon", "i9", "MatIconButton", "i10", "MatMenu", "MatMenuItem", "MatMenuTrigger", "i11", "MatBadge", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule, TitleCasePipe } from '@angular/common';\nimport { RouterOutlet, RouterModule, Router, NavigationEnd } from '@angular/router';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Observable } from 'rxjs';\nimport { filter, map, startWith } from 'rxjs/operators';\nimport { AuthService, User } from './services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    RouterModule,\n    MatToolbarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatMenuModule,\n    MatBadgeModule,\n    MatDividerModule\n  ],\n  template: `\n    <!-- Documentation Route - No Authentication Required -->\n    <div *ngIf=\"isDocumentationRoute$ | async\" class=\"doc-container\">\n      <router-outlet></router-outlet>\n    </div>\n\n    <!-- Main App - Authentication Required -->\n    <div *ngIf=\"!(isDocumentationRoute$ | async)\" class=\"app-container\">\n      <!-- Authenticated Layout (temporarily always show for testing) -->\n      <div class=\"authenticated-layout\">\n        <mat-sidenav-container class=\"sidenav-container\">\n          <mat-sidenav #drawer\n                       class=\"sidenav\"\n                       fixedInViewport\n                       [attr.role]=\"'navigation'\"\n                       [mode]=\"isMobile ? 'over' : 'side'\"\n                       [opened]=\"!isMobile\"\n                       [disableClose]=\"!isMobile\">\n\n            <!-- Sidenav Header -->\n            <div class=\"sidenav-header\">\n              <div class=\"brand\">\n                <mat-icon class=\"brand-icon\">shield</mat-icon>\n                <span class=\"brand-text\">SPT</span>\n              </div>\n              <button mat-icon-button\n                      *ngIf=\"isMobile\"\n                      (click)=\"drawer.close()\"\n                      class=\"close-button\">\n                <mat-icon>close</mat-icon>\n              </button>\n            </div>\n\n            <!-- Navigation Menu -->\n            <mat-nav-list class=\"nav-list\">\n              <a mat-list-item\n                 routerLink=\"/dashboard\"\n                 routerLinkActive=\"active\"\n                 class=\"nav-item\"\n                 (click)=\"isMobile && drawer.close()\">\n                <mat-icon matListItemIcon class=\"nav-icon\">dashboard</mat-icon>\n                <span matListItemTitle class=\"nav-text\">Dashboard</span>\n              </a>\n\n              <a mat-list-item\n                 routerLink=\"/scan\"\n                 routerLinkActive=\"active\"\n                 class=\"nav-item\"\n                 (click)=\"isMobile && drawer.close()\">\n                <mat-icon matListItemIcon class=\"nav-icon\">security</mat-icon>\n                <span matListItemTitle class=\"nav-text\">Security Scan</span>\n              </a>\n\n              <a mat-list-item\n                 routerLink=\"/checklist\"\n                 routerLinkActive=\"active\"\n                 class=\"nav-item\"\n                 (click)=\"isMobile && drawer.close()\">\n                <mat-icon matListItemIcon class=\"nav-icon\">checklist</mat-icon>\n                <span matListItemTitle class=\"nav-text\">Security Checklist</span>\n              </a>\n\n              <a mat-list-item\n                 routerLink=\"/reports\"\n                 routerLinkActive=\"active\"\n                 class=\"nav-item\"\n                 (click)=\"isMobile && drawer.close()\">\n                <mat-icon matListItemIcon class=\"nav-icon\">assessment</mat-icon>\n                <span matListItemTitle class=\"nav-text\">Reports</span>\n              </a>\n\n              <a mat-list-item\n                 routerLink=\"/projects\"\n                 routerLinkActive=\"active\"\n                 class=\"nav-item\"\n                 (click)=\"isMobile && drawer.close()\">\n                <mat-icon matListItemIcon class=\"nav-icon\">folder</mat-icon>\n                <span matListItemTitle class=\"nav-text\">Projects</span>\n              </a>\n\n              <a mat-list-item\n                 routerLink=\"/settings\"\n                 routerLinkActive=\"active\"\n                 class=\"nav-item\"\n                 (click)=\"isMobile && drawer.close()\">\n                <mat-icon matListItemIcon class=\"nav-icon\">settings</mat-icon>\n                <span matListItemTitle class=\"nav-text\">Settings</span>\n              </a>\n            </mat-nav-list>\n\n            <!-- User Info -->\n            <div class=\"user-info\">\n              <div class=\"user-avatar\">\n                <mat-icon>account_circle</mat-icon>\n              </div>\n              <div class=\"user-details\">\n                <div class=\"user-name\">{{ user.username }}</div>\n                <div class=\"user-role\">{{ user.role | titlecase }}</div>\n              </div>\n            </div>\n          </mat-sidenav>\n\n          <!-- Main Content -->\n          <mat-sidenav-content class=\"main-content\">\n            <!-- Top Toolbar -->\n            <mat-toolbar color=\"primary\" class=\"top-toolbar\">\n              <button type=\"button\"\n                      mat-icon-button\n                      (click)=\"drawer.toggle()\"\n                      class=\"menu-button\">\n                <mat-icon>menu</mat-icon>\n              </button>\n\n              <span class=\"app-title\">🛡️ Blockchain Security Protocol Tool</span>\n              <span class=\"spacer\"></span>\n\n              <!-- Notifications -->\n              <button mat-icon-button class=\"toolbar-button\">\n                <mat-icon matBadge=\"3\" matBadgeColor=\"warn\">notifications</mat-icon>\n              </button>\n\n              <!-- User Menu -->\n              <button mat-icon-button\n                      [matMenuTriggerFor]=\"userMenu\"\n                      class=\"toolbar-button\">\n                <mat-icon>account_circle</mat-icon>\n              </button>\n\n              <mat-menu #userMenu=\"matMenu\">\n                <button mat-menu-item>\n                  <mat-icon>person</mat-icon>\n                  <span>Profile</span>\n                </button>\n                <button mat-menu-item>\n                  <mat-icon>settings</mat-icon>\n                  <span>Settings</span>\n                </button>\n                <mat-divider></mat-divider>\n                <button mat-menu-item (click)=\"logout()\">\n                  <mat-icon>logout</mat-icon>\n                  <span>Logout</span>\n                </button>\n              </mat-menu>\n            </mat-toolbar>\n\n            <!-- Page Content -->\n            <div class=\"page-content\">\n              <router-outlet></router-outlet>\n            </div>\n          </mat-sidenav-content>\n        </mat-sidenav-container>\n      </div>\n\n      <!-- Login View -->\n      <ng-template #loginView>\n        <div class=\"login-layout\">\n          <router-outlet></router-outlet>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [`\n    /* Global Layout */\n    .app-container {\n      height: 100vh;\n      overflow: hidden;\n    }\n\n    .doc-container {\n      height: 100vh;\n    }\n\n    .login-layout {\n      height: 100vh;\n    }\n\n    .authenticated-layout {\n      height: 100vh;\n    }\n\n    /* Sidenav Container */\n    .sidenav-container {\n      height: 100vh;\n    }\n\n    .sidenav {\n      width: 280px;\n      background: #ffffff;\n      border-right: 1px solid #e0e0e0;\n      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\n    }\n\n    /* Sidenav Header */\n    .sidenav-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 20px 24px;\n      border-bottom: 1px solid #e0e0e0;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n    }\n\n    .brand {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .brand-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n    }\n\n    .brand-text {\n      font-size: 24px;\n      font-weight: 700;\n      letter-spacing: -0.5px;\n    }\n\n    .close-button {\n      color: white;\n    }\n\n    /* Navigation */\n    .nav-list {\n      padding: 16px 0;\n    }\n\n    .nav-item {\n      margin: 4px 16px;\n      border-radius: 12px;\n      transition: all 0.3s ease;\n      color: #64748b;\n      text-decoration: none;\n      min-height: 48px;\n    }\n\n    .nav-item:hover {\n      background-color: rgba(102, 126, 234, 0.08);\n      color: #667eea;\n    }\n\n    .nav-item.active {\n      background-color: rgba(102, 126, 234, 0.12);\n      color: #667eea;\n      font-weight: 600;\n    }\n\n    .nav-icon {\n      color: inherit;\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .nav-text {\n      font-size: 14px;\n      font-weight: 500;\n    }\n\n    /* User Info */\n    .user-info {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      padding: 20px 24px;\n      border-top: 1px solid #e0e0e0;\n      background: #f8fafc;\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar mat-icon {\n      font-size: 40px;\n      width: 40px;\n      height: 40px;\n      color: #667eea;\n    }\n\n    .user-details {\n      flex: 1;\n    }\n\n    .user-name {\n      font-weight: 600;\n      font-size: 14px;\n      color: #1e293b;\n      margin-bottom: 2px;\n    }\n\n    .user-role {\n      font-size: 12px;\n      color: #64748b;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    /* Main Content */\n    .main-content {\n      display: flex;\n      flex-direction: column;\n      height: 100vh;\n    }\n\n    .top-toolbar {\n      position: sticky;\n      top: 0;\n      z-index: 100;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .menu-button {\n      margin-right: 16px;\n    }\n\n    .app-title {\n      font-size: 18px;\n      font-weight: 600;\n    }\n\n    .spacer {\n      flex: 1 1 auto;\n    }\n\n    .toolbar-button {\n      margin-left: 8px;\n    }\n\n    /* Page Content */\n    .page-content {\n      flex: 1;\n      overflow-y: auto;\n      background: #f8fafc;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 768px) {\n      .sidenav {\n        width: 100%;\n        max-width: 320px;\n      }\n\n      .app-title {\n        font-size: 16px;\n      }\n\n      .menu-button {\n        margin-right: 8px;\n      }\n\n      .user-info {\n        padding: 16px 20px;\n      }\n    }\n\n    /* Material Design Overrides */\n    .mat-mdc-list-item {\n      --mdc-list-list-item-container-height: 48px;\n    }\n\n    .mat-mdc-list-item .mdc-list-item__content {\n      padding: 0 16px;\n    }\n\n    .mat-toolbar {\n      padding: 0 16px;\n    }\n\n    .mat-toolbar.mat-primary {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n\n    /* Menu Styling */\n    .mat-mdc-menu-panel {\n      border-radius: 12px;\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n  `]\n})\nexport class AppComponent implements OnInit {\n  title = 'SPT - Blockchain Security Protocol Tool';\n  currentUser$: Observable<User | null>;\n  isDocumentationRoute$: Observable<boolean>;\n  isMobile = false;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private breakpointObserver: BreakpointObserver\n  ) {\n    this.currentUser$ = this.authService.currentUser$;\n\n    // Check if current route is documentation - include initial route\n    this.isDocumentationRoute$ = this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd),\n      map((event: NavigationEnd) => event.url.startsWith('/doc')),\n      // Start with current URL check\n      startWith(this.router.url.startsWith('/doc'))\n    );\n\n    // Check for mobile breakpoint\n    this.breakpointObserver.observe([Breakpoints.Handset])\n      .subscribe(result => {\n        this.isMobile = result.matches;\n      });\n  }\n\n  ngOnInit(): void {\n    // Debug authentication state\n    this.currentUser$.subscribe(user => {\n      console.log('App Component - Current user changed:', user);\n    });\n\n    console.log('App Component - Initial auth state:', this.authService.isAuthenticated());\n    console.log('App Component - Initial user:', this.authService.getCurrentUser());\n  }\n\n  hasRole(roles: string[]): boolean {\n    return this.authService.hasAnyRole(roles);\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAuB,iBAAiB;AAC7D,SAASC,YAAY,EAAEC,YAAY,EAAUC,aAAa,QAAQ,iBAAiB;AACnF,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAA6BC,WAAW,QAAQ,qBAAqB;AAErE,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;IAqBnDC,EAAA,CAAAC,cAAA,aAAiE;IAC/DD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAqBIH,EAAA,CAAAC,cAAA,iBAG6B;IADrBD,EAAA,CAAAI,UAAA,mBAAAC,8DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAC,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,SAAA,CAAAG,KAAA,EAAc;IAAA,EAAC;IAE9BZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAa,MAAA,YAAK;IACjBb,EADiB,CAAAG,YAAA,EAAW,EACnB;;;;;IA6HfH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAvIEH,EAfZ,CAAAC,cAAA,aAAoE,aAEhC,+BACiB,wBAOP,cAGV,cACP,mBACY;IAAAD,EAAA,CAAAa,MAAA,aAAM;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,WAAG;IAC9Bb,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAc,UAAA,KAAAC,qCAAA,qBAG6B;IAG/Bf,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,wBAA+B,aAKW;IAArCD,EAAA,CAAAI,UAAA,mBAAAY,gDAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAR,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,MAAAQ,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAAO,MAAA,CAAAC,QAAA,IAAqBV,SAAA,CAAAG,KAAA,EAAc;IAAA,EAAC;IACrCZ,EAAA,CAAAC,cAAA,oBAA2C;IAAAD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAa,MAAA,iBAAS;IACnDb,EADmD,CAAAG,YAAA,EAAO,EACtD;IAEJH,EAAA,CAAAC,cAAA,aAIwC;IAArCD,EAAA,CAAAI,UAAA,mBAAAgB,gDAAA;MAAApB,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAR,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,MAAAQ,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAAO,MAAA,CAAAC,QAAA,IAAqBV,SAAA,CAAAG,KAAA,EAAc;IAAA,EAAC;IACrCZ,EAAA,CAAAC,cAAA,oBAA2C;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC9DH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAa,MAAA,qBAAa;IACvDb,EADuD,CAAAG,YAAA,EAAO,EAC1D;IAEJH,EAAA,CAAAC,cAAA,aAIwC;IAArCD,EAAA,CAAAI,UAAA,mBAAAiB,gDAAA;MAAArB,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAR,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,MAAAQ,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAAO,MAAA,CAAAC,QAAA,IAAqBV,SAAA,CAAAG,KAAA,EAAc;IAAA,EAAC;IACrCZ,EAAA,CAAAC,cAAA,oBAA2C;IAAAD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAC5Db,EAD4D,CAAAG,YAAA,EAAO,EAC/D;IAEJH,EAAA,CAAAC,cAAA,aAIwC;IAArCD,EAAA,CAAAI,UAAA,mBAAAkB,gDAAA;MAAAtB,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAR,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,MAAAQ,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAAO,MAAA,CAAAC,QAAA,IAAqBV,SAAA,CAAAG,KAAA,EAAc;IAAA,EAAC;IACrCZ,EAAA,CAAAC,cAAA,oBAA2C;IAAAD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAChEH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAa,MAAA,eAAO;IACjDb,EADiD,CAAAG,YAAA,EAAO,EACpD;IAEJH,EAAA,CAAAC,cAAA,aAIwC;IAArCD,EAAA,CAAAI,UAAA,mBAAAmB,gDAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAR,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,MAAAQ,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAAO,MAAA,CAAAC,QAAA,IAAqBV,SAAA,CAAAG,KAAA,EAAc;IAAA,EAAC;IACrCZ,EAAA,CAAAC,cAAA,oBAA2C;IAAAD,EAAA,CAAAa,MAAA,cAAM;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC5DH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAClDb,EADkD,CAAAG,YAAA,EAAO,EACrD;IAEJH,EAAA,CAAAC,cAAA,aAIwC;IAArCD,EAAA,CAAAI,UAAA,mBAAAoB,gDAAA;MAAAxB,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAR,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,MAAAQ,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAAO,MAAA,CAAAC,QAAA,IAAqBV,SAAA,CAAAG,KAAA,EAAc;IAAA,EAAC;IACrCZ,EAAA,CAAAC,cAAA,oBAA2C;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC9DH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAEpDb,EAFoD,CAAAG,YAAA,EAAO,EACrD,EACS;IAKXH,EAFJ,CAAAC,cAAA,eAAuB,eACI,gBACb;IAAAD,EAAA,CAAAa,MAAA,sBAAc;IAC1Bb,EAD0B,CAAAG,YAAA,EAAW,EAC/B;IAEJH,EADF,CAAAC,cAAA,eAA0B,eACD;IAAAD,EAAA,CAAAa,MAAA,IAAmB;IAAAb,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAa,MAAA,IAA2B;;IAGxDb,EAHwD,CAAAG,YAAA,EAAM,EACpD,EACF,EACM;IAMVH,EAHJ,CAAAC,cAAA,+BAA0C,uBAES,kBAInB;IADpBD,EAAA,CAAAI,UAAA,mBAAAqB,qDAAA;MAAAzB,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAR,SAAA,GAAAT,EAAA,CAAAU,WAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,SAAA,CAAAiB,MAAA,EAAe;IAAA,EAAC;IAE/B1B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAa,MAAA,YAAI;IAChBb,EADgB,CAAAG,YAAA,EAAW,EAClB;IAETH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAa,MAAA,4DAAqC;IAAAb,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAE,SAAA,gBAA4B;IAI1BF,EADF,CAAAC,cAAA,kBAA+C,oBACD;IAAAD,EAAA,CAAAa,MAAA,qBAAa;IAC3Db,EAD2D,CAAAG,YAAA,EAAW,EAC7D;IAMPH,EAHF,CAAAC,cAAA,kBAE+B,gBACnB;IAAAD,EAAA,CAAAa,MAAA,sBAAc;IAC1Bb,EAD0B,CAAAG,YAAA,EAAW,EAC5B;IAILH,EAFJ,CAAAC,cAAA,yBAA8B,kBACN,gBACV;IAAAD,EAAA,CAAAa,MAAA,cAAM;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,eAAO;IACfb,EADe,CAAAG,YAAA,EAAO,EACb;IAEPH,EADF,CAAAC,cAAA,kBAAsB,gBACV;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAChBb,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAE,SAAA,mBAA2B;IAC3BF,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAI,UAAA,mBAAAuB,qDAAA;MAAA3B,EAAA,CAAAM,aAAA,CAAAW,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASO,MAAA,CAAAU,MAAA,EAAQ;IAAA,EAAC;IACtC5B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAa,MAAA,cAAM;IAAAb,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,cAAM;IAGlBb,EAHkB,CAAAG,YAAA,EAAO,EACZ,EACA,EACC;IAGdH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,qBAA+B;IAIvCF,EAHM,CAAAG,YAAA,EAAM,EACc,EACA,EACpB;IAGNH,EAAA,CAAAc,UAAA,KAAAe,0CAAA,gCAAA7B,EAAA,CAAA8B,sBAAA,CAAwB;IAK1B9B,EAAA,CAAAG,YAAA,EAAM;;;;;IAhJaH,EAAA,CAAA+B,SAAA,GAAmC;IAEnC/B,EAFA,CAAAgC,UAAA,SAAAd,MAAA,CAAAC,QAAA,mBAAmC,YAAAD,MAAA,CAAAC,QAAA,CACf,kBAAAD,MAAA,CAAAC,QAAA,CACM;;IAS1BnB,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAgC,UAAA,SAAAd,MAAA,CAAAC,QAAA,CAAc;IAsEEnB,EAAA,CAAA+B,SAAA,IAAmB;IAAnB/B,EAAA,CAAAiC,iBAAA,CAAAf,MAAA,CAAAgB,IAAA,CAAAC,QAAA,CAAmB;IACnBnC,EAAA,CAAA+B,SAAA,GAA2B;IAA3B/B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAoC,WAAA,QAAAlB,MAAA,CAAAgB,IAAA,CAAAG,IAAA,EAA2B;IA0B5CrC,EAAA,CAAA+B,SAAA,IAA8B;IAA9B/B,EAAA,CAAAgC,UAAA,sBAAAM,WAAA,CAA8B;;;AAoQpD,OAAM,MAAOC,YAAY;EAMvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,kBAAsC;IAFtC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAR5B,KAAAC,KAAK,GAAG,yCAAyC;IAGjD,KAAAzB,QAAQ,GAAG,KAAK;IAOd,IAAI,CAAC0B,YAAY,GAAG,IAAI,CAACJ,WAAW,CAACI,YAAY;IAEjD;IACA,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACJ,MAAM,CAACK,MAAM,CAACC,IAAI,CAClDnD,MAAM,CAACoD,KAAK,IAAIA,KAAK,YAAY9D,aAAa,CAAC,EAC/CW,GAAG,CAAEmD,KAAoB,IAAKA,KAAK,CAACC,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC3D;IACApD,SAAS,CAAC,IAAI,CAAC2C,MAAM,CAACQ,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,CAAC,CAC9C;IAED;IACA,IAAI,CAACR,kBAAkB,CAACS,OAAO,CAAC,CAACxD,WAAW,CAACyD,OAAO,CAAC,CAAC,CACnDC,SAAS,CAACC,MAAM,IAAG;MAClB,IAAI,CAACpC,QAAQ,GAAGoC,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC;EACN;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,YAAY,CAACS,SAAS,CAACpB,IAAI,IAAG;MACjCwB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEzB,IAAI,CAAC;IAC5D,CAAC,CAAC;IAEFwB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAClB,WAAW,CAACmB,eAAe,EAAE,CAAC;IACtFF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAClB,WAAW,CAACoB,cAAc,EAAE,CAAC;EACjF;EAEAC,OAAOA,CAACC,KAAe;IACrB,OAAO,IAAI,CAACtB,WAAW,CAACuB,UAAU,CAACD,KAAK,CAAC;EAC3C;EAEAnC,MAAMA,CAAA;IACJ,IAAI,CAACa,WAAW,CAACb,MAAM,EAAE;EAC3B;;;uCA5CWW,YAAY,EAAAvC,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnE,EAAA,CAAAiE,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAArE,EAAA,CAAAiE,iBAAA,CAAAK,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAZhC,YAAY;MAAAiC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7XrB9E,EAAA,CAAAc,UAAA,IAAAkE,2BAAA,iBAAiE;;UAKjEhF,EAAA,CAAAc,UAAA,IAAAmE,2BAAA,mBAAoE;;;;UAL9DjF,EAAA,CAAAgC,UAAA,SAAAhC,EAAA,CAAAoC,WAAA,OAAA2C,GAAA,CAAAjC,qBAAA,EAAmC;UAKnC9C,EAAA,CAAA+B,SAAA,GAAsC;UAAtC/B,EAAA,CAAAgC,UAAA,UAAAhC,EAAA,CAAAoC,WAAA,OAAA2C,GAAA,CAAAjC,qBAAA,EAAsC;;;qBAnB5C9D,YAAY,EAAAkG,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,SAAA,EAAAF,EAAA,CAAAG,aAAA,EACZpG,YAAY,EACZC,YAAY,EAAAkF,EAAA,CAAAkB,UAAA,EAAAlB,EAAA,CAAAmB,gBAAA,EACZnG,gBAAgB,EAAAoG,EAAA,CAAAC,UAAA,EAChBpG,gBAAgB,EAAAqG,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAChBvG,aAAa,EAAAwG,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,UAAA,EAAAJ,EAAA,CAAAK,gBAAA,EACb5G,aAAa,EAAA6G,EAAA,CAAAC,OAAA,EACb7G,eAAe,EAAA8G,EAAA,CAAAC,aAAA,EACf9G,aAAa,EAAA+G,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA,EACbjH,cAAc,EAAAkH,GAAA,CAAAC,QAAA,EACdlH,gBAAgB;MAAAmH,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}