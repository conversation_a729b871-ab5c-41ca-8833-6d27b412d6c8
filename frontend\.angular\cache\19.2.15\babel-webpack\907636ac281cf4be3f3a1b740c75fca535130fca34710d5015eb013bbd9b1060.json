{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/tabs\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/expansion\";\nfunction GettingStartedComponent_mat_card_16_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"strong\");\n    i0.ɵɵtext(2, \"Installation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"code\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const req_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(req_r1.installCommand);\n  }\n}\nfunction GettingStartedComponent_mat_card_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 26)(1, \"mat-card-header\")(2, \"mat-icon\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, GettingStartedComponent_mat_card_16_div_11_Template, 5, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const req_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", req_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", req_r1.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(req_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(req_r1.version);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(req_r1.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", req_r1.installCommand);\n  }\n}\nfunction GettingStartedComponent_mat_expansion_panel_22_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Commands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(step_r2.commands);\n  }\n}\nfunction GettingStartedComponent_mat_expansion_panel_22_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(step_r2.notes);\n  }\n}\nfunction GettingStartedComponent_mat_expansion_panel_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 29)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"span\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 31)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, GettingStartedComponent_mat_expansion_panel_22_div_11_Template, 9, 1, \"div\", 32)(12, GettingStartedComponent_mat_expansion_panel_22_div_12_Template, 5, 1, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵproperty(\"expanded\", i_r3 === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", step_r2.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", step_r2.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r2.details);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.commands);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.notes);\n  }\n}\nfunction GettingStartedComponent_div_72_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"pre\")(2, \"code\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.command);\n  }\n}\nfunction GettingStartedComponent_div_72_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_downward\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GettingStartedComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, GettingStartedComponent_div_72_div_8_Template, 4, 1, \"div\", 32)(9, GettingStartedComponent_div_72_div_9_Template, 3, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r5 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r5 < ctx_r5.quickStartFlow.length - 1);\n  }\n}\nexport let GettingStartedComponent = /*#__PURE__*/(() => {\n  class GettingStartedComponent {\n    constructor() {\n      this.prerequisites = [{\n        name: 'Go',\n        version: '1.21+',\n        description: 'Required for the backend services and CLI tool',\n        icon: 'code',\n        color: '#00ADD8',\n        installCommand: 'https://golang.org/doc/install'\n      }, {\n        name: 'Node.js',\n        version: '18+',\n        description: 'Required for the frontend dashboard and development tools',\n        icon: 'javascript',\n        color: '#339933',\n        installCommand: 'https://nodejs.org/'\n      }, {\n        name: 'Angular CLI',\n        version: 'Latest',\n        description: 'Required for frontend development and building',\n        icon: 'web',\n        color: '#DD0031',\n        installCommand: 'npm install -g @angular/cli'\n      }, {\n        name: 'Git',\n        version: 'Latest',\n        description: 'Required for version control and repository management',\n        icon: 'source',\n        color: '#F05032',\n        installCommand: 'https://git-scm.com/downloads'\n      }];\n      this.installationSteps = [{\n        title: 'Clone Repository',\n        description: 'Get the SPT source code',\n        details: 'Clone the SPT repository from GitHub to your local development environment.',\n        commands: 'git clone https://github.com/blockchain-spt/spt.git\\ncd spt',\n        notes: 'Make sure you have Git installed and configured'\n      }, {\n        title: 'Backend Setup',\n        description: 'Install Go dependencies and start backend',\n        details: 'Navigate to the backend directory, install dependencies, and start the Go server.',\n        commands: 'cd backend\\ngo mod tidy\\ngo run cmd/main.go',\n        notes: 'Backend will start on port 8080 by default'\n      }, {\n        title: 'Frontend Setup',\n        description: 'Install Node.js dependencies and start frontend',\n        details: 'Navigate to the frontend directory, install npm packages, and start the Angular development server.',\n        commands: 'cd frontend\\nnpm install\\nnpm start',\n        notes: 'Frontend will start on port 4200 by default'\n      }, {\n        title: 'CLI Tool (Optional)',\n        description: 'Build the command-line interface',\n        details: 'Build the SPT CLI tool for command-line security scanning.',\n        commands: 'make cli\\n# or\\ngo build -o spt cmd/main.go',\n        notes: 'CLI tool will be available as ./spt'\n      }];\n      this.quickStartFlow = [{\n        title: 'Clone & Navigate',\n        description: 'Get the source code and navigate to the project directory',\n        command: 'git clone https://github.com/blockchain-spt/spt.git\\ncd spt'\n      }, {\n        title: 'Start Backend',\n        description: 'Launch the Go backend server with all APIs',\n        command: 'cd backend && go run cmd/main.go'\n      }, {\n        title: 'Start Frontend',\n        description: 'Launch the Angular development server in a new terminal',\n        command: 'cd frontend && npm install && npm start'\n      }, {\n        title: 'Access Application',\n        description: 'Open your browser and start using SPT',\n        command: 'Open http://localhost:4200 in your browser'\n      }];\n      this.backendConfig = `{\n  \"environment\": \"development\",\n  \"server\": {\n    \"port\": 8080,\n    \"host\": \"localhost\"\n  },\n  \"database\": {\n    \"type\": \"sqlite\",\n    \"database\": \"spt.db\"\n  },\n  \"security\": {\n    \"jwt_secret\": \"your-secret-key\",\n    \"cors_enabled\": true\n  },\n  \"scanning\": {\n    \"max_concurrent_scans\": 5,\n    \"timeout_seconds\": 300\n  }\n}`;\n      this.frontendConfig = `# Frontend Environment Variables\nAPI_URL=http://localhost:8080\nWS_URL=ws://localhost:8080/ws\nENVIRONMENT=development\nDEBUG=true`;\n    }\n    static {\n      this.ɵfac = function GettingStartedComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || GettingStartedComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: GettingStartedComponent,\n        selectors: [[\"app-getting-started\"]],\n        decls: 109,\n        vars: 5,\n        consts: [[1, \"getting-started-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [\"animationDuration\", \"300ms\", 1, \"content-tabs\"], [\"label\", \"Prerequisites\"], [1, \"tab-content\"], [1, \"requirements-grid\"], [\"class\", \"requirement-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Installation\"], [1, \"installation-steps\"], [3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-card\"], [\"mat-card-avatar\", \"\"], [1, \"code-block\"], [1, \"code-header\"], [\"label\", \"Quick Start\"], [1, \"quick-start-flow\"], [\"class\", \"flow-step\", 4, \"ngFor\", \"ngForOf\"], [1, \"success-card\"], [\"mat-card-avatar\", \"\", 2, \"background-color\", \"#4caf50\"], [\"href\", \"http://localhost:4200\", \"target\", \"_blank\"], [\"href\", \"http://localhost:8080\", \"target\", \"_blank\"], [\"href\", \"http://localhost:8080/health\", \"target\", \"_blank\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/doc/api-reference\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/doc/security-practices\"], [1, \"requirement-card\"], [\"class\", \"install-command\", 4, \"ngIf\"], [1, \"install-command\"], [3, \"expanded\"], [1, \"step-number\"], [1, \"step-content\"], [\"class\", \"code-block\", 4, \"ngIf\"], [\"class\", \"step-notes\", 4, \"ngIf\"], [1, \"step-notes\"], [1, \"flow-step\"], [1, \"flow-step-header\"], [1, \"flow-step-number\"], [\"class\", \"flow-arrow\", 4, \"ngIf\"], [1, \"flow-arrow\"]],\n        template: function GettingStartedComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"play_arrow\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(5, \" Getting Started \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 2);\n            i0.ɵɵtext(7, \" Get SPT up and running in your development environment \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"mat-tab-group\", 3)(9, \"mat-tab\", 4)(10, \"div\", 5)(11, \"h2\");\n            i0.ɵɵtext(12, \"System Requirements\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"p\");\n            i0.ɵɵtext(14, \"Before installing SPT, ensure you have the following prerequisites:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 6);\n            i0.ɵɵtemplate(16, GettingStartedComponent_mat_card_16_Template, 12, 7, \"mat-card\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"mat-tab\", 8)(18, \"div\", 5)(19, \"h2\");\n            i0.ɵɵtext(20, \"Installation Steps\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"mat-accordion\", 9);\n            i0.ɵɵtemplate(22, GettingStartedComponent_mat_expansion_panel_22_Template, 13, 7, \"mat-expansion-panel\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"mat-tab\", 11)(24, \"div\", 5)(25, \"h2\");\n            i0.ɵɵtext(26, \"Configuration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"p\");\n            i0.ɵɵtext(28, \"Configure SPT for your development environment:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"mat-card\", 12)(30, \"mat-card-header\")(31, \"mat-icon\", 13);\n            i0.ɵɵtext(32, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"mat-card-title\");\n            i0.ɵɵtext(34, \"Backend Configuration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"mat-card-subtitle\");\n            i0.ɵɵtext(36, \"spt.config.json\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"mat-card-content\")(38, \"div\", 14)(39, \"div\", 15)(40, \"mat-icon\");\n            i0.ɵɵtext(41, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"span\");\n            i0.ɵɵtext(43, \"Configuration File\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"pre\")(45, \"code\");\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(47, \"mat-card\", 12)(48, \"mat-card-header\")(49, \"mat-icon\", 13);\n            i0.ɵɵtext(50, \"web\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"mat-card-title\");\n            i0.ɵɵtext(52, \"Frontend Configuration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"mat-card-subtitle\");\n            i0.ɵɵtext(54, \"Environment Variables\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"mat-card-content\")(56, \"div\", 14)(57, \"div\", 15)(58, \"mat-icon\");\n            i0.ɵɵtext(59, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"span\");\n            i0.ɵɵtext(61, \"Environment File\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(62, \"pre\")(63, \"code\");\n            i0.ɵɵtext(64);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(65, \"mat-tab\", 16)(66, \"div\", 5)(67, \"h2\");\n            i0.ɵɵtext(68, \"Quick Start Guide\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"p\");\n            i0.ɵɵtext(70, \"Get SPT running with these simple commands:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"div\", 17);\n            i0.ɵɵtemplate(72, GettingStartedComponent_div_72_Template, 10, 5, \"div\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"mat-card\", 19)(74, \"mat-card-header\")(75, \"mat-icon\", 20);\n            i0.ɵɵtext(76, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"mat-card-title\");\n            i0.ɵɵtext(78, \"Success!\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"mat-card-subtitle\");\n            i0.ɵɵtext(80, \"SPT is now running\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(81, \"mat-card-content\")(82, \"p\");\n            i0.ɵɵtext(83, \"You can now access:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"ul\")(85, \"li\")(86, \"strong\");\n            i0.ɵɵtext(87, \"Web Dashboard:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"a\", 21);\n            i0.ɵɵtext(89, \"http://localhost:4200\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(90, \"li\")(91, \"strong\");\n            i0.ɵɵtext(92, \"API Server:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"a\", 22);\n            i0.ɵɵtext(94, \"http://localhost:8080\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(95, \"li\")(96, \"strong\");\n            i0.ɵɵtext(97, \"Health Check:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"a\", 23);\n            i0.ɵɵtext(99, \"http://localhost:8080/health\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(100, \"mat-card-actions\")(101, \"button\", 24)(102, \"mat-icon\");\n            i0.ɵɵtext(103, \"api\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(104, \" Explore API \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(105, \"button\", 25)(106, \"mat-icon\");\n            i0.ɵɵtext(107, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(108, \" Security Guide \");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.prerequisites);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.installationSteps);\n            i0.ɵɵadvance(24);\n            i0.ɵɵtextInterpolate(ctx.backendConfig);\n            i0.ɵɵadvance(18);\n            i0.ɵɵtextInterpolate(ctx.frontendConfig);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.quickStartFlow);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink, MatTabsModule, i3.MatTab, i3.MatTabGroup, MatCardModule, i4.MatCard, i4.MatCardActions, i4.MatCardAvatar, i4.MatCardContent, i4.MatCardHeader, i4.MatCardSubtitle, i4.MatCardTitle, MatIconModule, i5.MatIcon, MatButtonModule, i6.MatButton, MatExpansionModule, i7.MatAccordion, i7.MatExpansionPanel, i7.MatExpansionPanelHeader, i7.MatExpansionPanelTitle, i7.MatExpansionPanelDescription],\n        styles: [\".getting-started-container[_ngcontent-%COMP%]{max-width:1000px;margin:0 auto}.page-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;color:#1976d2;margin:0 0 8px}.page-subtitle[_ngcontent-%COMP%]{color:#666;font-size:1.1em;margin:0}.content-tabs[_ngcontent-%COMP%]{margin-bottom:32px}.tab-content[_ngcontent-%COMP%]{padding:24px 0}.requirements-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:16px;margin-top:24px}.requirement-card[_ngcontent-%COMP%]{height:100%}.install-command[_ngcontent-%COMP%]{margin-top:12px;padding:8px;background:#f5f5f5;border-radius:4px}.install-command[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:.9em}.installation-steps[_ngcontent-%COMP%]{margin-top:24px}.step-number[_ngcontent-%COMP%]{background:#1976d2;color:#fff;border-radius:50%;width:24px;height:24px;display:inline-flex;align-items:center;justify-content:center;font-size:.8em;font-weight:700;margin-right:12px}.step-content[_ngcontent-%COMP%]{padding:16px 0}.code-block[_ngcontent-%COMP%]{margin:16px 0;border:1px solid #e0e0e0;border-radius:8px;overflow:hidden}.code-header[_ngcontent-%COMP%]{background:#f5f5f5;padding:8px 16px;display:flex;align-items:center;gap:8px;font-weight:500;border-bottom:1px solid #e0e0e0}.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{margin:0;padding:16px;background:#fafafa;overflow-x:auto}.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:.9em}.step-notes[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-top:12px;padding:8px 12px;background:#e3f2fd;border-radius:4px;color:#1976d2}.config-card[_ngcontent-%COMP%]{margin-bottom:24px}.quick-start-flow[_ngcontent-%COMP%]{margin:24px 0}.flow-step[_ngcontent-%COMP%]{margin-bottom:32px;position:relative}.flow-step-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;margin-bottom:12px}.flow-step-number[_ngcontent-%COMP%]{background:#1976d2;color:#fff;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:1.1em}.flow-step[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#1976d2}.flow-arrow[_ngcontent-%COMP%]{text-align:center;margin:16px 0;color:#1976d2}.success-card[_ngcontent-%COMP%]{margin-top:32px;border:2px solid #4caf50}.success-card[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:16px 0}.success-card[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1976d2;text-decoration:none}.success-card[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 768px){.requirements-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.flow-step-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:8px}}\"]\n      });\n    }\n  }\n  return GettingStartedComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}