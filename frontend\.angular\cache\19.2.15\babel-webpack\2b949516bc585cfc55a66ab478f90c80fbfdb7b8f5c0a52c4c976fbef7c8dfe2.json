{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/checkbox\";\nfunction LoginComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction LoginComponent_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  loginWithDemo() {\n    this.isLoading = true;\n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', {\n        duration: 3000\n      });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 41,\n      vars: 9,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"logo\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"form-options\"], [\"formControlName\", \"rememberMe\"], [\"href\", \"#\", 1, \"forgot-password\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"auth-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"demo-button\", 3, \"click\", \"disabled\"], [1, \"auth-footer\"], [\"routerLink\", \"/register\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7, \"SPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"h1\");\n          i0.ɵɵtext(9, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\");\n          i0.ɵɵtext(11, \"Sign in to your security dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(13, \"mat-form-field\", 5)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 6);\n          i0.ɵɵtemplate(17, LoginComponent_mat_error_17_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 5)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 8);\n          i0.ɵɵelementStart(22, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_22_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, LoginComponent_mat_error_25_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"mat-checkbox\", 11);\n          i0.ɵɵtext(28, \" Remember me \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"a\", 12);\n          i0.ɵɵtext(30, \"Forgot password?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"button\", 13);\n          i0.ɵɵtemplate(32, LoginComponent_mat_spinner_32_Template, 1, 0, \"mat-spinner\", 14)(33, LoginComponent_span_33_Template, 2, 0, \"span\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_34_listener() {\n            return ctx.loginWithDemo();\n          });\n          i0.ɵɵtext(35, \" Demo Login \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 16)(37, \"p\");\n          i0.ɵɵtext(38, \"Don't have an account? \");\n          i0.ɵɵelementStart(39, \"a\", 17);\n          i0.ɵɵtext(40, \"Sign up\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_4_0.hasError(\"required\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatSuffix, MatInputModule, i7.MatInput, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatProgressSpinnerModule, i10.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i11.MatCheckbox],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "hidePassword", "isLoading", "returnUrl", "loginForm", "group", "username", "required", "password", "rememberMe", "ngOnInit", "snapshot", "queryParams", "onSubmit", "valid", "credentials", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "message", "loginWithDemo", "setTimeout", "mockLogin", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_12_listener", "ɵɵtemplate", "LoginComponent_mat_error_17_Template", "LoginComponent_Template_button_click_22_listener", "LoginComponent_mat_error_25_Template", "LoginComponent_mat_spinner_32_Template", "LoginComponent_span_33_Template", "LoginComponent_Template_button_click_34_listener", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "tmp_4_0", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i7", "MatInput", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatProgressSpinner", "i11", "MatCheckbox", "encapsulation"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\n\nimport { AuthService, LoginRequest } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  hidePassword = true;\n  isLoading = false;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      \n      const credentials: LoginRequest = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n\n  loginWithDemo(): void {\n    this.isLoading = true;\n    \n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card\">\n    <div class=\"auth-header\">\n      <div class=\"logo\">\n        <mat-icon>security</mat-icon>\n        <span>SPT</span>\n      </div>\n      <h1>Welcome Back</h1>\n      <p>Sign in to your security dashboard</p>\n    </div>\n\n    <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Username</mat-label>\n        <input matInput formControlName=\"username\" autocomplete=\"username\">\n        <mat-error *ngIf=\"loginForm.get('username')?.hasError('required')\">\n          Username is required\n        </mat-error>\n      </mat-form-field>\n\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Password</mat-label>\n        <input matInput\n               [type]=\"hidePassword ? 'password' : 'text'\"\n               formControlName=\"password\"\n               autocomplete=\"current-password\">\n        <button mat-icon-button matSuffix\n                (click)=\"hidePassword = !hidePassword\"\n                type=\"button\">\n          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n        </button>\n        <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n          Password is required\n        </mat-error>\n      </mat-form-field>\n\n      <div class=\"form-options\">\n        <mat-checkbox formControlName=\"rememberMe\">\n          Remember me\n        </mat-checkbox>\n        <a href=\"#\" class=\"forgot-password\">Forgot password?</a>\n      </div>\n\n      <button mat-raised-button\n              color=\"primary\"\n              type=\"submit\"\n              class=\"auth-button\"\n              [disabled]=\"loginForm.invalid || isLoading\">\n        <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n        <span *ngIf=\"!isLoading\">Sign In</span>\n      </button>\n\n      <button mat-button\n              type=\"button\"\n              class=\"demo-button\"\n              (click)=\"loginWithDemo()\"\n              [disabled]=\"isLoading\">\n        Demo Login\n      </button>\n    </form>\n\n    <div class=\"auth-footer\">\n      <p>Don't have an account? <a routerLink=\"/register\">Sign up</a></p>\n    </div>\n  </div>\n\n\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACxF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;ICItDC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAcZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAeZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADd/C,OAAM,MAAOE,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MAErB,MAAMa,WAAW,GAAiB;QAChCT,QAAQ,EAAE,IAAI,CAACF,SAAS,CAACY,KAAK,CAACV,QAAQ;QACvCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACY,KAAK,CAACR;OAChC;MAED,IAAI,CAACX,WAAW,CAACoB,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;QACxC,CAAC;QACDqB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;OACD,CAAC;IACJ;EACF;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACxB,SAAS,GAAG,IAAI;IAErB;IACAyB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9B,WAAW,CAAC+B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;MAC5C,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;IACxC,CAAC,EAAE,IAAI,CAAC;EACV;;;uCA1DWT,cAAc,EAAAL,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA/C,EAAA,CAAAwC,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd5C,cAAc;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/BnBxD,EAJR,CAAAC,cAAA,aAA4B,aACH,aACI,aACL,eACN;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,UAAG;UACXF,EADW,CAAAG,YAAA,EAAO,EACZ;UACNH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0CAAkC;UACvCF,EADuC,CAAAG,YAAA,EAAI,EACrC;UAENH,EAAA,CAAAC,cAAA,eAAwE;UAA1CD,EAAA,CAAA0D,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAEjDxB,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAAmE;UACnEJ,EAAA,CAAA4D,UAAA,KAAAC,oCAAA,uBAAmE;UAGrE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAGuC;UACvCJ,EAAA,CAAAC,cAAA,iBAEsB;UADdD,EAAA,CAAA0D,UAAA,mBAAAI,iDAAA;YAAA,OAAAL,GAAA,CAAA7C,YAAA,IAAA6C,GAAA,CAAA7C,YAAA;UAAA,EAAsC;UAE5CZ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAA4D,UAAA,KAAAG,oCAAA,uBAAmE;UAGrE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,wBACmB;UACzCD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UACtDF,EADsD,CAAAG,YAAA,EAAI,EACpD;UAENH,EAAA,CAAAC,cAAA,kBAIoD;UAElDD,EADA,CAAA4D,UAAA,KAAAI,sCAAA,0BAA6C,KAAAC,+BAAA,kBACpB;UAC3BjE,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAI+B;UADvBD,EAAA,CAAA0D,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAE/BrC,EAAA,CAAAE,MAAA,oBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACJ;UAGLH,EADF,CAAAC,cAAA,eAAyB,SACpB;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAE/DF,EAF+D,CAAAG,YAAA,EAAI,EAAI,EAC/D,EACF,EAhEoB;;;;;UAWlBH,EAAA,CAAAmE,SAAA,IAAuB;UAAvBnE,EAAA,CAAAoE,UAAA,cAAAX,GAAA,CAAA1C,SAAA,CAAuB;UAIbf,EAAA,CAAAmE,SAAA,GAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAC,OAAA,GAAAZ,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAD,OAAA,CAAAE,QAAA,aAAqD;UAQ1DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA7C,YAAA,uBAA2C;UAMtCZ,EAAA,CAAAmE,SAAA,GAAkD;UAAlDnE,EAAA,CAAAwE,iBAAA,CAAAf,GAAA,CAAA7C,YAAA,mCAAkD;UAElDZ,EAAA,CAAAmE,SAAA,EAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAK,OAAA,GAAAhB,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAG,OAAA,CAAAF,QAAA,aAAqD;UAgB3DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA1C,SAAA,CAAA2D,OAAA,IAAAjB,GAAA,CAAA5C,SAAA,CAA2C;UACnCb,EAAA,CAAAmE,SAAA,EAAe;UAAfnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA5C,SAAA,CAAe;UACtBb,EAAA,CAAAmE,SAAA,EAAgB;UAAhBnE,EAAA,CAAAoE,UAAA,UAAAX,GAAA,CAAA5C,SAAA,CAAgB;UAOjBb,EAAA,CAAAmE,SAAA,EAAsB;UAAtBnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA5C,SAAA,CAAsB;;;qBDrChCzB,YAAY,EAAAuF,EAAA,CAAAC,IAAA,EACZvF,mBAAmB,EAAAoD,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,kBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EACnB3F,YAAY,EAAAsD,EAAA,CAAAsC,UAAA,EACZ3F,aAAa,EACbC,kBAAkB,EAAA2F,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClB9F,cAAc,EAAA+F,EAAA,CAAAC,QAAA,EACd/F,eAAe,EAAAgG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfjG,aAAa,EAAAkG,EAAA,CAAAC,OAAA,EACblG,wBAAwB,EAAAmG,GAAA,CAAAC,kBAAA,EACxBnG,iBAAiB,EACjBC,iBAAiB,EAAAmG,GAAA,CAAAC,WAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}