import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatChipsModule,
    MatProgressBarModule,
    MatTooltipModule,
    RouterModule
  ],
  template: `
    <div class="dashboard-container">
      <!-- Enhanced Header Section -->
      <div class="dashboard-header">
        <div class="header-content">
          <div class="header-text">
            <h1 class="dashboard-title">
              <mat-icon class="title-icon">dashboard</mat-icon>
              Security Dashboard
            </h1>
            <p class="dashboard-subtitle">Monitor your blockchain security posture in real-time</p>
          </div>
          <div class="header-stats">
            <div class="quick-stat">
              <span class="stat-value">{{ totalScans }}</span>
              <span class="stat-label">Total Scans</span>
            </div>
            <div class="quick-stat">
              <span class="stat-value">{{ activeScans }}</span>
              <span class="stat-label">Active</span>
            </div>
            <div class="quick-stat">
              <span class="stat-value">{{ criticalIssues }}</span>
              <span class="stat-label">Critical</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button mat-raised-button color="primary" routerLink="/scan" class="action-button primary">
            <mat-icon>security</mat-icon>
            Start New Scan
          </button>
          <button mat-stroked-button routerLink="/reports" class="action-button secondary">
            <mat-icon>assessment</mat-icon>
            View Reports
          </button>
        </div>
      </div>

      <!-- Enhanced Stats Grid -->
      <div class="stats-grid">
        <mat-card class="stat-card total-scans elevated">
          <div class="stat-card-header">
            <div class="stat-icon-wrapper success">
              <mat-icon class="stat-icon">security</mat-icon>
            </div>
            <div class="stat-trend">
              <mat-icon class="trend-icon positive">trending_up</mat-icon>
              <span class="trend-value">+12%</span>
            </div>
          </div>
          <div class="stat-card-content">
            <div class="stat-number">{{ stats.totalScans }}</div>
            <div class="stat-label">Total Scans</div>
            <div class="stat-description">Completed this month</div>
          </div>
          <div class="stat-card-footer">
            <button mat-button color="primary" routerLink="/scan">
              <mat-icon>add</mat-icon>
              New Scan
            </button>
          </div>
        </mat-card>

        <mat-card class="stat-card critical-issues elevated">
          <div class="stat-card-header">
            <div class="stat-icon-wrapper critical">
              <mat-icon class="stat-icon">error</mat-icon>
            </div>
            <div class="stat-trend">
              <mat-icon class="trend-icon negative">trending_up</mat-icon>
              <span class="trend-value">+3</span>
            </div>
          </div>
          <div class="stat-card-content">
            <div class="stat-number critical">{{ stats.criticalIssues }}</div>
            <div class="stat-label">Critical Issues</div>
            <div class="stat-description">Require immediate attention</div>
          </div>
          <div class="stat-card-footer">
            <button mat-button color="warn" routerLink="/reports">
              <mat-icon>priority_high</mat-icon>
              View Details
            </button>
          </div>
        </mat-card>

        <mat-card class="stat-card high-issues elevated">
          <div class="stat-card-header">
            <div class="stat-icon-wrapper warning">
              <mat-icon class="stat-icon">warning</mat-icon>
            </div>
            <div class="stat-trend">
              <mat-icon class="trend-icon neutral">trending_flat</mat-icon>
              <span class="trend-value">0</span>
            </div>
          </div>
          <div class="stat-card-content">
            <div class="stat-number warning">{{ stats.highIssues }}</div>
            <div class="stat-label">High Priority</div>
            <div class="stat-description">Should be addressed soon</div>
          </div>
          <div class="stat-card-footer">
            <button mat-button color="accent" routerLink="/reports">
              <mat-icon>visibility</mat-icon>
              Review
            </button>
          </div>
        </mat-card>

        <mat-card class="stat-card medium-issues elevated">
          <div class="stat-card-header">
            <div class="stat-icon-wrapper info">
              <mat-icon class="stat-icon">info</mat-icon>
            </div>
            <div class="stat-trend">
              <mat-icon class="trend-icon positive">trending_down</mat-icon>
              <span class="trend-value">-5</span>
            </div>
          </div>
          <div class="stat-card-content">
            <div class="stat-number info">{{ stats.mediumIssues }}</div>
            <div class="stat-label">Medium Priority</div>
            <div class="stat-description">Monitor and plan fixes</div>
          </div>
          <div class="stat-card-footer">
            <button mat-button routerLink="/checklist">
              <mat-icon>checklist</mat-icon>
              Checklist
            </button>
          </div>
        </mat-card>
      </div>

      <!-- Recent Scans -->
      <div class="content-section">
        <mat-card class="recent-scans-card">
          <mat-card-header class="section-header">
            <div class="section-title">
              <mat-icon>history</mat-icon>
              <mat-card-title>Recent Scans</mat-card-title>
            </div>
            <button mat-stroked-button routerLink="/scan" class="secondary-button">
              <mat-icon>add</mat-icon>
              New Scan
            </button>
          </mat-card-header>
          <mat-card-content>
            <div class="table-container" *ngIf="recentScans.length > 0; else noScans">
              <table mat-table [dataSource]="recentScans" class="scans-table">
                <ng-container matColumnDef="id">
                  <th mat-header-cell *matHeaderCellDef>Scan ID</th>
                  <td mat-cell *matCellDef="let scan">
                    <span class="scan-id">{{ scan.id.substring(0, 8) }}...</span>
                  </td>
                </ng-container>

                <ng-container matColumnDef="project">
                  <th mat-header-cell *matHeaderCellDef>Project</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="project-info">
                      <mat-icon class="project-icon">folder</mat-icon>
                      <span>{{ getProjectName(scan.project_path) }}</span>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="chains">
                  <th mat-header-cell *matHeaderCellDef>Chains</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="chains-container">
                      <mat-chip *ngFor="let chain of scan.chains" class="chain-chip">
                        {{ chain }}
                      </mat-chip>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let scan">
                    <mat-chip [class]="'status-chip status-' + scan.status">
                      <mat-icon class="status-icon">{{ getStatusIcon(scan.status) }}</mat-icon>
                      {{ scan.status | titlecase }}
                    </mat-chip>
                  </td>
                </ng-container>

                <ng-container matColumnDef="issues">
                  <th mat-header-cell *matHeaderCellDef>Issues</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="issues-count">
                      <span class="count">{{ scan.issues?.length || 0 }}</span>
                      <span class="label">issues</span>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="date">
                  <th mat-header-cell *matHeaderCellDef>Date</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="date-info">
                      <span class="date">{{ formatDate(scan.created_at) }}</span>
                      <span class="time">{{ formatTime(scan.created_at) }}</span>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="action-buttons">
                      <button mat-icon-button [routerLink]="['/scan', scan.id]" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                      <button mat-icon-button matTooltip="Download Report">
                        <mat-icon>download</mat-icon>
                      </button>
                    </div>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="scan-row"></tr>
              </table>
            </div>

            <ng-template #noScans>
              <div class="empty-state">
                <mat-icon class="empty-icon">security</mat-icon>
                <h3>No scans yet</h3>
                <p>Start your first security scan to see results here</p>
                <button mat-raised-button color="primary" routerLink="/scan">
                  <mat-icon>add</mat-icon>
                  Start First Scan
                </button>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Top Issues -->
      <mat-card class="top-issues-card">
        <mat-card-header>
          <mat-card-title>Top Security Issues</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="issues-list">
            <div *ngFor="let issue of topIssues" class="issue-item">
              <div class="issue-severity">
                <mat-chip [style.background-color]="getSeverityColor(issue.severity)">
                  {{ issue.severity }}
                </mat-chip>
              </div>
              <div class="issue-details">
                <h4>{{ issue.title }}</h4>
                <p>{{ issue.description }}</p>
                <small>{{ issue.file }}:{{ issue.line }}</small>
              </div>
              <div class="issue-chain">
                <mat-chip>{{ issue.chain }}</mat-chip>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Quick Actions -->
      <mat-card class="quick-actions-card">
        <mat-card-header>
          <mat-card-title>Quick Actions</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="actions-grid">
            <button mat-raised-button color="primary" routerLink="/scan">
              <mat-icon>security</mat-icon>
              Start New Scan
            </button>
            <button mat-raised-button color="accent" routerLink="/checklist">
              <mat-icon>checklist</mat-icon>
              Security Checklist
            </button>
            <button mat-raised-button routerLink="/reports">
              <mat-icon>assessment</mat-icon>
              Generate Report
            </button>
            <button mat-raised-button routerLink="/settings">
              <mat-icon>settings</mat-icon>
              Settings
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 0;
      max-width: 100%;
      margin: 0;
      min-height: 100%;
    }

    .dashboard-header {
      background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);
      color: white;
      padding: var(--spt-space-12) var(--spt-space-8);
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spt-space-8);
      border-radius: 0 0 var(--spt-radius-3xl) var(--spt-radius-3xl);
      position: relative;
      overflow: hidden;
    }

    .dashboard-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .header-content {
      display: flex;
      gap: var(--spt-space-12);
      align-items: flex-start;
      position: relative;
      z-index: 1;
    }

    .header-text {
      flex: 1;
    }

    .dashboard-title {
      margin: 0 0 var(--spt-space-2) 0;
      font-size: var(--spt-text-4xl);
      font-weight: var(--spt-font-bold);
      display: flex;
      align-items: center;
      gap: var(--spt-space-3);
      line-height: var(--spt-leading-tight);
    }

    .title-icon {
      font-size: 40px;
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: var(--spt-radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .dashboard-subtitle {
      margin: 0;
      opacity: 0.9;
      font-size: var(--spt-text-lg);
      font-weight: var(--spt-font-normal);
      line-height: var(--spt-leading-relaxed);
    }

    .header-stats {
      display: flex;
      gap: var(--spt-space-6);
      margin-top: var(--spt-space-2);
    }

    .quick-stat {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: var(--spt-space-3) var(--spt-space-4);
      background: rgba(255, 255, 255, 0.15);
      border-radius: var(--spt-radius-xl);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-value {
      font-size: var(--spt-text-2xl);
      font-weight: var(--spt-font-bold);
      line-height: 1;
    }

    .stat-label {
      font-size: var(--spt-text-xs);
      opacity: 0.8;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-top: var(--spt-space-1);
    }

    .header-actions {
      display: flex;
      gap: var(--spt-space-3);
      position: relative;
      z-index: 1;
    }

    .action-button {
      height: 48px;
      padding: 0 var(--spt-space-6);
      border-radius: var(--spt-radius-xl);
      font-weight: var(--spt-font-semibold);
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .action-button.primary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .action-button.primary:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: var(--spt-shadow-lg);
    }

    .action-button.secondary {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }

    .action-button.secondary:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-1px);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: var(--spt-space-6);
      margin: 0 var(--spt-space-8) var(--spt-space-12) var(--spt-space-8);
    }

    .stat-card {
      border-radius: var(--spt-radius-2xl);
      border: 1px solid var(--spt-border);
      background: var(--spt-surface);
      box-shadow: var(--spt-shadow-sm);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      position: relative;
    }

    .stat-card.elevated {
      box-shadow: var(--spt-shadow-lg);
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--spt-shadow-xl);
      border-color: var(--spt-primary-200);
    }

    .stat-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spt-space-5) var(--spt-space-6) var(--spt-space-3) var(--spt-space-6);
    }

    .stat-card-content {
      padding: 0 var(--spt-space-6) var(--spt-space-4) var(--spt-space-6);
    }

    .stat-card-footer {
      padding: var(--spt-space-3) var(--spt-space-6) var(--spt-space-5) var(--spt-space-6);
      border-top: 1px solid var(--spt-border-light);
      background: var(--spt-bg-secondary);
    }

    .stat-icon-wrapper {
      width: 56px;
      height: 56px;
      border-radius: var(--spt-radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: all 0.3s ease;
    }

    .stat-icon-wrapper.success {
      background: linear-gradient(135deg, var(--spt-success-100) 0%, var(--spt-success-200) 100%);
      border: 1px solid var(--spt-success-300);
    }

    .stat-icon-wrapper.critical {
      background: linear-gradient(135deg, var(--spt-error-100) 0%, var(--spt-error-200) 100%);
      border: 1px solid var(--spt-error-300);
    }

    .stat-icon-wrapper.warning {
      background: linear-gradient(135deg, var(--spt-warning-100) 0%, var(--spt-warning-200) 100%);
      border: 1px solid var(--spt-warning-300);
    }

    .stat-icon-wrapper.info {
      background: linear-gradient(135deg, var(--spt-info-100) 0%, var(--spt-info-200) 100%);
      border: 1px solid var(--spt-info-300);
    }

    .stat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      transition: transform 0.3s ease;
    }

    .stat-card:hover .stat-icon {
      transform: scale(1.1);
    }

    .stat-icon-wrapper.success .stat-icon { color: var(--spt-success-700); }
    .stat-icon-wrapper.critical .stat-icon { color: var(--spt-error-700); }
    .stat-icon-wrapper.warning .stat-icon { color: var(--spt-warning-700); }
    .stat-icon-wrapper.info .stat-icon { color: var(--spt-info-700); }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: var(--spt-space-1);
      padding: var(--spt-space-1) var(--spt-space-2);
      border-radius: var(--spt-radius-lg);
      font-size: var(--spt-text-xs);
      font-weight: var(--spt-font-semibold);
    }

    .trend-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .trend-icon.positive { color: var(--spt-success-600); }
    .trend-icon.negative { color: var(--spt-error-600); }
    .trend-icon.neutral { color: var(--spt-text-tertiary); }

    .trend-value {
      font-weight: var(--spt-font-bold);
    }

    .stat-number {
      font-size: var(--spt-text-4xl);
      font-weight: var(--spt-font-bold);
      color: var(--spt-text-primary);
      margin-bottom: var(--spt-space-1);
      line-height: var(--spt-leading-none);
      transition: color 0.3s ease;
    }

    .stat-number.critical { color: var(--spt-error-600); }
    .stat-number.warning { color: var(--spt-warning-600); }
    .stat-number.info { color: var(--spt-info-600); }

    .stat-label {
      font-size: var(--spt-text-base);
      color: var(--spt-text-primary);
      font-weight: var(--spt-font-semibold);
      margin-bottom: var(--spt-space-1);
      line-height: var(--spt-leading-tight);
    }

    .stat-description {
      font-size: var(--spt-text-sm);
      color: var(--spt-text-secondary);
      font-weight: var(--spt-font-normal);
      line-height: var(--spt-leading-relaxed);
    }

    .stat-card-footer button {
      width: 100%;
      justify-content: flex-start;
      gap: var(--spt-space-2);
      padding: var(--spt-space-2) var(--spt-space-3);
      border-radius: var(--spt-radius-lg);
      font-weight: var(--spt-font-medium);
      transition: all 0.2s ease;
    }

    .stat-card-footer button:hover {
      transform: translateX(4px);
    }

    .stat-card-footer button mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .content-section {
      margin: 0 32px 40px 32px;
    }

    .recent-scans-card,
    .top-issues-card,
    .quick-actions-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 24px 16px 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .section-title mat-icon {
      color: #667eea;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .section-title mat-card-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }

    .secondary-button {
      border: 2px solid #667eea;
      color: #667eea;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .secondary-button:hover {
      background: #667eea;
      color: white;
    }

    .table-container {
      overflow-x: auto;
      margin: 0 -24px;
    }

    .scans-table {
      width: 100%;
      background: transparent;
    }

    .scans-table th {
      background: #f8fafc;
      color: #64748b;
      font-weight: 600;
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 16px 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .scans-table td {
      padding: 16px 24px;
      border-bottom: 1px solid #f1f5f9;
    }

    .scan-row {
      transition: all 0.2s ease;
    }

    .scan-row:hover {
      background: #f8fafc;
    }

    .scan-id {
      font-family: 'Courier New', monospace;
      background: #f1f5f9;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 12px;
      color: #475569;
    }

    .project-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .project-icon {
      color: #64748b;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .chains-container {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
    }

    .chain-chip {
      background: #e0e7ff;
      color: #3730a3;
      font-size: 11px;
      font-weight: 500;
      height: 24px;
      border-radius: 6px;
    }

    .status-chip {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      font-weight: 500;
      height: 28px;
      border-radius: 8px;
      padding: 0 12px;
    }

    .status-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .status-completed {
      background: #dcfce7;
      color: #166534;
    }

    .status-running {
      background: #dbeafe;
      color: #1d4ed8;
    }

    .status-failed {
      background: #fee2e2;
      color: #dc2626;
    }

    .status-pending {
      background: #fef3c7;
      color: #d97706;
    }

    .issues-count {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .issues-count .count {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }

    .issues-count .label {
      font-size: 11px;
      color: #64748b;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .date-info {
      display: flex;
      flex-direction: column;
    }

    .date-info .date {
      font-size: 14px;
      color: #1e293b;
      font-weight: 500;
    }

    .date-info .time {
      font-size: 12px;
      color: #64748b;
    }

    .action-buttons {
      display: flex;
      gap: 4px;
    }

    .action-buttons button {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      color: #64748b;
      transition: all 0.2s ease;
    }

    .action-buttons button:hover {
      background: #f1f5f9;
      color: #667eea;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #64748b;
    }

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #cbd5e1;
      margin-bottom: 16px;
    }

    .empty-state h3 {
      margin: 0 0 8px 0;
      color: #475569;
      font-size: 20px;
      font-weight: 600;
    }

    .empty-state p {
      margin: 0 0 24px 0;
      font-size: 14px;
    }

    .issues-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .issue-item {
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .issue-details {
      flex: 1;
    }

    .issue-details h4 {
      margin: 0 0 5px 0;
      color: #333;
    }

    .issue-details p {
      margin: 0 0 5px 0;
      color: #666;
      font-size: 14px;
    }

    .issue-details small {
      color: #999;
      font-size: 12px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }

    .actions-grid button {
      height: 60px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .actions-grid mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  `]
})
export class DashboardComponent implements OnInit {
  stats = {
    totalScans: 0,
    criticalIssues: 0,
    highIssues: 0,
    mediumIssues: 0
  };

  recentScans: ScanResult[] = [];
  topIssues: SecurityIssue[] = [];
  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];

  // Additional properties for enhanced header
  get totalScans(): number { return this.stats.totalScans; }
  get activeScans(): number { return this.recentScans.filter(scan => scan.status === 'running').length; }
  get criticalIssues(): number { return this.stats.criticalIssues; }

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    // Load recent scans
    this.apiService.getScanHistory().subscribe({
      next: (response) => {
        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent
        this.calculateStats(response.scans);
      },
      error: (error) => {
        console.error('Error loading scan history:', error);
        // Use mock data for development
        this.loadMockData();
      }
    });
  }

  loadMockData(): void {
    // Generate mock data for development
    const mockScan = this.apiService.generateMockScanResult();
    this.recentScans = [mockScan];
    this.topIssues = mockScan.issues;
    this.stats = {
      totalScans: 1,
      criticalIssues: mockScan.severity_counts['critical'] || 0,
      highIssues: mockScan.severity_counts['high'] || 0,
      mediumIssues: mockScan.severity_counts['medium'] || 0
    };
  }

  calculateStats(scans: ScanResult[]): void {
    this.stats.totalScans = scans.length;
    
    let criticalTotal = 0;
    let highTotal = 0;
    let mediumTotal = 0;
    let allIssues: SecurityIssue[] = [];

    scans.forEach(scan => {
      criticalTotal += scan.severity_counts?.['critical'] || 0;
      highTotal += scan.severity_counts?.['high'] || 0;
      mediumTotal += scan.severity_counts?.['medium'] || 0;
      allIssues = allIssues.concat(scan.issues || []);
    });

    this.stats.criticalIssues = criticalTotal;
    this.stats.highIssues = highTotal;
    this.stats.mediumIssues = mediumTotal;

    // Get top 5 most severe issues
    this.topIssues = allIssues
      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))
      .slice(0, 5);
  }

  getSeverityWeight(severity: string): number {
    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };
    return weights[severity as keyof typeof weights] || 0;
  }

  getSeverityColor(severity: string): string {
    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';
  }

  getProjectName(path: string): string {
    return path.split('/').pop() || path;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  formatTime(dateString: string): string {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getStatusIcon(status: string): string {
    const icons = {
      completed: 'check_circle',
      running: 'hourglass_empty',
      failed: 'error',
      pending: 'schedule'
    };
    return icons[status as keyof typeof icons] || 'help';
  }
}
