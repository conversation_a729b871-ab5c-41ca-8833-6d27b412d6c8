{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'http://localhost:8080/api/v1/auth';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.tokenSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.token$ = this.tokenSubject.asObservable();\n    // Check for existing token on service initialization\n    const token = localStorage.getItem('auth_token');\n    const user = localStorage.getItem('current_user');\n    if (token && user) {\n      this.tokenSubject.next(token);\n      this.currentUserSubject.next(JSON.parse(user));\n    }\n  }\n  // Authentication methods\n  login(credentials) {\n    return this.http.post(`${this.baseUrl}/login`, credentials).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(this.handleError));\n  }\n  register(userData) {\n    return this.http.post(`${this.baseUrl}/register`, userData).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(this.handleError));\n  }\n  logout() {\n    // Call logout endpoint to invalidate token on server\n    this.http.post(`${this.baseUrl}/logout`, {}).subscribe({\n      complete: () => {\n        this.clearAuthData();\n      },\n      error: () => {\n        // Clear local data even if server call fails\n        this.clearAuthData();\n      }\n    });\n  }\n  refreshToken() {\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n    return this.http.post(`${this.baseUrl}/refresh`, {\n      refresh_token: refreshToken\n    }).pipe(map(response => {\n      this.setAuthData(response);\n      return response;\n    }), catchError(error => {\n      this.clearAuthData();\n      return throwError(() => error);\n    }));\n  }\n  // Utility methods\n  isAuthenticated() {\n    const token = this.getToken();\n    if (!token) return false;\n    // Check if token is expired\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp > currentTime;\n    } catch {\n      return false;\n    }\n  }\n  getToken() {\n    return localStorage.getItem('auth_token');\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  hasRole(role) {\n    const user = this.getCurrentUser();\n    return user ? user.role === role : false;\n  }\n  hasAnyRole(roles) {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n  // HTTP headers with authentication\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n  // Private methods\n  setAuthData(response) {\n    localStorage.setItem('auth_token', response.token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    this.tokenSubject.next(response.token);\n    this.currentUserSubject.next(response.user);\n    // Set token expiration timer\n    if (response.expires_in) {\n      setTimeout(() => {\n        this.refreshToken().subscribe({\n          error: () => this.logout()\n        });\n      }, (response.expires_in - 300) * 1000); // Refresh 5 minutes before expiry\n    }\n  }\n  clearAuthData() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('current_user');\n    localStorage.removeItem('refresh_token');\n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  // Mock authentication for development\n  mockLogin(username = 'admin', role = 'admin') {\n    const mockUser = {\n      id: '1',\n      username: username,\n      email: `${username}@spt.local`,\n      role: role,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n    const mockToken = this.generateMockToken(mockUser);\n    const mockResponse = {\n      token: mockToken,\n      user: mockUser,\n      expires_in: 3600\n    };\n    this.setAuthData(mockResponse);\n  }\n  generateMockToken(user) {\n    const header = btoa(JSON.stringify({\n      alg: 'HS256',\n      typ: 'JWT'\n    }));\n    const payload = btoa(JSON.stringify({\n      sub: user.id,\n      username: user.username,\n      role: user.role,\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + 3600\n    }));\n    const signature = btoa('mock-signature');\n    return `${header}.${payload}.${signature}`;\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "map", "catchError", "AuthService", "constructor", "http", "router", "baseUrl", "currentUserSubject", "tokenSubject", "currentUser$", "asObservable", "token$", "token", "localStorage", "getItem", "user", "next", "JSON", "parse", "login", "credentials", "post", "pipe", "response", "setAuthData", "handleError", "register", "userData", "logout", "subscribe", "complete", "clearAuthData", "error", "refreshToken", "Error", "refresh_token", "isAuthenticated", "getToken", "payload", "atob", "split", "currentTime", "Math", "floor", "Date", "now", "exp", "getCurrentUser", "value", "hasRole", "role", "hasAnyRole", "roles", "includes", "getAuthHeaders", "setItem", "stringify", "expires_in", "setTimeout", "removeItem", "navigate", "errorMessage", "message", "console", "mockLogin", "username", "mockUser", "id", "email", "created_at", "toISOString", "last_login", "mockToken", "generateMockToken", "mockResponse", "header", "btoa", "alg", "typ", "sub", "iat", "signature", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { Router } from '@angular/router';\n\nexport interface User {\n  id: string;\n  username: string;\n  email: string;\n  role: string;\n  created_at: string;\n  last_login?: string;\n}\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  fullName?: string;\n}\n\nexport interface AuthResponse {\n  token: string;\n  user: User;\n  expires_in: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly baseUrl = 'http://localhost:8080/api/v1/auth';\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private tokenSubject = new BehaviorSubject<string | null>(null);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public token$ = this.tokenSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    // Check for existing token on service initialization\n    const token = localStorage.getItem('auth_token');\n    const user = localStorage.getItem('current_user');\n    \n    if (token && user) {\n      this.tokenSubject.next(token);\n      this.currentUserSubject.next(JSON.parse(user));\n    }\n  }\n\n  // Authentication methods\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.baseUrl}/login`, credentials)\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.baseUrl}/register`, userData)\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  logout(): void {\n    // Call logout endpoint to invalidate token on server\n    this.http.post(`${this.baseUrl}/logout`, {}).subscribe({\n      complete: () => {\n        this.clearAuthData();\n      },\n      error: () => {\n        // Clear local data even if server call fails\n        this.clearAuthData();\n      }\n    });\n  }\n\n  refreshToken(): Observable<AuthResponse> {\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n\n    return this.http.post<AuthResponse>(`${this.baseUrl}/refresh`, { refresh_token: refreshToken })\n      .pipe(\n        map(response => {\n          this.setAuthData(response);\n          return response;\n        }),\n        catchError(error => {\n          this.clearAuthData();\n          return throwError(() => error);\n        })\n      );\n  }\n\n  // Utility methods\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    if (!token) return false;\n\n    // Check if token is expired\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp > currentTime;\n    } catch {\n      return false;\n    }\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  hasRole(role: string): boolean {\n    const user = this.getCurrentUser();\n    return user ? user.role === role : false;\n  }\n\n  hasAnyRole(roles: string[]): boolean {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n\n  // HTTP headers with authentication\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n\n  // Private methods\n  private setAuthData(response: AuthResponse): void {\n    localStorage.setItem('auth_token', response.token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    \n    this.tokenSubject.next(response.token);\n    this.currentUserSubject.next(response.user);\n\n    // Set token expiration timer\n    if (response.expires_in) {\n      setTimeout(() => {\n        this.refreshToken().subscribe({\n          error: () => this.logout()\n        });\n      }, (response.expires_in - 300) * 1000); // Refresh 5 minutes before expiry\n    }\n  }\n\n  private clearAuthData(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('current_user');\n    localStorage.removeItem('refresh_token');\n    \n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    \n    this.router.navigate(['/login']);\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An error occurred';\n    \n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    \n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n\n  // Mock authentication for development\n  mockLogin(username: string = 'admin', role: string = 'admin'): void {\n    const mockUser: User = {\n      id: '1',\n      username: username,\n      email: `${username}@spt.local`,\n      role: role,\n      created_at: new Date().toISOString(),\n      last_login: new Date().toISOString()\n    };\n\n    const mockToken = this.generateMockToken(mockUser);\n    \n    const mockResponse: AuthResponse = {\n      token: mockToken,\n      user: mockUser,\n      expires_in: 3600\n    };\n\n    this.setAuthData(mockResponse);\n  }\n\n  private generateMockToken(user: User): string {\n    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));\n    const payload = btoa(JSON.stringify({\n      sub: user.id,\n      username: user.username,\n      role: user.role,\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + 3600\n    }));\n    const signature = btoa('mock-signature');\n    \n    return `${header}.${payload}.${signature}`;\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;;AAkChD,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAG,mCAAmC;IACtD,KAAAC,kBAAkB,GAAG,IAAIT,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAU,YAAY,GAAG,IAAIV,eAAe,CAAgB,IAAI,CAAC;IAExD,KAAAW,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,MAAM,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAM9C;IACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAEjD,IAAIF,KAAK,IAAIG,IAAI,EAAE;MACjB,IAAI,CAACP,YAAY,CAACQ,IAAI,CAACJ,KAAK,CAAC;MAC7B,IAAI,CAACL,kBAAkB,CAACS,IAAI,CAACC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAAC;IAChD;EACF;EAEA;EACAI,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAAChB,IAAI,CAACiB,IAAI,CAAe,GAAG,IAAI,CAACf,OAAO,QAAQ,EAAEc,WAAW,CAAC,CACtEE,IAAI,CACHtB,GAAG,CAACuB,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtB,UAAU,CAAC,IAAI,CAACwB,WAAW,CAAC,CAC7B;EACL;EAEAC,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAACvB,IAAI,CAACiB,IAAI,CAAe,GAAG,IAAI,CAACf,OAAO,WAAW,EAAEqB,QAAQ,CAAC,CACtEL,IAAI,CACHtB,GAAG,CAACuB,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtB,UAAU,CAAC,IAAI,CAACwB,WAAW,CAAC,CAC7B;EACL;EAEAG,MAAMA,CAAA;IACJ;IACA,IAAI,CAACxB,IAAI,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACf,OAAO,SAAS,EAAE,EAAE,CAAC,CAACuB,SAAS,CAAC;MACrDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACD,aAAa,EAAE;MACtB;KACD,CAAC;EACJ;EAEAE,YAAYA,CAAA;IACV,MAAMA,YAAY,GAAGpB,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC1D,IAAI,CAACmB,YAAY,EAAE;MACjB,OAAOlC,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE;IAEA,OAAO,IAAI,CAAC9B,IAAI,CAACiB,IAAI,CAAe,GAAG,IAAI,CAACf,OAAO,UAAU,EAAE;MAAE6B,aAAa,EAAEF;IAAY,CAAE,CAAC,CAC5FX,IAAI,CACHtB,GAAG,CAACuB,QAAQ,IAAG;MACb,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtB,UAAU,CAAC+B,KAAK,IAAG;MACjB,IAAI,CAACD,aAAa,EAAE;MACpB,OAAOhC,UAAU,CAAC,MAAMiC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;EACAI,eAAeA,CAAA;IACb,MAAMxB,KAAK,GAAG,IAAI,CAACyB,QAAQ,EAAE;IAC7B,IAAI,CAACzB,KAAK,EAAE,OAAO,KAAK;IAExB;IACA,IAAI;MACF,MAAM0B,OAAO,GAAGrB,IAAI,CAACC,KAAK,CAACqB,IAAI,CAAC3B,KAAK,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MACjD,OAAOP,OAAO,CAACQ,GAAG,GAAGL,WAAW;IAClC,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF;EAEAJ,QAAQA,CAAA;IACN,OAAOxB,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAC3C;EAEAiC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACxC,kBAAkB,CAACyC,KAAK;EACtC;EAEAC,OAAOA,CAACC,IAAY;IAClB,MAAMnC,IAAI,GAAG,IAAI,CAACgC,cAAc,EAAE;IAClC,OAAOhC,IAAI,GAAGA,IAAI,CAACmC,IAAI,KAAKA,IAAI,GAAG,KAAK;EAC1C;EAEAC,UAAUA,CAACC,KAAe;IACxB,MAAMrC,IAAI,GAAG,IAAI,CAACgC,cAAc,EAAE;IAClC,OAAOhC,IAAI,GAAGqC,KAAK,CAACC,QAAQ,CAACtC,IAAI,CAACmC,IAAI,CAAC,GAAG,KAAK;EACjD;EAEA;EACAI,cAAcA,CAAA;IACZ,MAAM1C,KAAK,GAAG,IAAI,CAACyB,QAAQ,EAAE;IAC7B,OAAO,IAAIxC,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAEe,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;KAC9C,CAAC;EACJ;EAEA;EACQY,WAAWA,CAACD,QAAsB;IACxCV,YAAY,CAAC0C,OAAO,CAAC,YAAY,EAAEhC,QAAQ,CAACX,KAAK,CAAC;IAClDC,YAAY,CAAC0C,OAAO,CAAC,cAAc,EAAEtC,IAAI,CAACuC,SAAS,CAACjC,QAAQ,CAACR,IAAI,CAAC,CAAC;IAEnE,IAAI,CAACP,YAAY,CAACQ,IAAI,CAACO,QAAQ,CAACX,KAAK,CAAC;IACtC,IAAI,CAACL,kBAAkB,CAACS,IAAI,CAACO,QAAQ,CAACR,IAAI,CAAC;IAE3C;IACA,IAAIQ,QAAQ,CAACkC,UAAU,EAAE;MACvBC,UAAU,CAAC,MAAK;QACd,IAAI,CAACzB,YAAY,EAAE,CAACJ,SAAS,CAAC;UAC5BG,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACJ,MAAM;SACzB,CAAC;MACJ,CAAC,EAAE,CAACL,QAAQ,CAACkC,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IAC1C;EACF;EAEQ1B,aAAaA,CAAA;IACnBlB,YAAY,CAAC8C,UAAU,CAAC,YAAY,CAAC;IACrC9C,YAAY,CAAC8C,UAAU,CAAC,cAAc,CAAC;IACvC9C,YAAY,CAAC8C,UAAU,CAAC,eAAe,CAAC;IAExC,IAAI,CAACnD,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACT,kBAAkB,CAACS,IAAI,CAAC,IAAI,CAAC;IAElC,IAAI,CAACX,MAAM,CAACuD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEQnC,WAAWA,CAACO,KAAU;IAC5B,IAAI6B,YAAY,GAAG,mBAAmB;IAEtC,IAAI7B,KAAK,CAACA,KAAK,EAAE8B,OAAO,EAAE;MACxBD,YAAY,GAAG7B,KAAK,CAACA,KAAK,CAAC8B,OAAO;IACpC,CAAC,MAAM,IAAI9B,KAAK,CAAC8B,OAAO,EAAE;MACxBD,YAAY,GAAG7B,KAAK,CAAC8B,OAAO;IAC9B;IAEAC,OAAO,CAAC/B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,OAAOjC,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC2B,YAAY,CAAC,CAAC;EAClD;EAEA;EACAG,SAASA,CAACC,QAAA,GAAmB,OAAO,EAAEf,IAAA,GAAe,OAAO;IAC1D,MAAMgB,QAAQ,GAAS;MACrBC,EAAE,EAAE,GAAG;MACPF,QAAQ,EAAEA,QAAQ;MAClBG,KAAK,EAAE,GAAGH,QAAQ,YAAY;MAC9Bf,IAAI,EAAEA,IAAI;MACVmB,UAAU,EAAE,IAAIzB,IAAI,EAAE,CAAC0B,WAAW,EAAE;MACpCC,UAAU,EAAE,IAAI3B,IAAI,EAAE,CAAC0B,WAAW;KACnC;IAED,MAAME,SAAS,GAAG,IAAI,CAACC,iBAAiB,CAACP,QAAQ,CAAC;IAElD,MAAMQ,YAAY,GAAiB;MACjC9D,KAAK,EAAE4D,SAAS;MAChBzD,IAAI,EAAEmD,QAAQ;MACdT,UAAU,EAAE;KACb;IAED,IAAI,CAACjC,WAAW,CAACkD,YAAY,CAAC;EAChC;EAEQD,iBAAiBA,CAAC1D,IAAU;IAClC,MAAM4D,MAAM,GAAGC,IAAI,CAAC3D,IAAI,CAACuC,SAAS,CAAC;MAAEqB,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAK,CAAE,CAAC,CAAC;IACjE,MAAMxC,OAAO,GAAGsC,IAAI,CAAC3D,IAAI,CAACuC,SAAS,CAAC;MAClCuB,GAAG,EAAEhE,IAAI,CAACoD,EAAE;MACZF,QAAQ,EAAElD,IAAI,CAACkD,QAAQ;MACvBf,IAAI,EAAEnC,IAAI,CAACmC,IAAI;MACf8B,GAAG,EAAEtC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MAClCC,GAAG,EAAEJ,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG;KACtC,CAAC,CAAC;IACH,MAAMoC,SAAS,GAAGL,IAAI,CAAC,gBAAgB,CAAC;IAExC,OAAO,GAAGD,MAAM,IAAIrC,OAAO,IAAI2C,SAAS,EAAE;EAC5C;;;uCAnMW/E,WAAW,EAAAgF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXrF,WAAW;MAAAsF,OAAA,EAAXtF,WAAW,CAAAuF,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}