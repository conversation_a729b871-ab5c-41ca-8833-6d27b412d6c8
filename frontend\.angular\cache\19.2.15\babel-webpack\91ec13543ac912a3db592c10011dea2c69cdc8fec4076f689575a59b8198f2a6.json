{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ProjectDialogComponent } from './project-dialog/project-dialog.component';\nimport { ConfirmDialogComponent } from '../../shared/confirm-dialog/confirm-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/chips\";\nfunction ProjectsComponent_th_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"div\", 23)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r1.description);\n  }\n}\nfunction ProjectsComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"type-\" + project_r2.blockchain_type);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, project_r2.blockchain_type), \" \");\n  }\n}\nfunction ProjectsComponent_th_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + project_r3.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, project_r3.status), \" \");\n  }\n}\nfunction ProjectsComponent_th_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Scans\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(project_r4.scan_count);\n  }\n}\nfunction ProjectsComponent_th_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const project_r5 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r5.getIssueClass(project_r5.issue_count));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r5.issue_count, \" \");\n  }\n}\nfunction ProjectsComponent_th_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Last Scan\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", project_r7.last_scan ? i0.ɵɵpipeBind2(2, 1, project_r7.last_scan, \"short\") : \"Never\", \" \");\n  }\n}\nfunction ProjectsComponent_th_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectsComponent_td_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 22)(1, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_1_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.scanProject(project_r9));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_4_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.viewProject(project_r9));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_7_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.editProject(project_r9));\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_td_60_Template_button_click_10_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.deleteProject(project_r9));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectsComponent_tr_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 28);\n  }\n}\nfunction ProjectsComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 29);\n  }\n}\nexport let ProjectsComponent = /*#__PURE__*/(() => {\n  class ProjectsComponent {\n    constructor(dialog, snackBar, router) {\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n      this.router = router;\n      this.projects = [];\n      this.displayedColumns = ['name', 'type', 'status', 'scans', 'issues', 'lastScan', 'actions'];\n      this.isLoading = false;\n    }\n    ngOnInit() {\n      this.loadProjects();\n    }\n    loadProjects() {\n      this.isLoading = true;\n      // Mock data for development\n      setTimeout(() => {\n        this.projects = this.generateMockProjects();\n        this.isLoading = false;\n      }, 1000);\n    }\n    getActiveProjects() {\n      return this.projects.filter(p => p.status === 'active').length;\n    }\n    getTotalScans() {\n      return this.projects.reduce((total, p) => total + (p.scan_count || 0), 0);\n    }\n    getTotalIssues() {\n      // Since issue_count is not in the Project model, we'll return 0 for now\n      return 0;\n    }\n    getIssueClass(count) {\n      if (count === 0) return 'issue-none';\n      if (count <= 5) return 'issue-low';\n      if (count <= 15) return 'issue-medium';\n      return 'issue-high';\n    }\n    openCreateDialog() {\n      const dialogRef = this.dialog.open(ProjectDialogComponent, {\n        width: '600px',\n        data: {\n          mode: 'create'\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result) {\n          // Add the new project to the list\n          this.projects.unshift(result);\n          this.snackBar.open('Project created successfully!', 'Close', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    scanProject(project) {\n      this.router.navigate(['/scan'], {\n        queryParams: {\n          project: project.id\n        }\n      });\n    }\n    viewProject(project) {\n      this.router.navigate(['/projects', project.id]);\n    }\n    editProject(project) {\n      const dialogRef = this.dialog.open(ProjectDialogComponent, {\n        width: '600px',\n        data: {\n          mode: 'edit',\n          project: project\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result) {\n          // Update the project in the list\n          const index = this.projects.findIndex(p => p.id === project.id);\n          if (index !== -1) {\n            this.projects[index] = result;\n            this.snackBar.open('Project updated successfully!', 'Close', {\n              duration: 3000\n            });\n          }\n        }\n      });\n    }\n    deleteProject(project) {\n      const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n        width: '450px',\n        data: {\n          title: 'Delete Project',\n          message: `Are you sure you want to delete \"${project.name}\"? This action cannot be undone.`,\n          confirmText: 'Delete',\n          cancelText: 'Cancel',\n          type: 'danger'\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result) {\n          // Remove the project from the list\n          const index = this.projects.findIndex(p => p.id === project.id);\n          if (index !== -1) {\n            this.projects.splice(index, 1);\n            this.snackBar.open('Project deleted successfully!', 'Close', {\n              duration: 3000\n            });\n          }\n        }\n      });\n    }\n    generateMockProjects() {\n      return [{\n        id: '1',\n        name: 'DeFi Protocol',\n        description: 'Decentralized finance smart contracts',\n        blockchain_type: 'ethereum',\n        status: 'active',\n        repository_url: 'https://github.com/example/defi-protocol',\n        created_at: new Date('2024-01-15T10:00:00Z'),\n        updated_at: new Date('2024-07-20T15:30:00Z'),\n        scan_count: 15,\n        last_scan_date: new Date('2024-07-28T09:15:00Z')\n      }, {\n        id: '2',\n        name: 'Bitcoin Wallet',\n        description: 'Multi-signature Bitcoin wallet implementation',\n        blockchain_type: 'bitcoin',\n        status: 'active',\n        repository_url: 'https://github.com/example/bitcoin-wallet',\n        created_at: new Date('2024-02-01T14:00:00Z'),\n        updated_at: new Date('2024-07-25T11:20:00Z'),\n        scan_count: 8,\n        last_scan_date: new Date('2024-07-27T16:45:00Z')\n      }, {\n        id: '3',\n        name: 'Cross-Chain Bridge',\n        description: 'Multi-chain asset bridge protocol',\n        blockchain_type: 'ethereum',\n        status: 'inactive',\n        repository_url: 'https://github.com/example/cross-chain-bridge',\n        created_at: new Date('2024-03-10T09:30:00Z'),\n        updated_at: new Date('2024-06-15T13:45:00Z'),\n        scan_count: 22\n      }];\n    }\n    static {\n      this.ɵfac = function ProjectsComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ProjectsComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.MatSnackBar), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectsComponent,\n        selectors: [[\"app-projects\"]],\n        decls: 63,\n        vars: 7,\n        consts: [[1, \"projects-container\"], [1, \"header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"projects-table-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"projects-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"scans\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"lastScan\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"project-name\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Start Scan\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Details\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Edit Project\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete Project\", \"color\", \"warn\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function ProjectsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"\\uD83D\\uDDC2\\uFE0F Project Management\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function ProjectsComponent_Template_button_click_4_listener() {\n              return ctx.openCreateDialog();\n            });\n            i0.ɵɵelementStart(5, \"mat-icon\");\n            i0.ɵɵtext(6, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(7, \" New Project \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-content\")(11, \"div\", 5);\n            i0.ɵɵtext(12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 6);\n            i0.ɵɵtext(14, \"Total Projects\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"mat-card\", 4)(16, \"mat-card-content\")(17, \"div\", 5);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 6);\n            i0.ɵɵtext(20, \"Active Projects\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(21, \"mat-card\", 4)(22, \"mat-card-content\")(23, \"div\", 5);\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 6);\n            i0.ɵɵtext(26, \"Total Scans\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"mat-card\", 4)(28, \"mat-card-content\")(29, \"div\", 5);\n            i0.ɵɵtext(30);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"div\", 6);\n            i0.ɵɵtext(32, \"Total Issues\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(33, \"mat-card\", 7)(34, \"mat-card-header\")(35, \"mat-card-title\");\n            i0.ɵɵtext(36, \"Projects\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"mat-card-content\")(38, \"div\", 8)(39, \"table\", 9);\n            i0.ɵɵelementContainerStart(40, 10);\n            i0.ɵɵtemplate(41, ProjectsComponent_th_41_Template, 2, 0, \"th\", 11)(42, ProjectsComponent_td_42_Template, 6, 2, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(43, 13);\n            i0.ɵɵtemplate(44, ProjectsComponent_th_44_Template, 2, 0, \"th\", 11)(45, ProjectsComponent_td_45_Template, 4, 5, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(46, 14);\n            i0.ɵɵtemplate(47, ProjectsComponent_th_47_Template, 2, 0, \"th\", 11)(48, ProjectsComponent_td_48_Template, 4, 5, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(49, 15);\n            i0.ɵɵtemplate(50, ProjectsComponent_th_50_Template, 2, 0, \"th\", 11)(51, ProjectsComponent_td_51_Template, 2, 1, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(52, 16);\n            i0.ɵɵtemplate(53, ProjectsComponent_th_53_Template, 2, 0, \"th\", 11)(54, ProjectsComponent_td_54_Template, 3, 3, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(55, 17);\n            i0.ɵɵtemplate(56, ProjectsComponent_th_56_Template, 2, 0, \"th\", 11)(57, ProjectsComponent_td_57_Template, 3, 4, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(58, 18);\n            i0.ɵɵtemplate(59, ProjectsComponent_th_59_Template, 2, 0, \"th\", 11)(60, ProjectsComponent_td_60_Template, 13, 0, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(61, ProjectsComponent_tr_61_Template, 1, 0, \"tr\", 19)(62, ProjectsComponent_tr_62_Template, 1, 0, \"tr\", 20);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate(ctx.projects.length);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getActiveProjects());\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getTotalScans());\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getTotalIssues());\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"dataSource\", ctx.projects);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          }\n        },\n        dependencies: [CommonModule, i4.TitleCasePipe, i4.DatePipe, ReactiveFormsModule, MatCardModule, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, MatButtonModule, i6.MatButton, i6.MatIconButton, MatIconModule, i7.MatIcon, MatTableModule, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow, MatDialogModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatChipsModule, i9.MatChip, MatSnackBarModule, MatProgressSpinnerModule],\n        styles: [\".projects-container[_ngcontent-%COMP%]{padding:20px;max-width:1400px;margin:0 auto}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;color:#333}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin-bottom:30px}.stat-card[_ngcontent-%COMP%]{text-align:center;padding:20px}.stat-number[_ngcontent-%COMP%]{font-size:36px;font-weight:700;color:#667eea;margin-bottom:8px}.stat-label[_ngcontent-%COMP%]{font-size:14px;color:#666;text-transform:uppercase;letter-spacing:.5px}.projects-table-card[_ngcontent-%COMP%]{margin-bottom:30px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.projects-table[_ngcontent-%COMP%]{width:100%}.project-name[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;font-size:16px;margin-bottom:4px}.project-name[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#666;font-size:12px}.type-ethereum[_ngcontent-%COMP%]{background-color:#627eea;color:#fff}.type-bitcoin[_ngcontent-%COMP%]{background-color:#f7931a;color:#fff}.type-multi-chain[_ngcontent-%COMP%]{background-color:#9c27b0;color:#fff}.status-active[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.status-inactive[_ngcontent-%COMP%]{background-color:#ff9800;color:#fff}.status-archived[_ngcontent-%COMP%]{background-color:#757575;color:#fff}.issue-high[_ngcontent-%COMP%]{color:#f44336;font-weight:700}.issue-medium[_ngcontent-%COMP%]{color:#ff9800;font-weight:700}.issue-low[_ngcontent-%COMP%]{color:#4caf50}.issue-none[_ngcontent-%COMP%]{color:#666}mat-chip[_ngcontent-%COMP%]{font-size:12px;min-height:24px}\"]\n      });\n    }\n  }\n  return ProjectsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}