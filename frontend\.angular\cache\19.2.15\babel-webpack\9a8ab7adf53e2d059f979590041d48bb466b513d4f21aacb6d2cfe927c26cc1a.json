{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ApiService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = 'http://localhost:8080/api/v1';\n    this.scanStatusSubject = new BehaviorSubject('idle');\n    this.scanStatus$ = this.scanStatusSubject.asObservable();\n  }\n  // Health check\n  getHealth() {\n    return this.http.get(`${this.baseUrl}/health`);\n  }\n  // Scanning endpoints\n  startScan(request) {\n    this.scanStatusSubject.next('starting');\n    return this.http.post(`${this.baseUrl}/scan/start`, request);\n  }\n  getScanResult(scanId) {\n    return this.http.get(`${this.baseUrl}/scan/result/${scanId}`);\n  }\n  getScanHistory() {\n    return this.http.get(`${this.baseUrl}/scan/history`);\n  }\n  scanFile(filePath) {\n    const params = new HttpParams().set('file', filePath);\n    return this.http.get(`${this.baseUrl}/scan/file`, {\n      params\n    });\n  }\n  // Report endpoints\n  generateReport(request) {\n    return this.http.post(`${this.baseUrl}/report/generate`, request);\n  }\n  // Checklist endpoints\n  getSecurityChecklist(chain) {\n    let params = new HttpParams();\n    if (chain) {\n      params = params.set('chain', chain);\n    }\n    return this.http.get(`${this.baseUrl}/checklist`, {\n      params\n    });\n  }\n  // Configuration endpoints\n  getConfiguration() {\n    return this.http.get(`${this.baseUrl}/config`);\n  }\n  updateConfiguration(config) {\n    return this.http.put(`${this.baseUrl}/config`, config);\n  }\n  // Dashboard data (mock implementation)\n  getDashboardStats() {\n    // This would typically call a real endpoint\n    return this.http.get(`${this.baseUrl}/scan/history`).pipe(map(response => {\n      // Transform the data to dashboard stats format\n      const stats = {\n        total_scans: response.total,\n        total_issues: response.scans.reduce((sum, scan) => sum + (scan.issues?.length || 0), 0),\n        critical_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['critical'] || 0), 0),\n        high_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['high'] || 0), 0),\n        medium_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['medium'] || 0), 0),\n        low_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['low'] || 0), 0),\n        active_projects: 3,\n        // Mock value\n        recent_scans: response.scans.slice(0, 5)\n      };\n      return stats;\n    }));\n  }\n  // WebSocket connection for real-time updates\n  connectWebSocket() {\n    const token = localStorage.getItem('auth_token');\n    const wsUrl = `ws://localhost:8080/ws${token ? '?token=' + token : ''}`;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket connected');\n    };\n    ws.onmessage = event => {\n      const data = JSON.parse(event.data);\n      if (data.type === 'scan_status') {\n        this.scanStatusSubject.next(data.status);\n      }\n    };\n    ws.onclose = () => {\n      console.log('WebSocket disconnected');\n    };\n    ws.onerror = error => {\n      console.error('WebSocket error:', error);\n    };\n    return ws;\n  }\n  // Utility methods\n  updateScanStatus(status) {\n    this.scanStatusSubject.next(status);\n  }\n  getCurrentScanStatus() {\n    return this.scanStatusSubject.value;\n  }\n  // Mock data generators for development\n  generateMockScanResult() {\n    return {\n      id: 'scan_' + Date.now(),\n      project_path: '/path/to/project',\n      chains: ['ethereum', 'bitcoin'],\n      status: 'completed',\n      start_time: new Date().toISOString(),\n      end_time: new Date().toISOString(),\n      duration: 30000,\n      issues: this.generateMockIssues(),\n      severity_counts: {\n        critical: 2,\n        high: 5,\n        medium: 8,\n        low: 12,\n        info: 3\n      },\n      files_scanned: 45,\n      lines_scanned: 2500,\n      configuration: {},\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    };\n  }\n  generateMockIssues() {\n    return [{\n      id: 'issue_1',\n      type: 'reentrancy',\n      severity: 'critical',\n      title: 'Reentrancy Vulnerability',\n      description: 'External call followed by state change may be vulnerable to reentrancy attacks',\n      file: 'contracts/Token.sol',\n      line: 45,\n      code: 'recipient.call{value: amount}(\"\");',\n      chain: 'ethereum',\n      category: 'smart_contract',\n      cwe: 'CWE-362',\n      owasp: 'A06:2021',\n      references: ['https://consensys.github.io/smart-contract-best-practices/attacks/reentrancy/'],\n      suggestion: 'Use the checks-effects-interactions pattern or reentrancy guards',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }, {\n      id: 'issue_2',\n      type: 'private_key_exposure',\n      severity: 'critical',\n      title: 'Private Key Exposure',\n      description: 'Bitcoin private key found in code',\n      file: 'src/wallet.js',\n      line: 23,\n      code: 'const privateKey = \"L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ\";',\n      chain: 'bitcoin',\n      category: 'wallet',\n      cwe: 'CWE-798',\n      owasp: 'A02:2021',\n      references: ['https://bitcoin.org/en/secure-your-wallet'],\n      suggestion: 'Remove private keys from code and use secure key management',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }];\n  }\n  generateMockChecklist() {\n    return [{\n      id: 'check_1',\n      category: 'wallet',\n      chain: 'general',\n      title: 'Private Key Security',\n      description: 'Ensure private keys are not exposed in code',\n      priority: 'critical',\n      status: 'pending',\n      auto_check: true,\n      references: ['https://owasp.org/www-project-top-ten/'],\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }, {\n      id: 'check_2',\n      category: 'smart_contract',\n      chain: 'ethereum',\n      title: 'Reentrancy Protection',\n      description: 'Check for reentrancy vulnerabilities in smart contracts',\n      priority: 'high',\n      status: 'completed',\n      auto_check: true,\n      references: ['https://consensys.github.io/smart-contract-best-practices/'],\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }];\n  }\n  static {\n    this.ɵfac = function ApiService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApiService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ApiService,\n      factory: ApiService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "ApiService", "constructor", "http", "baseUrl", "scanStatusSubject", "scanStatus$", "asObservable", "getHealth", "get", "startScan", "request", "next", "post", "getScanResult", "scanId", "getScanHistory", "scanFile", "filePath", "params", "set", "generateReport", "getSecurityChecklist", "chain", "getConfiguration", "updateConfiguration", "config", "put", "getDashboardStats", "pipe", "response", "stats", "total_scans", "total", "total_issues", "scans", "reduce", "sum", "scan", "issues", "length", "critical_issues", "severity_counts", "high_issues", "medium_issues", "low_issues", "active_projects", "recent_scans", "slice", "connectWebSocket", "token", "localStorage", "getItem", "wsUrl", "ws", "WebSocket", "onopen", "console", "log", "onmessage", "event", "data", "JSON", "parse", "type", "status", "onclose", "onerror", "error", "updateScanStatus", "getCurrentScanStatus", "value", "generateMockScanResult", "id", "Date", "now", "project_path", "chains", "start_time", "toISOString", "end_time", "duration", "generateMockIssues", "critical", "high", "medium", "low", "info", "files_scanned", "lines_scanned", "configuration", "created_at", "updated_at", "severity", "title", "description", "file", "line", "code", "category", "cwe", "owasp", "references", "suggestion", "generateMockChecklist", "priority", "auto_check", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\services\\api.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject, map } from 'rxjs';\nimport { \n  ScanResult, \n  ScanRequest, \n  ScanResponse, \n  SecurityIssue, \n  SecurityChecklist, \n  SecurityReport, \n  HealthCheck,\n  DashboardStats\n} from '../models/security.models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApiService {\n  private readonly baseUrl = 'http://localhost:8080/api/v1';\n  private scanStatusSubject = new BehaviorSubject<string>('idle');\n  public scanStatus$ = this.scanStatusSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  // Health check\n  getHealth(): Observable<HealthCheck> {\n    return this.http.get<HealthCheck>(`${this.baseUrl}/health`);\n  }\n\n  // Scanning endpoints\n  startScan(request: ScanRequest): Observable<ScanResponse> {\n    this.scanStatusSubject.next('starting');\n    return this.http.post<ScanResponse>(`${this.baseUrl}/scan/start`, request);\n  }\n\n  getScanResult(scanId: string): Observable<ScanResult> {\n    return this.http.get<ScanResult>(`${this.baseUrl}/scan/result/${scanId}`);\n  }\n\n  getScanHistory(): Observable<{ scans: ScanResult[], total: number }> {\n    return this.http.get<{ scans: ScanResult[], total: number }>(`${this.baseUrl}/scan/history`);\n  }\n\n  scanFile(filePath: string): Observable<{ file: string, issues: SecurityIssue[], count: number }> {\n    const params = new HttpParams().set('file', filePath);\n    return this.http.get<{ file: string, issues: SecurityIssue[], count: number }>(`${this.baseUrl}/scan/file`, { params });\n  }\n\n  // Report endpoints\n  generateReport(request: any): Observable<SecurityReport> {\n    return this.http.post<SecurityReport>(`${this.baseUrl}/report/generate`, request);\n  }\n\n  // Checklist endpoints\n  getSecurityChecklist(chain?: string): Observable<{ checklist: SecurityChecklist[], total: number }> {\n    let params = new HttpParams();\n    if (chain) {\n      params = params.set('chain', chain);\n    }\n    return this.http.get<{ checklist: SecurityChecklist[], total: number }>(`${this.baseUrl}/checklist`, { params });\n  }\n\n  // Configuration endpoints\n  getConfiguration(): Observable<any> {\n    return this.http.get<any>(`${this.baseUrl}/config`);\n  }\n\n  updateConfiguration(config: any): Observable<{ message: string }> {\n    return this.http.put<{ message: string }>(`${this.baseUrl}/config`, config);\n  }\n\n  // Dashboard data (mock implementation)\n  getDashboardStats(): Observable<DashboardStats> {\n    // This would typically call a real endpoint\n    return this.http.get<{ scans: ScanResult[], total: number }>(`${this.baseUrl}/scan/history`)\n      .pipe(\n        map(response => {\n          // Transform the data to dashboard stats format\n          const stats: DashboardStats = {\n            total_scans: response.total,\n            total_issues: response.scans.reduce((sum, scan) => sum + (scan.issues?.length || 0), 0),\n            critical_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['critical'] || 0), 0),\n            high_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['high'] || 0), 0),\n            medium_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['medium'] || 0), 0),\n            low_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['low'] || 0), 0),\n            active_projects: 3, // Mock value\n            recent_scans: response.scans.slice(0, 5)\n          };\n          return stats;\n        })\n      );\n  }\n\n  // WebSocket connection for real-time updates\n  connectWebSocket(): WebSocket {\n    const token = localStorage.getItem('auth_token');\n    const wsUrl = `ws://localhost:8080/ws${token ? '?token=' + token : ''}`;\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      console.log('WebSocket connected');\n    };\n\n    ws.onmessage = (event) => {\n      const data = JSON.parse(event.data);\n      if (data.type === 'scan_status') {\n        this.scanStatusSubject.next(data.status);\n      }\n    };\n\n    ws.onclose = () => {\n      console.log('WebSocket disconnected');\n    };\n\n    ws.onerror = (error) => {\n      console.error('WebSocket error:', error);\n    };\n\n    return ws;\n  }\n\n  // Utility methods\n  updateScanStatus(status: string): void {\n    this.scanStatusSubject.next(status);\n  }\n\n  getCurrentScanStatus(): string {\n    return this.scanStatusSubject.value;\n  }\n\n  // Mock data generators for development\n  generateMockScanResult(): ScanResult {\n    return {\n      id: 'scan_' + Date.now(),\n      project_path: '/path/to/project',\n      chains: ['ethereum', 'bitcoin'],\n      status: 'completed',\n      start_time: new Date().toISOString(),\n      end_time: new Date().toISOString(),\n      duration: 30000,\n      issues: this.generateMockIssues(),\n      severity_counts: {\n        critical: 2,\n        high: 5,\n        medium: 8,\n        low: 12,\n        info: 3\n      },\n      files_scanned: 45,\n      lines_scanned: 2500,\n      configuration: {},\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    };\n  }\n\n  generateMockIssues(): SecurityIssue[] {\n    return [\n      {\n        id: 'issue_1',\n        type: 'reentrancy',\n        severity: 'critical',\n        title: 'Reentrancy Vulnerability',\n        description: 'External call followed by state change may be vulnerable to reentrancy attacks',\n        file: 'contracts/Token.sol',\n        line: 45,\n        code: 'recipient.call{value: amount}(\"\");',\n        chain: 'ethereum',\n        category: 'smart_contract',\n        cwe: 'CWE-362',\n        owasp: 'A06:2021',\n        references: ['https://consensys.github.io/smart-contract-best-practices/attacks/reentrancy/'],\n        suggestion: 'Use the checks-effects-interactions pattern or reentrancy guards',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        id: 'issue_2',\n        type: 'private_key_exposure',\n        severity: 'critical',\n        title: 'Private Key Exposure',\n        description: 'Bitcoin private key found in code',\n        file: 'src/wallet.js',\n        line: 23,\n        code: 'const privateKey = \"L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ\";',\n        chain: 'bitcoin',\n        category: 'wallet',\n        cwe: 'CWE-798',\n        owasp: 'A02:2021',\n        references: ['https://bitcoin.org/en/secure-your-wallet'],\n        suggestion: 'Remove private keys from code and use secure key management',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    ];\n  }\n\n  generateMockChecklist(): SecurityChecklist[] {\n    return [\n      {\n        id: 'check_1',\n        category: 'wallet',\n        chain: 'general',\n        title: 'Private Key Security',\n        description: 'Ensure private keys are not exposed in code',\n        priority: 'critical',\n        status: 'pending',\n        auto_check: true,\n        references: ['https://owasp.org/www-project-top-ten/'],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        id: 'check_2',\n        category: 'smart_contract',\n        chain: 'ethereum',\n        title: 'Reentrancy Protection',\n        description: 'Check for reentrancy vulnerabilities in smart contracts',\n        priority: 'high',\n        status: 'completed',\n        auto_check: true,\n        references: ['https://consensys.github.io/smart-contract-best-practices/'],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    ];\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAAqBC,eAAe,EAAEC,GAAG,QAAQ,MAAM;;;AAevD,OAAM,MAAOC,UAAU;EAKrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJP,KAAAC,OAAO,GAAG,8BAA8B;IACjD,KAAAC,iBAAiB,GAAG,IAAIN,eAAe,CAAS,MAAM,CAAC;IACxD,KAAAO,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACE,YAAY,EAAE;EAEnB;EAEvC;EACAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACL,IAAI,CAACM,GAAG,CAAc,GAAG,IAAI,CAACL,OAAO,SAAS,CAAC;EAC7D;EAEA;EACAM,SAASA,CAACC,OAAoB;IAC5B,IAAI,CAACN,iBAAiB,CAACO,IAAI,CAAC,UAAU,CAAC;IACvC,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAAe,GAAG,IAAI,CAACT,OAAO,aAAa,EAAEO,OAAO,CAAC;EAC5E;EAEAG,aAAaA,CAACC,MAAc;IAC1B,OAAO,IAAI,CAACZ,IAAI,CAACM,GAAG,CAAa,GAAG,IAAI,CAACL,OAAO,gBAAgBW,MAAM,EAAE,CAAC;EAC3E;EAEAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACb,IAAI,CAACM,GAAG,CAAyC,GAAG,IAAI,CAACL,OAAO,eAAe,CAAC;EAC9F;EAEAa,QAAQA,CAACC,QAAgB;IACvB,MAAMC,MAAM,GAAG,IAAIrB,UAAU,EAAE,CAACsB,GAAG,CAAC,MAAM,EAAEF,QAAQ,CAAC;IACrD,OAAO,IAAI,CAACf,IAAI,CAACM,GAAG,CAA2D,GAAG,IAAI,CAACL,OAAO,YAAY,EAAE;MAAEe;IAAM,CAAE,CAAC;EACzH;EAEA;EACAE,cAAcA,CAACV,OAAY;IACzB,OAAO,IAAI,CAACR,IAAI,CAACU,IAAI,CAAiB,GAAG,IAAI,CAACT,OAAO,kBAAkB,EAAEO,OAAO,CAAC;EACnF;EAEA;EACAW,oBAAoBA,CAACC,KAAc;IACjC,IAAIJ,MAAM,GAAG,IAAIrB,UAAU,EAAE;IAC7B,IAAIyB,KAAK,EAAE;MACTJ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,OAAO,EAAEG,KAAK,CAAC;IACrC;IACA,OAAO,IAAI,CAACpB,IAAI,CAACM,GAAG,CAAoD,GAAG,IAAI,CAACL,OAAO,YAAY,EAAE;MAAEe;IAAM,CAAE,CAAC;EAClH;EAEA;EACAK,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACrB,IAAI,CAACM,GAAG,CAAM,GAAG,IAAI,CAACL,OAAO,SAAS,CAAC;EACrD;EAEAqB,mBAAmBA,CAACC,MAAW;IAC7B,OAAO,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAsB,GAAG,IAAI,CAACvB,OAAO,SAAS,EAAEsB,MAAM,CAAC;EAC7E;EAEA;EACAE,iBAAiBA,CAAA;IACf;IACA,OAAO,IAAI,CAACzB,IAAI,CAACM,GAAG,CAAyC,GAAG,IAAI,CAACL,OAAO,eAAe,CAAC,CACzFyB,IAAI,CACH7B,GAAG,CAAC8B,QAAQ,IAAG;MACb;MACA,MAAMC,KAAK,GAAmB;QAC5BC,WAAW,EAAEF,QAAQ,CAACG,KAAK;QAC3BC,YAAY,EAAEJ,QAAQ,CAACK,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIC,IAAI,CAACC,MAAM,EAAEC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACvFC,eAAe,EAAEX,QAAQ,CAACK,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIC,IAAI,CAACI,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACzGC,WAAW,EAAEb,QAAQ,CAACK,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIC,IAAI,CAACI,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACjGE,aAAa,EAAEd,QAAQ,CAACK,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIC,IAAI,CAACI,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACrGG,UAAU,EAAEf,QAAQ,CAACK,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIC,IAAI,CAACI,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/FI,eAAe,EAAE,CAAC;QAAE;QACpBC,YAAY,EAAEjB,QAAQ,CAACK,KAAK,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC;OACxC;MACD,OAAOjB,KAAK;IACd,CAAC,CAAC,CACH;EACL;EAEA;EACAkB,gBAAgBA,CAAA;IACd,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAMC,KAAK,GAAG,yBAAyBH,KAAK,GAAG,SAAS,GAAGA,KAAK,GAAG,EAAE,EAAE;IACvE,MAAMI,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAK;MACfC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAEDJ,EAAE,CAACK,SAAS,GAAIC,KAAK,IAAI;MACvB,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;MACnC,IAAIA,IAAI,CAACG,IAAI,KAAK,aAAa,EAAE;QAC/B,IAAI,CAAC3D,iBAAiB,CAACO,IAAI,CAACiD,IAAI,CAACI,MAAM,CAAC;MAC1C;IACF,CAAC;IAEDX,EAAE,CAACY,OAAO,GAAG,MAAK;MAChBT,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACvC,CAAC;IAEDJ,EAAE,CAACa,OAAO,GAAIC,KAAK,IAAI;MACrBX,OAAO,CAACW,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C,CAAC;IAED,OAAOd,EAAE;EACX;EAEA;EACAe,gBAAgBA,CAACJ,MAAc;IAC7B,IAAI,CAAC5D,iBAAiB,CAACO,IAAI,CAACqD,MAAM,CAAC;EACrC;EAEAK,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACjE,iBAAiB,CAACkE,KAAK;EACrC;EAEA;EACAC,sBAAsBA,CAAA;IACpB,OAAO;MACLC,EAAE,EAAE,OAAO,GAAGC,IAAI,CAACC,GAAG,EAAE;MACxBC,YAAY,EAAE,kBAAkB;MAChCC,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;MAC/BZ,MAAM,EAAE,WAAW;MACnBa,UAAU,EAAE,IAAIJ,IAAI,EAAE,CAACK,WAAW,EAAE;MACpCC,QAAQ,EAAE,IAAIN,IAAI,EAAE,CAACK,WAAW,EAAE;MAClCE,QAAQ,EAAE,KAAK;MACf1C,MAAM,EAAE,IAAI,CAAC2C,kBAAkB,EAAE;MACjCxC,eAAe,EAAE;QACfyC,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE,CAAC;QACPC,MAAM,EAAE,CAAC;QACTC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE;OACP;MACDC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,IAAIjB,IAAI,EAAE,CAACK,WAAW,EAAE;MACpCa,UAAU,EAAE,IAAIlB,IAAI,EAAE,CAACK,WAAW;KACnC;EACH;EAEAG,kBAAkBA,CAAA;IAChB,OAAO,CACL;MACET,EAAE,EAAE,SAAS;MACbT,IAAI,EAAE,YAAY;MAClB6B,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,0BAA0B;MACjCC,WAAW,EAAE,gFAAgF;MAC7FC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,oCAAoC;MAC1C3E,KAAK,EAAE,UAAU;MACjB4E,QAAQ,EAAE,gBAAgB;MAC1BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,CAAC,+EAA+E,CAAC;MAC7FC,UAAU,EAAE,kEAAkE;MAC9EZ,UAAU,EAAE,IAAIjB,IAAI,EAAE,CAACK,WAAW,EAAE;MACpCa,UAAU,EAAE,IAAIlB,IAAI,EAAE,CAACK,WAAW;KACnC,EACD;MACEN,EAAE,EAAE,SAAS;MACbT,IAAI,EAAE,sBAAsB;MAC5B6B,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,mCAAmC;MAChDC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,4EAA4E;MAClF3E,KAAK,EAAE,SAAS;MAChB4E,QAAQ,EAAE,QAAQ;MAClBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,CAAC,2CAA2C,CAAC;MACzDC,UAAU,EAAE,6DAA6D;MACzEZ,UAAU,EAAE,IAAIjB,IAAI,EAAE,CAACK,WAAW,EAAE;MACpCa,UAAU,EAAE,IAAIlB,IAAI,EAAE,CAACK,WAAW;KACnC,CACF;EACH;EAEAyB,qBAAqBA,CAAA;IACnB,OAAO,CACL;MACE/B,EAAE,EAAE,SAAS;MACb0B,QAAQ,EAAE,QAAQ;MAClB5E,KAAK,EAAE,SAAS;MAChBuE,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,6CAA6C;MAC1DU,QAAQ,EAAE,UAAU;MACpBxC,MAAM,EAAE,SAAS;MACjByC,UAAU,EAAE,IAAI;MAChBJ,UAAU,EAAE,CAAC,wCAAwC,CAAC;MACtDX,UAAU,EAAE,IAAIjB,IAAI,EAAE,CAACK,WAAW,EAAE;MACpCa,UAAU,EAAE,IAAIlB,IAAI,EAAE,CAACK,WAAW;KACnC,EACD;MACEN,EAAE,EAAE,SAAS;MACb0B,QAAQ,EAAE,gBAAgB;MAC1B5E,KAAK,EAAE,UAAU;MACjBuE,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,yDAAyD;MACtEU,QAAQ,EAAE,MAAM;MAChBxC,MAAM,EAAE,WAAW;MACnByC,UAAU,EAAE,IAAI;MAChBJ,UAAU,EAAE,CAAC,4DAA4D,CAAC;MAC1EX,UAAU,EAAE,IAAIjB,IAAI,EAAE,CAACK,WAAW,EAAE;MACpCa,UAAU,EAAE,IAAIlB,IAAI,EAAE,CAACK,WAAW;KACnC,CACF;EACH;;;uCAjNW9E,UAAU,EAAA0G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAV7G,UAAU;MAAA8G,OAAA,EAAV9G,UAAU,CAAA+G,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}