{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/checkbox\";\nfunction LoginComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction LoginComponent_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const credentials = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  loginWithDemo() {\n    this.isLoading = true;\n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', {\n        duration: 3000\n      });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 41,\n      vars: 9,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"logo\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"form-options\"], [\"formControlName\", \"rememberMe\"], [\"href\", \"#\", 1, \"forgot-password\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"auth-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"demo-button\", 3, \"click\", \"disabled\"], [1, \"auth-footer\"], [\"routerLink\", \"/register\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7, \"SPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"h1\");\n          i0.ɵɵtext(9, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\");\n          i0.ɵɵtext(11, \"Sign in to your security dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(13, \"mat-form-field\", 5)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 6);\n          i0.ɵɵtemplate(17, LoginComponent_mat_error_17_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 5)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 8);\n          i0.ɵɵelementStart(22, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_22_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, LoginComponent_mat_error_25_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"mat-checkbox\", 11);\n          i0.ɵɵtext(28, \" Remember me \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"a\", 12);\n          i0.ɵɵtext(30, \"Forgot password?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"button\", 13);\n          i0.ɵɵtemplate(32, LoginComponent_mat_spinner_32_Template, 1, 0, \"mat-spinner\", 14)(33, LoginComponent_span_33_Template, 2, 0, \"span\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_34_listener() {\n            return ctx.loginWithDemo();\n          });\n          i0.ɵɵtext(35, \" Demo Login \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 16)(37, \"p\");\n          i0.ɵɵtext(38, \"Don't have an account? \");\n          i0.ɵɵelementStart(39, \"a\", 17);\n          i0.ɵɵtext(40, \"Sign up\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_4_0.hasError(\"required\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink, MatCardModule, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatSuffix, MatInputModule, i7.MatInput, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatProgressSpinnerModule, i10.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i11.MatCheckbox],\n      styles: [\".auth-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  padding: 40px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.auth-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.auth-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.auth-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: #667eea;\\n}\\n.auth-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a202c;\\n}\\n.auth-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 28px;\\n  font-weight: 700;\\n  color: #1a202c;\\n}\\n.auth-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 16px;\\n}\\n\\n.auth-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n.auth-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 0;\\n}\\n.auth-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n}\\n.auth-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background-color: #f8fafc;\\n  border-radius: 8px;\\n}\\n.auth-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-focus-overlay[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n.auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin: -8px 0 8px 0;\\n  font-size: 14px;\\n}\\n.auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-mdc-checkbox-frame[_ngcontent-%COMP%] {\\n  border-color: #cbd5e1;\\n}\\n.auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.brand-text[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  letter-spacing: -0.5px;\\n}\\n\\n.header-link[_ngcontent-%COMP%] {\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 8px;\\n  padding: 8px 16px;\\n  transition: all 0.3s ease;\\n}\\n\\n.header-link[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.login-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  gap: 60px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n.login-card-wrapper[_ngcontent-%COMP%] {\\n  flex: 0 0 400px;\\n  max-width: 400px;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  background: rgba(255, 255, 255, 0.95);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  padding: 32px 32px 0 32px;\\n  text-align: center;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.logo-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: white;\\n}\\n\\n.logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: 700;\\n  color: #1a202c;\\n  line-height: 1.2;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 0;\\n  color: #64748b;\\n  font-size: 16px;\\n  line-height: 1.4;\\n}\\n\\nmat-card-content[_ngcontent-%COMP%] {\\n  padding: 32px;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  margin-bottom: 24px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 8px;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  border-radius: 12px;\\n  font-weight: 600;\\n  font-size: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\\n}\\n\\n.demo-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  border-radius: 12px;\\n  font-weight: 600;\\n  color: #667eea;\\n  border: 2px solid #667eea;\\n  transition: all 0.3s ease;\\n}\\n\\n.demo-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #667eea;\\n  color: white;\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  padding: 0 32px 32px 32px;\\n  text-align: center;\\n}\\n\\n.card-actions[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 14px;\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 600;\\n}\\n\\n.register-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.features-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px;\\n  color: white;\\n}\\n\\n.features-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  margin-bottom: 32px;\\n}\\n\\n.features-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: #fbbf24;\\n}\\n\\n.features-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 700;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 24px;\\n  margin-bottom: 48px;\\n}\\n\\n.feature-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 24px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.feature-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateY(-2px);\\n}\\n\\n.feature-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  color: #fbbf24;\\n  margin-top: 4px;\\n}\\n\\n.feature-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1.3;\\n}\\n\\n.feature-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  line-height: 1.5;\\n  opacity: 0.9;\\n}\\n\\n.stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  gap: 24px;\\n  padding: 32px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 32px;\\n  font-weight: 700;\\n  color: #fbbf24;\\n  line-height: 1;\\n  margin-bottom: 8px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  opacity: 0.9;\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 1024px) {\\n  .login-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 40px;\\n  }\\n  .features-info[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .login-content[_ngcontent-%COMP%] {\\n    padding: 20px 16px;\\n  }\\n  .login-card-wrapper[_ngcontent-%COMP%] {\\n    flex: none;\\n    max-width: 100%;\\n  }\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n  .logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  mat-card-content[_ngcontent-%COMP%] {\\n    padding: 24px;\\n  }\\n  .login-header[_ngcontent-%COMP%] {\\n    padding: 24px 24px 0 24px;\\n  }\\n  .card-actions[_ngcontent-%COMP%] {\\n    padding: 0 24px 24px 24px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .auth-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .auth-card[_ngcontent-%COMP%] {\\n    padding: 32px 24px;\\n  }\\n  .auth-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .form-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "snackBar", "hidePassword", "isLoading", "returnUrl", "loginForm", "group", "username", "required", "password", "rememberMe", "ngOnInit", "snapshot", "queryParams", "onSubmit", "valid", "credentials", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "message", "loginWithDemo", "setTimeout", "mockLogin", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_12_listener", "ɵɵtemplate", "LoginComponent_mat_error_17_Template", "LoginComponent_Template_button_click_22_listener", "LoginComponent_mat_error_25_Template", "LoginComponent_mat_spinner_32_Template", "LoginComponent_span_33_Template", "LoginComponent_Template_button_click_34_listener", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "tmp_4_0", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i7", "MatInput", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatProgressSpinner", "i11", "MatCheckbox", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\n\nimport { AuthService, LoginRequest } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  hidePassword = true;\n  isLoading = false;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      username: ['', [Validators.required]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      \n      const credentials: LoginRequest = {\n        username: this.loginForm.value.username,\n        password: this.loginForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n\n  loginWithDemo(): void {\n    this.isLoading = true;\n    \n    // Use mock login for development\n    setTimeout(() => {\n      this.authService.mockLogin('admin', 'admin');\n      this.isLoading = false;\n      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    }, 1000);\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card\">\n    <div class=\"auth-header\">\n      <div class=\"logo\">\n        <mat-icon>security</mat-icon>\n        <span>SPT</span>\n      </div>\n      <h1>Welcome Back</h1>\n      <p>Sign in to your security dashboard</p>\n    </div>\n\n    <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Username</mat-label>\n        <input matInput formControlName=\"username\" autocomplete=\"username\">\n        <mat-error *ngIf=\"loginForm.get('username')?.hasError('required')\">\n          Username is required\n        </mat-error>\n      </mat-form-field>\n\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Password</mat-label>\n        <input matInput\n               [type]=\"hidePassword ? 'password' : 'text'\"\n               formControlName=\"password\"\n               autocomplete=\"current-password\">\n        <button mat-icon-button matSuffix\n                (click)=\"hidePassword = !hidePassword\"\n                type=\"button\">\n          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n        </button>\n        <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n          Password is required\n        </mat-error>\n      </mat-form-field>\n\n      <div class=\"form-options\">\n        <mat-checkbox formControlName=\"rememberMe\">\n          Remember me\n        </mat-checkbox>\n        <a href=\"#\" class=\"forgot-password\">Forgot password?</a>\n      </div>\n\n      <button mat-raised-button\n              color=\"primary\"\n              type=\"submit\"\n              class=\"auth-button\"\n              [disabled]=\"loginForm.invalid || isLoading\">\n        <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n        <span *ngIf=\"!isLoading\">Sign In</span>\n      </button>\n\n      <button mat-button\n              type=\"button\"\n              class=\"demo-button\"\n              (click)=\"loginWithDemo()\"\n              [disabled]=\"isLoading\">\n        Demo Login\n      </button>\n    </form>\n\n    <div class=\"auth-footer\">\n      <p>Don't have an account? <a routerLink=\"/register\">Sign up</a></p>\n    </div>\n  </div>\n\n\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACxF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;ICItDC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAcZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAeZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADd/C,OAAM,MAAOE,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MAErB,MAAMa,WAAW,GAAiB;QAChCT,QAAQ,EAAE,IAAI,CAACF,SAAS,CAACY,KAAK,CAACV,QAAQ;QACvCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACY,KAAK,CAACR;OAChC;MAED,IAAI,CAACX,WAAW,CAACoB,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;QACxC,CAAC;QACDqB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QAClF;OACD,CAAC;IACJ;EACF;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACxB,SAAS,GAAG,IAAI;IAErB;IACAyB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9B,WAAW,CAAC+B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;MAC5C,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,QAAQ,CAACqB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC;IACxC,CAAC,EAAE,IAAI,CAAC;EACV;;;uCA1DWT,cAAc,EAAAL,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA/C,EAAA,CAAAwC,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd5C,cAAc;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/BnBxD,EAJR,CAAAC,cAAA,aAA4B,aACH,aACI,aACL,eACN;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,UAAG;UACXF,EADW,CAAAG,YAAA,EAAO,EACZ;UACNH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0CAAkC;UACvCF,EADuC,CAAAG,YAAA,EAAI,EACrC;UAENH,EAAA,CAAAC,cAAA,eAAwE;UAA1CD,EAAA,CAAA0D,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAEjDxB,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAAmE;UACnEJ,EAAA,CAAA4D,UAAA,KAAAC,oCAAA,uBAAmE;UAGrE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAGuC;UACvCJ,EAAA,CAAAC,cAAA,iBAEsB;UADdD,EAAA,CAAA0D,UAAA,mBAAAI,iDAAA;YAAA,OAAAL,GAAA,CAAA7C,YAAA,IAAA6C,GAAA,CAAA7C,YAAA;UAAA,EAAsC;UAE5CZ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAA4D,UAAA,KAAAG,oCAAA,uBAAmE;UAGrE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,wBACmB;UACzCD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UACtDF,EADsD,CAAAG,YAAA,EAAI,EACpD;UAENH,EAAA,CAAAC,cAAA,kBAIoD;UAElDD,EADA,CAAA4D,UAAA,KAAAI,sCAAA,0BAA6C,KAAAC,+BAAA,kBACpB;UAC3BjE,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAI+B;UADvBD,EAAA,CAAA0D,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAE/BrC,EAAA,CAAAE,MAAA,oBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACJ;UAGLH,EADF,CAAAC,cAAA,eAAyB,SACpB;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAE/DF,EAF+D,CAAAG,YAAA,EAAI,EAAI,EAC/D,EACF,EAhEoB;;;;;UAWlBH,EAAA,CAAAmE,SAAA,IAAuB;UAAvBnE,EAAA,CAAAoE,UAAA,cAAAX,GAAA,CAAA1C,SAAA,CAAuB;UAIbf,EAAA,CAAAmE,SAAA,GAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAC,OAAA,GAAAZ,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAD,OAAA,CAAAE,QAAA,aAAqD;UAQ1DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA7C,YAAA,uBAA2C;UAMtCZ,EAAA,CAAAmE,SAAA,GAAkD;UAAlDnE,EAAA,CAAAwE,iBAAA,CAAAf,GAAA,CAAA7C,YAAA,mCAAkD;UAElDZ,EAAA,CAAAmE,SAAA,EAAqD;UAArDnE,EAAA,CAAAoE,UAAA,UAAAK,OAAA,GAAAhB,GAAA,CAAA1C,SAAA,CAAAuD,GAAA,+BAAAG,OAAA,CAAAF,QAAA,aAAqD;UAgB3DvE,EAAA,CAAAmE,SAAA,GAA2C;UAA3CnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA1C,SAAA,CAAA2D,OAAA,IAAAjB,GAAA,CAAA5C,SAAA,CAA2C;UACnCb,EAAA,CAAAmE,SAAA,EAAe;UAAfnE,EAAA,CAAAoE,UAAA,SAAAX,GAAA,CAAA5C,SAAA,CAAe;UACtBb,EAAA,CAAAmE,SAAA,EAAgB;UAAhBnE,EAAA,CAAAoE,UAAA,UAAAX,GAAA,CAAA5C,SAAA,CAAgB;UAOjBb,EAAA,CAAAmE,SAAA,EAAsB;UAAtBnE,EAAA,CAAAoE,UAAA,aAAAX,GAAA,CAAA5C,SAAA,CAAsB;;;qBDrChCzB,YAAY,EAAAuF,EAAA,CAAAC,IAAA,EACZvF,mBAAmB,EAAAoD,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,kBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EACnB3F,YAAY,EAAAsD,EAAA,CAAAsC,UAAA,EACZ3F,aAAa,EACbC,kBAAkB,EAAA2F,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClB9F,cAAc,EAAA+F,EAAA,CAAAC,QAAA,EACd/F,eAAe,EAAAgG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfjG,aAAa,EAAAkG,EAAA,CAAAC,OAAA,EACblG,wBAAwB,EAAAmG,GAAA,CAAAC,kBAAA,EACxBnG,iBAAiB,EACjBC,iBAAiB,EAAAmG,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}