{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/checkbox\";\nfunction RegisterComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Full name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username must be at least 3 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_spinner_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 26);\n  }\n}\nfunction RegisterComponent_span_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control) {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return {\n      'passwordMismatch': true\n    };\n  }\n  return null;\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.isLoading = false;\n    this.registerForm = this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: passwordMatchValidator\n    });\n  }\n  onSubmit() {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      const userData = {\n        username: this.registerForm.value.username,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword\n      };\n      this.authService.register(userData).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate(['/dashboard']);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Registration failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 88,\n      vars: 21,\n      consts: [[1, \"register-container\"], [1, \"register-card-wrapper\"], [1, \"register-card\"], [1, \"register-header\"], [1, \"logo\"], [1, \"logo-icon\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"fullName\", \"autocomplete\", \"name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"form-options\"], [\"formControlName\", \"agreeToTerms\"], [\"href\", \"#\", 1, \"terms-link\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"register-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"card-actions\"], [\"routerLink\", \"/login\", 1, \"login-link\"], [1, \"benefits-info\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h1\");\n          i0.ɵɵtext(8, \"Create Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n          i0.ɵɵtext(10, \"Join SPT Security Platform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"mat-form-field\", 8)(15, \"mat-label\");\n          i0.ɵɵtext(16, \"Full Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 9);\n          i0.ɵɵelementStart(18, \"mat-icon\", 10);\n          i0.ɵɵtext(19, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, RegisterComponent_mat_error_20_Template, 2, 0, \"mat-error\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-form-field\", 8)(22, \"mat-label\");\n          i0.ɵɵtext(23, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 12);\n          i0.ɵɵelementStart(25, \"mat-icon\", 10);\n          i0.ɵɵtext(26, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, RegisterComponent_mat_error_27_Template, 2, 0, \"mat-error\", 11)(28, RegisterComponent_mat_error_28_Template, 2, 0, \"mat-error\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-form-field\", 8)(30, \"mat-label\");\n          i0.ɵɵtext(31, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"input\", 13);\n          i0.ɵɵelementStart(33, \"mat-icon\", 10);\n          i0.ɵɵtext(34, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, RegisterComponent_mat_error_35_Template, 2, 0, \"mat-error\", 11)(36, RegisterComponent_mat_error_36_Template, 2, 0, \"mat-error\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 8)(38, \"mat-label\");\n          i0.ɵɵtext(39, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 14);\n          i0.ɵɵelementStart(41, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_41_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(42, \"mat-icon\");\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(44, RegisterComponent_mat_error_44_Template, 2, 0, \"mat-error\", 11)(45, RegisterComponent_mat_error_45_Template, 2, 0, \"mat-error\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 8)(47, \"mat-label\");\n          i0.ɵɵtext(48, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"input\", 16);\n          i0.ɵɵelementStart(50, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_50_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(51, \"mat-icon\");\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(53, RegisterComponent_mat_error_53_Template, 2, 0, \"mat-error\", 11)(54, RegisterComponent_mat_error_54_Template, 2, 0, \"mat-error\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 17)(56, \"mat-checkbox\", 18);\n          i0.ɵɵtext(57, \" I agree to the \");\n          i0.ɵɵelementStart(58, \"a\", 19);\n          i0.ɵɵtext(59, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" and \");\n          i0.ɵɵelementStart(61, \"a\", 19);\n          i0.ɵɵtext(62, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(63, \"div\", 20)(64, \"button\", 21);\n          i0.ɵɵtemplate(65, RegisterComponent_mat_spinner_65_Template, 1, 0, \"mat-spinner\", 22)(66, RegisterComponent_span_66_Template, 2, 0, \"span\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(67, \"mat-card-actions\", 23)(68, \"p\");\n          i0.ɵɵtext(69, \"Already have an account? \");\n          i0.ɵɵelementStart(70, \"a\", 24);\n          i0.ɵɵtext(71, \"Sign in\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"div\", 25)(73, \"h3\");\n          i0.ɵɵtext(74, \"\\uD83D\\uDE80 Why Choose SPT?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"ul\")(76, \"li\");\n          i0.ɵɵtext(77, \"\\uD83D\\uDD0D Advanced vulnerability detection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"li\");\n          i0.ɵɵtext(79, \"\\uD83D\\uDCCA Real-time security monitoring\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"li\");\n          i0.ɵɵtext(81, \"\\uD83D\\uDCC8 Comprehensive analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"li\");\n          i0.ɵɵtext(83, \"\\uD83D\\uDEE1\\uFE0F Enterprise-grade security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"li\");\n          i0.ɵɵtext(85, \"\\uD83D\\uDD27 Easy integration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"li\");\n          i0.ɵɵtext(87, \"\\uD83D\\uDCF1 Multi-platform support\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_10_0;\n          let tmp_11_0;\n          let tmp_16_0;\n          let tmp_17_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.registerForm.get(\"fullName\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_2_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.hasError(\"email\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_4_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_5_0.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_10_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_11_0.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hideConfirmPassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.registerForm.hasError(\"passwordMismatch\") && ((tmp_17_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_17_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i12.MatCheckbox],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.register-card-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 40px;\\n  align-items: flex-start;\\n  max-width: 1000px;\\n  width: 100%;\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  flex: 0 0 450px;\\n  padding: 40px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 20px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.register-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #667eea;\\n  background: rgba(102, 126, 234, 0.1);\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 10px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1a202c;\\n  font-weight: 700;\\n  font-size: 28px;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  margin-bottom: 25px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-options[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.terms-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n}\\n\\n.terms-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  margin-top: 25px;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n.register-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.card-actions[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 14px;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 600;\\n}\\n\\n.login-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.benefits-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n  padding: 20px;\\n}\\n\\n.benefits-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 20px;\\n  font-weight: 700;\\n}\\n\\n.benefits-info[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.benefits-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  opacity: 0.9;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .register-card-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n  .register-card[_ngcontent-%COMP%] {\\n    flex: none;\\n    max-width: 100%;\\n    padding: 30px 20px;\\n  }\\n  .benefits-info[_ngcontent-%COMP%] {\\n    order: -1;\\n    text-align: center;\\n  }\\n  .logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n}\\nmat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\nmat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\nmat-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "passwordMatchValidator", "control", "password", "get", "confirmPassword", "value", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "snackBar", "hidePassword", "hideConfirmPassword", "isLoading", "registerForm", "group", "username", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "acceptTerms", "requiredTrue", "validators", "onSubmit", "valid", "userData", "register", "subscribe", "next", "response", "open", "duration", "navigate", "error", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_12_listener", "ɵɵtemplate", "RegisterComponent_mat_error_20_Template", "RegisterComponent_mat_error_27_Template", "RegisterComponent_mat_error_28_Template", "RegisterComponent_mat_error_35_Template", "RegisterComponent_mat_error_36_Template", "RegisterComponent_Template_button_click_41_listener", "RegisterComponent_mat_error_44_Template", "RegisterComponent_mat_error_45_Template", "RegisterComponent_Template_button_click_50_listener", "RegisterComponent_mat_error_53_Template", "RegisterComponent_mat_error_54_Template", "RegisterComponent_mat_spinner_65_Template", "RegisterComponent_span_66_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "ɵɵtextInterpolate", "tmp_10_0", "tmp_11_0", "tmp_16_0", "tmp_17_0", "touched", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatButton", "MatIconButton", "i10", "MatIcon", "i11", "MatProgressSpinner", "i12", "MatCheckbox", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\register.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\register.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { AuthService, RegisterRequest } from '../../services/auth.service';\n\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control: AbstractControl): {[key: string]: any} | null {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  \n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return { 'passwordMismatch': true };\n  }\n  return null;\n}\n\n@Component({\n  selector: 'app-register',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.scss']\n})\nexport class RegisterComponent {\n  registerForm: FormGroup;\n  hidePassword = true;\n  hideConfirmPassword = true;\n  isLoading = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, { validators: passwordMatchValidator });\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      \n      const userData: RegisterRequest = {\n        username: this.registerForm.value.username,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword\n      };\n\n      this.authService.register(userData).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', { duration: 3000 });\n          this.router.navigate(['/dashboard']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Registration failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n}\n", "<div class=\"register-container\">\n  <div class=\"register-card-wrapper\">\n    <mat-card class=\"register-card\">\n      <mat-card-header class=\"register-header\">\n        <div class=\"logo\">\n          <mat-icon class=\"logo-icon\">security</mat-icon>\n          <h1>Create Account</h1>\n        </div>\n        <mat-card-subtitle>Join SPT Security Platform</mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n          <div class=\"form-fields\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Full Name</mat-label>\n              <input matInput formControlName=\"fullName\" autocomplete=\"name\">\n              <mat-icon matSuffix>person</mat-icon>\n              <mat-error *ngIf=\"registerForm.get('fullName')?.hasError('required')\">\n                Full name is required\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Email</mat-label>\n              <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n              <mat-icon matSuffix>email</mat-icon>\n              <mat-error *ngIf=\"registerForm.get('email')?.hasError('required')\">\n                Email is required\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.get('email')?.hasError('email')\">\n                Please enter a valid email\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Username</mat-label>\n              <input matInput formControlName=\"username\" autocomplete=\"username\">\n              <mat-icon matSuffix>account_circle</mat-icon>\n              <mat-error *ngIf=\"registerForm.get('username')?.hasError('required')\">\n                Username is required\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.get('username')?.hasError('minlength')\">\n                Username must be at least 3 characters\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Password</mat-label>\n              <input matInput \n                     [type]=\"hidePassword ? 'password' : 'text'\" \n                     formControlName=\"password\"\n                     autocomplete=\"new-password\">\n              <button mat-icon-button matSuffix \n                      (click)=\"hidePassword = !hidePassword\" \n                      [attr.aria-label]=\"'Hide password'\" \n                      [attr.aria-pressed]=\"hidePassword\"\n                      type=\"button\">\n                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n              </button>\n              <mat-error *ngIf=\"registerForm.get('password')?.hasError('required')\">\n                Password is required\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.get('password')?.hasError('minlength')\">\n                Password must be at least 8 characters\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Confirm Password</mat-label>\n              <input matInput \n                     [type]=\"hideConfirmPassword ? 'password' : 'text'\" \n                     formControlName=\"confirmPassword\"\n                     autocomplete=\"new-password\">\n              <button mat-icon-button matSuffix \n                      (click)=\"hideConfirmPassword = !hideConfirmPassword\" \n                      [attr.aria-label]=\"'Hide password'\" \n                      [attr.aria-pressed]=\"hideConfirmPassword\"\n                      type=\"button\">\n                <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n              </button>\n              <mat-error *ngIf=\"registerForm.get('confirmPassword')?.hasError('required')\">\n                Please confirm your password\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched\">\n                Passwords do not match\n              </mat-error>\n            </mat-form-field>\n\n            <div class=\"form-options\">\n              <mat-checkbox formControlName=\"agreeToTerms\">\n                I agree to the <a href=\"#\" class=\"terms-link\">Terms of Service</a> and <a href=\"#\" class=\"terms-link\">Privacy Policy</a>\n              </mat-checkbox>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button mat-raised-button \n                    color=\"primary\" \n                    type=\"submit\" \n                    class=\"register-button\"\n                    [disabled]=\"registerForm.invalid || isLoading\">\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n              <span *ngIf=\"!isLoading\">Create Account</span>\n            </button>\n          </div>\n        </form>\n      </mat-card-content>\n\n      <mat-card-actions class=\"card-actions\">\n        <p>Already have an account? \n          <a routerLink=\"/login\" class=\"login-link\">Sign in</a>\n        </p>\n      </mat-card-actions>\n    </mat-card>\n\n    <div class=\"benefits-info\">\n      <h3>🚀 Why Choose SPT?</h3>\n      <ul>\n        <li>🔍 Advanced vulnerability detection</li>\n        <li>📊 Real-time security monitoring</li>\n        <li>📈 Comprehensive analytics</li>\n        <li>🛡️ Enterprise-grade security</li>\n        <li>🔧 Easy integration</li>\n        <li>📱 Multi-platform support</li>\n      </ul>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAyB,gBAAgB;AAEzG,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;ICOhDC,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA6G;IAC3GD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADzF5D;AACA,SAASE,sBAAsBA,CAACC,OAAwB;EACtD,MAAMC,QAAQ,GAAGD,OAAO,CAACE,GAAG,CAAC,UAAU,CAAC;EACxC,MAAMC,eAAe,GAAGH,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC;EAEtD,IAAID,QAAQ,IAAIE,eAAe,IAAIF,QAAQ,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;IAC3E,OAAO;MAAE,kBAAkB,EAAE;IAAI,CAAE;EACrC;EACA,OAAO,IAAI;AACb;AAoBA,OAAM,MAAOC,iBAAiB;EAM5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,SAAS,GAAG,KAAK;IAQf,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACzCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACkC,KAAK,CAAC,CAAC;MACpDlB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9Df,eAAe,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACgC,QAAQ,CAAC,CAAC;MAC5CG,WAAW,EAAE,CAAC,KAAK,EAAE,CAACnC,UAAU,CAACoC,YAAY,CAAC;KAC/C,EAAE;MAAEC,UAAU,EAAEvB;IAAsB,CAAE,CAAC;EAC5C;EAEAwB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,YAAY,CAACU,KAAK,EAAE;MAC3B,IAAI,CAACX,SAAS,GAAG,IAAI;MAErB,MAAMY,QAAQ,GAAoB;QAChCT,QAAQ,EAAE,IAAI,CAACF,YAAY,CAACV,KAAK,CAACY,QAAQ;QAC1CG,KAAK,EAAE,IAAI,CAACL,YAAY,CAACV,KAAK,CAACe,KAAK;QACpClB,QAAQ,EAAE,IAAI,CAACa,YAAY,CAACV,KAAK,CAACH,QAAQ;QAC1CE,eAAe,EAAE,IAAI,CAACW,YAAY,CAACV,KAAK,CAACD;OAC1C;MAED,IAAI,CAACK,WAAW,CAACkB,QAAQ,CAACD,QAAQ,CAAC,CAACE,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAChB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACH,QAAQ,CAACoB,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAC3F,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACpB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACH,QAAQ,CAACoB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,qBAAqB,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QACzF;OACD,CAAC;IACJ;EACF;;;uCA5CW1B,iBAAiB,EAAAX,EAAA,CAAAyC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3C,EAAA,CAAAyC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7C,EAAA,CAAAyC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA/C,EAAA,CAAAyC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBtC,iBAAiB;MAAAuC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtCpBxD,EALV,CAAAC,cAAA,aAAgC,aACK,kBACD,yBACW,aACrB,kBACY;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/CH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UACpBF,EADoB,CAAAG,YAAA,EAAK,EACnB;UACNH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,kCAA0B;UAC/CF,EAD+C,CAAAG,YAAA,EAAoB,EACjD;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,eACyC;UAAxBD,EAAA,CAAA0D,UAAA,sBAAAC,qDAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAGlD7B,EAFJ,CAAAC,cAAA,cAAyB,yBACiC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAI,SAAA,gBAA+D;UAC/DJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA4D,UAAA,KAAAC,uCAAA,wBAAsE;UAGxE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA0E;UAC1EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAIpCH,EAHA,CAAA4D,UAAA,KAAAE,uCAAA,wBAAmE,KAAAC,uCAAA,wBAGH;UAGlE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAAmE;UACnEJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAI7CH,EAHA,CAAA4D,UAAA,KAAAI,uCAAA,wBAAsE,KAAAC,uCAAA,wBAGC;UAGzEjE,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAGmC;UACnCJ,EAAA,CAAAC,cAAA,kBAIsB;UAHdD,EAAA,CAAA0D,UAAA,mBAAAQ,oDAAA;YAAA,OAAAT,GAAA,CAAAxC,YAAA,IAAAwC,GAAA,CAAAxC,YAAA;UAAA,EAAsC;UAI5CjB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UAITH,EAHA,CAAA4D,UAAA,KAAAO,uCAAA,wBAAsE,KAAAC,uCAAA,wBAGC;UAGzEpE,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAI,SAAA,iBAGmC;UACnCJ,EAAA,CAAAC,cAAA,kBAIsB;UAHdD,EAAA,CAAA0D,UAAA,mBAAAW,oDAAA;YAAA,OAAAZ,GAAA,CAAAvC,mBAAA,IAAAuC,GAAA,CAAAvC,mBAAA;UAAA,EAAoD;UAI1DlB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAyD;UACrEF,EADqE,CAAAG,YAAA,EAAW,EACvE;UAITH,EAHA,CAAA4D,UAAA,KAAAU,uCAAA,wBAA6E,KAAAC,uCAAA,wBAGgC;UAG/GvE,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,wBACqB;UAC3CD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAG1HF,EAH0H,CAAAG,YAAA,EAAI,EAC3G,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,eAA0B,kBAK+B;UAErDD,EADA,CAAA4D,UAAA,KAAAY,yCAAA,0BAA6C,KAAAC,kCAAA,mBACpB;UAIjCzE,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACU;UAGjBH,EADF,CAAAC,cAAA,4BAAuC,SAClC;UAAAD,EAAA,CAAAE,MAAA,iCACD;UAAAF,EAAA,CAAAC,cAAA,aAA0C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAGvDF,EAHuD,CAAAG,YAAA,EAAI,EACnD,EACa,EACV;UAGTH,EADF,CAAAC,cAAA,eAA2B,UACrB;UAAAD,EAAA,CAAAE,MAAA,oCAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEzBH,EADF,CAAAC,cAAA,UAAI,UACE;UAAAD,EAAA,CAAAE,MAAA,qDAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,kDAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,4CAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oDAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,qCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,2CAAyB;UAIrCF,EAJqC,CAAAG,YAAA,EAAK,EAC/B,EACD,EACF,EACF;;;;;;;;;;;;UApHQH,EAAA,CAAA0E,SAAA,IAA0B;UAA1B1E,EAAA,CAAA2E,UAAA,cAAAlB,GAAA,CAAArC,YAAA,CAA0B;UAMdpB,EAAA,CAAA0E,SAAA,GAAwD;UAAxD1E,EAAA,CAAA2E,UAAA,UAAAC,OAAA,GAAAnB,GAAA,CAAArC,YAAA,CAAAZ,GAAA,+BAAAoE,OAAA,CAAAC,QAAA,aAAwD;UASxD7E,EAAA,CAAA0E,SAAA,GAAqD;UAArD1E,EAAA,CAAA2E,UAAA,UAAAG,OAAA,GAAArB,GAAA,CAAArC,YAAA,CAAAZ,GAAA,4BAAAsE,OAAA,CAAAD,QAAA,aAAqD;UAGrD7E,EAAA,CAAA0E,SAAA,EAAkD;UAAlD1E,EAAA,CAAA2E,UAAA,UAAAI,OAAA,GAAAtB,GAAA,CAAArC,YAAA,CAAAZ,GAAA,4BAAAuE,OAAA,CAAAF,QAAA,UAAkD;UASlD7E,EAAA,CAAA0E,SAAA,GAAwD;UAAxD1E,EAAA,CAAA2E,UAAA,UAAAK,OAAA,GAAAvB,GAAA,CAAArC,YAAA,CAAAZ,GAAA,+BAAAwE,OAAA,CAAAH,QAAA,aAAwD;UAGxD7E,EAAA,CAAA0E,SAAA,EAAyD;UAAzD1E,EAAA,CAAA2E,UAAA,UAAAM,OAAA,GAAAxB,GAAA,CAAArC,YAAA,CAAAZ,GAAA,+BAAAyE,OAAA,CAAAJ,QAAA,cAAyD;UAQ9D7E,EAAA,CAAA0E,SAAA,GAA2C;UAA3C1E,EAAA,CAAA2E,UAAA,SAAAlB,GAAA,CAAAxC,YAAA,uBAA2C;UAK1CjB,EAAA,CAAA0E,SAAA,EAAmC;;UAG/B1E,EAAA,CAAA0E,SAAA,GAAkD;UAAlD1E,EAAA,CAAAkF,iBAAA,CAAAzB,GAAA,CAAAxC,YAAA,mCAAkD;UAElDjB,EAAA,CAAA0E,SAAA,EAAwD;UAAxD1E,EAAA,CAAA2E,UAAA,UAAAQ,QAAA,GAAA1B,GAAA,CAAArC,YAAA,CAAAZ,GAAA,+BAAA2E,QAAA,CAAAN,QAAA,aAAwD;UAGxD7E,EAAA,CAAA0E,SAAA,EAAyD;UAAzD1E,EAAA,CAAA2E,UAAA,UAAAS,QAAA,GAAA3B,GAAA,CAAArC,YAAA,CAAAZ,GAAA,+BAAA4E,QAAA,CAAAP,QAAA,cAAyD;UAQ9D7E,EAAA,CAAA0E,SAAA,GAAkD;UAAlD1E,EAAA,CAAA2E,UAAA,SAAAlB,GAAA,CAAAvC,mBAAA,uBAAkD;UAKjDlB,EAAA,CAAA0E,SAAA,EAAmC;;UAG/B1E,EAAA,CAAA0E,SAAA,GAAyD;UAAzD1E,EAAA,CAAAkF,iBAAA,CAAAzB,GAAA,CAAAvC,mBAAA,mCAAyD;UAEzDlB,EAAA,CAAA0E,SAAA,EAA+D;UAA/D1E,EAAA,CAAA2E,UAAA,UAAAU,QAAA,GAAA5B,GAAA,CAAArC,YAAA,CAAAZ,GAAA,sCAAA6E,QAAA,CAAAR,QAAA,aAA+D;UAG/D7E,EAAA,CAAA0E,SAAA,EAA+F;UAA/F1E,EAAA,CAAA2E,UAAA,SAAAlB,GAAA,CAAArC,YAAA,CAAAyD,QAAA,0BAAAS,QAAA,GAAA7B,GAAA,CAAArC,YAAA,CAAAZ,GAAA,sCAAA8E,QAAA,CAAAC,OAAA,EAA+F;UAiBrGvF,EAAA,CAAA0E,SAAA,IAA8C;UAA9C1E,EAAA,CAAA2E,UAAA,aAAAlB,GAAA,CAAArC,YAAA,CAAAoE,OAAA,IAAA/B,GAAA,CAAAtC,SAAA,CAA8C;UACtCnB,EAAA,CAAA0E,SAAA,EAAe;UAAf1E,EAAA,CAAA2E,UAAA,SAAAlB,GAAA,CAAAtC,SAAA,CAAe;UACtBnB,EAAA,CAAA0E,SAAA,EAAgB;UAAhB1E,EAAA,CAAA2E,UAAA,UAAAlB,GAAA,CAAAtC,SAAA,CAAgB;;;qBD1EjC9B,YAAY,EAAAoG,EAAA,CAAAC,IAAA,EACZpG,mBAAmB,EAAAoD,EAAA,CAAAiD,aAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAlD,EAAA,CAAAmD,eAAA,EAAAnD,EAAA,CAAAoD,oBAAA,EAAApD,EAAA,CAAAqD,kBAAA,EAAArD,EAAA,CAAAsD,eAAA,EACnBxG,aAAa,EAAAyG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EACb7G,kBAAkB,EAAA8G,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBjH,cAAc,EAAAkH,EAAA,CAAAC,QAAA,EACdlH,eAAe,EAAAmH,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfpH,aAAa,EAAAqH,GAAA,CAAAC,OAAA,EACbrH,wBAAwB,EAAAsH,GAAA,CAAAC,kBAAA,EACxBtH,iBAAiB,EACjBC,iBAAiB,EAAAsH,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}