{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output, Renderer2, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { EMPTY, Subject } from 'rxjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { auditTime } from 'rxjs/operators';\n\n/** Component used to load the structural styles of the text field. */\nclass _CdkTextFieldStyleLoader {\n  static ɵfac = function _CdkTextFieldStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkTextFieldStyleLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _CdkTextFieldStyleLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"cdk-text-field-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _CdkTextFieldStyleLoader_Template(rf, ctx) {},\n    styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkTextFieldStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-text-field-style-loader': ''\n      },\n      styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = {\n  passive: true\n};\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  _monitoredElements = new Map();\n  constructor() {}\n  monitor(elementOrRef) {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n    this._styleLoader.load(_CdkTextFieldStyleLoader);\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      return info.subject;\n    }\n    const subject = new Subject();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = event => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => subject.next({\n          target: event.target,\n          isAutofilled: true\n        }));\n      } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() => subject.next({\n          target: event.target,\n          isAutofilled: false\n        }));\n      }\n    };\n    const unlisten = this._ngZone.runOutsideAngular(() => {\n      element.classList.add('cdk-text-field-autofill-monitored');\n      return _bindEventWithOptions(this._renderer, element, 'animationstart', listener, listenerOptions);\n    });\n    this._monitoredElements.set(element, {\n      subject,\n      unlisten\n    });\n    return subject;\n  }\n  stopMonitoring(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  static ɵfac = function AutofillMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AutofillMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AutofillMonitor,\n    factory: AutofillMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofillMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n  _elementRef = inject(ElementRef);\n  _autofillMonitor = inject(AutofillMonitor);\n  /** Emits when the autofill state of the element changes. */\n  cdkAutofill = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n  }\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n  static ɵfac = function CdkAutofill_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAutofill)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAutofill,\n    selectors: [[\"\", \"cdkAutofill\", \"\"]],\n    outputs: {\n      cdkAutofill: \"cdkAutofill\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAutofill, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAutofill]'\n    }]\n  }], () => [], {\n    cdkAutofill: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(Renderer2);\n  _resizeEvents = new Subject();\n  /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n  _previousValue;\n  _initialHeight;\n  _destroyed = new Subject();\n  _listenerCleanups;\n  _minRows;\n  _maxRows;\n  _enabled = true;\n  /**\n   * Value of minRows as of last resize. If the minRows has decreased, the\n   * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n   * does not have the same problem because it does not affect the textarea's scrollHeight.\n   */\n  _previousMinRows = -1;\n  _textareaElement;\n  /** Minimum amount of rows in the textarea. */\n  get minRows() {\n    return this._minRows;\n  }\n  set minRows(value) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n  /** Maximum amount of rows in the textarea. */\n  get maxRows() {\n    return this._maxRows;\n  }\n  set maxRows(value) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n  /** Whether autosizing is enabled or not */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n  get placeholder() {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value) {\n    this._cachedPlaceholderHeight = undefined;\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n    this._cacheTextareaPlaceholderHeight();\n  }\n  /** Cached height of a textarea with a single row. */\n  _cachedLineHeight;\n  /** Cached height of a textarea with only the placeholder. */\n  _cachedPlaceholderHeight;\n  /** Cached scroll top of a textarea */\n  _cachedScrollTop;\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  _hasFocus;\n  _isViewInited = false;\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_CdkTextFieldStyleLoader);\n    this._textareaElement = this._elementRef.nativeElement;\n  }\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight() {\n    const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight() {\n    const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n      this._ngZone.runOutsideAngular(() => {\n        this._listenerCleanups = [this._renderer.listen('window', 'resize', () => this._resizeEvents.next()), this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent), this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent)];\n        this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n          // Clear the cached heights since the styles can change\n          // when the window is resized (e.g. by media queries).\n          this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n          this.resizeToFitContent(true);\n        });\n      });\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n  ngOnDestroy() {\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n    this._resizeEvents.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  _cacheTextareaLineHeight() {\n    if (this._cachedLineHeight) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    const textareaClone = this._textareaElement.cloneNode(false);\n    const cloneStyles = textareaClone.style;\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    cloneStyles.position = 'absolute';\n    cloneStyles.visibility = 'hidden';\n    cloneStyles.border = 'none';\n    cloneStyles.padding = '0';\n    cloneStyles.height = '';\n    cloneStyles.minHeight = '';\n    cloneStyles.maxHeight = '';\n    // App styles might be messing with the height through the positioning properties.\n    cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    cloneStyles.overflow = 'hidden';\n    this._textareaElement.parentNode.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n  _measureScrollHeight() {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n    return scrollHeight;\n  }\n  _cacheTextareaPlaceholderHeight() {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n    const value = this._textareaElement.value;\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n  /** Handles `focus` and `blur` events. */\n  _handleFocusEvent = event => {\n    this._hasFocus = event.type === 'focus';\n  };\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n    this._cachedScrollTop = this._textareaElement.scrollTop;\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n    const textarea = this._elementRef.nativeElement;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  _scrollToCaretPosition(textarea) {\n    const {\n      selectionStart,\n      selectionEnd\n    } = textarea;\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n      textarea.scrollTop = this._cachedScrollTop;\n    }\n  }\n  static ɵfac = function CdkTextareaAutosize_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTextareaAutosize)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTextareaAutosize,\n    selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n    hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n    hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n          return ctx._noopInputHandler();\n        });\n      }\n    },\n    inputs: {\n      minRows: [0, \"cdkAutosizeMinRows\", \"minRows\"],\n      maxRows: [0, \"cdkAutosizeMaxRows\", \"maxRows\"],\n      enabled: [2, \"cdkTextareaAutosize\", \"enabled\", booleanAttribute],\n      placeholder: \"placeholder\"\n    },\n    exportAs: [\"cdkTextareaAutosize\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextareaAutosize, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[cdkTextareaAutosize]',\n      exportAs: 'cdkTextareaAutosize',\n      host: {\n        'class': 'cdk-textarea-autosize',\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        'rows': '1',\n        '(input)': '_noopInputHandler()'\n      }\n    }]\n  }], () => [], {\n    minRows: [{\n      type: Input,\n      args: ['cdkAutosizeMinRows']\n    }],\n    maxRows: [{\n      type: Input,\n      args: ['cdkAutosizeMaxRows']\n    }],\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTextareaAutosize',\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\nclass TextFieldModule {\n  static ɵfac = function TextFieldModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TextFieldModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TextFieldModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    }]\n  }], null, null);\n})();\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "EventEmitter", "Directive", "Output", "Renderer2", "booleanAttribute", "Input", "NgModule", "EMPTY", "Subject", "P", "Platform", "_", "_CdkPrivateStyleLoader", "_bindEventWithOptions", "a", "coerceElement", "c", "coerceNumberProperty", "DOCUMENT", "auditTime", "_CdkTextFieldStyleLoader", "ɵfac", "_CdkTextFieldStyleLoader_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "decls", "vars", "template", "_CdkTextFieldStyleLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "OnPush", "None", "host", "listenerOptions", "passive", "AutofillMonitor", "_platform", "_ngZone", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_styleLoader", "_monitoredElements", "Map", "constructor", "monitor", "elementOrRef", "<PERSON><PERSON><PERSON><PERSON>", "load", "element", "info", "get", "subject", "cssClass", "listener", "event", "animationName", "classList", "contains", "add", "run", "next", "target", "isAutofilled", "remove", "unlisten", "runOutsideAngular", "set", "stopMonitoring", "complete", "delete", "ngOnDestroy", "for<PERSON>ach", "_info", "AutofillMonitor_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "CdkAutofill", "_elementRef", "_autofillMonitor", "cdkAutofill", "ngOnInit", "subscribe", "emit", "CdkAutofill_Factory", "ɵdir", "ɵɵdefineDirective", "outputs", "selector", "CdkTextareaAutosize", "_resizeEvents", "_previousValue", "_initialHeight", "_destroyed", "_listenerCleanups", "_minRows", "_maxRows", "_enabled", "_previousMinRows", "_textareaElement", "minRows", "value", "_setMinHeight", "maxRows", "_setMaxHeight", "enabled", "resizeToFitContent", "reset", "placeholder", "_cachedPlaceholderHeight", "undefined", "setAttribute", "removeAttribute", "_cacheTextareaPlaceholderHeight", "_cachedLineHeight", "_cachedScrollTop", "_document", "optional", "_hasFocus", "_isViewInited", "<PERSON><PERSON><PERSON><PERSON>", "nativeElement", "minHeight", "style", "maxHeight", "ngAfterViewInit", "height", "listen", "_handleFocusEvent", "pipe", "cleanup", "_cacheTextareaLineHeight", "textareaClone", "cloneNode", "cloneStyles", "rows", "position", "visibility", "border", "padding", "top", "bottom", "left", "right", "overflow", "parentNode", "append<PERSON><PERSON><PERSON>", "clientHeight", "_measureScrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "marginBottom", "isFirefox", "FIREFOX", "needsMarginFiller", "measuringClass", "scrollHeight", "ngDoCheck", "force", "scrollTop", "textarea", "Math", "max", "requestAnimationFrame", "_scrollToCaretPosition", "setTimeout", "_noopInputHandler", "selectionStart", "selectionEnd", "isStopped", "setSelectionRange", "CdkTextareaAutosize_Factory", "hostBindings", "CdkTextareaAutosize_HostBindings", "ɵɵlistener", "CdkTextareaAutosize_input_HostBindingHandler", "inputs", "exportAs", "alias", "transform", "TextFieldModule", "TextFieldModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["D:/TGI/Blockchain.SPT/frontend/node_modules/@angular/cdk/fesm2022/text-field.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output, Renderer2, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { EMPTY, Subject } from 'rxjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { auditTime } from 'rxjs/operators';\n\n/** Component used to load the structural styles of the text field. */\nclass _CdkTextFieldStyleLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkTextFieldStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _CdkTextFieldStyleLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"cdk-text-field-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkTextFieldStyleLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'cdk-text-field-style-loader': '' }, styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"] }]\n        }] });\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = { passive: true };\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    _monitoredElements = new Map();\n    constructor() { }\n    monitor(elementOrRef) {\n        if (!this._platform.isBrowser) {\n            return EMPTY;\n        }\n        this._styleLoader.load(_CdkTextFieldStyleLoader);\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            return info.subject;\n        }\n        const subject = new Subject();\n        const cssClass = 'cdk-text-field-autofilled';\n        const listener = (event) => {\n            // Animation events fire on initial element render, we check for the presence of the autofill\n            // CSS class to make sure this is a real change in state, not just the initial render before\n            // we fire off events.\n            if (event.animationName === 'cdk-text-field-autofill-start' &&\n                !element.classList.contains(cssClass)) {\n                element.classList.add(cssClass);\n                this._ngZone.run(() => subject.next({ target: event.target, isAutofilled: true }));\n            }\n            else if (event.animationName === 'cdk-text-field-autofill-end' &&\n                element.classList.contains(cssClass)) {\n                element.classList.remove(cssClass);\n                this._ngZone.run(() => subject.next({ target: event.target, isAutofilled: false }));\n            }\n        };\n        const unlisten = this._ngZone.runOutsideAngular(() => {\n            element.classList.add('cdk-text-field-autofill-monitored');\n            return _bindEventWithOptions(this._renderer, element, 'animationstart', listener, listenerOptions);\n        });\n        this._monitoredElements.set(element, { subject, unlisten });\n        return subject;\n    }\n    stopMonitoring(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            info.unlisten();\n            info.subject.complete();\n            element.classList.remove('cdk-text-field-autofill-monitored');\n            element.classList.remove('cdk-text-field-autofilled');\n            this._monitoredElements.delete(element);\n        }\n    }\n    ngOnDestroy() {\n        this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AutofillMonitor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AutofillMonitor, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AutofillMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n    _elementRef = inject(ElementRef);\n    _autofillMonitor = inject(AutofillMonitor);\n    /** Emits when the autofill state of the element changes. */\n    cdkAutofill = new EventEmitter();\n    constructor() { }\n    ngOnInit() {\n        this._autofillMonitor\n            .monitor(this._elementRef)\n            .subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n        this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAutofill, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkAutofill, isStandalone: true, selector: \"[cdkAutofill]\", outputs: { cdkAutofill: \"cdkAutofill\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAutofill, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAutofill]',\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkAutofill: [{\n                type: Output\n            }] } });\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _resizeEvents = new Subject();\n    /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n    _previousValue;\n    _initialHeight;\n    _destroyed = new Subject();\n    _listenerCleanups;\n    _minRows;\n    _maxRows;\n    _enabled = true;\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n    _previousMinRows = -1;\n    _textareaElement;\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n        return this._minRows;\n    }\n    set minRows(value) {\n        this._minRows = coerceNumberProperty(value);\n        this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n        return this._maxRows;\n    }\n    set maxRows(value) {\n        this._maxRows = coerceNumberProperty(value);\n        this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        // Only act if the actual value changed. This specifically helps to not run\n        // resizeToFitContent too early (i.e. before ngAfterViewInit)\n        if (this._enabled !== value) {\n            (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n        }\n    }\n    get placeholder() {\n        return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n        this._cachedPlaceholderHeight = undefined;\n        if (value) {\n            this._textareaElement.setAttribute('placeholder', value);\n        }\n        else {\n            this._textareaElement.removeAttribute('placeholder');\n        }\n        this._cacheTextareaPlaceholderHeight();\n    }\n    /** Cached height of a textarea with a single row. */\n    _cachedLineHeight;\n    /** Cached height of a textarea with only the placeholder. */\n    _cachedPlaceholderHeight;\n    /** Cached scroll top of a textarea */\n    _cachedScrollTop;\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    _hasFocus;\n    _isViewInited = false;\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_CdkTextFieldStyleLoader);\n        this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n        const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n        if (minHeight) {\n            this._textareaElement.style.minHeight = minHeight;\n        }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n        const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n        if (maxHeight) {\n            this._textareaElement.style.maxHeight = maxHeight;\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            // Remember the height which we started with in case autosizing is disabled\n            this._initialHeight = this._textareaElement.style.height;\n            this.resizeToFitContent();\n            this._ngZone.runOutsideAngular(() => {\n                this._listenerCleanups = [\n                    this._renderer.listen('window', 'resize', () => this._resizeEvents.next()),\n                    this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent),\n                    this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent),\n                ];\n                this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n                    // Clear the cached heights since the styles can change\n                    // when the window is resized (e.g. by media queries).\n                    this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n                    this.resizeToFitContent(true);\n                });\n            });\n            this._isViewInited = true;\n            this.resizeToFitContent(true);\n        }\n    }\n    ngOnDestroy() {\n        this._listenerCleanups?.forEach(cleanup => cleanup());\n        this._resizeEvents.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n        if (this._cachedLineHeight) {\n            return;\n        }\n        // Use a clone element because we have to override some styles.\n        const textareaClone = this._textareaElement.cloneNode(false);\n        const cloneStyles = textareaClone.style;\n        textareaClone.rows = 1;\n        // Use `position: absolute` so that this doesn't cause a browser layout and use\n        // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n        // would affect the height.\n        cloneStyles.position = 'absolute';\n        cloneStyles.visibility = 'hidden';\n        cloneStyles.border = 'none';\n        cloneStyles.padding = '0';\n        cloneStyles.height = '';\n        cloneStyles.minHeight = '';\n        cloneStyles.maxHeight = '';\n        // App styles might be messing with the height through the positioning properties.\n        cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n        // In Firefox it happens that textarea elements are always bigger than the specified amount\n        // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n        // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n        // to hidden. This ensures that there is no invalid calculation of the line height.\n        // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n        cloneStyles.overflow = 'hidden';\n        this._textareaElement.parentNode.appendChild(textareaClone);\n        this._cachedLineHeight = textareaClone.clientHeight;\n        textareaClone.remove();\n        // Min and max heights have to be re-calculated if the cached line height changes\n        this._setMinHeight();\n        this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n        const element = this._textareaElement;\n        const previousMargin = element.style.marginBottom || '';\n        const isFirefox = this._platform.FIREFOX;\n        const needsMarginFiller = isFirefox && this._hasFocus;\n        const measuringClass = isFirefox\n            ? 'cdk-textarea-autosize-measuring-firefox'\n            : 'cdk-textarea-autosize-measuring';\n        // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n        // work around it by assigning a temporary margin with the same height as the `textarea` so that\n        // it occupies the same amount of space. See #23233.\n        if (needsMarginFiller) {\n            element.style.marginBottom = `${element.clientHeight}px`;\n        }\n        // Reset the textarea height to auto in order to shrink back to its default size.\n        // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n        element.classList.add(measuringClass);\n        // The measuring class includes a 2px padding to workaround an issue with Chrome,\n        // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n        const scrollHeight = element.scrollHeight - 4;\n        element.classList.remove(measuringClass);\n        if (needsMarginFiller) {\n            element.style.marginBottom = previousMargin;\n        }\n        return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n        if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n            return;\n        }\n        if (!this.placeholder) {\n            this._cachedPlaceholderHeight = 0;\n            return;\n        }\n        const value = this._textareaElement.value;\n        this._textareaElement.value = this._textareaElement.placeholder;\n        this._cachedPlaceholderHeight = this._measureScrollHeight();\n        this._textareaElement.value = value;\n    }\n    /** Handles `focus` and `blur` events. */\n    _handleFocusEvent = (event) => {\n        this._hasFocus = event.type === 'focus';\n    };\n    ngDoCheck() {\n        if (this._platform.isBrowser) {\n            this.resizeToFitContent();\n        }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n        // If autosizing is disabled, just skip everything else\n        if (!this._enabled) {\n            return;\n        }\n        this._cacheTextareaLineHeight();\n        this._cacheTextareaPlaceholderHeight();\n        this._cachedScrollTop = this._textareaElement.scrollTop;\n        // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n        // in checking the height of the textarea.\n        if (!this._cachedLineHeight) {\n            return;\n        }\n        const textarea = this._elementRef.nativeElement;\n        const value = textarea.value;\n        // Only resize if the value or minRows have changed since these calculations can be expensive.\n        if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n            return;\n        }\n        const scrollHeight = this._measureScrollHeight();\n        const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n        // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n        textarea.style.height = `${height}px`;\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame !== 'undefined') {\n                requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n            }\n            else {\n                setTimeout(() => this._scrollToCaretPosition(textarea));\n            }\n        });\n        this._previousValue = value;\n        this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n        // Do not try to change the textarea, if the initialHeight has not been determined yet\n        // This might potentially remove styles when reset() is called before ngAfterViewInit\n        if (this._initialHeight !== undefined) {\n            this._textareaElement.style.height = this._initialHeight;\n        }\n    }\n    _noopInputHandler() {\n        // no-op handler that ensures we're running change detection on input events.\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n        const { selectionStart, selectionEnd } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this._destroyed.isStopped && this._hasFocus) {\n            textarea.setSelectionRange(selectionStart, selectionEnd);\n            textarea.scrollTop = this._cachedScrollTop;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTextareaAutosize, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkTextareaAutosize, isStandalone: true, selector: \"textarea[cdkTextareaAutosize]\", inputs: { minRows: [\"cdkAutosizeMinRows\", \"minRows\"], maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"], enabled: [\"cdkTextareaAutosize\", \"enabled\", booleanAttribute], placeholder: \"placeholder\" }, host: { attributes: { \"rows\": \"1\" }, listeners: { \"input\": \"_noopInputHandler()\" }, classAttribute: \"cdk-textarea-autosize\" }, exportAs: [\"cdkTextareaAutosize\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTextareaAutosize, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'textarea[cdkTextareaAutosize]',\n                    exportAs: 'cdkTextareaAutosize',\n                    host: {\n                        'class': 'cdk-textarea-autosize',\n                        // Textarea elements that have the directive applied should have a single row by default.\n                        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n                        'rows': '1',\n                        '(input)': '_noopInputHandler()',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { minRows: [{\n                type: Input,\n                args: ['cdkAutosizeMinRows']\n            }], maxRows: [{\n                type: Input,\n                args: ['cdkAutosizeMaxRows']\n            }], enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTextareaAutosize', transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }] } });\n\nclass TextFieldModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule, imports: [CdkAutofill, CdkTextareaAutosize], exports: [CdkAutofill, CdkTextareaAutosize] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkAutofill, CdkTextareaAutosize],\n                    exports: [CdkAutofill, CdkTextareaAutosize],\n                }]\n        }] });\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC9N,SAASC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACrC,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,6BAA6B;AACzE,SAASD,CAAC,IAAIE,qBAAqB,QAAQ,wCAAwC;AACnF,SAASC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,oBAAoB,QAAQ,wBAAwB;AACtF,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA,MAAMC,wBAAwB,CAAC;EAC3B,OAAOC,IAAI,YAAAC,iCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,wBAAwB;EAAA;EAC3H,OAAOI,IAAI,kBAD8EjC,EAAE,CAAAkC,iBAAA;IAAAC,IAAA,EACJN,wBAAwB;IAAAO,SAAA;IAAAC,SAAA,kCAAqG,EAAE;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC1N;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F/C,EAAE,CAAAgD,iBAAA,CAGJnB,wBAAwB,EAAc,CAAC;IACtHM,IAAI,EAAElC,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAET,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAE5C,uBAAuB,CAACgD,MAAM;MAAEL,aAAa,EAAE1C,iBAAiB,CAACgD,IAAI;MAAEC,IAAI,EAAE;QAAE,6BAA6B,EAAE;MAAG,CAAC;MAAER,MAAM,EAAE,CAAC,2mBAA2mB;IAAE,CAAC;EACvxB,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMS,eAAe,GAAG;EAAEC,OAAO,EAAE;AAAK,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,SAAS,GAAGpD,MAAM,CAACe,QAAQ,CAAC;EAC5BsC,OAAO,GAAGrD,MAAM,CAACC,MAAM,CAAC;EACxBqD,SAAS,GAAGtD,MAAM,CAACE,gBAAgB,CAAC,CAACqD,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/DC,YAAY,GAAGxD,MAAM,CAACiB,sBAAsB,CAAC;EAC7CwC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9BC,WAAWA,CAAA,EAAG,CAAE;EAChBC,OAAOA,CAACC,YAAY,EAAE;IAClB,IAAI,CAAC,IAAI,CAACT,SAAS,CAACU,SAAS,EAAE;MAC3B,OAAOlD,KAAK;IAChB;IACA,IAAI,CAAC4C,YAAY,CAACO,IAAI,CAACtC,wBAAwB,CAAC;IAChD,MAAMuC,OAAO,GAAG5C,aAAa,CAACyC,YAAY,CAAC;IAC3C,MAAMI,IAAI,GAAG,IAAI,CAACR,kBAAkB,CAACS,GAAG,CAACF,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACN,OAAOA,IAAI,CAACE,OAAO;IACvB;IACA,MAAMA,OAAO,GAAG,IAAItD,OAAO,CAAC,CAAC;IAC7B,MAAMuD,QAAQ,GAAG,2BAA2B;IAC5C,MAAMC,QAAQ,GAAIC,KAAK,IAAK;MACxB;MACA;MACA;MACA,IAAIA,KAAK,CAACC,aAAa,KAAK,+BAA+B,IACvD,CAACP,OAAO,CAACQ,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACvCJ,OAAO,CAACQ,SAAS,CAACE,GAAG,CAACN,QAAQ,CAAC;QAC/B,IAAI,CAACf,OAAO,CAACsB,GAAG,CAAC,MAAMR,OAAO,CAACS,IAAI,CAAC;UAAEC,MAAM,EAAEP,KAAK,CAACO,MAAM;UAAEC,YAAY,EAAE;QAAK,CAAC,CAAC,CAAC;MACtF,CAAC,MACI,IAAIR,KAAK,CAACC,aAAa,KAAK,6BAA6B,IAC1DP,OAAO,CAACQ,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACtCJ,OAAO,CAACQ,SAAS,CAACO,MAAM,CAACX,QAAQ,CAAC;QAClC,IAAI,CAACf,OAAO,CAACsB,GAAG,CAAC,MAAMR,OAAO,CAACS,IAAI,CAAC;UAAEC,MAAM,EAAEP,KAAK,CAACO,MAAM;UAAEC,YAAY,EAAE;QAAM,CAAC,CAAC,CAAC;MACvF;IACJ,CAAC;IACD,MAAME,QAAQ,GAAG,IAAI,CAAC3B,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;MAClDjB,OAAO,CAACQ,SAAS,CAACE,GAAG,CAAC,mCAAmC,CAAC;MAC1D,OAAOxD,qBAAqB,CAAC,IAAI,CAACoC,SAAS,EAAEU,OAAO,EAAE,gBAAgB,EAAEK,QAAQ,EAAEpB,eAAe,CAAC;IACtG,CAAC,CAAC;IACF,IAAI,CAACQ,kBAAkB,CAACyB,GAAG,CAAClB,OAAO,EAAE;MAAEG,OAAO;MAAEa;IAAS,CAAC,CAAC;IAC3D,OAAOb,OAAO;EAClB;EACAgB,cAAcA,CAACtB,YAAY,EAAE;IACzB,MAAMG,OAAO,GAAG5C,aAAa,CAACyC,YAAY,CAAC;IAC3C,MAAMI,IAAI,GAAG,IAAI,CAACR,kBAAkB,CAACS,GAAG,CAACF,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACNA,IAAI,CAACe,QAAQ,CAAC,CAAC;MACff,IAAI,CAACE,OAAO,CAACiB,QAAQ,CAAC,CAAC;MACvBpB,OAAO,CAACQ,SAAS,CAACO,MAAM,CAAC,mCAAmC,CAAC;MAC7Df,OAAO,CAACQ,SAAS,CAACO,MAAM,CAAC,2BAA2B,CAAC;MACrD,IAAI,CAACtB,kBAAkB,CAAC4B,MAAM,CAACrB,OAAO,CAAC;IAC3C;EACJ;EACAsB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7B,kBAAkB,CAAC8B,OAAO,CAAC,CAACC,KAAK,EAAExB,OAAO,KAAK,IAAI,CAACmB,cAAc,CAACnB,OAAO,CAAC,CAAC;EACrF;EACA,OAAOtC,IAAI,YAAA+D,wBAAA7D,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuB,eAAe;EAAA;EAClH,OAAOuC,KAAK,kBAvE6E9F,EAAE,CAAA+F,kBAAA;IAAAC,KAAA,EAuEYzC,eAAe;IAAA0C,OAAA,EAAf1C,eAAe,CAAAzB,IAAA;IAAAoE,UAAA,EAAc;EAAM;AAC9I;AACA;EAAA,QAAAnD,SAAA,oBAAAA,SAAA,KAzE6F/C,EAAE,CAAAgD,iBAAA,CAyEJO,eAAe,EAAc,CAAC;IAC7GpB,IAAI,EAAE5B,UAAU;IAChB0C,IAAI,EAAE,CAAC;MAAEiD,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMC,WAAW,CAAC;EACdC,WAAW,GAAGhG,MAAM,CAACI,UAAU,CAAC;EAChC6F,gBAAgB,GAAGjG,MAAM,CAACmD,eAAe,CAAC;EAC1C;EACA+C,WAAW,GAAG,IAAI7F,YAAY,CAAC,CAAC;EAChCsD,WAAWA,CAAA,EAAG,CAAE;EAChBwC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,gBAAgB,CAChBrC,OAAO,CAAC,IAAI,CAACoC,WAAW,CAAC,CACzBI,SAAS,CAAC9B,KAAK,IAAI,IAAI,CAAC4B,WAAW,CAACG,IAAI,CAAC/B,KAAK,CAAC,CAAC;EACzD;EACAgB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACW,gBAAgB,CAACd,cAAc,CAAC,IAAI,CAACa,WAAW,CAAC;EAC1D;EACA,OAAOtE,IAAI,YAAA4E,oBAAA1E,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmE,WAAW;EAAA;EAC9G,OAAOQ,IAAI,kBA7F8E3G,EAAE,CAAA4G,iBAAA;IAAAzE,IAAA,EA6FJgE,WAAW;IAAA/D,SAAA;IAAAyE,OAAA;MAAAP,WAAA;IAAA;EAAA;AACtG;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KA/F6F/C,EAAE,CAAAgD,iBAAA,CA+FJmD,WAAW,EAAc,CAAC;IACzGhE,IAAI,EAAEzB,SAAS;IACfuC,IAAI,EAAE,CAAC;MACC6D,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAER,WAAW,EAAE,CAAC;MACtDnE,IAAI,EAAExB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMoG,mBAAmB,CAAC;EACtBX,WAAW,GAAGhG,MAAM,CAACI,UAAU,CAAC;EAChCgD,SAAS,GAAGpD,MAAM,CAACe,QAAQ,CAAC;EAC5BsC,OAAO,GAAGrD,MAAM,CAACC,MAAM,CAAC;EACxBqD,SAAS,GAAGtD,MAAM,CAACQ,SAAS,CAAC;EAC7BoG,aAAa,GAAG,IAAI/F,OAAO,CAAC,CAAC;EAC7B;EACAgG,cAAc;EACdC,cAAc;EACdC,UAAU,GAAG,IAAIlG,OAAO,CAAC,CAAC;EAC1BmG,iBAAiB;EACjBC,QAAQ;EACRC,QAAQ;EACRC,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;AACA;AACA;EACIC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,gBAAgB;EAChB;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACL,QAAQ;EACxB;EACA,IAAIK,OAAOA,CAACC,KAAK,EAAE;IACf,IAAI,CAACN,QAAQ,GAAG3F,oBAAoB,CAACiG,KAAK,CAAC;IAC3C,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACP,QAAQ;EACxB;EACA,IAAIO,OAAOA,CAACF,KAAK,EAAE;IACf,IAAI,CAACL,QAAQ,GAAG5F,oBAAoB,CAACiG,KAAK,CAAC;IAC3C,IAAI,CAACG,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACR,QAAQ;EACxB;EACA,IAAIQ,OAAOA,CAACJ,KAAK,EAAE;IACf;IACA;IACA,IAAI,IAAI,CAACJ,QAAQ,KAAKI,KAAK,EAAE;MACzB,CAAC,IAAI,CAACJ,QAAQ,GAAGI,KAAK,IAAI,IAAI,CAACK,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;IAC1E;EACJ;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACT,gBAAgB,CAACS,WAAW;EAC5C;EACA,IAAIA,WAAWA,CAACP,KAAK,EAAE;IACnB,IAAI,CAACQ,wBAAwB,GAAGC,SAAS;IACzC,IAAIT,KAAK,EAAE;MACP,IAAI,CAACF,gBAAgB,CAACY,YAAY,CAAC,aAAa,EAAEV,KAAK,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACF,gBAAgB,CAACa,eAAe,CAAC,aAAa,CAAC;IACxD;IACA,IAAI,CAACC,+BAA+B,CAAC,CAAC;EAC1C;EACA;EACAC,iBAAiB;EACjB;EACAL,wBAAwB;EACxB;EACAM,gBAAgB;EAChB;EACAC,SAAS,GAAGtI,MAAM,CAACuB,QAAQ,EAAE;IAAEgH,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChDC,SAAS;EACTC,aAAa,GAAG,KAAK;EACrB9E,WAAWA,CAAA,EAAG;IACV,MAAM+E,WAAW,GAAG1I,MAAM,CAACiB,sBAAsB,CAAC;IAClDyH,WAAW,CAAC3E,IAAI,CAACtC,wBAAwB,CAAC;IAC1C,IAAI,CAAC4F,gBAAgB,GAAG,IAAI,CAACrB,WAAW,CAAC2C,aAAa;EAC1D;EACA;EACAnB,aAAaA,CAAA,EAAG;IACZ,MAAMoB,SAAS,GAAG,IAAI,CAACtB,OAAO,IAAI,IAAI,CAACc,iBAAiB,GAAG,GAAG,IAAI,CAACd,OAAO,GAAG,IAAI,CAACc,iBAAiB,IAAI,GAAG,IAAI;IAC9G,IAAIQ,SAAS,EAAE;MACX,IAAI,CAACvB,gBAAgB,CAACwB,KAAK,CAACD,SAAS,GAAGA,SAAS;IACrD;EACJ;EACA;EACAlB,aAAaA,CAAA,EAAG;IACZ,MAAMoB,SAAS,GAAG,IAAI,CAACrB,OAAO,IAAI,IAAI,CAACW,iBAAiB,GAAG,GAAG,IAAI,CAACX,OAAO,GAAG,IAAI,CAACW,iBAAiB,IAAI,GAAG,IAAI;IAC9G,IAAIU,SAAS,EAAE;MACX,IAAI,CAACzB,gBAAgB,CAACwB,KAAK,CAACC,SAAS,GAAGA,SAAS;IACrD;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC3F,SAAS,CAACU,SAAS,EAAE;MAC1B;MACA,IAAI,CAACgD,cAAc,GAAG,IAAI,CAACO,gBAAgB,CAACwB,KAAK,CAACG,MAAM;MACxD,IAAI,CAACpB,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACvE,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAC+B,iBAAiB,GAAG,CACrB,IAAI,CAAC1D,SAAS,CAAC2F,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,CAACrC,aAAa,CAAChC,IAAI,CAAC,CAAC,CAAC,EAC1E,IAAI,CAACtB,SAAS,CAAC2F,MAAM,CAAC,IAAI,CAAC5B,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC6B,iBAAiB,CAAC,EAC7E,IAAI,CAAC5F,SAAS,CAAC2F,MAAM,CAAC,IAAI,CAAC5B,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC6B,iBAAiB,CAAC,CAC/E;QACD,IAAI,CAACtC,aAAa,CAACuC,IAAI,CAAC3H,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC4E,SAAS,CAAC,MAAM;UACnD;UACA;UACA,IAAI,CAACgC,iBAAiB,GAAG,IAAI,CAACL,wBAAwB,GAAGC,SAAS;UAClE,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAAC;QACjC,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACa,aAAa,GAAG,IAAI;MACzB,IAAI,CAACb,kBAAkB,CAAC,IAAI,CAAC;IACjC;EACJ;EACAtC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0B,iBAAiB,EAAEzB,OAAO,CAAC6D,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IACrD,IAAI,CAACxC,aAAa,CAACxB,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC2B,UAAU,CAACnC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACmC,UAAU,CAAC3B,QAAQ,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiE,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACjB,iBAAiB,EAAE;MACxB;IACJ;IACA;IACA,MAAMkB,aAAa,GAAG,IAAI,CAACjC,gBAAgB,CAACkC,SAAS,CAAC,KAAK,CAAC;IAC5D,MAAMC,WAAW,GAAGF,aAAa,CAACT,KAAK;IACvCS,aAAa,CAACG,IAAI,GAAG,CAAC;IACtB;IACA;IACA;IACAD,WAAW,CAACE,QAAQ,GAAG,UAAU;IACjCF,WAAW,CAACG,UAAU,GAAG,QAAQ;IACjCH,WAAW,CAACI,MAAM,GAAG,MAAM;IAC3BJ,WAAW,CAACK,OAAO,GAAG,GAAG;IACzBL,WAAW,CAACR,MAAM,GAAG,EAAE;IACvBQ,WAAW,CAACZ,SAAS,GAAG,EAAE;IAC1BY,WAAW,CAACV,SAAS,GAAG,EAAE;IAC1B;IACAU,WAAW,CAACM,GAAG,GAAGN,WAAW,CAACO,MAAM,GAAGP,WAAW,CAACQ,IAAI,GAAGR,WAAW,CAACS,KAAK,GAAG,MAAM;IACpF;IACA;IACA;IACA;IACA;IACAT,WAAW,CAACU,QAAQ,GAAG,QAAQ;IAC/B,IAAI,CAAC7C,gBAAgB,CAAC8C,UAAU,CAACC,WAAW,CAACd,aAAa,CAAC;IAC3D,IAAI,CAAClB,iBAAiB,GAAGkB,aAAa,CAACe,YAAY;IACnDf,aAAa,CAACvE,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAACyC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACE,aAAa,CAAC,CAAC;EACxB;EACA4C,oBAAoBA,CAAA,EAAG;IACnB,MAAMtG,OAAO,GAAG,IAAI,CAACqD,gBAAgB;IACrC,MAAMkD,cAAc,GAAGvG,OAAO,CAAC6E,KAAK,CAAC2B,YAAY,IAAI,EAAE;IACvD,MAAMC,SAAS,GAAG,IAAI,CAACrH,SAAS,CAACsH,OAAO;IACxC,MAAMC,iBAAiB,GAAGF,SAAS,IAAI,IAAI,CAACjC,SAAS;IACrD,MAAMoC,cAAc,GAAGH,SAAS,GAC1B,yCAAyC,GACzC,iCAAiC;IACvC;IACA;IACA;IACA,IAAIE,iBAAiB,EAAE;MACnB3G,OAAO,CAAC6E,KAAK,CAAC2B,YAAY,GAAG,GAAGxG,OAAO,CAACqG,YAAY,IAAI;IAC5D;IACA;IACA;IACArG,OAAO,CAACQ,SAAS,CAACE,GAAG,CAACkG,cAAc,CAAC;IACrC;IACA;IACA,MAAMC,YAAY,GAAG7G,OAAO,CAAC6G,YAAY,GAAG,CAAC;IAC7C7G,OAAO,CAACQ,SAAS,CAACO,MAAM,CAAC6F,cAAc,CAAC;IACxC,IAAID,iBAAiB,EAAE;MACnB3G,OAAO,CAAC6E,KAAK,CAAC2B,YAAY,GAAGD,cAAc;IAC/C;IACA,OAAOM,YAAY;EACvB;EACA1C,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACM,aAAa,IAAI,IAAI,CAACV,wBAAwB,IAAIC,SAAS,EAAE;MACnE;IACJ;IACA,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;MACnB,IAAI,CAACC,wBAAwB,GAAG,CAAC;MACjC;IACJ;IACA,MAAMR,KAAK,GAAG,IAAI,CAACF,gBAAgB,CAACE,KAAK;IACzC,IAAI,CAACF,gBAAgB,CAACE,KAAK,GAAG,IAAI,CAACF,gBAAgB,CAACS,WAAW;IAC/D,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACuC,oBAAoB,CAAC,CAAC;IAC3D,IAAI,CAACjD,gBAAgB,CAACE,KAAK,GAAGA,KAAK;EACvC;EACA;EACA2B,iBAAiB,GAAI5E,KAAK,IAAK;IAC3B,IAAI,CAACkE,SAAS,GAAGlE,KAAK,CAACvC,IAAI,KAAK,OAAO;EAC3C,CAAC;EACD+I,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC1H,SAAS,CAACU,SAAS,EAAE;MAC1B,IAAI,CAAC8D,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIA,kBAAkBA,CAACmD,KAAK,GAAG,KAAK,EAAE;IAC9B;IACA,IAAI,CAAC,IAAI,CAAC5D,QAAQ,EAAE;MAChB;IACJ;IACA,IAAI,CAACkC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAClB,+BAA+B,CAAC,CAAC;IACtC,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAAChB,gBAAgB,CAAC2D,SAAS;IACvD;IACA;IACA,IAAI,CAAC,IAAI,CAAC5C,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAM6C,QAAQ,GAAG,IAAI,CAACjF,WAAW,CAAC2C,aAAa;IAC/C,MAAMpB,KAAK,GAAG0D,QAAQ,CAAC1D,KAAK;IAC5B;IACA,IAAI,CAACwD,KAAK,IAAI,IAAI,CAAC9D,QAAQ,KAAK,IAAI,CAACG,gBAAgB,IAAIG,KAAK,KAAK,IAAI,CAACV,cAAc,EAAE;MACpF;IACJ;IACA,MAAMgE,YAAY,GAAG,IAAI,CAACP,oBAAoB,CAAC,CAAC;IAChD,MAAMtB,MAAM,GAAGkC,IAAI,CAACC,GAAG,CAACN,YAAY,EAAE,IAAI,CAAC9C,wBAAwB,IAAI,CAAC,CAAC;IACzE;IACAkD,QAAQ,CAACpC,KAAK,CAACG,MAAM,GAAG,GAAGA,MAAM,IAAI;IACrC,IAAI,CAAC3F,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAOmG,qBAAqB,KAAK,WAAW,EAAE;QAC9CA,qBAAqB,CAAC,MAAM,IAAI,CAACC,sBAAsB,CAACJ,QAAQ,CAAC,CAAC;MACtE,CAAC,MACI;QACDK,UAAU,CAAC,MAAM,IAAI,CAACD,sBAAsB,CAACJ,QAAQ,CAAC,CAAC;MAC3D;IACJ,CAAC,CAAC;IACF,IAAI,CAACpE,cAAc,GAAGU,KAAK;IAC3B,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACH,QAAQ;EACzC;EACA;AACJ;AACA;EACIY,KAAKA,CAAA,EAAG;IACJ;IACA;IACA,IAAI,IAAI,CAACf,cAAc,KAAKkB,SAAS,EAAE;MACnC,IAAI,CAACX,gBAAgB,CAACwB,KAAK,CAACG,MAAM,GAAG,IAAI,CAAClC,cAAc;IAC5D;EACJ;EACAyE,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIF,sBAAsBA,CAACJ,QAAQ,EAAE;IAC7B,MAAM;MAAEO,cAAc;MAAEC;IAAa,CAAC,GAAGR,QAAQ;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAClE,UAAU,CAAC2E,SAAS,IAAI,IAAI,CAAClD,SAAS,EAAE;MAC9CyC,QAAQ,CAACU,iBAAiB,CAACH,cAAc,EAAEC,YAAY,CAAC;MACxDR,QAAQ,CAACD,SAAS,GAAG,IAAI,CAAC3C,gBAAgB;IAC9C;EACJ;EACA,OAAO3G,IAAI,YAAAkK,4BAAAhK,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+E,mBAAmB;EAAA;EACtH,OAAOJ,IAAI,kBA9X8E3G,EAAE,CAAA4G,iBAAA;IAAAzE,IAAA,EA8XJ4E,mBAAmB;IAAA3E,SAAA;IAAAC,SAAA,WAA8R,GAAG;IAAA4J,YAAA,WAAAC,iCAAAxJ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9XlT1C,EAAE,CAAAmM,UAAA,mBAAAC,6CAAA;UAAA,OA8XJzJ,GAAA,CAAAgJ,iBAAA,CAAkB,CAAC;QAAA,CAAD,CAAC;MAAA;IAAA;IAAAU,MAAA;MAAA3E,OAAA;MAAAG,OAAA;MAAAE,OAAA,wCAA+MlH,gBAAgB;MAAAqH,WAAA;IAAA;IAAAoE,QAAA;EAAA;AAC7U;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAhY6F/C,EAAE,CAAAgD,iBAAA,CAgYJ+D,mBAAmB,EAAc,CAAC;IACjH5E,IAAI,EAAEzB,SAAS;IACfuC,IAAI,EAAE,CAAC;MACC6D,QAAQ,EAAE,+BAA+B;MACzCwF,QAAQ,EAAE,qBAAqB;MAC/BlJ,IAAI,EAAE;QACF,OAAO,EAAE,uBAAuB;QAChC;QACA;QACA,MAAM,EAAE,GAAG;QACX,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEsE,OAAO,EAAE,CAAC;MAClDvF,IAAI,EAAErB,KAAK;MACXmC,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE4E,OAAO,EAAE,CAAC;MACV1F,IAAI,EAAErB,KAAK;MACXmC,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE8E,OAAO,EAAE,CAAC;MACV5F,IAAI,EAAErB,KAAK;MACXmC,IAAI,EAAE,CAAC;QAAEsJ,KAAK,EAAE,qBAAqB;QAAEC,SAAS,EAAE3L;MAAiB,CAAC;IACxE,CAAC,CAAC;IAAEqH,WAAW,EAAE,CAAC;MACd/F,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2L,eAAe,CAAC;EAClB,OAAO3K,IAAI,YAAA4K,wBAAA1K,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyK,eAAe;EAAA;EAClH,OAAOE,IAAI,kBA5Z8E3M,EAAE,CAAA4M,gBAAA;IAAAzK,IAAA,EA4ZSsK;EAAe;EACnH,OAAOI,IAAI,kBA7Z8E7M,EAAE,CAAA8M,gBAAA;AA8Z/F;AACA;EAAA,QAAA/J,SAAA,oBAAAA,SAAA,KA/Z6F/C,EAAE,CAAAgD,iBAAA,CA+ZJyJ,eAAe,EAAc,CAAC;IAC7GtK,IAAI,EAAEpB,QAAQ;IACdkC,IAAI,EAAE,CAAC;MACC8J,OAAO,EAAE,CAAC5G,WAAW,EAAEY,mBAAmB,CAAC;MAC3CiG,OAAO,EAAE,CAAC7G,WAAW,EAAEY,mBAAmB;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASxD,eAAe,EAAE4C,WAAW,EAAEY,mBAAmB,EAAE0F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}