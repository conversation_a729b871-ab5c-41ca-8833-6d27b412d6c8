<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPT - VS Code Authentication</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .auth-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 16px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #667eea;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1a202c;
        }

        h1 {
            margin: 0 0 8px 0;
            font-size: 28px;
            font-weight: 700;
            color: #1a202c;
        }

        .subtitle {
            margin: 0;
            color: #64748b;
            font-size: 16px;
        }

        .vscode-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            text-align: center;
        }

        .vscode-info .icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        input {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s ease;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .auth-button {
            width: 100%;
            height: 48px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 8px;
        }

        .auth-button:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .auth-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 16px;
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 16px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="logo">
                <div class="logo-icon">🛡️</div>
                <span class="logo-text">SPT</span>
            </div>
            <h1>VS Code Authentication</h1>
            <p class="subtitle">Sign in to connect your VS Code extension</p>
        </div>

        <div class="vscode-info">
            <div class="icon">💻</div>
            <p><strong>VS Code Extension Authentication</strong></p>
            <p>After signing in, you'll be redirected back to VS Code</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <form class="auth-form" id="authForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>

            <button type="submit" class="auth-button" id="submitButton">
                Sign In to VS Code
            </button>
        </form>

        <div class="loading" id="loadingMessage">
            <div class="spinner"></div>
            <p>Authenticating and redirecting to VS Code...</p>
        </div>
    </div>

    <script>
        const form = document.getElementById('authForm');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const loadingMessage = document.getElementById('loadingMessage');
        const submitButton = document.getElementById('submitButton');

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const state = urlParams.get('state');
        const redirectURI = urlParams.get('redirect_uri');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showError('Please enter both username and password');
                return;
            }

            try {
                submitButton.disabled = true;
                submitButton.textContent = 'Signing In...';
                hideMessages();

                const response = await fetch('/api/v1/auth/vscode/callback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        state: state,
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    showSuccess('Authentication successful! Redirecting to VS Code...');
                    
                    // Show loading state
                    form.style.display = 'none';
                    loadingMessage.style.display = 'block';

                    // Construct the callback URL for VS Code
                    const callbackUrl = new URL(redirectURI);
                    callbackUrl.searchParams.set('token', data.token);
                    callbackUrl.searchParams.set('state', data.state);
                    callbackUrl.searchParams.set('user', encodeURIComponent(JSON.stringify(data.user)));
                    callbackUrl.searchParams.set('expires_in', data.expires_in.toString());

                    // Redirect to VS Code
                    setTimeout(() => {
                        window.location.href = callbackUrl.toString();
                    }, 2000);
                } else {
                    showError(data.error || 'Authentication failed');
                    submitButton.disabled = false;
                    submitButton.textContent = 'Sign In to VS Code';
                }
            } catch (error) {
                showError('Network error. Please try again.');
                submitButton.disabled = false;
                submitButton.textContent = 'Sign In to VS Code';
            }
        });

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }
    </script>
</body>
</html>
