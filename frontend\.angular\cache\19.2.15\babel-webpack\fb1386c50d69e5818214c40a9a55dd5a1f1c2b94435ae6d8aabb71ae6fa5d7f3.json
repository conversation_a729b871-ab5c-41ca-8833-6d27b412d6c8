{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule, NavigationEnd } from '@angular/router';\nimport { filter, map, startWith } from 'rxjs/operators';\nimport { NavigationComponent } from './shared/navigation/navigation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction AppComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"app-navigation\");\n    i0.ɵɵelementStart(2, \"main\", 7);\n    i0.ɵɵelement(3, \"router-outlet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppComponent_div_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, AppComponent_div_2_div_1_Template, 4, 0, \"div\", 5);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵtemplate(3, AppComponent_div_2_ng_template_3_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const loginView_r1 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 2, ctx_r1.currentUser$))(\"ngIfElse\", loginView_r1);\n  }\n}\nexport class AppComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.title = 'SPT - Blockchain Security Protocol Tool';\n    this.currentUser$ = this.authService.currentUser$;\n    // Check if current route is documentation - include initial route\n    this.isDocumentationRoute$ = this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(event => event.url.startsWith('/doc')),\n    // Start with current URL check\n    startWith(this.router.url.startsWith('/doc')));\n  }\n  ngOnInit() {\n    // Ensure AuthService is initialized\n    // This will trigger the authentication state initialization\n    this.authService.isAuthenticated();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 4,\n      vars: 6,\n      consts: [[\"loginView\", \"\"], [\"class\", \"doc-container\", 4, \"ngIf\"], [\"class\", \"app-container\", 4, \"ngIf\"], [1, \"doc-container\"], [1, \"app-container\"], [\"class\", \"authenticated-layout\", 4, \"ngIf\", \"ngIfElse\"], [1, \"authenticated-layout\"], [1, \"main-content\"], [1, \"login-layout\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppComponent_div_0_Template, 2, 0, \"div\", 1);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵtemplate(2, AppComponent_div_2_Template, 5, 4, \"div\", 2);\n          i0.ɵɵpipe(3, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 2, ctx.isDocumentationRoute$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(3, 4, ctx.isDocumentationRoute$));\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, i3.AsyncPipe, RouterOutlet, RouterModule, NavigationComponent],\n      styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  overflow: hidden;\\n  background: var(--spt-bg-primary);\\n}\\n\\n.doc-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  background: var(--spt-bg-primary);\\n}\\n\\n.login-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  background: var(--spt-bg-primary);\\n}\\n\\n.authenticated-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: var(--spt-bg-primary);\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  background: var(--spt-bg-secondary);\\n  min-height: 0; \\n\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .authenticated-layout[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHdCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQ0FBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLGlDQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0EsaUNBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxpQ0FBQTtBQUNGOztBQUVBO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUNBQUE7RUFDQSxhQUFBLEVBQUEsdUNBQUE7QUFDRjs7QUFFQSxzQkFBQTtBQUNBO0VBQ0U7SUFDRSxzQkFBQTtFQUNGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBFbmhhbmNlZCBBcHAgTGF5b3V0ICovXG4uYXBwLWNvbnRhaW5lciB7XG4gIGhlaWdodDogMTAwdmg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJhY2tncm91bmQ6IHZhcigtLXNwdC1iZy1wcmltYXJ5KTtcbn1cblxuLmRvYy1jb250YWluZXIge1xuICBoZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1zcHQtYmctcHJpbWFyeSk7XG59XG5cbi5sb2dpbi1sYXlvdXQge1xuICBoZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1zcHQtYmctcHJpbWFyeSk7XG59XG5cbi5hdXRoZW50aWNhdGVkLWxheW91dCB7XG4gIGhlaWdodDogMTAwdmg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGJhY2tncm91bmQ6IHZhcigtLXNwdC1iZy1wcmltYXJ5KTtcbn1cblxuLm1haW4tY29udGVudCB7XG4gIGZsZXg6IDE7XG4gIG92ZXJmbG93LXk6IGF1dG87XG4gIGJhY2tncm91bmQ6IHZhcigtLXNwdC1iZy1zZWNvbmRhcnkpO1xuICBtaW4taGVpZ2h0OiAwOyAvKiBJbXBvcnRhbnQgZm9yIGZsZXggY2hpbGQgc2Nyb2xsaW5nICovXG59XG5cbi8qIFJlc3BvbnNpdmUgRGVzaWduICovXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmF1dGhlbnRpY2F0ZWQtbGF5b3V0IHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "RouterModule", "NavigationEnd", "filter", "map", "startWith", "NavigationComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "AppComponent_div_2_div_1_Template", "AppComponent_div_2_ng_template_3_Template", "ɵɵtemplateRefExtractor", "ɵɵadvance", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r1", "currentUser$", "loginView_r1", "AppComponent", "constructor", "authService", "router", "title", "isDocumentationRoute$", "events", "pipe", "event", "url", "startsWith", "ngOnInit", "isAuthenticated", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_div_0_Template", "AppComponent_div_2_Template", "i3", "NgIf", "AsyncPipe", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\app.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule, TitleCasePipe } from '@angular/common';\nimport { RouterOutlet, RouterModule, Router, NavigationEnd } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { filter, map, startWith } from 'rxjs/operators';\nimport { AuthService, User } from './services/auth.service';\nimport { NavigationComponent } from './shared/navigation/navigation.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    RouterModule,\n    NavigationComponent\n  ],\n  templateUrl: './app.component.html',\n\n\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'SPT - Blockchain Security Protocol Tool';\n  currentUser$: Observable<User | null>;\n  isDocumentationRoute$: Observable<boolean>;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.currentUser$ = this.authService.currentUser$;\n\n    // Check if current route is documentation - include initial route\n    this.isDocumentationRoute$ = this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd),\n      map((event: NavigationEnd) => event.url.startsWith('/doc')),\n      // Start with current URL check\n      startWith(this.router.url.startsWith('/doc'))\n    );\n  }\n\n  ngOnInit(): void {\n    // Ensure AuthService is initialized\n    // This will trigger the authentication state initialization\n    this.authService.isAuthenticated();\n  }\n}\n", "<!-- Documentation Route - No Authentication Required -->\n<div *ngIf=\"isDocumentationRoute$ | async\" class=\"doc-container\">\n  <router-outlet></router-outlet>\n</div>\n\n<!-- Main App - Authentication Required -->\n<div *ngIf=\"!(isDocumentationRoute$ | async)\" class=\"app-container\">\n  <!-- Authenticated Layout -->\n  <div *ngIf=\"currentUser$ | async as user; else loginView\" class=\"authenticated-layout\">\n    <!-- Enhanced Navigation -->\n    <app-navigation></app-navigation>\n    \n    <!-- Main Content Area -->\n    <main class=\"main-content\">\n      <router-outlet></router-outlet>\n    </main>\n  </div>\n\n  <!-- Login View -->\n  <ng-template #loginView>\n    <div class=\"login-layout\">\n      <router-outlet></router-outlet>\n    </div>\n  </ng-template>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAuB,iBAAiB;AAC7D,SAASC,YAAY,EAAEC,YAAY,EAAUC,aAAa,QAAQ,iBAAiB;AAEnF,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAEvD,SAASC,mBAAmB,QAAQ,0CAA0C;;;;;;;ICL9EC,EAAA,CAAAC,cAAA,aAAiE;IAC/DD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKJH,EAAA,CAAAC,cAAA,aAAuF;IAErFD,EAAA,CAAAE,SAAA,qBAAiC;IAGjCF,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,oBAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IAIJH,EAAA,CAAAC,cAAA,aAA0B;IACxBD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBVH,EAAA,CAAAC,cAAA,aAAoE;IAElED,EAAA,CAAAI,UAAA,IAAAC,iCAAA,iBAAuF;;IAWvFL,EAAA,CAAAI,UAAA,IAAAE,yCAAA,gCAAAN,EAAA,CAAAO,sBAAA,CAAwB;IAK1BP,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBEH,EAAA,CAAAQ,SAAA,EAA2B;IAASR,EAApC,CAAAS,UAAA,SAAAT,EAAA,CAAAU,WAAA,OAAAC,MAAA,CAAAC,YAAA,EAA2B,aAAAC,YAAA,CAAuB;;;ADc1D,OAAM,MAAOC,YAAY;EAKvBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAC,KAAK,GAAG,yCAAyC;IAQ/C,IAAI,CAACN,YAAY,GAAG,IAAI,CAACI,WAAW,CAACJ,YAAY;IAEjD;IACA,IAAI,CAACO,qBAAqB,GAAG,IAAI,CAACF,MAAM,CAACG,MAAM,CAACC,IAAI,CAClDzB,MAAM,CAAC0B,KAAK,IAAIA,KAAK,YAAY3B,aAAa,CAAC,EAC/CE,GAAG,CAAEyB,KAAoB,IAAKA,KAAK,CAACC,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC3D;IACA1B,SAAS,CAAC,IAAI,CAACmB,MAAM,CAACM,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,CAAC,CAC9C;EACH;EAEAC,QAAQA,CAAA;IACN;IACA;IACA,IAAI,CAACT,WAAW,CAACU,eAAe,EAAE;EACpC;;;uCAxBWZ,YAAY,EAAAd,EAAA,CAAA2B,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7B,EAAA,CAAA2B,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZjB,YAAY;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBzBtC,EAAA,CAAAI,UAAA,IAAAoC,2BAAA,iBAAiE;;UAKjExC,EAAA,CAAAI,UAAA,IAAAqC,2BAAA,iBAAoE;;;;UAL9DzC,EAAA,CAAAS,UAAA,SAAAT,EAAA,CAAAU,WAAA,OAAA6B,GAAA,CAAApB,qBAAA,EAAmC;UAKnCnB,EAAA,CAAAQ,SAAA,GAAsC;UAAtCR,EAAA,CAAAS,UAAA,UAAAT,EAAA,CAAAU,WAAA,OAAA6B,GAAA,CAAApB,qBAAA,EAAsC;;;qBDMxC3B,YAAY,EAAAkD,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,SAAA,EACZnD,YAAY,EACZC,YAAY,EACZK,mBAAmB;MAAA8C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}