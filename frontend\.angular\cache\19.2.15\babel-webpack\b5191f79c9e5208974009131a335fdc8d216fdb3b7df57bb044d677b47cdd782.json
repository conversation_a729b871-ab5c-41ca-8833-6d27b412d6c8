{"ast": null, "code": "import { AuthGuard, GuestGuard, RoleGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n},\n// Authentication routes\n{\n  path: 'login',\n  loadComponent: () => import('./components/auth/login.component').then(m => m.LoginComponent),\n  canActivate: [GuestGuard]\n}, {\n  path: 'register',\n  loadComponent: () => import('./components/auth/register.component').then(m => m.RegisterComponent),\n  canActivate: [GuestGuard]\n},\n// Protected routes\n{\n  path: 'dashboard',\n  loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'scan',\n  loadComponent: () => import('./components/scan/scan.component').then(m => m.ScanComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'scan/:id',\n  loadComponent: () => import('./components/scan-details/scan-details.component').then(m => m.ScanDetailsComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'checklist',\n  loadComponent: () => import('./components/checklist/checklist.component').then(m => m.ChecklistComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'reports',\n  loadComponent: () => import('./components/reports/reports.component').then(m => m.ReportsComponent),\n  canActivate: [AuthGuard]\n}, {\n  path: 'settings',\n  loadComponent: () => import('./components/settings/settings.component').then(m => m.SettingsComponent),\n  canActivate: [AuthGuard]\n},\n// Projects management (admin only)\n{\n  path: 'projects',\n  loadComponent: () => import('./components/projects/projects.component').then(m => m.ProjectsComponent),\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: ['admin', 'manager']\n  }\n},\n// Documentation routes (no authentication required)\n{\n  path: 'doc',\n  loadChildren: () => import('./modules/documentation/documentation.module').then(m => m.DocumentationModule)\n},\n// Test route (no authentication required)\n{\n  path: 'test',\n  loadComponent: () => import('./components/test/test.component').then(m => m.TestComponent)\n},\n// Error pages\n{\n  path: 'unauthorized',\n  loadComponent: () => import('./components/error/unauthorized.component').then(m => m.UnauthorizedComponent)\n}, {\n  path: '**',\n  redirectTo: '/dashboard'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}