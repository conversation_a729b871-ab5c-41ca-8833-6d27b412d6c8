{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nfunction VscodeExtensionComponent_div_29_div_9_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 57)(1, \"mat-icon\", 58);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const benefit_r1 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(benefit_r1);\n  }\n}\nfunction VscodeExtensionComponent_div_29_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"ul\", 55);\n    i0.ɵɵtemplate(2, VscodeExtensionComponent_div_29_div_9_li_2_Template, 5, 1, \"li\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", feature_r2.benefits);\n  }\n}\nfunction VscodeExtensionComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 50)(5, \"h3\", 51);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 52);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, VscodeExtensionComponent_div_29_div_9_Template, 3, 1, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", feature_r2.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", feature_r2.benefits.length > 0);\n  }\n}\nfunction VscodeExtensionComponent_div_40_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Command\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function VscodeExtensionComponent_div_40_div_12_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const step_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.copyToClipboard(step_r4.command));\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"pre\", 36)(10, \"code\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(step_r4.command);\n  }\n}\nfunction VscodeExtensionComponent_div_40_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(step_r4.notes);\n  }\n}\nfunction VscodeExtensionComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"div\", 61);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 62)(5, \"h3\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 64);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 65)(10, \"p\", 66);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, VscodeExtensionComponent_div_40_div_12_Template, 12, 1, \"div\", 67)(13, VscodeExtensionComponent_div_40_div_13_Template, 5, 1, \"div\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.subtitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.notes);\n  }\n}\nfunction VscodeExtensionComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 40)(2, \"code\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 40)(5, \"span\", 72);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"code\", 73);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 40)(11, \"span\", 74);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const setting_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.key);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.default);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.description);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_15_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r10);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"h4\", 87);\n    i0.ɵɵtext(2, \"Steps:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ol\", 88);\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_div_121_div_15_li_4_Template, 2, 1, \"li\", 89);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r9.steps);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_16_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 95)(1, \"mat-icon\", 96);\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r11 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r11);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"h4\", 92);\n    i0.ɵɵtext(2, \"Tips:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 93);\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_div_121_div_16_li_4_Template, 5, 1, \"li\", 94);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r9.tips);\n  }\n}\nfunction VscodeExtensionComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76);\n    i0.ɵɵlistener(\"click\", function VscodeExtensionComponent_div_121_Template_div_click_1_listener() {\n      const usage_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      return i0.ɵɵresetView(usage_r9.expanded = !usage_r9.expanded);\n    });\n    i0.ɵɵelementStart(2, \"div\", 77)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78)(6, \"h3\", 79);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 80);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-icon\", 81);\n    i0.ɵɵtext(11, \"expand_more\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 82)(13, \"p\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, VscodeExtensionComponent_div_121_div_15_Template, 5, 1, \"div\", 84)(16, VscodeExtensionComponent_div_121_div_16_Template, 5, 1, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r9 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(usage_r9.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(usage_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(usage_r9.description);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"spt-expanded\", usage_r9.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"spt-expanded\", usage_r9.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(usage_r9.details);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r9.steps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r9.tips);\n  }\n}\nfunction VscodeExtensionComponent_div_130_li_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 109);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r12);\n  }\n}\nfunction VscodeExtensionComponent_div_130_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"h4\", 111);\n    i0.ɵɵtext(2, \"Prevention:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 112);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(issue_r13.prevention);\n  }\n}\nfunction VscodeExtensionComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 99)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 100)(6, \"h3\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 102);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 103)(11, \"div\", 104)(12, \"h4\", 105);\n    i0.ɵɵtext(13, \"Solution:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ol\", 106);\n    i0.ɵɵtemplate(15, VscodeExtensionComponent_div_130_li_15_Template, 2, 1, \"li\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, VscodeExtensionComponent_div_130_div_16_Template, 5, 1, \"div\", 108);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"spt-severity-\" + issue_r13.severity);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r13.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r13.problem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r13.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", issue_r13.solution);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", issue_r13.prevention);\n  }\n}\nexport let VscodeExtensionComponent = /*#__PURE__*/(() => {\n  class VscodeExtensionComponent {\n    constructor() {\n      this.configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n      this.primaryFeatures = [{\n        title: 'Real-time Security Scanning',\n        description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities and smart suggestions.',\n        icon: 'security',\n        color: 'linear-gradient(135deg, var(--spt-primary-500), var(--spt-primary-600))',\n        benefits: ['Immediate vulnerability detection', 'Reduced security debt', 'Faster development cycles', 'Proactive security measures']\n      }, {\n        title: 'Inline Decorations',\n        description: 'Visual indicators directly in your code highlighting security issues with severity-based color coding.',\n        icon: 'visibility',\n        color: 'linear-gradient(135deg, var(--spt-success-500), var(--spt-success-600))',\n        benefits: ['Clear visual feedback', 'Context-aware highlighting', 'Severity-based color coding', 'Non-intrusive indicators']\n      }, {\n        title: 'Problems Panel Integration',\n        description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and quick fixes.',\n        icon: 'bug_report',\n        color: 'linear-gradient(135deg, var(--spt-error-500), var(--spt-error-600))',\n        benefits: ['Centralized issue tracking', 'Detailed error descriptions', 'Quick navigation to issues', 'Integration with existing workflow']\n      }, {\n        title: 'CodeLens Integration',\n        description: 'Actionable security suggestions and metrics displayed directly above your code for instant access.',\n        icon: 'lens',\n        color: 'linear-gradient(135deg, var(--spt-warning-500), var(--spt-warning-600))',\n        benefits: ['Contextual security metrics', 'One-click security actions', 'Code quality insights', 'Performance recommendations']\n      }, {\n        title: 'Hover Information',\n        description: 'Detailed security information and recommendations on hover over code elements with rich documentation.',\n        icon: 'info',\n        color: 'linear-gradient(135deg, var(--spt-info-500), var(--spt-info-600))',\n        benefits: ['Instant security documentation', 'Best practice suggestions', 'Vulnerability explanations', 'Quick reference access']\n      }, {\n        title: 'Multi-chain Support',\n        description: 'Comprehensive support for Ethereum, Bitcoin, and general blockchain security patterns.',\n        icon: 'link',\n        color: 'linear-gradient(135deg, var(--spt-secondary-500), var(--spt-secondary-600))',\n        benefits: ['Comprehensive blockchain coverage', 'Chain-specific security rules', 'Unified security approach', 'Extensible architecture']\n      }];\n      this.installationSteps = [{\n        title: 'Install from VS Code Marketplace',\n        subtitle: 'Recommended method',\n        description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n        command: 'ext install blockchain-spt.vscode-spt',\n        notes: 'Extension will be automatically activated after installation'\n      }, {\n        title: 'Configure Backend Connection',\n        subtitle: 'Connect to SPT server',\n        description: 'Configure the extension to connect to your SPT backend server.',\n        command: 'Configure spt.serverUrl in VS Code settings',\n        notes: 'Default server URL is http://localhost:8080'\n      }, {\n        title: 'Verify Installation',\n        subtitle: 'Test the connection',\n        description: 'Open a blockchain project and verify that security scanning is working.',\n        notes: 'Check the status bar for SPT connection indicator'\n      }];\n      this.configSettings = [{\n        key: 'spt.enabled',\n        type: 'boolean',\n        default: 'true',\n        description: 'Enable/disable SPT security analysis'\n      }, {\n        key: 'spt.serverUrl',\n        type: 'string',\n        default: 'http://localhost:8080',\n        description: 'SPT backend server URL'\n      }, {\n        key: 'spt.apiKey',\n        type: 'string',\n        default: '\"\"',\n        description: 'API key for authentication'\n      }, {\n        key: 'spt.autoScan',\n        type: 'boolean',\n        default: 'true',\n        description: 'Automatically scan files on save'\n      }, {\n        key: 'spt.scanOnOpen',\n        type: 'boolean',\n        default: 'false',\n        description: 'Automatically scan files when opened'\n      }, {\n        key: 'spt.chains',\n        type: 'array',\n        default: '[\"ethereum\", \"bitcoin\", \"general\"]',\n        description: 'Blockchain chains to analyze'\n      }, {\n        key: 'spt.severity',\n        type: 'string',\n        default: '\"medium\"',\n        description: 'Minimum severity level to show'\n      }, {\n        key: 'spt.showInlineDecorations',\n        type: 'boolean',\n        default: 'true',\n        description: 'Show inline security decorations'\n      }, {\n        key: 'spt.showProblems',\n        type: 'boolean',\n        default: 'true',\n        description: 'Show security issues in Problems panel'\n      }, {\n        key: 'spt.enableCodeLens',\n        type: 'boolean',\n        default: 'true',\n        description: 'Enable security-related CodeLens'\n      }, {\n        key: 'spt.enableHover',\n        type: 'boolean',\n        default: 'true',\n        description: 'Enable security information on hover'\n      }];\n      this.usageExamples = [{\n        title: 'Scanning Files',\n        description: 'How to scan files for security issues',\n        icon: 'scanner',\n        expanded: true,\n        details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n        steps: ['Open a blockchain project in VS Code', 'Save a file to trigger automatic scanning', 'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"', 'View results in Problems panel or inline decorations'],\n        tips: ['Enable auto-scan for continuous security monitoring', 'Use the Problems panel to navigate between issues', 'Check the status bar for scan progress']\n      }, {\n        title: 'Understanding Security Issues',\n        description: 'How to interpret and resolve security findings',\n        icon: 'help',\n        details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n        steps: ['Hover over highlighted code to see issue details', 'Click on issues in Problems panel for more information', 'Use CodeLens actions for quick fixes', 'Follow the recommended solutions'],\n        tips: ['Start with critical and high severity issues', 'Use hover information for quick context', 'Check references for additional learning']\n      }, {\n        title: 'Configuring Scan Settings',\n        description: 'Customize scanning behavior for your project',\n        icon: 'tune',\n        details: 'Adjust scan settings to match your project requirements and development workflow.',\n        steps: ['Open VS Code settings (Ctrl+,)', 'Search for \"SPT\" to find extension settings', 'Adjust chains, severity, and scan triggers', 'Save settings and restart if needed'],\n        tips: ['Use workspace settings for project-specific configuration', 'Adjust severity threshold based on project maturity', 'Enable scan-on-open for comprehensive coverage']\n      }];\n      this.troubleshootingIssues = [{\n        problem: 'Extension Not Connecting to Server',\n        description: 'SPT extension cannot connect to the backend server',\n        icon: 'cloud_off',\n        severity: 'high',\n        solution: ['Verify SPT backend server is running on configured port', 'Check spt.serverUrl setting in VS Code preferences', 'Ensure firewall is not blocking the connection', 'Try restarting VS Code and the SPT server'],\n        prevention: 'Always start the SPT backend server before using the extension'\n      }, {\n        problem: 'No Security Issues Detected',\n        description: 'Extension is running but not finding any security issues',\n        icon: 'search_off',\n        severity: 'medium',\n        solution: ['Check if the file type is supported (Solidity, JavaScript, etc.)', 'Verify the correct blockchain chains are selected', 'Lower the severity threshold in settings', 'Ensure the file contains actual security-relevant code'],\n        prevention: 'Review supported file types and ensure proper project structure'\n      }, {\n        problem: 'Performance Issues',\n        description: 'Extension is causing VS Code to slow down',\n        icon: 'speed',\n        severity: 'medium',\n        solution: ['Disable auto-scan and use manual scanning', 'Increase scan timeout in settings', 'Exclude large files or directories from scanning', 'Reduce the number of enabled blockchain chains'],\n        prevention: 'Configure appropriate scan settings for your project size'\n      }];\n    }\n    copyToClipboard(text) {\n      navigator.clipboard.writeText(text).then(() => {\n        // Could add a toast notification here\n        console.log('Copied to clipboard');\n      }).catch(err => {\n        console.error('Failed to copy: ', err);\n      });\n    }\n    static {\n      this.ɵfac = function VscodeExtensionComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || VscodeExtensionComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VscodeExtensionComponent,\n        selectors: [[\"app-vscode-extension\"]],\n        decls: 131,\n        vars: 6,\n        consts: [[1, \"spt-vscode-container\"], [1, \"spt-hero-section\"], [1, \"spt-hero-content\"], [1, \"spt-hero-icon\"], [1, \"spt-hero-text\"], [1, \"spt-hero-title\"], [1, \"spt-hero-subtitle\"], [1, \"spt-hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"spt-primary-btn\"], [\"mat-stroked-button\", \"\", 1, \"spt-secondary-btn\"], [1, \"spt-features-overview\"], [1, \"spt-section-header\"], [1, \"spt-section-title\"], [1, \"spt-section-subtitle\"], [1, \"spt-features-grid\"], [\"class\", \"spt-feature-card spt-feature-primary\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-details-section\"], [\"animationDuration\", \"300ms\", 1, \"spt-tab-group\"], [\"label\", \"Installation\"], [1, \"spt-tab-content\"], [1, \"spt-tab-header\"], [1, \"spt-tab-title\"], [1, \"spt-tab-subtitle\"], [1, \"spt-installation-steps\"], [\"class\", \"spt-step-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"spt-config-sections\"], [1, \"spt-config-card\"], [1, \"spt-config-header\"], [1, \"spt-config-icon\"], [1, \"spt-config-info\"], [1, \"spt-config-title\"], [1, \"spt-config-subtitle\"], [1, \"spt-config-content\"], [1, \"spt-code-block\"], [1, \"spt-code-header\"], [1, \"spt-code-content\"], [\"mat-icon-button\", \"\", 1, \"spt-copy-btn\", 3, \"click\"], [1, \"spt-settings-table\"], [1, \"spt-table-header\"], [1, \"spt-table-cell\"], [\"class\", \"spt-table-row\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Usage\"], [1, \"spt-usage-sections\"], [\"class\", \"spt-usage-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Troubleshooting\"], [1, \"spt-troubleshooting-grid\"], [\"class\", \"spt-issue-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-feature-card\", \"spt-feature-primary\"], [1, \"spt-feature-icon\"], [1, \"spt-feature-content\"], [1, \"spt-feature-title\"], [1, \"spt-feature-description\"], [\"class\", \"spt-feature-benefits\", 4, \"ngIf\"], [1, \"spt-feature-benefits\"], [1, \"spt-benefits-list\"], [\"class\", \"spt-benefit-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-benefit-item\"], [1, \"spt-benefit-icon\"], [1, \"spt-step-card\"], [1, \"spt-step-header\"], [1, \"spt-step-number\"], [1, \"spt-step-info\"], [1, \"spt-step-title\"], [1, \"spt-step-subtitle\"], [1, \"spt-step-content\"], [1, \"spt-step-description\"], [\"class\", \"spt-code-block\", 4, \"ngIf\"], [\"class\", \"spt-step-note\", 4, \"ngIf\"], [1, \"spt-step-note\"], [1, \"spt-table-row\"], [1, \"spt-setting-key\"], [1, \"spt-badge\", \"spt-badge-info\"], [1, \"spt-setting-value\"], [1, \"spt-setting-desc\"], [1, \"spt-usage-card\"], [1, \"spt-usage-header\", 3, \"click\"], [1, \"spt-usage-icon\"], [1, \"spt-usage-info\"], [1, \"spt-usage-title\"], [1, \"spt-usage-description\"], [1, \"spt-expand-icon\"], [1, \"spt-usage-content\"], [1, \"spt-usage-details\"], [\"class\", \"spt-usage-steps\", 4, \"ngIf\"], [\"class\", \"spt-usage-tips\", 4, \"ngIf\"], [1, \"spt-usage-steps\"], [1, \"spt-steps-title\"], [1, \"spt-steps-list\"], [\"class\", \"spt-step-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-step-item\"], [1, \"spt-usage-tips\"], [1, \"spt-tips-title\"], [1, \"spt-tips-list\"], [\"class\", \"spt-tip-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-tip-item\"], [1, \"spt-tip-icon\"], [1, \"spt-issue-card\"], [1, \"spt-issue-header\"], [1, \"spt-issue-icon\"], [1, \"spt-issue-info\"], [1, \"spt-issue-title\"], [1, \"spt-issue-description\"], [1, \"spt-issue-content\"], [1, \"spt-solution-section\"], [1, \"spt-solution-title\"], [1, \"spt-solution-steps\"], [\"class\", \"spt-solution-step\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"spt-prevention-section\", 4, \"ngIf\"], [1, \"spt-solution-step\"], [1, \"spt-prevention-section\"], [1, \"spt-prevention-title\"], [1, \"spt-prevention-text\"]],\n        template: function VscodeExtensionComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"extension\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 4)(7, \"h1\", 5);\n            i0.ɵɵtext(8, \"VS Code Extension\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p\", 6);\n            i0.ɵɵtext(10, \" Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration. \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 7)(12, \"button\", 8)(13, \"mat-icon\");\n            i0.ɵɵtext(14, \"download\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"span\");\n            i0.ɵɵtext(16, \"Install Extension\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"button\", 9)(18, \"mat-icon\");\n            i0.ɵɵtext(19, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"span\");\n            i0.ɵɵtext(21, \"View Source\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(22, \"section\", 10)(23, \"div\", 11)(24, \"h2\", 12);\n            i0.ɵɵtext(25, \"Key Features\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\", 13);\n            i0.ɵɵtext(27, \" Discover the powerful features that make SPT extension essential for secure blockchain development. \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 14);\n            i0.ɵɵtemplate(29, VscodeExtensionComponent_div_29_Template, 10, 6, \"div\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"section\", 16)(31, \"mat-tab-group\", 17)(32, \"mat-tab\", 18)(33, \"div\", 19)(34, \"div\", 20)(35, \"h2\", 21);\n            i0.ɵɵtext(36, \"Installation Guide\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"p\", 22);\n            i0.ɵɵtext(38, \"Get the SPT extension installed and configured in VS Code quickly and easily.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"div\", 23);\n            i0.ɵɵtemplate(40, VscodeExtensionComponent_div_40_Template, 14, 6, \"div\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"mat-tab\", 25)(42, \"div\", 19)(43, \"div\", 20)(44, \"h2\", 21);\n            i0.ɵɵtext(45, \"Extension Configuration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"p\", 22);\n            i0.ɵɵtext(47, \"Customize the SPT extension to fit your development workflow and preferences.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"div\", 26)(49, \"div\", 27)(50, \"div\", 28)(51, \"mat-icon\", 29);\n            i0.ɵɵtext(52, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"div\", 30)(54, \"h3\", 31);\n            i0.ɵɵtext(55, \"Settings Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"p\", 32);\n            i0.ɵɵtext(57, \"Configure extension behavior\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(58, \"div\", 33)(59, \"p\");\n            i0.ɵɵtext(60, \"Access extension settings through VS Code preferences:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"div\", 34)(62, \"div\", 35)(63, \"mat-icon\");\n            i0.ɵɵtext(64, \"keyboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"span\");\n            i0.ɵɵtext(66, \"Keyboard Shortcut\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"pre\", 36)(68, \"code\");\n            i0.ɵɵtext(69, \"Ctrl+Shift+P \\u2192 \\\"Preferences: Open Settings (JSON)\\\"\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(70, \"div\", 27)(71, \"div\", 28)(72, \"mat-icon\", 29);\n            i0.ɵɵtext(73, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"div\", 30)(75, \"h3\", 31);\n            i0.ɵɵtext(76, \"Configuration Example\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"p\", 32);\n            i0.ɵɵtext(78, \"settings.json\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(79, \"div\", 33)(80, \"div\", 34)(81, \"div\", 35)(82, \"mat-icon\");\n            i0.ɵɵtext(83, \"code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"span\");\n            i0.ɵɵtext(85, \"JSON Configuration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function VscodeExtensionComponent_Template_button_click_86_listener() {\n              return ctx.copyToClipboard(ctx.configExample);\n            });\n            i0.ɵɵelementStart(87, \"mat-icon\");\n            i0.ɵɵtext(88, \"content_copy\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(89, \"pre\", 36)(90, \"code\");\n            i0.ɵɵtext(91);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(92, \"div\", 27)(93, \"div\", 28)(94, \"mat-icon\", 29);\n            i0.ɵɵtext(95, \"list\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"div\", 30)(97, \"h3\", 31);\n            i0.ɵɵtext(98, \"Available Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(99, \"p\", 32);\n            i0.ɵɵtext(100, \"Complete settings reference\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(101, \"div\", 33)(102, \"div\", 38)(103, \"div\", 39)(104, \"div\", 40);\n            i0.ɵɵtext(105, \"Setting\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(106, \"div\", 40);\n            i0.ɵɵtext(107, \"Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"div\", 40);\n            i0.ɵɵtext(109, \"Default\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"div\", 40);\n            i0.ɵɵtext(111, \"Description\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(112, VscodeExtensionComponent_div_112_Template, 13, 4, \"div\", 41);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(113, \"mat-tab\", 42)(114, \"div\", 19)(115, \"div\", 20)(116, \"h2\", 21);\n            i0.ɵɵtext(117, \"Using the Extension\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"p\", 22);\n            i0.ɵɵtext(119, \"Learn how to effectively use SPT extension features in your daily development workflow.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(120, \"div\", 43);\n            i0.ɵɵtemplate(121, VscodeExtensionComponent_div_121_Template, 17, 10, \"div\", 44);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(122, \"mat-tab\", 45)(123, \"div\", 19)(124, \"div\", 20)(125, \"h2\", 21);\n            i0.ɵɵtext(126, \"Troubleshooting\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(127, \"p\", 22);\n            i0.ɵɵtext(128, \"Common issues and solutions for the SPT VS Code extension.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(129, \"div\", 46);\n            i0.ɵɵtemplate(130, VscodeExtensionComponent_div_130_Template, 17, 7, \"div\", 47);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngForOf\", ctx.primaryFeatures);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.installationSteps);\n            i0.ɵɵadvance(51);\n            i0.ɵɵtextInterpolate(ctx.configExample);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngForOf\", ctx.configSettings);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.usageExamples);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.troubleshootingIssues);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, MatIconModule, i3.MatIcon, MatButtonModule, i4.MatButton, i4.MatIconButton, MatChipsModule, MatExpansionModule, MatTableModule],\n        styles: [\".spt-vscode-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;font-family:Inter,sans-serif}.spt-hero-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-primary-50) 0%,var(--spt-secondary-50) 100%);border-radius:var(--spt-radius-3xl);padding:var(--spt-space-12) var(--spt-space-8);margin-bottom:var(--spt-space-12);border:1px solid var(--spt-primary-200)}.spt-hero-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-6);margin-bottom:var(--spt-space-8)}.spt-hero-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-primary-600) 0%,var(--spt-secondary-600) 100%);border-radius:var(--spt-radius-3xl);padding:var(--spt-space-6);display:flex;align-items:center;justify-content:center;box-shadow:var(--spt-shadow-xl)}.spt-hero-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff;font-size:48px;width:48px;height:48px}.spt-hero-text[_ngcontent-%COMP%]{flex:1}.spt-hero-title[_ngcontent-%COMP%]{font-size:var(--spt-text-5xl);font-weight:var(--spt-font-bold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-4) 0;letter-spacing:-.025em}.spt-hero-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-xl);color:var(--spt-gray-600);margin:0;line-height:1.6;font-weight:var(--spt-font-normal)}.spt-hero-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-4);align-items:center}.spt-primary-btn[_ngcontent-%COMP%]{background:var(--spt-primary-600)!important;color:#fff!important;font-weight:var(--spt-font-semibold)!important;padding:var(--spt-space-4) var(--spt-space-6)!important;border-radius:var(--spt-radius-xl)!important;box-shadow:var(--spt-shadow-md)!important;transition:all .2s ease!important}.spt-primary-btn[_ngcontent-%COMP%]:hover{background:var(--spt-primary-700)!important;box-shadow:var(--spt-shadow-lg)!important;transform:translateY(-2px)}.spt-secondary-btn[_ngcontent-%COMP%]{color:var(--spt-gray-700)!important;border-color:var(--spt-gray-300)!important;font-weight:var(--spt-font-medium)!important;padding:var(--spt-space-4) var(--spt-space-6)!important;border-radius:var(--spt-radius-xl)!important;transition:all .2s ease!important}.spt-secondary-btn[_ngcontent-%COMP%]:hover{background:var(--spt-gray-100)!important;border-color:var(--spt-gray-400)!important;transform:translateY(-1px)}.spt-section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:var(--spt-space-12)}.spt-section-title[_ngcontent-%COMP%]{font-size:var(--spt-text-4xl);font-weight:var(--spt-font-bold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-4) 0;letter-spacing:-.025em}.spt-section-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-lg);color:var(--spt-gray-600);max-width:600px;margin:0 auto;line-height:1.6}.spt-features-overview[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-16)}.spt-features-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:var(--spt-space-8)}.spt-feature-card[_ngcontent-%COMP%]{background:#fff;border-radius:var(--spt-radius-3xl);padding:var(--spt-space-8);border:1px solid var(--spt-gray-200);box-shadow:var(--spt-shadow-sm);transition:all .3s ease;position:relative;overflow:hidden}.spt-feature-card[_ngcontent-%COMP%]:hover{box-shadow:var(--spt-shadow-xl);transform:translateY(-4px);border-color:var(--spt-primary-300)}.spt-feature-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,var(--spt-primary-500),var(--spt-secondary-500))}.spt-feature-icon[_ngcontent-%COMP%]{width:64px;height:64px;border-radius:var(--spt-radius-2xl);display:flex;align-items:center;justify-content:center;margin-bottom:var(--spt-space-6);box-shadow:var(--spt-shadow-md)}.spt-feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff;font-size:32px;width:32px;height:32px}.spt-feature-content[_ngcontent-%COMP%]{flex:1}.spt-feature-title[_ngcontent-%COMP%]{font-size:var(--spt-text-2xl);font-weight:var(--spt-font-bold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-3) 0}.spt-feature-description[_ngcontent-%COMP%]{font-size:var(--spt-text-base);color:var(--spt-gray-600);margin:0 0 var(--spt-space-6) 0;line-height:1.6}.spt-feature-benefits[_ngcontent-%COMP%]{margin-top:var(--spt-space-4)}.spt-benefits-list[_ngcontent-%COMP%]{list-style:none;margin:0;padding:0}.spt-benefit-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);margin-bottom:var(--spt-space-2);font-size:var(--spt-text-sm);color:var(--spt-gray-700)}.spt-benefit-icon[_ngcontent-%COMP%]{color:var(--spt-success-600);font-size:16px;width:16px;height:16px}.spt-details-section[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-16)}.spt-tab-group[_ngcontent-%COMP%]{background:#fff;border-radius:var(--spt-radius-2xl);box-shadow:var(--spt-shadow-lg);overflow:hidden;border:1px solid var(--spt-gray-200)}.spt-tab-content[_ngcontent-%COMP%]{padding:var(--spt-space-8)}.spt-tab-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:var(--spt-space-8)}.spt-tab-title[_ngcontent-%COMP%]{font-size:var(--spt-text-3xl);font-weight:var(--spt-font-bold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-3) 0}.spt-tab-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-lg);color:var(--spt-gray-600);margin:0;line-height:1.6}.spt-installation-steps[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-6)}.spt-step-card[_ngcontent-%COMP%]{background:var(--spt-gray-50);border-radius:var(--spt-radius-2xl);padding:var(--spt-space-6);border:1px solid var(--spt-gray-200);transition:all .2s ease}.spt-step-card[_ngcontent-%COMP%]:hover{box-shadow:var(--spt-shadow-md);border-color:var(--spt-primary-300)}.spt-step-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-4);margin-bottom:var(--spt-space-4)}.spt-step-number[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-primary-600),var(--spt-primary-700));color:#fff;border-radius:50%;width:48px;height:48px;display:flex;align-items:center;justify-content:center;font-weight:var(--spt-font-bold);font-size:var(--spt-text-lg);box-shadow:var(--spt-shadow-md)}.spt-step-info[_ngcontent-%COMP%]{flex:1}.spt-step-title[_ngcontent-%COMP%]{font-size:var(--spt-text-xl);font-weight:var(--spt-font-semibold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-1) 0}.spt-step-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-gray-600);margin:0;font-weight:var(--spt-font-medium)}.spt-step-content[_ngcontent-%COMP%]{margin-left:64px}.spt-step-description[_ngcontent-%COMP%]{font-size:var(--spt-text-base);color:var(--spt-gray-700);margin:0 0 var(--spt-space-4) 0;line-height:1.6}.spt-code-block[_ngcontent-%COMP%]{background:var(--spt-gray-50);border:1px solid var(--spt-gray-200);border-radius:var(--spt-radius-xl);overflow:hidden;margin:var(--spt-space-4) 0;box-shadow:var(--spt-shadow-sm)}.spt-code-header[_ngcontent-%COMP%]{background:var(--spt-primary-50);padding:var(--spt-space-3) var(--spt-space-4);display:flex;align-items:center;gap:var(--spt-space-2);border-bottom:1px solid var(--spt-primary-200);justify-content:space-between}.spt-code-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-primary-600);font-size:16px;width:16px;height:16px}.spt-code-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--spt-primary-700);font-size:var(--spt-text-sm);font-weight:var(--spt-font-medium)}.spt-copy-btn[_ngcontent-%COMP%]{color:var(--spt-primary-600)!important;transition:all .2s ease!important;background:var(--spt-primary-100)!important;border-radius:var(--spt-radius-md)!important}.spt-copy-btn[_ngcontent-%COMP%]:hover{color:var(--spt-primary-700)!important;background:var(--spt-primary-200)!important}.spt-code-content[_ngcontent-%COMP%]{margin:0;padding:var(--spt-space-4);background:#fff;color:var(--spt-gray-800);font-family:JetBrains Mono,Fira Code,Courier New,monospace;font-size:var(--spt-text-sm);line-height:1.6;overflow-x:auto;border:1px solid var(--spt-gray-100);border-top:none}.spt-step-note[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);margin-top:var(--spt-space-3);padding:var(--spt-space-3);background:var(--spt-info-50);border-radius:var(--spt-radius-lg);color:var(--spt-info-700);border:1px solid var(--spt-info-200)}.spt-step-note[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-info-600);font-size:16px;width:16px;height:16px}.spt-config-sections[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-8)}.spt-config-card[_ngcontent-%COMP%]{background:var(--spt-gray-50);border-radius:var(--spt-radius-2xl);padding:var(--spt-space-6);border:1px solid var(--spt-gray-200)}.spt-config-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-4);margin-bottom:var(--spt-space-4)}.spt-config-icon[_ngcontent-%COMP%]{background:var(--spt-primary-600);color:#fff;border-radius:var(--spt-radius-xl);padding:var(--spt-space-3);font-size:24px;width:24px;height:24px}.spt-config-info[_ngcontent-%COMP%]{flex:1}.spt-config-title[_ngcontent-%COMP%]{font-size:var(--spt-text-xl);font-weight:var(--spt-font-semibold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-1) 0}.spt-config-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-gray-600);margin:0}.spt-config-content[_ngcontent-%COMP%]{margin-left:56px}.spt-settings-table[_ngcontent-%COMP%]{background:#fff;border-radius:var(--spt-radius-xl);overflow:hidden;border:1px solid var(--spt-gray-200)}.spt-table-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1.5fr 3fr;gap:var(--spt-space-4);padding:var(--spt-space-4);background:var(--spt-gray-100);font-weight:var(--spt-font-semibold);color:var(--spt-gray-700);font-size:var(--spt-text-sm)}.spt-table-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1.5fr 3fr;gap:var(--spt-space-4);padding:var(--spt-space-4);border-top:1px solid var(--spt-gray-200);transition:background .2s ease}.spt-table-row[_ngcontent-%COMP%]:hover{background:var(--spt-gray-50)}.spt-table-cell[_ngcontent-%COMP%]{display:flex;align-items:center}.spt-setting-key[_ngcontent-%COMP%]{background:var(--spt-gray-100);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-md);font-family:JetBrains Mono,monospace;font-size:var(--spt-text-xs);color:var(--spt-gray-800)}.spt-setting-value[_ngcontent-%COMP%]{background:var(--spt-success-100);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-md);font-family:JetBrains Mono,monospace;font-size:var(--spt-text-xs);color:var(--spt-success-800)}.spt-setting-desc[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-gray-600);line-height:1.4}.spt-usage-sections[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-4)}.spt-usage-card[_ngcontent-%COMP%]{background:#fff;border-radius:var(--spt-radius-2xl);border:1px solid var(--spt-gray-200);overflow:hidden;transition:all .2s ease}.spt-usage-card[_ngcontent-%COMP%]:hover{box-shadow:var(--spt-shadow-md);border-color:var(--spt-primary-300)}.spt-usage-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-4);padding:var(--spt-space-6);cursor:pointer;transition:background .2s ease}.spt-usage-header[_ngcontent-%COMP%]:hover{background:var(--spt-gray-50)}.spt-usage-icon[_ngcontent-%COMP%]{background:var(--spt-primary-100);color:var(--spt-primary-600);border-radius:var(--spt-radius-xl);padding:var(--spt-space-3);display:flex;align-items:center;justify-content:center}.spt-usage-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.spt-usage-info[_ngcontent-%COMP%]{flex:1}.spt-usage-title[_ngcontent-%COMP%]{font-size:var(--spt-text-xl);font-weight:var(--spt-font-semibold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-1) 0}.spt-usage-description[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-gray-600);margin:0}.spt-expand-icon[_ngcontent-%COMP%]{color:var(--spt-gray-400);transition:transform .2s ease}.spt-expand-icon.spt-expanded[_ngcontent-%COMP%]{transform:rotate(180deg)}.spt-usage-content[_ngcontent-%COMP%]{max-height:0;overflow:hidden;transition:max-height .3s ease}.spt-usage-content.spt-expanded[_ngcontent-%COMP%]{max-height:1000px}.spt-usage-details[_ngcontent-%COMP%]{padding:0 var(--spt-space-6) var(--spt-space-4);color:var(--spt-gray-700);line-height:1.6}.spt-usage-steps[_ngcontent-%COMP%], .spt-usage-tips[_ngcontent-%COMP%]{padding:0 var(--spt-space-6) var(--spt-space-4)}.spt-steps-title[_ngcontent-%COMP%], .spt-tips-title[_ngcontent-%COMP%]{font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-3) 0}.spt-steps-list[_ngcontent-%COMP%]{margin:0;padding-left:var(--spt-space-5);color:var(--spt-gray-700)}.spt-step-item[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-2);line-height:1.5}.spt-tips-list[_ngcontent-%COMP%]{list-style:none;margin:0;padding:0}.spt-tip-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:var(--spt-space-2);margin-bottom:var(--spt-space-3);padding:var(--spt-space-3);background:var(--spt-warning-50);border-radius:var(--spt-radius-lg);border:1px solid var(--spt-warning-200)}.spt-tip-icon[_ngcontent-%COMP%]{color:var(--spt-warning-600);font-size:16px;width:16px;height:16px;margin-top:2px}.spt-troubleshooting-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:var(--spt-space-6)}.spt-issue-card[_ngcontent-%COMP%]{background:#fff;border-radius:var(--spt-radius-2xl);padding:var(--spt-space-6);border:1px solid var(--spt-gray-200);box-shadow:var(--spt-shadow-sm);transition:all .2s ease}.spt-issue-card[_ngcontent-%COMP%]:hover{box-shadow:var(--spt-shadow-md);transform:translateY(-2px)}.spt-issue-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-4);margin-bottom:var(--spt-space-4)}.spt-issue-icon[_ngcontent-%COMP%]{border-radius:var(--spt-radius-xl);padding:var(--spt-space-3);display:flex;align-items:center;justify-content:center}.spt-issue-icon.spt-severity-high[_ngcontent-%COMP%]{background:var(--spt-error-100);color:var(--spt-error-600)}.spt-issue-icon.spt-severity-medium[_ngcontent-%COMP%]{background:var(--spt-warning-100);color:var(--spt-warning-600)}.spt-issue-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.spt-issue-info[_ngcontent-%COMP%]{flex:1}.spt-issue-title[_ngcontent-%COMP%]{font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-1) 0}.spt-issue-description[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-gray-600);margin:0}.spt-solution-section[_ngcontent-%COMP%], .spt-prevention-section[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-4)}.spt-solution-title[_ngcontent-%COMP%], .spt-prevention-title[_ngcontent-%COMP%]{font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold);color:var(--spt-gray-900);margin:0 0 var(--spt-space-2) 0}.spt-solution-steps[_ngcontent-%COMP%]{margin:0;padding-left:var(--spt-space-5);color:var(--spt-gray-700)}.spt-solution-step[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-2);line-height:1.5}.spt-prevention-text[_ngcontent-%COMP%]{margin:0;color:var(--spt-gray-700);line-height:1.6}@media (max-width: 1024px){.spt-features-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.spt-hero-content[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:var(--spt-space-4)}.spt-hero-actions[_ngcontent-%COMP%]{justify-content:center}}@media (max-width: 768px){.spt-hero-section[_ngcontent-%COMP%]{padding:var(--spt-space-8) var(--spt-space-4)}.spt-hero-title[_ngcontent-%COMP%]{font-size:var(--spt-text-4xl)}.spt-hero-subtitle[_ngcontent-%COMP%]{font-size:var(--spt-text-lg)}.spt-hero-actions[_ngcontent-%COMP%]{flex-direction:column;width:100%}.spt-primary-btn[_ngcontent-%COMP%], .spt-secondary-btn[_ngcontent-%COMP%]{width:100%;justify-content:center}.spt-troubleshooting-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.spt-table-header[_ngcontent-%COMP%], .spt-table-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:var(--spt-space-2)}.spt-step-content[_ngcontent-%COMP%], .spt-config-content[_ngcontent-%COMP%]{margin-left:0}}@media (max-width: 480px){.spt-tab-content[_ngcontent-%COMP%], .spt-hero-icon[_ngcontent-%COMP%]{padding:var(--spt-space-4)}.spt-hero-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px}.spt-feature-card[_ngcontent-%COMP%]{padding:var(--spt-space-4)}}\"]\n      });\n    }\n  }\n  return VscodeExtensionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}