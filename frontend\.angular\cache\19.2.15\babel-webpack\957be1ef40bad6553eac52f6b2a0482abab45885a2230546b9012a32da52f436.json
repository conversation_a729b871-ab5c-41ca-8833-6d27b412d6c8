{"ast": null, "code": "/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n  _defaultMatcher;\n  ngControl;\n  _parentFormGroup;\n  _parentForm;\n  _stateChanges;\n  /** Whether the tracker is currently in an error state. */\n  errorState = false;\n  /** User-defined matcher for the error state. */\n  matcher;\n  constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n    this._defaultMatcher = _defaultMatcher;\n    this.ngControl = ngControl;\n    this._parentFormGroup = _parentFormGroup;\n    this._parentForm = _parentForm;\n    this._stateChanges = _stateChanges;\n  }\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState() {\n    const oldState = this.errorState;\n    const parent = this._parentFormGroup || this._parentForm;\n    const matcher = this.matcher || this._defaultMatcher;\n    const control = this.ngControl ? this.ngControl.control : null;\n    const newState = matcher?.isErrorState(control, parent) ?? false;\n    if (newState !== oldState) {\n      this.errorState = newState;\n      this._stateChanges.next();\n    }\n  }\n}\nexport { _ErrorStateTracker as _ };", "map": {"version": 3, "names": ["_ErrorStateTracker", "_defaultMatcher", "ngControl", "_parentFormGroup", "_parentForm", "_stateChanges", "errorState", "matcher", "constructor", "updateErrorState", "oldState", "parent", "control", "newState", "isErrorState", "next", "_"], "sources": ["D:/TGI/Blockchain.SPT/frontend/node_modules/@angular/material/fesm2022/error-state-Dtb1IHM-.mjs"], "sourcesContent": ["/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n    _defaultMatcher;\n    ngControl;\n    _parentFormGroup;\n    _parentForm;\n    _stateChanges;\n    /** Whether the tracker is currently in an error state. */\n    errorState = false;\n    /** User-defined matcher for the error state. */\n    matcher;\n    constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n        this._defaultMatcher = _defaultMatcher;\n        this.ngControl = ngControl;\n        this._parentFormGroup = _parentFormGroup;\n        this._parentForm = _parentForm;\n        this._stateChanges = _stateChanges;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n        const oldState = this.errorState;\n        const parent = this._parentFormGroup || this._parentForm;\n        const matcher = this.matcher || this._defaultMatcher;\n        const control = this.ngControl ? this.ngControl.control : null;\n        const newState = matcher?.isErrorState(control, parent) ?? false;\n        if (newState !== oldState) {\n            this.errorState = newState;\n            this._stateChanges.next();\n        }\n    }\n}\n\nexport { _ErrorStateTracker as _ };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,kBAAkB,CAAC;EACrBC,eAAe;EACfC,SAAS;EACTC,gBAAgB;EAChBC,WAAW;EACXC,aAAa;EACb;EACAC,UAAU,GAAG,KAAK;EAClB;EACAC,OAAO;EACPC,WAAWA,CAACP,eAAe,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,aAAa,EAAE;IAClF,IAAI,CAACJ,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACA;EACAI,gBAAgBA,CAAA,EAAG;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACJ,UAAU;IAChC,MAAMK,MAAM,GAAG,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACC,WAAW;IACxD,MAAMG,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAACN,eAAe;IACpD,MAAMW,OAAO,GAAG,IAAI,CAACV,SAAS,GAAG,IAAI,CAACA,SAAS,CAACU,OAAO,GAAG,IAAI;IAC9D,MAAMC,QAAQ,GAAGN,OAAO,EAAEO,YAAY,CAACF,OAAO,EAAED,MAAM,CAAC,IAAI,KAAK;IAChE,IAAIE,QAAQ,KAAKH,QAAQ,EAAE;MACvB,IAAI,CAACJ,UAAU,GAAGO,QAAQ;MAC1B,IAAI,CAACR,aAAa,CAACU,IAAI,CAAC,CAAC;IAC7B;EACJ;AACJ;AAEA,SAASf,kBAAkB,IAAIgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}