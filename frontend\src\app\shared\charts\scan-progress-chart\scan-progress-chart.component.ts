import { Component, Input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import { Subject, interval, takeUntil } from 'rxjs';

// Register Chart.js components
Chart.register(...registerables);

export interface ScanProgress {
  scanId: string;
  projectName: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number; // 0-100
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  startTime: Date;
  estimatedCompletion?: Date;
  filesScanned: number;
  totalFiles: number;
  issuesFound: number;
}

export interface ProgressDataPoint {
  timestamp: Date;
  progress: number;
  filesScanned: number;
  issuesFound: number;
}

@Component({
  selector: 'app-scan-progress-chart',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule
  ],
  templateUrl: './scan-progress-chart.component.html',
  styleUrls: ['./scan-progress-chart.component.scss']
})
export class ScanProgressChartComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('progressCanvas', { static: true }) progressCanvas!: ElementRef<HTMLCanvasElement>;
  
  @Input() scanProgress: ScanProgress | null = null;
  @Input() showRealTimeUpdates: boolean = true;
  @Input() updateInterval: number = 2000; // milliseconds

  private destroy$ = new Subject<void>();
  private chart: Chart | null = null;
  progressHistory: ProgressDataPoint[] = [];

  // Make Math available in template
  Math = Math;

  // Animation properties
  animatedProgress: number = 0;
  animatedFilesScanned: number = 0;
  animatedIssuesFound: number = 0;

  ngOnInit(): void {
    if (this.showRealTimeUpdates && this.scanProgress?.status === 'running') {
      this.startRealTimeUpdates();
    }
  }

  ngAfterViewInit(): void {
    this.createProgressChart();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    if (this.chart) {
      this.chart.destroy();
    }
  }

  private startRealTimeUpdates(): void {
    interval(this.updateInterval)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.scanProgress?.status === 'running') {
          this.updateProgressData();
          this.animateCounters();
        }
      });
  }

  private updateProgressData(): void {
    if (!this.scanProgress) return;

    const dataPoint: ProgressDataPoint = {
      timestamp: new Date(),
      progress: this.scanProgress.progress,
      filesScanned: this.scanProgress.filesScanned,
      issuesFound: this.scanProgress.issuesFound
    };

    this.progressHistory.push(dataPoint);

    // Keep only last 50 data points for performance
    if (this.progressHistory.length > 50) {
      this.progressHistory.shift();
    }

    this.updateChart();
  }

  private animateCounters(): void {
    if (!this.scanProgress) return;

    // Animate progress
    const progressDiff = this.scanProgress.progress - this.animatedProgress;
    this.animatedProgress += progressDiff * 0.1;

    // Animate files scanned
    const filesDiff = this.scanProgress.filesScanned - this.animatedFilesScanned;
    this.animatedFilesScanned += filesDiff * 0.1;

    // Animate issues found
    const issuesDiff = this.scanProgress.issuesFound - this.animatedIssuesFound;
    this.animatedIssuesFound += issuesDiff * 0.1;
  }

  private createProgressChart(): void {
    if (!this.progressCanvas?.nativeElement) {
      return;
    }

    const ctx = this.progressCanvas.nativeElement.getContext('2d');
    if (!ctx) {
      return;
    }

    // Destroy existing chart
    if (this.chart) {
      this.chart.destroy();
    }

    const config: ChartConfiguration = {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: 'Progress %',
            data: [],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            yAxisID: 'y'
          },
          {
            label: 'Issues Found',
            data: [],
            borderColor: '#ef4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            borderWidth: 2,
            fill: false,
            tension: 0.4,
            yAxisID: 'y1'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          mode: 'index',
          intersect: false,
        },
        plugins: {
          legend: {
            display: true,
            position: 'top',
            labels: {
              usePointStyle: true,
              font: {
                family: 'Inter, sans-serif',
                size: 12,
                weight: 500
              }
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8,
            callbacks: {
              title: (context) => {
                const timestamp = new Date(context[0].label);
                return timestamp.toLocaleTimeString();
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Time',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            ticks: {
              callback: function(value, index) {
                const timestamp = this.getLabelForValue(value as number);
                return new Date(timestamp).toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                });
              },
              font: {
                family: 'Inter, sans-serif'
              }
            }
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: 'Progress (%)',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            min: 0,
            max: 100,
            ticks: {
              font: {
                family: 'Inter, sans-serif'
              }
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: 'Issues Found',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            min: 0,
            grid: {
              drawOnChartArea: false,
            },
            ticks: {
              font: {
                family: 'Inter, sans-serif'
              }
            }
          }
        },
        animation: {
          duration: 750,
          easing: 'easeInOutQuart'
        }
      }
    };

    this.chart = new Chart(ctx, config);
    this.updateChart();
  }

  private updateChart(): void {
    if (!this.chart || this.progressHistory.length === 0) {
      return;
    }

    const labels = this.progressHistory.map(point => point.timestamp.toISOString());
    const progressData = this.progressHistory.map(point => point.progress);
    const issuesData = this.progressHistory.map(point => point.issuesFound);

    this.chart.data.labels = labels;
    this.chart.data.datasets[0].data = progressData;
    this.chart.data.datasets[1].data = issuesData;

    this.chart.update('none');
  }

  getStatusIcon(): string {
    if (!this.scanProgress) return 'help';
    
    switch (this.scanProgress.status) {
      case 'pending': return 'schedule';
      case 'running': return 'hourglass_empty';
      case 'completed': return 'check_circle';
      case 'failed': return 'error';
      default: return 'help';
    }
  }

  getStatusColor(): string {
    if (!this.scanProgress) return 'var(--spt-text-secondary)';
    
    switch (this.scanProgress.status) {
      case 'pending': return 'var(--spt-warning-600)';
      case 'running': return 'var(--spt-info-600)';
      case 'completed': return 'var(--spt-success-600)';
      case 'failed': return 'var(--spt-error-600)';
      default: return 'var(--spt-text-secondary)';
    }
  }

  getEstimatedTimeRemaining(): string {
    if (!this.scanProgress || !this.scanProgress.estimatedCompletion) {
      return 'Calculating...';
    }

    const now = new Date();
    const remaining = this.scanProgress.estimatedCompletion.getTime() - now.getTime();
    
    if (remaining <= 0) {
      return 'Almost done';
    }

    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  getElapsedTime(): string {
    if (!this.scanProgress) return '0s';

    const now = new Date();
    const elapsed = now.getTime() - this.scanProgress.startTime.getTime();
    
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  cancelScan(): void {
    // Emit cancel event or call service
    console.log('Cancel scan requested for:', this.scanProgress?.scanId);
  }

  pauseScan(): void {
    // Emit pause event or call service
    console.log('Pause scan requested for:', this.scanProgress?.scanId);
  }

  getElapsedTimeInMinutes(): number {
    if (!this.scanProgress) return 0;

    const now = new Date();
    const elapsed = now.getTime() - this.scanProgress.startTime.getTime();
    return elapsed / 60000; // Convert to minutes
  }

  getScanSteps(): Array<{name: string, description: string}> {
    return [
      { name: 'Initialize', description: 'Setting up scan environment' },
      { name: 'File Discovery', description: 'Finding smart contract files' },
      { name: 'Static Analysis', description: 'Analyzing code structure' },
      { name: 'Security Checks', description: 'Running security rules' },
      { name: 'Vulnerability Detection', description: 'Identifying security issues' },
      { name: 'Report Generation', description: 'Compiling results' }
    ];
  }
}
