{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, inject, ElementRef, ChangeDetectorRef, EventEmitter, HostAttributeToken, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, Input, Output, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { FocusMonitor, _IdGenerator } from '@angular/cdk/a11y';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { _ as _MatInternalFormField } from './internal-form-field-grv62mCZ.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\nconst _c0 = [\"switch\"];\nconst _c1 = [\"*\"];\nfunction MatSlideToggle_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 12);\n    i0.ɵɵelement(2, \"path\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"svg\", 14);\n    i0.ɵɵelement(4, \"path\", 15);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-slide-toggle-default-options', {\n  providedIn: 'root',\n  factory: () => ({\n    disableToggleValue: false,\n    hideIcon: false,\n    disabledInteractive: false\n  })\n});\n\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatSlideToggle),\n  multi: true\n};\n/** Change event object emitted by a slide toggle. */\nclass MatSlideToggleChange {\n  source;\n  checked;\n  constructor(/** The source slide toggle of the event. */\n  source, /** The new `checked` value of the slide toggle. */\n  checked) {\n    this.source = source;\n    this.checked = checked;\n  }\n}\nlet MatSlideToggle = /*#__PURE__*/(() => {\n  class MatSlideToggle {\n    _elementRef = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    defaults = inject(MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS);\n    _onChange = _ => {};\n    _onTouched = () => {};\n    _validatorOnChange = () => {};\n    _uniqueId;\n    _checked = false;\n    _createChangeEvent(isChecked) {\n      return new MatSlideToggleChange(this, isChecked);\n    }\n    /** Unique ID for the label element. */\n    _labelId;\n    /** Returns the unique id for the visual hidden button. */\n    get buttonId() {\n      return `${this.id || this._uniqueId}-button`;\n    }\n    /** Reference to the MDC switch element. */\n    _switchElement;\n    /** Focuses the slide-toggle. */\n    focus() {\n      this._switchElement.nativeElement.focus();\n    }\n    /** Whether noop animations are enabled. */\n    _noopAnimations;\n    /** Whether the slide toggle is currently focused. */\n    _focused;\n    /** Name value will be applied to the input element if present. */\n    name = null;\n    /** A unique id for the slide-toggle input. If none is supplied, it will be auto-generated. */\n    id;\n    /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n    labelPosition = 'after';\n    /** Used to set the aria-label attribute on the underlying input element. */\n    ariaLabel = null;\n    /** Used to set the aria-labelledby attribute on the underlying input element. */\n    ariaLabelledby = null;\n    /** Used to set the aria-describedby attribute on the underlying input element. */\n    ariaDescribedby;\n    /** Whether the slide-toggle is required. */\n    required;\n    // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n    // the lack of type checking previously and assigning random strings.\n    /**\n     * Theme color of the slide toggle. This API is supported in M2 themes only,\n     * it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/slide-toggle/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the slide toggle is disabled. */\n    disabled = false;\n    /** Whether the slide toggle has a ripple. */\n    disableRipple = false;\n    /** Tabindex of slide toggle. */\n    tabIndex = 0;\n    /** Whether the slide-toggle element is checked or not. */\n    get checked() {\n      return this._checked;\n    }\n    set checked(value) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Whether to hide the icon inside of the slide toggle. */\n    hideIcon;\n    /** Whether the slide toggle should remain interactive when it is disabled. */\n    disabledInteractive;\n    /** An event will be dispatched each time the slide-toggle changes its value. */\n    change = new EventEmitter();\n    /**\n     * An event will be dispatched each time the slide-toggle input is toggled.\n     * This event is always emitted when the user toggles the slide toggle, but this does not mean\n     * the slide toggle's value has changed.\n     */\n    toggleChange = new EventEmitter();\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n      return `${this.id || this._uniqueId}-input`;\n    }\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const tabIndex = inject(new HostAttributeToken('tabindex'), {\n        optional: true\n      });\n      const defaults = this.defaults;\n      const animationMode = inject(ANIMATION_MODULE_TYPE, {\n        optional: true\n      });\n      this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n      this.color = defaults.color || 'accent';\n      this._noopAnimations = animationMode === 'NoopAnimations';\n      this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-slide-toggle-');\n      this.hideIcon = defaults.hideIcon ?? false;\n      this.disabledInteractive = defaults.disabledInteractive ?? false;\n      this._labelId = this._uniqueId + '-label';\n    }\n    ngAfterContentInit() {\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n        if (focusOrigin === 'keyboard' || focusOrigin === 'program') {\n          this._focused = true;\n          this._changeDetectorRef.markForCheck();\n        } else if (!focusOrigin) {\n          // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n          // Angular does not expect events to be raised during change detection, so any state\n          // change (such as a form control's ng-touched) will cause a changed-after-checked error.\n          // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n          // telling the form control it has been touched until the next tick.\n          Promise.resolve().then(() => {\n            this._focused = false;\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n          });\n        }\n      });\n    }\n    ngOnChanges(changes) {\n      if (changes['required']) {\n        this._validatorOnChange();\n      }\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    writeValue(value) {\n      this.checked = !!value;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /** Implemented as a part of Validator. */\n    validate(control) {\n      return this.required && control.value !== true ? {\n        'required': true\n      } : null;\n    }\n    /** Implemented as a part of Validator. */\n    registerOnValidatorChange(fn) {\n      this._validatorOnChange = fn;\n    }\n    /** Implemented as a part of ControlValueAccessor. */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Toggles the checked state of the slide-toggle. */\n    toggle() {\n      this.checked = !this.checked;\n      this._onChange(this.checked);\n    }\n    /**\n     * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n     */\n    _emitChangeEvent() {\n      this._onChange(this.checked);\n      this.change.emit(this._createChangeEvent(this.checked));\n    }\n    /** Method being called whenever the underlying button is clicked. */\n    _handleClick() {\n      if (!this.disabled) {\n        this.toggleChange.emit();\n        if (!this.defaults.disableToggleValue) {\n          this.checked = !this.checked;\n          this._onChange(this.checked);\n          this.change.emit(new MatSlideToggleChange(this, this.checked));\n        }\n      }\n    }\n    _getAriaLabelledBy() {\n      if (this.ariaLabelledby) {\n        return this.ariaLabelledby;\n      }\n      // Even though we have a `label` element with a `for` pointing to the button, we need the\n      // `aria-labelledby`, because the button gets flagged as not having a label by tools like axe.\n      return this.ariaLabel ? null : this._labelId;\n    }\n    static ɵfac = function MatSlideToggle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSlideToggle)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSlideToggle,\n      selectors: [[\"mat-slide-toggle\"]],\n      viewQuery: function MatSlideToggle_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._switchElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-slide-toggle\"],\n      hostVars: 13,\n      hostBindings: function MatSlideToggle_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"name\", null)(\"aria-labelledby\", null);\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n          i0.ɵɵclassProp(\"mat-mdc-slide-toggle-focused\", ctx._focused)(\"mat-mdc-slide-toggle-checked\", ctx.checked)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        name: \"name\",\n        id: \"id\",\n        labelPosition: \"labelPosition\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n        required: [2, \"required\", \"required\", booleanAttribute],\n        color: \"color\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        checked: [2, \"checked\", \"checked\", booleanAttribute],\n        hideIcon: [2, \"hideIcon\", \"hideIcon\", booleanAttribute],\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\",\n        toggleChange: \"toggleChange\"\n      },\n      exportAs: [\"matSlideToggle\"],\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatSlideToggle,\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c1,\n      decls: 13,\n      vars: 27,\n      consts: [[\"switch\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"labelPosition\"], [\"role\", \"switch\", \"type\", \"button\", 1, \"mdc-switch\", 3, \"click\", \"tabIndex\", \"disabled\"], [1, \"mdc-switch__track\"], [1, \"mdc-switch__handle-track\"], [1, \"mdc-switch__handle\"], [1, \"mdc-switch__shadow\"], [1, \"mdc-elevation-overlay\"], [1, \"mdc-switch__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-slide-toggle-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-switch__icons\"], [1, \"mdc-label\", 3, \"click\", \"for\"], [\"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-switch__icon\", \"mdc-switch__icon--on\"], [\"d\", \"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\"], [\"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-switch__icon\", \"mdc-switch__icon--off\"], [\"d\", \"M20 13H4v-2h16v2z\"]],\n      template: function MatSlideToggle_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2, 0);\n          i0.ɵɵlistener(\"click\", function MatSlideToggle_Template_button_click_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleClick());\n          });\n          i0.ɵɵelement(3, \"span\", 3);\n          i0.ɵɵelementStart(4, \"span\", 4)(5, \"span\", 5)(6, \"span\", 6);\n          i0.ɵɵelement(7, \"span\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"span\", 8);\n          i0.ɵɵelement(9, \"span\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, MatSlideToggle_Conditional_10_Template, 5, 0, \"span\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵlistener(\"click\", function MatSlideToggle_Template_label_click_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event.stopPropagation());\n          });\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const switch_r2 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"mdc-switch--selected\", ctx.checked)(\"mdc-switch--unselected\", !ctx.checked)(\"mdc-switch--checked\", ctx.checked)(\"mdc-switch--disabled\", ctx.disabled)(\"mat-mdc-slide-toggle-disabled-interactive\", ctx.disabledInteractive);\n          i0.ɵɵproperty(\"tabIndex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"disabled\", ctx.disabled && !ctx.disabledInteractive);\n          i0.ɵɵattribute(\"id\", ctx.buttonId)(\"name\", ctx.name)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx._getAriaLabelledBy())(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-required\", ctx.required || null)(\"aria-checked\", ctx.checked)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matRippleTrigger\", switch_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.hideIcon ? 10 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"for\", ctx.buttonId);\n          i0.ɵɵattribute(\"id\", ctx._labelId);\n        }\n      },\n      dependencies: [MatRipple, _MatInternalFormField],\n      styles: [\".mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSlideToggle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => MatSlideToggleRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nlet MatSlideToggleRequiredValidator = /*#__PURE__*/(() => {\n  class MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatSlideToggleRequiredValidator_BaseFactory;\n      return function MatSlideToggleRequiredValidator_Factory(__ngFactoryType__) {\n        return (ɵMatSlideToggleRequiredValidator_BaseFactory || (ɵMatSlideToggleRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatSlideToggleRequiredValidator)))(__ngFactoryType__ || MatSlideToggleRequiredValidator);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSlideToggleRequiredValidator,\n      selectors: [[\"mat-slide-toggle\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"formControl\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"ngModel\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatSlideToggleRequiredValidator;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @deprecated No longer used, `MatSlideToggle` implements required validation directly.\n * @breaking-change 19.0.0\n */\nlet _MatSlideToggleRequiredValidatorModule = /*#__PURE__*/(() => {\n  class _MatSlideToggleRequiredValidatorModule {\n    static ɵfac = function _MatSlideToggleRequiredValidatorModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _MatSlideToggleRequiredValidatorModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: _MatSlideToggleRequiredValidatorModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return _MatSlideToggleRequiredValidatorModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSlideToggleModule = /*#__PURE__*/(() => {\n  class MatSlideToggleModule {\n    static ɵfac = function MatSlideToggleModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSlideToggleModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSlideToggleModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatSlideToggle, MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatSlideToggleModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS, MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR, MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, MatSlideToggle, MatSlideToggleChange, MatSlideToggleModule, MatSlideToggleRequiredValidator, _MatSlideToggleRequiredValidatorModule };\n//# sourceMappingURL=slide-toggle.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}