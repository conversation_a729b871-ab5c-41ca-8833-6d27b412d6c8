{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nfunction VscodeExtensionComponent_div_29_div_9_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 57)(1, \"mat-icon\", 58);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const benefit_r1 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(benefit_r1);\n  }\n}\nfunction VscodeExtensionComponent_div_29_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"ul\", 55);\n    i0.ɵɵtemplate(2, VscodeExtensionComponent_div_29_div_9_li_2_Template, 5, 1, \"li\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", feature_r2.benefits);\n  }\n}\nfunction VscodeExtensionComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 50)(5, \"h3\", 51);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 52);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, VscodeExtensionComponent_div_29_div_9_Template, 3, 1, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", feature_r2.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", feature_r2.benefits.length > 0);\n  }\n}\nfunction VscodeExtensionComponent_div_40_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Command\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function VscodeExtensionComponent_div_40_div_12_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const step_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.copyToClipboard(step_r4.command));\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"pre\", 36)(10, \"code\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(step_r4.command);\n  }\n}\nfunction VscodeExtensionComponent_div_40_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(step_r4.notes);\n  }\n}\nfunction VscodeExtensionComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"div\", 61);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 62)(5, \"h3\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 64);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 65)(10, \"p\", 66);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, VscodeExtensionComponent_div_40_div_12_Template, 12, 1, \"div\", 67)(13, VscodeExtensionComponent_div_40_div_13_Template, 5, 1, \"div\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.subtitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.notes);\n  }\n}\nfunction VscodeExtensionComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 40)(2, \"code\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 40)(5, \"span\", 72);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"code\", 73);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 40)(11, \"span\", 74);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const setting_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.key);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.default);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r7.description);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_15_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r10);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"h4\", 87);\n    i0.ɵɵtext(2, \"Steps:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ol\", 88);\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_div_121_div_15_li_4_Template, 2, 1, \"li\", 89);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r9.steps);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_16_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 95)(1, \"mat-icon\", 96);\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r11 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r11);\n  }\n}\nfunction VscodeExtensionComponent_div_121_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"h4\", 92);\n    i0.ɵɵtext(2, \"Tips:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 93);\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_div_121_div_16_li_4_Template, 5, 1, \"li\", 94);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r9.tips);\n  }\n}\nfunction VscodeExtensionComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76);\n    i0.ɵɵlistener(\"click\", function VscodeExtensionComponent_div_121_Template_div_click_1_listener() {\n      const usage_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      return i0.ɵɵresetView(usage_r9.expanded = !usage_r9.expanded);\n    });\n    i0.ɵɵelementStart(2, \"div\", 77)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78)(6, \"h3\", 79);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 80);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-icon\", 81);\n    i0.ɵɵtext(11, \"expand_more\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 82)(13, \"p\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, VscodeExtensionComponent_div_121_div_15_Template, 5, 1, \"div\", 84)(16, VscodeExtensionComponent_div_121_div_16_Template, 5, 1, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r9 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(usage_r9.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(usage_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(usage_r9.description);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"spt-expanded\", usage_r9.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"spt-expanded\", usage_r9.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(usage_r9.details);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r9.steps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r9.tips);\n  }\n}\nfunction VscodeExtensionComponent_div_130_li_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 109);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r12);\n  }\n}\nfunction VscodeExtensionComponent_div_130_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"h4\", 111);\n    i0.ɵɵtext(2, \"Prevention:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 112);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(issue_r13.prevention);\n  }\n}\nfunction VscodeExtensionComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 99)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 100)(6, \"h3\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 102);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 103)(11, \"div\", 104)(12, \"h4\", 105);\n    i0.ɵɵtext(13, \"Solution:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ol\", 106);\n    i0.ɵɵtemplate(15, VscodeExtensionComponent_div_130_li_15_Template, 2, 1, \"li\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, VscodeExtensionComponent_div_130_div_16_Template, 5, 1, \"div\", 108);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"spt-severity-\" + issue_r13.severity);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r13.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r13.problem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r13.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", issue_r13.solution);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", issue_r13.prevention);\n  }\n}\nexport class VscodeExtensionComponent {\n  constructor() {\n    this.configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n    this.primaryFeatures = [{\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities and smart suggestions.',\n      icon: 'security',\n      color: 'linear-gradient(135deg, var(--spt-primary-500), var(--spt-primary-600))',\n      benefits: ['Immediate vulnerability detection', 'Reduced security debt', 'Faster development cycles', 'Proactive security measures']\n    }, {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues with severity-based color coding.',\n      icon: 'visibility',\n      color: 'linear-gradient(135deg, var(--spt-success-500), var(--spt-success-600))',\n      benefits: ['Clear visual feedback', 'Context-aware highlighting', 'Severity-based color coding', 'Non-intrusive indicators']\n    }, {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and quick fixes.',\n      icon: 'bug_report',\n      color: 'linear-gradient(135deg, var(--spt-error-500), var(--spt-error-600))',\n      benefits: ['Centralized issue tracking', 'Detailed error descriptions', 'Quick navigation to issues', 'Integration with existing workflow']\n    }, {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code for instant access.',\n      icon: 'lens',\n      color: 'linear-gradient(135deg, var(--spt-warning-500), var(--spt-warning-600))',\n      benefits: ['Contextual security metrics', 'One-click security actions', 'Code quality insights', 'Performance recommendations']\n    }, {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements with rich documentation.',\n      icon: 'info',\n      color: 'linear-gradient(135deg, var(--spt-info-500), var(--spt-info-600))',\n      benefits: ['Instant security documentation', 'Best practice suggestions', 'Vulnerability explanations', 'Quick reference access']\n    }, {\n      title: 'Multi-chain Support',\n      description: 'Comprehensive support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      color: 'linear-gradient(135deg, var(--spt-secondary-500), var(--spt-secondary-600))',\n      benefits: ['Comprehensive blockchain coverage', 'Chain-specific security rules', 'Unified security approach', 'Extensible architecture']\n    }];\n    this.installationSteps = [{\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    }, {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    }, {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }];\n    this.configSettings = [{\n      key: 'spt.enabled',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable/disable SPT security analysis'\n    }, {\n      key: 'spt.serverUrl',\n      type: 'string',\n      default: 'http://localhost:8080',\n      description: 'SPT backend server URL'\n    }, {\n      key: 'spt.apiKey',\n      type: 'string',\n      default: '\"\"',\n      description: 'API key for authentication'\n    }, {\n      key: 'spt.autoScan',\n      type: 'boolean',\n      default: 'true',\n      description: 'Automatically scan files on save'\n    }, {\n      key: 'spt.scanOnOpen',\n      type: 'boolean',\n      default: 'false',\n      description: 'Automatically scan files when opened'\n    }, {\n      key: 'spt.chains',\n      type: 'array',\n      default: '[\"ethereum\", \"bitcoin\", \"general\"]',\n      description: 'Blockchain chains to analyze'\n    }, {\n      key: 'spt.severity',\n      type: 'string',\n      default: '\"medium\"',\n      description: 'Minimum severity level to show'\n    }, {\n      key: 'spt.showInlineDecorations',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show inline security decorations'\n    }, {\n      key: 'spt.showProblems',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show security issues in Problems panel'\n    }, {\n      key: 'spt.enableCodeLens',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security-related CodeLens'\n    }, {\n      key: 'spt.enableHover',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security information on hover'\n    }];\n    this.usageExamples = [{\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: ['Open a blockchain project in VS Code', 'Save a file to trigger automatic scanning', 'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"', 'View results in Problems panel or inline decorations'],\n      tips: ['Enable auto-scan for continuous security monitoring', 'Use the Problems panel to navigate between issues', 'Check the status bar for scan progress']\n    }, {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: ['Hover over highlighted code to see issue details', 'Click on issues in Problems panel for more information', 'Use CodeLens actions for quick fixes', 'Follow the recommended solutions'],\n      tips: ['Start with critical and high severity issues', 'Use hover information for quick context', 'Check references for additional learning']\n    }, {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: ['Open VS Code settings (Ctrl+,)', 'Search for \"SPT\" to find extension settings', 'Adjust chains, severity, and scan triggers', 'Save settings and restart if needed'],\n      tips: ['Use workspace settings for project-specific configuration', 'Adjust severity threshold based on project maturity', 'Enable scan-on-open for comprehensive coverage']\n    }];\n    this.troubleshootingIssues = [{\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: ['Verify SPT backend server is running on configured port', 'Check spt.serverUrl setting in VS Code preferences', 'Ensure firewall is not blocking the connection', 'Try restarting VS Code and the SPT server'],\n      prevention: 'Always start the SPT backend server before using the extension'\n    }, {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: ['Check if the file type is supported (Solidity, JavaScript, etc.)', 'Verify the correct blockchain chains are selected', 'Lower the severity threshold in settings', 'Ensure the file contains actual security-relevant code'],\n      prevention: 'Review supported file types and ensure proper project structure'\n    }, {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: ['Disable auto-scan and use manual scanning', 'Increase scan timeout in settings', 'Exclude large files or directories from scanning', 'Reduce the number of enabled blockchain chains'],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }];\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      // Could add a toast notification here\n      console.log('Copied to clipboard');\n    }).catch(err => {\n      console.error('Failed to copy: ', err);\n    });\n  }\n  static {\n    this.ɵfac = function VscodeExtensionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VscodeExtensionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VscodeExtensionComponent,\n      selectors: [[\"app-vscode-extension\"]],\n      decls: 131,\n      vars: 6,\n      consts: [[1, \"spt-vscode-container\"], [1, \"spt-hero-section\"], [1, \"spt-hero-content\"], [1, \"spt-hero-icon\"], [1, \"spt-hero-text\"], [1, \"spt-hero-title\"], [1, \"spt-hero-subtitle\"], [1, \"spt-hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"spt-primary-btn\"], [\"mat-stroked-button\", \"\", 1, \"spt-secondary-btn\"], [1, \"spt-features-overview\"], [1, \"spt-section-header\"], [1, \"spt-section-title\"], [1, \"spt-section-subtitle\"], [1, \"spt-features-grid\"], [\"class\", \"spt-feature-card spt-feature-primary\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-details-section\"], [\"animationDuration\", \"300ms\", 1, \"spt-tab-group\"], [\"label\", \"Installation\"], [1, \"spt-tab-content\"], [1, \"spt-tab-header\"], [1, \"spt-tab-title\"], [1, \"spt-tab-subtitle\"], [1, \"spt-installation-steps\"], [\"class\", \"spt-step-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"spt-config-sections\"], [1, \"spt-config-card\"], [1, \"spt-config-header\"], [1, \"spt-config-icon\"], [1, \"spt-config-info\"], [1, \"spt-config-title\"], [1, \"spt-config-subtitle\"], [1, \"spt-config-content\"], [1, \"spt-code-block\"], [1, \"spt-code-header\"], [1, \"spt-code-content\"], [\"mat-icon-button\", \"\", 1, \"spt-copy-btn\", 3, \"click\"], [1, \"spt-settings-table\"], [1, \"spt-table-header\"], [1, \"spt-table-cell\"], [\"class\", \"spt-table-row\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Usage\"], [1, \"spt-usage-sections\"], [\"class\", \"spt-usage-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Troubleshooting\"], [1, \"spt-troubleshooting-grid\"], [\"class\", \"spt-issue-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-feature-card\", \"spt-feature-primary\"], [1, \"spt-feature-icon\"], [1, \"spt-feature-content\"], [1, \"spt-feature-title\"], [1, \"spt-feature-description\"], [\"class\", \"spt-feature-benefits\", 4, \"ngIf\"], [1, \"spt-feature-benefits\"], [1, \"spt-benefits-list\"], [\"class\", \"spt-benefit-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-benefit-item\"], [1, \"spt-benefit-icon\"], [1, \"spt-step-card\"], [1, \"spt-step-header\"], [1, \"spt-step-number\"], [1, \"spt-step-info\"], [1, \"spt-step-title\"], [1, \"spt-step-subtitle\"], [1, \"spt-step-content\"], [1, \"spt-step-description\"], [\"class\", \"spt-code-block\", 4, \"ngIf\"], [\"class\", \"spt-step-note\", 4, \"ngIf\"], [1, \"spt-step-note\"], [1, \"spt-table-row\"], [1, \"spt-setting-key\"], [1, \"spt-badge\", \"spt-badge-info\"], [1, \"spt-setting-value\"], [1, \"spt-setting-desc\"], [1, \"spt-usage-card\"], [1, \"spt-usage-header\", 3, \"click\"], [1, \"spt-usage-icon\"], [1, \"spt-usage-info\"], [1, \"spt-usage-title\"], [1, \"spt-usage-description\"], [1, \"spt-expand-icon\"], [1, \"spt-usage-content\"], [1, \"spt-usage-details\"], [\"class\", \"spt-usage-steps\", 4, \"ngIf\"], [\"class\", \"spt-usage-tips\", 4, \"ngIf\"], [1, \"spt-usage-steps\"], [1, \"spt-steps-title\"], [1, \"spt-steps-list\"], [\"class\", \"spt-step-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-step-item\"], [1, \"spt-usage-tips\"], [1, \"spt-tips-title\"], [1, \"spt-tips-list\"], [\"class\", \"spt-tip-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"spt-tip-item\"], [1, \"spt-tip-icon\"], [1, \"spt-issue-card\"], [1, \"spt-issue-header\"], [1, \"spt-issue-icon\"], [1, \"spt-issue-info\"], [1, \"spt-issue-title\"], [1, \"spt-issue-description\"], [1, \"spt-issue-content\"], [1, \"spt-solution-section\"], [1, \"spt-solution-title\"], [1, \"spt-solution-steps\"], [\"class\", \"spt-solution-step\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"spt-prevention-section\", 4, \"ngIf\"], [1, \"spt-solution-step\"], [1, \"spt-prevention-section\"], [1, \"spt-prevention-title\"], [1, \"spt-prevention-text\"]],\n      template: function VscodeExtensionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"extension\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"h1\", 5);\n          i0.ɵɵtext(8, \"VS Code Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 6);\n          i0.ɵɵtext(10, \" Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"button\", 8)(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Install Extension\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"button\", 9)(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\");\n          i0.ɵɵtext(21, \"View Source\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"section\", 10)(23, \"div\", 11)(24, \"h2\", 12);\n          i0.ɵɵtext(25, \"Key Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 13);\n          i0.ɵɵtext(27, \" Discover the powerful features that make SPT extension essential for secure blockchain development. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 14);\n          i0.ɵɵtemplate(29, VscodeExtensionComponent_div_29_Template, 10, 6, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"section\", 16)(31, \"mat-tab-group\", 17)(32, \"mat-tab\", 18)(33, \"div\", 19)(34, \"div\", 20)(35, \"h2\", 21);\n          i0.ɵɵtext(36, \"Installation Guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"p\", 22);\n          i0.ɵɵtext(38, \"Get the SPT extension installed and configured in VS Code quickly and easily.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 23);\n          i0.ɵɵtemplate(40, VscodeExtensionComponent_div_40_Template, 14, 6, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"mat-tab\", 25)(42, \"div\", 19)(43, \"div\", 20)(44, \"h2\", 21);\n          i0.ɵɵtext(45, \"Extension Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\", 22);\n          i0.ɵɵtext(47, \"Customize the SPT extension to fit your development workflow and preferences.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 26)(49, \"div\", 27)(50, \"div\", 28)(51, \"mat-icon\", 29);\n          i0.ɵɵtext(52, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 30)(54, \"h3\", 31);\n          i0.ɵɵtext(55, \"Settings Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\", 32);\n          i0.ɵɵtext(57, \"Configure extension behavior\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 33)(59, \"p\");\n          i0.ɵɵtext(60, \"Access extension settings through VS Code preferences:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 34)(62, \"div\", 35)(63, \"mat-icon\");\n          i0.ɵɵtext(64, \"keyboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\");\n          i0.ɵɵtext(66, \"Keyboard Shortcut\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"pre\", 36)(68, \"code\");\n          i0.ɵɵtext(69, \"Ctrl+Shift+P \\u2192 \\\"Preferences: Open Settings (JSON)\\\"\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(70, \"div\", 27)(71, \"div\", 28)(72, \"mat-icon\", 29);\n          i0.ɵɵtext(73, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 30)(75, \"h3\", 31);\n          i0.ɵɵtext(76, \"Configuration Example\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"p\", 32);\n          i0.ɵɵtext(78, \"settings.json\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"div\", 33)(80, \"div\", 34)(81, \"div\", 35)(82, \"mat-icon\");\n          i0.ɵɵtext(83, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"span\");\n          i0.ɵɵtext(85, \"JSON Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function VscodeExtensionComponent_Template_button_click_86_listener() {\n            return ctx.copyToClipboard(ctx.configExample);\n          });\n          i0.ɵɵelementStart(87, \"mat-icon\");\n          i0.ɵɵtext(88, \"content_copy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(89, \"pre\", 36)(90, \"code\");\n          i0.ɵɵtext(91);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(92, \"div\", 27)(93, \"div\", 28)(94, \"mat-icon\", 29);\n          i0.ɵɵtext(95, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"div\", 30)(97, \"h3\", 31);\n          i0.ɵɵtext(98, \"Available Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"p\", 32);\n          i0.ɵɵtext(100, \"Complete settings reference\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"div\", 33)(102, \"div\", 38)(103, \"div\", 39)(104, \"div\", 40);\n          i0.ɵɵtext(105, \"Setting\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"div\", 40);\n          i0.ɵɵtext(107, \"Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"div\", 40);\n          i0.ɵɵtext(109, \"Default\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 40);\n          i0.ɵɵtext(111, \"Description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(112, VscodeExtensionComponent_div_112_Template, 13, 4, \"div\", 41);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(113, \"mat-tab\", 42)(114, \"div\", 19)(115, \"div\", 20)(116, \"h2\", 21);\n          i0.ɵɵtext(117, \"Using the Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"p\", 22);\n          i0.ɵɵtext(119, \"Learn how to effectively use SPT extension features in your daily development workflow.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(120, \"div\", 43);\n          i0.ɵɵtemplate(121, VscodeExtensionComponent_div_121_Template, 17, 10, \"div\", 44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"mat-tab\", 45)(123, \"div\", 19)(124, \"div\", 20)(125, \"h2\", 21);\n          i0.ɵɵtext(126, \"Troubleshooting\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"p\", 22);\n          i0.ɵɵtext(128, \"Common issues and solutions for the SPT VS Code extension.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(129, \"div\", 46);\n          i0.ɵɵtemplate(130, VscodeExtensionComponent_div_130_Template, 17, 7, \"div\", 47);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngForOf\", ctx.primaryFeatures);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.installationSteps);\n          i0.ɵɵadvance(51);\n          i0.ɵɵtextInterpolate(ctx.configExample);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.configSettings);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.usageExamples);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.troubleshootingIssues);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, MatIconModule, i3.MatIcon, MatButtonModule, i4.MatButton, i4.MatIconButton, MatChipsModule, MatExpansionModule, MatTableModule],\n      styles: [\"\\n\\n.spt-vscode-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  font-family: \\\"Inter\\\", sans-serif;\\n}\\n\\n\\n\\n.spt-hero-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-primary-50) 0%, var(--spt-secondary-50) 100%);\\n  border-radius: var(--spt-radius-3xl);\\n  padding: var(--spt-space-12) var(--spt-space-8);\\n  margin-bottom: var(--spt-space-12);\\n  border: 1px solid var(--spt-primary-200);\\n}\\n\\n.spt-hero-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-6);\\n  margin-bottom: var(--spt-space-8);\\n}\\n\\n.spt-hero-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);\\n  border-radius: var(--spt-radius-3xl);\\n  padding: var(--spt-space-6);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: var(--spt-shadow-xl);\\n}\\n\\n.spt-hero-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n}\\n\\n.spt-hero-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.spt-hero-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-5xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-4) 0;\\n  letter-spacing: -0.025em;\\n}\\n\\n.spt-hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xl);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n  line-height: 1.6;\\n  font-weight: var(--spt-font-normal);\\n}\\n\\n.spt-hero-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spt-space-4);\\n  align-items: center;\\n}\\n\\n.spt-primary-btn[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-600) !important;\\n  color: white !important;\\n  font-weight: var(--spt-font-semibold) !important;\\n  padding: var(--spt-space-4) var(--spt-space-6) !important;\\n  border-radius: var(--spt-radius-xl) !important;\\n  box-shadow: var(--spt-shadow-md) !important;\\n  transition: all 0.2s ease !important;\\n}\\n\\n.spt-primary-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-primary-700) !important;\\n  box-shadow: var(--spt-shadow-lg) !important;\\n  transform: translateY(-2px);\\n}\\n\\n.spt-secondary-btn[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-700) !important;\\n  border-color: var(--spt-gray-300) !important;\\n  font-weight: var(--spt-font-medium) !important;\\n  padding: var(--spt-space-4) var(--spt-space-6) !important;\\n  border-radius: var(--spt-radius-xl) !important;\\n  transition: all 0.2s ease !important;\\n}\\n\\n.spt-secondary-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-gray-100) !important;\\n  border-color: var(--spt-gray-400) !important;\\n  transform: translateY(-1px);\\n}\\n\\n\\n\\n.spt-section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: var(--spt-space-12);\\n}\\n\\n.spt-section-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-4xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-4) 0;\\n  letter-spacing: -0.025em;\\n}\\n\\n.spt-section-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-lg);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n  max-width: 600px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  line-height: 1.6;\\n}\\n\\n\\n\\n.spt-features-overview[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-16);\\n}\\n\\n.spt-features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: var(--spt-space-8);\\n}\\n\\n.spt-feature-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--spt-radius-3xl);\\n  padding: var(--spt-space-8);\\n  border: 1px solid var(--spt-gray-200);\\n  box-shadow: var(--spt-shadow-sm);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.spt-feature-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--spt-shadow-xl);\\n  transform: translateY(-4px);\\n  border-color: var(--spt-primary-300);\\n}\\n\\n.spt-feature-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, var(--spt-primary-500), var(--spt-secondary-500));\\n}\\n\\n.spt-feature-icon[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: var(--spt-radius-2xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: var(--spt-space-6);\\n  box-shadow: var(--spt-shadow-md);\\n}\\n\\n.spt-feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.spt-feature-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.spt-feature-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-2xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-3) 0;\\n}\\n\\n.spt-feature-description[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  color: var(--spt-gray-600);\\n  margin: 0 0 var(--spt-space-6) 0;\\n  line-height: 1.6;\\n}\\n\\n.spt-feature-benefits[_ngcontent-%COMP%] {\\n  margin-top: var(--spt-space-4);\\n}\\n\\n.spt-benefits-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.spt-benefit-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  margin-bottom: var(--spt-space-2);\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-gray-700);\\n}\\n\\n.spt-benefit-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-success-600);\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.spt-details-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-16);\\n}\\n\\n.spt-tab-group[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--spt-radius-2xl);\\n  box-shadow: var(--spt-shadow-lg);\\n  overflow: hidden;\\n  border: 1px solid var(--spt-gray-200);\\n}\\n\\n.spt-tab-content[_ngcontent-%COMP%] {\\n  padding: var(--spt-space-8);\\n}\\n\\n.spt-tab-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: var(--spt-space-8);\\n}\\n\\n.spt-tab-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-3xl);\\n  font-weight: var(--spt-font-bold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-3) 0;\\n}\\n\\n.spt-tab-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-lg);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n  line-height: 1.6;\\n}\\n\\n\\n\\n.spt-installation-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-6);\\n}\\n\\n.spt-step-card[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-50);\\n  border-radius: var(--spt-radius-2xl);\\n  padding: var(--spt-space-6);\\n  border: 1px solid var(--spt-gray-200);\\n  transition: all 0.2s ease;\\n}\\n\\n.spt-step-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--spt-shadow-md);\\n  border-color: var(--spt-primary-300);\\n}\\n\\n.spt-step-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-4);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.spt-step-number[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--spt-primary-600), var(--spt-primary-700));\\n  color: white;\\n  border-radius: 50%;\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: var(--spt-font-bold);\\n  font-size: var(--spt-text-lg);\\n  box-shadow: var(--spt-shadow-md);\\n}\\n\\n.spt-step-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.spt-step-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xl);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-1) 0;\\n}\\n\\n.spt-step-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n  font-weight: var(--spt-font-medium);\\n}\\n\\n.spt-step-content[_ngcontent-%COMP%] {\\n  margin-left: 64px;\\n}\\n\\n.spt-step-description[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  color: var(--spt-gray-700);\\n  margin: 0 0 var(--spt-space-4) 0;\\n  line-height: 1.6;\\n}\\n\\n\\n\\n.spt-code-block[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-900);\\n  border-radius: var(--spt-radius-xl);\\n  overflow: hidden;\\n  margin: var(--spt-space-4) 0;\\n  box-shadow: var(--spt-shadow-lg);\\n}\\n\\n.spt-code-header[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-800);\\n  padding: var(--spt-space-3) var(--spt-space-4);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  border-bottom: 1px solid var(--spt-gray-700);\\n  justify-content: space-between;\\n}\\n\\n.spt-code-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-400);\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.spt-code-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-300);\\n  font-size: var(--spt-text-sm);\\n  font-weight: var(--spt-font-medium);\\n}\\n\\n.spt-copy-btn[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-400) !important;\\n  transition: color 0.2s ease !important;\\n}\\n\\n.spt-copy-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--spt-primary-400) !important;\\n}\\n\\n.spt-code-content[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: var(--spt-space-4);\\n  background: var(--spt-gray-900);\\n  color: var(--spt-gray-100);\\n  font-family: \\\"JetBrains Mono\\\", \\\"Fira Code\\\", \\\"Courier New\\\", monospace;\\n  font-size: var(--spt-text-sm);\\n  line-height: 1.6;\\n  overflow-x: auto;\\n}\\n\\n.spt-step-note[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-2);\\n  margin-top: var(--spt-space-3);\\n  padding: var(--spt-space-3);\\n  background: var(--spt-info-50);\\n  border-radius: var(--spt-radius-lg);\\n  color: var(--spt-info-700);\\n  border: 1px solid var(--spt-info-200);\\n}\\n\\n.spt-step-note[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-info-600);\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.spt-config-sections[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-8);\\n}\\n\\n.spt-config-card[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-50);\\n  border-radius: var(--spt-radius-2xl);\\n  padding: var(--spt-space-6);\\n  border: 1px solid var(--spt-gray-200);\\n}\\n\\n.spt-config-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-4);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.spt-config-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-600);\\n  color: white;\\n  border-radius: var(--spt-radius-xl);\\n  padding: var(--spt-space-3);\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.spt-config-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.spt-config-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xl);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-1) 0;\\n}\\n\\n.spt-config-subtitle[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n}\\n\\n.spt-config-content[_ngcontent-%COMP%] {\\n  margin-left: 56px;\\n}\\n\\n\\n\\n.spt-settings-table[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--spt-radius-xl);\\n  overflow: hidden;\\n  border: 1px solid var(--spt-gray-200);\\n}\\n\\n.spt-table-header[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1.5fr 3fr;\\n  gap: var(--spt-space-4);\\n  padding: var(--spt-space-4);\\n  background: var(--spt-gray-100);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-700);\\n  font-size: var(--spt-text-sm);\\n}\\n\\n.spt-table-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1.5fr 3fr;\\n  gap: var(--spt-space-4);\\n  padding: var(--spt-space-4);\\n  border-top: 1px solid var(--spt-gray-200);\\n  transition: background 0.2s ease;\\n}\\n\\n.spt-table-row[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-gray-50);\\n}\\n\\n.spt-table-cell[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.spt-setting-key[_ngcontent-%COMP%] {\\n  background: var(--spt-gray-100);\\n  padding: var(--spt-space-1) var(--spt-space-2);\\n  border-radius: var(--spt-radius-md);\\n  font-family: \\\"JetBrains Mono\\\", monospace;\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-gray-800);\\n}\\n\\n.spt-setting-value[_ngcontent-%COMP%] {\\n  background: var(--spt-success-100);\\n  padding: var(--spt-space-1) var(--spt-space-2);\\n  border-radius: var(--spt-radius-md);\\n  font-family: \\\"JetBrains Mono\\\", monospace;\\n  font-size: var(--spt-text-xs);\\n  color: var(--spt-success-800);\\n}\\n\\n.spt-setting-desc[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-gray-600);\\n  line-height: 1.4;\\n}\\n\\n\\n\\n.spt-usage-sections[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spt-space-4);\\n}\\n\\n.spt-usage-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--spt-radius-2xl);\\n  border: 1px solid var(--spt-gray-200);\\n  overflow: hidden;\\n  transition: all 0.2s ease;\\n}\\n\\n.spt-usage-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--spt-shadow-md);\\n  border-color: var(--spt-primary-300);\\n}\\n\\n.spt-usage-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-4);\\n  padding: var(--spt-space-6);\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n}\\n\\n.spt-usage-header[_ngcontent-%COMP%]:hover {\\n  background: var(--spt-gray-50);\\n}\\n\\n.spt-usage-icon[_ngcontent-%COMP%] {\\n  background: var(--spt-primary-100);\\n  color: var(--spt-primary-600);\\n  border-radius: var(--spt-radius-xl);\\n  padding: var(--spt-space-3);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spt-usage-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.spt-usage-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.spt-usage-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-xl);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-1) 0;\\n}\\n\\n.spt-usage-description[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n}\\n\\n.spt-expand-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-gray-400);\\n  transition: transform 0.2s ease;\\n}\\n\\n.spt-expand-icon.spt-expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.spt-usage-content[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n\\n.spt-usage-content.spt-expanded[_ngcontent-%COMP%] {\\n  max-height: 1000px;\\n}\\n\\n.spt-usage-details[_ngcontent-%COMP%] {\\n  padding: 0 var(--spt-space-6) var(--spt-space-4);\\n  color: var(--spt-gray-700);\\n  line-height: 1.6;\\n}\\n\\n.spt-usage-steps[_ngcontent-%COMP%], \\n.spt-usage-tips[_ngcontent-%COMP%] {\\n  padding: 0 var(--spt-space-6) var(--spt-space-4);\\n}\\n\\n.spt-steps-title[_ngcontent-%COMP%], \\n.spt-tips-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-3) 0;\\n}\\n\\n.spt-steps-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: var(--spt-space-5);\\n  color: var(--spt-gray-700);\\n}\\n\\n.spt-step-item[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-2);\\n  line-height: 1.5;\\n}\\n\\n.spt-tips-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.spt-tip-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: var(--spt-space-2);\\n  margin-bottom: var(--spt-space-3);\\n  padding: var(--spt-space-3);\\n  background: var(--spt-warning-50);\\n  border-radius: var(--spt-radius-lg);\\n  border: 1px solid var(--spt-warning-200);\\n}\\n\\n.spt-tip-icon[_ngcontent-%COMP%] {\\n  color: var(--spt-warning-600);\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  margin-top: 2px;\\n}\\n\\n\\n\\n.spt-troubleshooting-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: var(--spt-space-6);\\n}\\n\\n.spt-issue-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--spt-radius-2xl);\\n  padding: var(--spt-space-6);\\n  border: 1px solid var(--spt-gray-200);\\n  box-shadow: var(--spt-shadow-sm);\\n  transition: all 0.2s ease;\\n}\\n\\n.spt-issue-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--spt-shadow-md);\\n  transform: translateY(-2px);\\n}\\n\\n.spt-issue-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spt-space-4);\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.spt-issue-icon[_ngcontent-%COMP%] {\\n  border-radius: var(--spt-radius-xl);\\n  padding: var(--spt-space-3);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spt-issue-icon.spt-severity-high[_ngcontent-%COMP%] {\\n  background: var(--spt-error-100);\\n  color: var(--spt-error-600);\\n}\\n\\n.spt-issue-icon.spt-severity-medium[_ngcontent-%COMP%] {\\n  background: var(--spt-warning-100);\\n  color: var(--spt-warning-600);\\n}\\n\\n.spt-issue-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.spt-issue-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.spt-issue-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-lg);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-1) 0;\\n}\\n\\n.spt-issue-description[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-sm);\\n  color: var(--spt-gray-600);\\n  margin: 0;\\n}\\n\\n.spt-solution-section[_ngcontent-%COMP%], \\n.spt-prevention-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-4);\\n}\\n\\n.spt-solution-title[_ngcontent-%COMP%], \\n.spt-prevention-title[_ngcontent-%COMP%] {\\n  font-size: var(--spt-text-base);\\n  font-weight: var(--spt-font-semibold);\\n  color: var(--spt-gray-900);\\n  margin: 0 0 var(--spt-space-2) 0;\\n}\\n\\n.spt-solution-steps[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: var(--spt-space-5);\\n  color: var(--spt-gray-700);\\n}\\n\\n.spt-solution-step[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spt-space-2);\\n  line-height: 1.5;\\n}\\n\\n.spt-prevention-text[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--spt-gray-700);\\n  line-height: 1.6;\\n}\\n\\n\\n\\n@media (max-width: 1024px) {\\n  .spt-features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .spt-hero-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: var(--spt-space-4);\\n  }\\n  .spt-hero-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .spt-hero-section[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-8) var(--spt-space-4);\\n  }\\n  .spt-hero-title[_ngcontent-%COMP%] {\\n    font-size: var(--spt-text-4xl);\\n  }\\n  .spt-hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: var(--spt-text-lg);\\n  }\\n  .spt-hero-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .spt-primary-btn[_ngcontent-%COMP%], \\n   .spt-secondary-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .spt-troubleshooting-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .spt-table-header[_ngcontent-%COMP%], \\n   .spt-table-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spt-space-2);\\n  }\\n  .spt-step-content[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n  .spt-config-content[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .spt-tab-content[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-4);\\n  }\\n  .spt-hero-icon[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-4);\\n  }\\n  .spt-hero-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 32px;\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .spt-feature-card[_ngcontent-%COMP%] {\\n    padding: var(--spt-space-4);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatTabsModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatChipsModule", "MatExpansionModule", "MatTableModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "benefit_r1", "ɵɵtemplate", "VscodeExtensionComponent_div_29_div_9_li_2_Template", "ɵɵproperty", "feature_r2", "benefits", "VscodeExtensionComponent_div_29_div_9_Template", "ɵɵstyleProp", "color", "icon", "title", "description", "length", "ɵɵlistener", "VscodeExtensionComponent_div_40_div_12_Template_button_click_6_listener", "ɵɵrestoreView", "_r3", "step_r4", "ɵɵnextContext", "$implicit", "ctx_r4", "ɵɵresetView", "copyToClipboard", "command", "notes", "VscodeExtensionComponent_div_40_div_12_Template", "VscodeExtensionComponent_div_40_div_13_Template", "i_r6", "subtitle", "setting_r7", "key", "type", "default", "step_r10", "VscodeExtensionComponent_div_121_div_15_li_4_Template", "usage_r9", "steps", "tip_r11", "VscodeExtensionComponent_div_121_div_16_li_4_Template", "tips", "VscodeExtensionComponent_div_121_Template_div_click_1_listener", "_r8", "expanded", "VscodeExtensionComponent_div_121_div_15_Template", "VscodeExtensionComponent_div_121_div_16_Template", "ɵɵclassProp", "details", "step_r12", "issue_r13", "prevention", "VscodeExtensionComponent_div_130_li_15_Template", "VscodeExtensionComponent_div_130_div_16_Template", "ɵɵclassMap", "severity", "problem", "solution", "VscodeExtensionComponent", "constructor", "config<PERSON><PERSON><PERSON>", "primaryFeatures", "installationSteps", "configSettings", "usageExamples", "troubleshootingIssues", "text", "navigator", "clipboard", "writeText", "then", "console", "log", "catch", "err", "error", "selectors", "decls", "vars", "consts", "template", "VscodeExtensionComponent_Template", "rf", "ctx", "VscodeExtensionComponent_div_29_Template", "VscodeExtensionComponent_div_40_Template", "VscodeExtensionComponent_Template_button_click_86_listener", "VscodeExtensionComponent_div_112_Template", "VscodeExtensionComponent_div_121_Template", "VscodeExtensionComponent_div_130_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "Mat<PERSON><PERSON>", "MatTabGroup", "i3", "MatIcon", "i4", "MatButton", "MatIconButton", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\vscode-extension\\vscode-extension.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\n\ninterface ExtensionFeature {\n  title: string;\n  description: string;\n  icon: string;\n  color: string;\n  screenshot?: string;\n  benefits: string[];\n}\n\ninterface ConfigSetting {\n  key: string;\n  type: string;\n  default: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-vscode-extension',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTabsModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatChipsModule,\n    MatExpansionModule,\n    MatTableModule\n  ],\n  template: `\n    <div class=\"spt-vscode-container\">\n      <!-- Hero Section -->\n      <header class=\"spt-hero-section\">\n        <div class=\"spt-hero-content\">\n          <div class=\"spt-hero-icon\">\n            <mat-icon>extension</mat-icon>\n          </div>\n          <div class=\"spt-hero-text\">\n            <h1 class=\"spt-hero-title\">VS Code Extension</h1>\n            <p class=\"spt-hero-subtitle\">\n              Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration.\n            </p>\n          </div>\n        </div>\n        <div class=\"spt-hero-actions\">\n          <button mat-raised-button color=\"primary\" class=\"spt-primary-btn\">\n            <mat-icon>download</mat-icon>\n            <span>Install Extension</span>\n          </button>\n          <button mat-stroked-button class=\"spt-secondary-btn\">\n            <mat-icon>code</mat-icon>\n            <span>View Source</span>\n          </button>\n        </div>\n      </header>\n\n      <!-- Key Features Overview -->\n      <section class=\"spt-features-overview\">\n        <div class=\"spt-section-header\">\n          <h2 class=\"spt-section-title\">Key Features</h2>\n          <p class=\"spt-section-subtitle\">\n            Discover the powerful features that make SPT extension essential for secure blockchain development.\n          </p>\n        </div>\n\n        <div class=\"spt-features-grid\">\n          <div class=\"spt-feature-card spt-feature-primary\" *ngFor=\"let feature of primaryFeatures\">\n            <div class=\"spt-feature-icon\" [style.background]=\"feature.color\">\n              <mat-icon>{{ feature.icon }}</mat-icon>\n            </div>\n            <div class=\"spt-feature-content\">\n              <h3 class=\"spt-feature-title\">{{ feature.title }}</h3>\n              <p class=\"spt-feature-description\">{{ feature.description }}</p>\n              <div class=\"spt-feature-benefits\" *ngIf=\"feature.benefits.length > 0\">\n                <ul class=\"spt-benefits-list\">\n                  <li *ngFor=\"let benefit of feature.benefits\" class=\"spt-benefit-item\">\n                    <mat-icon class=\"spt-benefit-icon\">check_circle</mat-icon>\n                    <span>{{ benefit }}</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Detailed Information Tabs -->\n      <section class=\"spt-details-section\">\n        <mat-tab-group class=\"spt-tab-group\" animationDuration=\"300ms\">\n          <!-- Installation Tab -->\n          <mat-tab label=\"Installation\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Installation Guide</h2>\n                <p class=\"spt-tab-subtitle\">Get the SPT extension installed and configured in VS Code quickly and easily.</p>\n              </div>\n\n              <div class=\"spt-installation-steps\">\n                <div class=\"spt-step-card\" *ngFor=\"let step of installationSteps; let i = index\">\n                  <div class=\"spt-step-header\">\n                    <div class=\"spt-step-number\">{{ i + 1 }}</div>\n                    <div class=\"spt-step-info\">\n                      <h3 class=\"spt-step-title\">{{ step.title }}</h3>\n                      <p class=\"spt-step-subtitle\">{{ step.subtitle }}</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-step-content\">\n                    <p class=\"spt-step-description\">{{ step.description }}</p>\n                    <div class=\"spt-code-block\" *ngIf=\"step.command\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>terminal</mat-icon>\n                        <span>Command</span>\n                        <button mat-icon-button class=\"spt-copy-btn\" (click)=\"copyToClipboard(step.command)\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>{{ step.command }}</code></pre>\n                    </div>\n                    <div class=\"spt-step-note\" *ngIf=\"step.notes\">\n                      <mat-icon>info</mat-icon>\n                      <span>{{ step.notes }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Configuration Tab -->\n          <mat-tab label=\"Configuration\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Extension Configuration</h2>\n                <p class=\"spt-tab-subtitle\">Customize the SPT extension to fit your development workflow and preferences.</p>\n              </div>\n\n              <div class=\"spt-config-sections\">\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">settings</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Settings Overview</h3>\n                      <p class=\"spt-config-subtitle\">Configure extension behavior</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <p>Access extension settings through VS Code preferences:</p>\n                    <div class=\"spt-code-block\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>keyboard</mat-icon>\n                        <span>Keyboard Shortcut</span>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>Ctrl+Shift+P → \"Preferences: Open Settings (JSON)\"</code></pre>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">code</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Configuration Example</h3>\n                      <p class=\"spt-config-subtitle\">settings.json</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <div class=\"spt-code-block\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>code</mat-icon>\n                        <span>JSON Configuration</span>\n                        <button mat-icon-button class=\"spt-copy-btn\" (click)=\"copyToClipboard(configExample)\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>{{ configExample }}</code></pre>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">list</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Available Settings</h3>\n                      <p class=\"spt-config-subtitle\">Complete settings reference</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <div class=\"spt-settings-table\">\n                      <div class=\"spt-table-header\">\n                        <div class=\"spt-table-cell\">Setting</div>\n                        <div class=\"spt-table-cell\">Type</div>\n                        <div class=\"spt-table-cell\">Default</div>\n                        <div class=\"spt-table-cell\">Description</div>\n                      </div>\n                      <div class=\"spt-table-row\" *ngFor=\"let setting of configSettings\">\n                        <div class=\"spt-table-cell\">\n                          <code class=\"spt-setting-key\">{{ setting.key }}</code>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <span class=\"spt-badge spt-badge-info\">{{ setting.type }}</span>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <code class=\"spt-setting-value\">{{ setting.default }}</code>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <span class=\"spt-setting-desc\">{{ setting.description }}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Usage Tab -->\n          <mat-tab label=\"Usage\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Using the Extension</h2>\n                <p class=\"spt-tab-subtitle\">Learn how to effectively use SPT extension features in your daily development workflow.</p>\n              </div>\n\n              <div class=\"spt-usage-sections\">\n                <div class=\"spt-usage-card\" *ngFor=\"let usage of usageExamples\">\n                  <div class=\"spt-usage-header\" (click)=\"usage.expanded = !usage.expanded\">\n                    <div class=\"spt-usage-icon\">\n                      <mat-icon>{{ usage.icon }}</mat-icon>\n                    </div>\n                    <div class=\"spt-usage-info\">\n                      <h3 class=\"spt-usage-title\">{{ usage.title }}</h3>\n                      <p class=\"spt-usage-description\">{{ usage.description }}</p>\n                    </div>\n                    <mat-icon class=\"spt-expand-icon\" [class.spt-expanded]=\"usage.expanded\">expand_more</mat-icon>\n                  </div>\n\n                  <div class=\"spt-usage-content\" [class.spt-expanded]=\"usage.expanded\">\n                    <p class=\"spt-usage-details\">{{ usage.details }}</p>\n                    <div class=\"spt-usage-steps\" *ngIf=\"usage.steps\">\n                      <h4 class=\"spt-steps-title\">Steps:</h4>\n                      <ol class=\"spt-steps-list\">\n                        <li *ngFor=\"let step of usage.steps\" class=\"spt-step-item\">{{ step }}</li>\n                      </ol>\n                    </div>\n                    <div class=\"spt-usage-tips\" *ngIf=\"usage.tips\">\n                      <h4 class=\"spt-tips-title\">Tips:</h4>\n                      <ul class=\"spt-tips-list\">\n                        <li *ngFor=\"let tip of usage.tips\" class=\"spt-tip-item\">\n                          <mat-icon class=\"spt-tip-icon\">lightbulb</mat-icon>\n                          <span>{{ tip }}</span>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Troubleshooting Tab -->\n          <mat-tab label=\"Troubleshooting\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Troubleshooting</h2>\n                <p class=\"spt-tab-subtitle\">Common issues and solutions for the SPT VS Code extension.</p>\n              </div>\n\n              <div class=\"spt-troubleshooting-grid\">\n                <div class=\"spt-issue-card\" *ngFor=\"let issue of troubleshootingIssues\">\n                  <div class=\"spt-issue-header\">\n                    <div class=\"spt-issue-icon\" [class]=\"'spt-severity-' + issue.severity\">\n                      <mat-icon>{{ issue.icon }}</mat-icon>\n                    </div>\n                    <div class=\"spt-issue-info\">\n                      <h3 class=\"spt-issue-title\">{{ issue.problem }}</h3>\n                      <p class=\"spt-issue-description\">{{ issue.description }}</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-issue-content\">\n                    <div class=\"spt-solution-section\">\n                      <h4 class=\"spt-solution-title\">Solution:</h4>\n                      <ol class=\"spt-solution-steps\">\n                        <li *ngFor=\"let step of issue.solution\" class=\"spt-solution-step\">{{ step }}</li>\n                      </ol>\n                    </div>\n                    <div class=\"spt-prevention-section\" *ngIf=\"issue.prevention\">\n                      <h4 class=\"spt-prevention-title\">Prevention:</h4>\n                      <p class=\"spt-prevention-text\">{{ issue.prevention }}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n        </mat-tab-group>\n      </section>\n    </div>\n  `,\n\n  styles: [`\n    /* Main Container */\n    .spt-vscode-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      font-family: 'Inter', sans-serif;\n    }\n\n    /* Hero Section */\n    .spt-hero-section {\n      background: linear-gradient(135deg, var(--spt-primary-50) 0%, var(--spt-secondary-50) 100%);\n      border-radius: var(--spt-radius-3xl);\n      padding: var(--spt-space-12) var(--spt-space-8);\n      margin-bottom: var(--spt-space-12);\n      border: 1px solid var(--spt-primary-200);\n    }\n\n    .spt-hero-content {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-6);\n      margin-bottom: var(--spt-space-8);\n    }\n\n    .spt-hero-icon {\n      background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);\n      border-radius: var(--spt-radius-3xl);\n      padding: var(--spt-space-6);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: var(--spt-shadow-xl);\n    }\n\n    .spt-hero-icon mat-icon {\n      color: white;\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n    }\n\n    .spt-hero-text {\n      flex: 1;\n    }\n\n    .spt-hero-title {\n      font-size: var(--spt-text-5xl);\n      font-weight: var(--spt-font-bold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-4) 0;\n      letter-spacing: -0.025em;\n    }\n\n    .spt-hero-subtitle {\n      font-size: var(--spt-text-xl);\n      color: var(--spt-gray-600);\n      margin: 0;\n      line-height: 1.6;\n      font-weight: var(--spt-font-normal);\n    }\n\n    .spt-hero-actions {\n      display: flex;\n      gap: var(--spt-space-4);\n      align-items: center;\n    }\n\n    .spt-primary-btn {\n      background: var(--spt-primary-600) !important;\n      color: white !important;\n      font-weight: var(--spt-font-semibold) !important;\n      padding: var(--spt-space-4) var(--spt-space-6) !important;\n      border-radius: var(--spt-radius-xl) !important;\n      box-shadow: var(--spt-shadow-md) !important;\n      transition: all 0.2s ease !important;\n    }\n\n    .spt-primary-btn:hover {\n      background: var(--spt-primary-700) !important;\n      box-shadow: var(--spt-shadow-lg) !important;\n      transform: translateY(-2px);\n    }\n\n    .spt-secondary-btn {\n      color: var(--spt-gray-700) !important;\n      border-color: var(--spt-gray-300) !important;\n      font-weight: var(--spt-font-medium) !important;\n      padding: var(--spt-space-4) var(--spt-space-6) !important;\n      border-radius: var(--spt-radius-xl) !important;\n      transition: all 0.2s ease !important;\n    }\n\n    .spt-secondary-btn:hover {\n      background: var(--spt-gray-100) !important;\n      border-color: var(--spt-gray-400) !important;\n      transform: translateY(-1px);\n    }\n\n    /* Section Headers */\n    .spt-section-header {\n      text-align: center;\n      margin-bottom: var(--spt-space-12);\n    }\n\n    .spt-section-title {\n      font-size: var(--spt-text-4xl);\n      font-weight: var(--spt-font-bold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-4) 0;\n      letter-spacing: -0.025em;\n    }\n\n    .spt-section-subtitle {\n      font-size: var(--spt-text-lg);\n      color: var(--spt-gray-600);\n      margin: 0;\n      max-width: 600px;\n      margin-left: auto;\n      margin-right: auto;\n      line-height: 1.6;\n    }\n\n    /* Features Overview */\n    .spt-features-overview {\n      margin-bottom: var(--spt-space-16);\n    }\n\n    .spt-features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: var(--spt-space-8);\n    }\n\n    .spt-feature-card {\n      background: white;\n      border-radius: var(--spt-radius-3xl);\n      padding: var(--spt-space-8);\n      border: 1px solid var(--spt-gray-200);\n      box-shadow: var(--spt-shadow-sm);\n      transition: all 0.3s ease;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .spt-feature-card:hover {\n      box-shadow: var(--spt-shadow-xl);\n      transform: translateY(-4px);\n      border-color: var(--spt-primary-300);\n    }\n\n    .spt-feature-card::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 4px;\n      background: linear-gradient(90deg, var(--spt-primary-500), var(--spt-secondary-500));\n    }\n\n    .spt-feature-icon {\n      width: 64px;\n      height: 64px;\n      border-radius: var(--spt-radius-2xl);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: var(--spt-space-6);\n      box-shadow: var(--spt-shadow-md);\n    }\n\n    .spt-feature-icon mat-icon {\n      color: white;\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n    }\n\n    .spt-feature-content {\n      flex: 1;\n    }\n\n    .spt-feature-title {\n      font-size: var(--spt-text-2xl);\n      font-weight: var(--spt-font-bold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-3) 0;\n    }\n\n    .spt-feature-description {\n      font-size: var(--spt-text-base);\n      color: var(--spt-gray-600);\n      margin: 0 0 var(--spt-space-6) 0;\n      line-height: 1.6;\n    }\n    .spt-feature-benefits {\n      margin-top: var(--spt-space-4);\n    }\n\n    .spt-benefits-list {\n      list-style: none;\n      margin: 0;\n      padding: 0;\n    }\n\n    .spt-benefit-item {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n      margin-bottom: var(--spt-space-2);\n      font-size: var(--spt-text-sm);\n      color: var(--spt-gray-700);\n    }\n\n    .spt-benefit-icon {\n      color: var(--spt-success-600);\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    /* Details Section */\n    .spt-details-section {\n      margin-bottom: var(--spt-space-16);\n    }\n\n    .spt-tab-group {\n      background: white;\n      border-radius: var(--spt-radius-2xl);\n      box-shadow: var(--spt-shadow-lg);\n      overflow: hidden;\n      border: 1px solid var(--spt-gray-200);\n    }\n\n    .spt-tab-content {\n      padding: var(--spt-space-8);\n    }\n\n    .spt-tab-header {\n      text-align: center;\n      margin-bottom: var(--spt-space-8);\n    }\n\n    .spt-tab-title {\n      font-size: var(--spt-text-3xl);\n      font-weight: var(--spt-font-bold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-3) 0;\n    }\n\n    .spt-tab-subtitle {\n      font-size: var(--spt-text-lg);\n      color: var(--spt-gray-600);\n      margin: 0;\n      line-height: 1.6;\n    }\n\n    /* Installation Steps */\n    .spt-installation-steps {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spt-space-6);\n    }\n\n    .spt-step-card {\n      background: var(--spt-gray-50);\n      border-radius: var(--spt-radius-2xl);\n      padding: var(--spt-space-6);\n      border: 1px solid var(--spt-gray-200);\n      transition: all 0.2s ease;\n    }\n\n    .spt-step-card:hover {\n      box-shadow: var(--spt-shadow-md);\n      border-color: var(--spt-primary-300);\n    }\n\n    .spt-step-header {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-4);\n      margin-bottom: var(--spt-space-4);\n    }\n\n    .spt-step-number {\n      background: linear-gradient(135deg, var(--spt-primary-600), var(--spt-primary-700));\n      color: white;\n      border-radius: 50%;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: var(--spt-font-bold);\n      font-size: var(--spt-text-lg);\n      box-shadow: var(--spt-shadow-md);\n    }\n\n    .spt-step-info {\n      flex: 1;\n    }\n\n    .spt-step-title {\n      font-size: var(--spt-text-xl);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-1) 0;\n    }\n\n    .spt-step-subtitle {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-gray-600);\n      margin: 0;\n      font-weight: var(--spt-font-medium);\n    }\n\n    .spt-step-content {\n      margin-left: 64px;\n    }\n\n    .spt-step-description {\n      font-size: var(--spt-text-base);\n      color: var(--spt-gray-700);\n      margin: 0 0 var(--spt-space-4) 0;\n      line-height: 1.6;\n    }\n\n    /* Code Blocks */\n    .spt-code-block {\n      background: var(--spt-gray-900);\n      border-radius: var(--spt-radius-xl);\n      overflow: hidden;\n      margin: var(--spt-space-4) 0;\n      box-shadow: var(--spt-shadow-lg);\n    }\n\n    .spt-code-header {\n      background: var(--spt-gray-800);\n      padding: var(--spt-space-3) var(--spt-space-4);\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n      border-bottom: 1px solid var(--spt-gray-700);\n      justify-content: space-between;\n    }\n\n    .spt-code-header mat-icon {\n      color: var(--spt-gray-400);\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    .spt-code-header span {\n      color: var(--spt-gray-300);\n      font-size: var(--spt-text-sm);\n      font-weight: var(--spt-font-medium);\n    }\n\n    .spt-copy-btn {\n      color: var(--spt-gray-400) !important;\n      transition: color 0.2s ease !important;\n    }\n\n    .spt-copy-btn:hover {\n      color: var(--spt-primary-400) !important;\n    }\n\n    .spt-code-content {\n      margin: 0;\n      padding: var(--spt-space-4);\n      background: var(--spt-gray-900);\n      color: var(--spt-gray-100);\n      font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;\n      font-size: var(--spt-text-sm);\n      line-height: 1.6;\n      overflow-x: auto;\n    }\n\n    .spt-step-note {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-2);\n      margin-top: var(--spt-space-3);\n      padding: var(--spt-space-3);\n      background: var(--spt-info-50);\n      border-radius: var(--spt-radius-lg);\n      color: var(--spt-info-700);\n      border: 1px solid var(--spt-info-200);\n    }\n\n    .spt-step-note mat-icon {\n      color: var(--spt-info-600);\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n    }\n\n    /* Configuration Sections */\n    .spt-config-sections {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spt-space-8);\n    }\n\n    .spt-config-card {\n      background: var(--spt-gray-50);\n      border-radius: var(--spt-radius-2xl);\n      padding: var(--spt-space-6);\n      border: 1px solid var(--spt-gray-200);\n    }\n\n    .spt-config-header {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-4);\n      margin-bottom: var(--spt-space-4);\n    }\n\n    .spt-config-icon {\n      background: var(--spt-primary-600);\n      color: white;\n      border-radius: var(--spt-radius-xl);\n      padding: var(--spt-space-3);\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .spt-config-info {\n      flex: 1;\n    }\n\n    .spt-config-title {\n      font-size: var(--spt-text-xl);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-1) 0;\n    }\n\n    .spt-config-subtitle {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-gray-600);\n      margin: 0;\n    }\n\n    .spt-config-content {\n      margin-left: 56px;\n    }\n\n    /* Settings Table */\n    .spt-settings-table {\n      background: white;\n      border-radius: var(--spt-radius-xl);\n      overflow: hidden;\n      border: 1px solid var(--spt-gray-200);\n    }\n\n    .spt-table-header {\n      display: grid;\n      grid-template-columns: 2fr 1fr 1.5fr 3fr;\n      gap: var(--spt-space-4);\n      padding: var(--spt-space-4);\n      background: var(--spt-gray-100);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-700);\n      font-size: var(--spt-text-sm);\n    }\n\n    .spt-table-row {\n      display: grid;\n      grid-template-columns: 2fr 1fr 1.5fr 3fr;\n      gap: var(--spt-space-4);\n      padding: var(--spt-space-4);\n      border-top: 1px solid var(--spt-gray-200);\n      transition: background 0.2s ease;\n    }\n\n    .spt-table-row:hover {\n      background: var(--spt-gray-50);\n    }\n\n    .spt-table-cell {\n      display: flex;\n      align-items: center;\n    }\n\n    .spt-setting-key {\n      background: var(--spt-gray-100);\n      padding: var(--spt-space-1) var(--spt-space-2);\n      border-radius: var(--spt-radius-md);\n      font-family: 'JetBrains Mono', monospace;\n      font-size: var(--spt-text-xs);\n      color: var(--spt-gray-800);\n    }\n\n    .spt-setting-value {\n      background: var(--spt-success-100);\n      padding: var(--spt-space-1) var(--spt-space-2);\n      border-radius: var(--spt-radius-md);\n      font-family: 'JetBrains Mono', monospace;\n      font-size: var(--spt-text-xs);\n      color: var(--spt-success-800);\n    }\n\n    .spt-setting-desc {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-gray-600);\n      line-height: 1.4;\n    }\n\n    /* Usage Sections */\n    .spt-usage-sections {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spt-space-4);\n    }\n\n    .spt-usage-card {\n      background: white;\n      border-radius: var(--spt-radius-2xl);\n      border: 1px solid var(--spt-gray-200);\n      overflow: hidden;\n      transition: all 0.2s ease;\n    }\n\n    .spt-usage-card:hover {\n      box-shadow: var(--spt-shadow-md);\n      border-color: var(--spt-primary-300);\n    }\n\n    .spt-usage-header {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-4);\n      padding: var(--spt-space-6);\n      cursor: pointer;\n      transition: background 0.2s ease;\n    }\n\n    .spt-usage-header:hover {\n      background: var(--spt-gray-50);\n    }\n\n    .spt-usage-icon {\n      background: var(--spt-primary-100);\n      color: var(--spt-primary-600);\n      border-radius: var(--spt-radius-xl);\n      padding: var(--spt-space-3);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .spt-usage-icon mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .spt-usage-info {\n      flex: 1;\n    }\n\n    .spt-usage-title {\n      font-size: var(--spt-text-xl);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-1) 0;\n    }\n\n    .spt-usage-description {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-gray-600);\n      margin: 0;\n    }\n\n    .spt-expand-icon {\n      color: var(--spt-gray-400);\n      transition: transform 0.2s ease;\n    }\n\n    .spt-expand-icon.spt-expanded {\n      transform: rotate(180deg);\n    }\n\n    .spt-usage-content {\n      max-height: 0;\n      overflow: hidden;\n      transition: max-height 0.3s ease;\n    }\n\n    .spt-usage-content.spt-expanded {\n      max-height: 1000px;\n    }\n\n    .spt-usage-details {\n      padding: 0 var(--spt-space-6) var(--spt-space-4);\n      color: var(--spt-gray-700);\n      line-height: 1.6;\n    }\n\n    .spt-usage-steps,\n    .spt-usage-tips {\n      padding: 0 var(--spt-space-6) var(--spt-space-4);\n    }\n\n    .spt-steps-title,\n    .spt-tips-title {\n      font-size: var(--spt-text-base);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-3) 0;\n    }\n\n    .spt-steps-list {\n      margin: 0;\n      padding-left: var(--spt-space-5);\n      color: var(--spt-gray-700);\n    }\n\n    .spt-step-item {\n      margin-bottom: var(--spt-space-2);\n      line-height: 1.5;\n    }\n\n    .spt-tips-list {\n      list-style: none;\n      margin: 0;\n      padding: 0;\n    }\n\n    .spt-tip-item {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spt-space-2);\n      margin-bottom: var(--spt-space-3);\n      padding: var(--spt-space-3);\n      background: var(--spt-warning-50);\n      border-radius: var(--spt-radius-lg);\n      border: 1px solid var(--spt-warning-200);\n    }\n\n    .spt-tip-icon {\n      color: var(--spt-warning-600);\n      font-size: 16px;\n      width: 16px;\n      height: 16px;\n      margin-top: 2px;\n    }\n    /* Troubleshooting Grid */\n    .spt-troubleshooting-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: var(--spt-space-6);\n    }\n\n    .spt-issue-card {\n      background: white;\n      border-radius: var(--spt-radius-2xl);\n      padding: var(--spt-space-6);\n      border: 1px solid var(--spt-gray-200);\n      box-shadow: var(--spt-shadow-sm);\n      transition: all 0.2s ease;\n    }\n\n    .spt-issue-card:hover {\n      box-shadow: var(--spt-shadow-md);\n      transform: translateY(-2px);\n    }\n\n    .spt-issue-header {\n      display: flex;\n      align-items: center;\n      gap: var(--spt-space-4);\n      margin-bottom: var(--spt-space-4);\n    }\n\n    .spt-issue-icon {\n      border-radius: var(--spt-radius-xl);\n      padding: var(--spt-space-3);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .spt-issue-icon.spt-severity-high {\n      background: var(--spt-error-100);\n      color: var(--spt-error-600);\n    }\n\n    .spt-issue-icon.spt-severity-medium {\n      background: var(--spt-warning-100);\n      color: var(--spt-warning-600);\n    }\n\n    .spt-issue-icon mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .spt-issue-info {\n      flex: 1;\n    }\n\n    .spt-issue-title {\n      font-size: var(--spt-text-lg);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-1) 0;\n    }\n\n    .spt-issue-description {\n      font-size: var(--spt-text-sm);\n      color: var(--spt-gray-600);\n      margin: 0;\n    }\n\n    .spt-solution-section,\n    .spt-prevention-section {\n      margin-bottom: var(--spt-space-4);\n    }\n\n    .spt-solution-title,\n    .spt-prevention-title {\n      font-size: var(--spt-text-base);\n      font-weight: var(--spt-font-semibold);\n      color: var(--spt-gray-900);\n      margin: 0 0 var(--spt-space-2) 0;\n    }\n\n    .spt-solution-steps {\n      margin: 0;\n      padding-left: var(--spt-space-5);\n      color: var(--spt-gray-700);\n    }\n\n    .spt-solution-step {\n      margin-bottom: var(--spt-space-2);\n      line-height: 1.5;\n    }\n\n    .spt-prevention-text {\n      margin: 0;\n      color: var(--spt-gray-700);\n      line-height: 1.6;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 1024px) {\n      .spt-features-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .spt-hero-content {\n        flex-direction: column;\n        text-align: center;\n        gap: var(--spt-space-4);\n      }\n\n      .spt-hero-actions {\n        justify-content: center;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .spt-hero-section {\n        padding: var(--spt-space-8) var(--spt-space-4);\n      }\n\n      .spt-hero-title {\n        font-size: var(--spt-text-4xl);\n      }\n\n      .spt-hero-subtitle {\n        font-size: var(--spt-text-lg);\n      }\n\n      .spt-hero-actions {\n        flex-direction: column;\n        width: 100%;\n      }\n\n      .spt-primary-btn,\n      .spt-secondary-btn {\n        width: 100%;\n        justify-content: center;\n      }\n\n      .spt-troubleshooting-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .spt-table-header,\n      .spt-table-row {\n        grid-template-columns: 1fr;\n        gap: var(--spt-space-2);\n      }\n\n      .spt-step-content {\n        margin-left: 0;\n      }\n\n      .spt-config-content {\n        margin-left: 0;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .spt-tab-content {\n        padding: var(--spt-space-4);\n      }\n\n      .spt-hero-icon {\n        padding: var(--spt-space-4);\n      }\n\n      .spt-hero-icon mat-icon {\n        font-size: 32px;\n        width: 32px;\n        height: 32px;\n      }\n\n      .spt-feature-card {\n        padding: var(--spt-space-4);\n      }\n    }\n  `]\n})\nexport class VscodeExtensionComponent {\n  configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n\n  primaryFeatures: ExtensionFeature[] = [\n    {\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities and smart suggestions.',\n      icon: 'security',\n      color: 'linear-gradient(135deg, var(--spt-primary-500), var(--spt-primary-600))',\n      benefits: [\n        'Immediate vulnerability detection',\n        'Reduced security debt',\n        'Faster development cycles',\n        'Proactive security measures'\n      ]\n    },\n    {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues with severity-based color coding.',\n      icon: 'visibility',\n      color: 'linear-gradient(135deg, var(--spt-success-500), var(--spt-success-600))',\n      benefits: [\n        'Clear visual feedback',\n        'Context-aware highlighting',\n        'Severity-based color coding',\n        'Non-intrusive indicators'\n      ]\n    },\n    {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and quick fixes.',\n      icon: 'bug_report',\n      color: 'linear-gradient(135deg, var(--spt-error-500), var(--spt-error-600))',\n      benefits: [\n        'Centralized issue tracking',\n        'Detailed error descriptions',\n        'Quick navigation to issues',\n        'Integration with existing workflow'\n      ]\n    },\n    {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code for instant access.',\n      icon: 'lens',\n      color: 'linear-gradient(135deg, var(--spt-warning-500), var(--spt-warning-600))',\n      benefits: [\n        'Contextual security metrics',\n        'One-click security actions',\n        'Code quality insights',\n        'Performance recommendations'\n      ]\n    },\n    {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements with rich documentation.',\n      icon: 'info',\n      color: 'linear-gradient(135deg, var(--spt-info-500), var(--spt-info-600))',\n      benefits: [\n        'Instant security documentation',\n        'Best practice suggestions',\n        'Vulnerability explanations',\n        'Quick reference access'\n      ]\n    },\n    {\n      title: 'Multi-chain Support',\n      description: 'Comprehensive support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      color: 'linear-gradient(135deg, var(--spt-secondary-500), var(--spt-secondary-600))',\n      benefits: [\n        'Comprehensive blockchain coverage',\n        'Chain-specific security rules',\n        'Unified security approach',\n        'Extensible architecture'\n      ]\n    }\n  ];\n\n  installationSteps = [\n    {\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    },\n    {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    },\n    {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }\n  ];\n\n  configSettings: ConfigSetting[] = [\n    { key: 'spt.enabled', type: 'boolean', default: 'true', description: 'Enable/disable SPT security analysis' },\n    { key: 'spt.serverUrl', type: 'string', default: 'http://localhost:8080', description: 'SPT backend server URL' },\n    { key: 'spt.apiKey', type: 'string', default: '\"\"', description: 'API key for authentication' },\n    { key: 'spt.autoScan', type: 'boolean', default: 'true', description: 'Automatically scan files on save' },\n    { key: 'spt.scanOnOpen', type: 'boolean', default: 'false', description: 'Automatically scan files when opened' },\n    { key: 'spt.chains', type: 'array', default: '[\"ethereum\", \"bitcoin\", \"general\"]', description: 'Blockchain chains to analyze' },\n    { key: 'spt.severity', type: 'string', default: '\"medium\"', description: 'Minimum severity level to show' },\n    { key: 'spt.showInlineDecorations', type: 'boolean', default: 'true', description: 'Show inline security decorations' },\n    { key: 'spt.showProblems', type: 'boolean', default: 'true', description: 'Show security issues in Problems panel' },\n    { key: 'spt.enableCodeLens', type: 'boolean', default: 'true', description: 'Enable security-related CodeLens' },\n    { key: 'spt.enableHover', type: 'boolean', default: 'true', description: 'Enable security information on hover' }\n  ];\n\n  usageExamples = [\n    {\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: [\n        'Open a blockchain project in VS Code',\n        'Save a file to trigger automatic scanning',\n        'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"',\n        'View results in Problems panel or inline decorations'\n      ],\n      tips: [\n        'Enable auto-scan for continuous security monitoring',\n        'Use the Problems panel to navigate between issues',\n        'Check the status bar for scan progress'\n      ]\n    },\n    {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: [\n        'Hover over highlighted code to see issue details',\n        'Click on issues in Problems panel for more information',\n        'Use CodeLens actions for quick fixes',\n        'Follow the recommended solutions'\n      ],\n      tips: [\n        'Start with critical and high severity issues',\n        'Use hover information for quick context',\n        'Check references for additional learning'\n      ]\n    },\n    {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: [\n        'Open VS Code settings (Ctrl+,)',\n        'Search for \"SPT\" to find extension settings',\n        'Adjust chains, severity, and scan triggers',\n        'Save settings and restart if needed'\n      ],\n      tips: [\n        'Use workspace settings for project-specific configuration',\n        'Adjust severity threshold based on project maturity',\n        'Enable scan-on-open for comprehensive coverage'\n      ]\n    }\n  ];\n\n  troubleshootingIssues = [\n    {\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: [\n        'Verify SPT backend server is running on configured port',\n        'Check spt.serverUrl setting in VS Code preferences',\n        'Ensure firewall is not blocking the connection',\n        'Try restarting VS Code and the SPT server'\n      ],\n      prevention: 'Always start the SPT backend server before using the extension'\n    },\n    {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: [\n        'Check if the file type is supported (Solidity, JavaScript, etc.)',\n        'Verify the correct blockchain chains are selected',\n        'Lower the severity threshold in settings',\n        'Ensure the file contains actual security-relevant code'\n      ],\n      prevention: 'Review supported file types and ensure proper project structure'\n    },\n    {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: [\n        'Disable auto-scan and use manual scanning',\n        'Increase scan timeout in settings',\n        'Exclude large files or directories from scanning',\n        'Reduce the number of enabled blockchain chains'\n      ],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }\n  ];\n\n  copyToClipboard(text: string): void {\n    navigator.clipboard.writeText(text).then(() => {\n      // Could add a toast notification here\n      console.log('Copied to clipboard');\n    }).catch(err => {\n      console.error('Failed to copy: ', err);\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;IA8EpCC,EADF,CAAAC,cAAA,aAAsE,mBACjC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1DH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAa;IACrBF,EADqB,CAAAG,YAAA,EAAO,EACvB;;;;IADGH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAa;;;;;IAHvBN,EADF,CAAAC,cAAA,cAAsE,aACtC;IAC5BD,EAAA,CAAAO,UAAA,IAAAC,mDAAA,iBAAsE;IAK1ER,EADE,CAAAG,YAAA,EAAK,EACD;;;;IALsBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,YAAAC,UAAA,CAAAC,QAAA,CAAmB;;;;;IAP/CX,EAFJ,CAAAC,cAAA,cAA0F,cACvB,eACrD;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACnC;IAEJH,EADF,CAAAC,cAAA,cAAiC,aACD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChEH,EAAA,CAAAO,UAAA,IAAAK,8CAAA,kBAAsE;IAS1EZ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAf0BH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAa,WAAA,eAAAH,UAAA,CAAAI,KAAA,CAAkC;IACpDd,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAK,IAAA,CAAkB;IAGEf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAM,KAAA,CAAmB;IACdhB,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAO,WAAA,CAAyB;IACzBjB,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAC,QAAA,CAAAO,MAAA,KAAiC;;;;;;IAqC1DlB,EAFJ,CAAAC,cAAA,cAAiD,cAClB,eACjB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpBH,EAAA,CAAAC,cAAA,iBAAqF;IAAxCD,EAAA,CAAAmB,UAAA,mBAAAC,wEAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAL,OAAA,CAAAM,OAAA,CAA6B;IAAA,EAAC;IAClF7B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACL;IACwBH,EAA9B,CAAAC,cAAA,cAA8B,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IACxDF,EADwD,CAAAG,YAAA,EAAO,EAAM,EAC/D;;;;IADgCH,EAAA,CAAAI,SAAA,IAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAM,OAAA,CAAkB;;;;;IAGtD7B,EADF,CAAAC,cAAA,cAA8C,eAClC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EACzB;;;;IADEH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAO,KAAA,CAAgB;;;;;IApBxB9B,EAFJ,CAAAC,cAAA,cAAiF,cAClD,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE5CH,EADF,CAAAC,cAAA,cAA2B,aACE;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,YAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAEpDF,EAFoD,CAAAG,YAAA,EAAI,EAChD,EACF;IAEJH,EADF,CAAAC,cAAA,cAA8B,aACI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAW1DH,EAVA,CAAAO,UAAA,KAAAwB,+CAAA,mBAAiD,KAAAC,+CAAA,kBAUH;IAKlDhC,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAvB2BH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAA4B,IAAA,KAAW;IAEXjC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAP,KAAA,CAAgB;IACdhB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAW,QAAA,CAAmB;IAIlBlC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAN,WAAA,CAAsB;IACzBjB,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAS,UAAA,SAAAc,OAAA,CAAAM,OAAA,CAAkB;IAUnB7B,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,SAAAc,OAAA,CAAAO,KAAA,CAAgB;;;;;IA+EtC9B,EAFJ,CAAAC,cAAA,cAAkE,cACpC,eACI;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EAClD;IAEJH,EADF,CAAAC,cAAA,cAA4B,eACa;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IAEJH,EADF,CAAAC,cAAA,cAA4B,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACK;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAE5DF,EAF4D,CAAAG,YAAA,EAAO,EAC3D,EACF;;;;IAX4BH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA8B,UAAA,CAAAC,GAAA,CAAiB;IAGRpC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA8B,UAAA,CAAAE,IAAA,CAAkB;IAGzBrC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAA8B,UAAA,CAAAG,OAAA,CAAqB;IAGtBtC,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAA8B,UAAA,CAAAlB,WAAA,CAAyB;;;;;IAoC1DjB,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAkC,QAAA,CAAU;;;;;IAFvEvC,EADF,CAAAC,cAAA,cAAiD,aACnB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAO,UAAA,IAAAiC,qDAAA,iBAA2D;IAE/DxC,EADE,CAAAG,YAAA,EAAK,EACD;;;;IAFmBH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAS,UAAA,YAAAgC,QAAA,CAAAC,KAAA,CAAc;;;;;IAOjC1C,EADF,CAAAC,cAAA,aAAwD,mBACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EACnB;;;;IADGH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAsC,OAAA,CAAS;;;;;IAJnB3C,EADF,CAAAC,cAAA,cAA+C,aAClB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,aAA0B;IACxBD,EAAA,CAAAO,UAAA,IAAAqC,qDAAA,iBAAwD;IAK5D5C,EADE,CAAAG,YAAA,EAAK,EACD;;;;IALkBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAS,UAAA,YAAAgC,QAAA,CAAAI,IAAA,CAAa;;;;;;IAtBvC7C,EADF,CAAAC,cAAA,cAAgE,cACW;IAA3CD,EAAA,CAAAmB,UAAA,mBAAA2B,+DAAA;MAAA,MAAAL,QAAA,GAAAzC,EAAA,CAAAqB,aAAA,CAAA0B,GAAA,EAAAtB,SAAA;MAAA,OAAAzB,EAAA,CAAA2B,WAAA,CAAAc,QAAA,CAAAO,QAAA,IAAAP,QAAA,CAAAO,QAAA;IAAA,EAA0C;IAEpEhD,EADF,CAAAC,cAAA,cAA4B,eAChB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACjC;IAEJH,EADF,CAAAC,cAAA,cAA4B,aACE;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACxD;IACNH,EAAA,CAAAC,cAAA,oBAAwE;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IACrFF,EADqF,CAAAG,YAAA,EAAW,EAC1F;IAGJH,EADF,CAAAC,cAAA,eAAqE,aACtC;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOpDH,EANA,CAAAO,UAAA,KAAA0C,gDAAA,kBAAiD,KAAAC,gDAAA,kBAMF;IAUnDlD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA3BUH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAoC,QAAA,CAAA1B,IAAA,CAAgB;IAGEf,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAoC,QAAA,CAAAzB,KAAA,CAAiB;IACZhB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAoC,QAAA,CAAAxB,WAAA,CAAuB;IAExBjB,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAmD,WAAA,iBAAAV,QAAA,CAAAO,QAAA,CAAqC;IAG1ChD,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAmD,WAAA,iBAAAV,QAAA,CAAAO,QAAA,CAAqC;IACrChD,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAoC,QAAA,CAAAW,OAAA,CAAmB;IAClBpD,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAS,UAAA,SAAAgC,QAAA,CAAAC,KAAA,CAAiB;IAMlB1C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,SAAAgC,QAAA,CAAAI,IAAA,CAAgB;;;;;IAsCzC7C,EAAA,CAAAC,cAAA,cAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAgD,QAAA,CAAU;;;;;IAI9ErD,EADF,CAAAC,cAAA,eAA6D,cAC1B;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;;;;IAD2BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAiD,SAAA,CAAAC,UAAA,CAAsB;;;;;IAhBrDvD,EAHN,CAAAC,cAAA,cAAwE,cACxC,cAC2C,eAC3D;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACjC;IAEJH,EADF,CAAAC,cAAA,eAA4B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAE5DF,EAF4D,CAAAG,YAAA,EAAI,EACxD,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAA+B,gBACK,eACD;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAO,UAAA,KAAAiD,+CAAA,kBAAkE;IAEtExD,EADE,CAAAG,YAAA,EAAK,EACD;IACNH,EAAA,CAAAO,UAAA,KAAAkD,gDAAA,mBAA6D;IAKjEzD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IApB0BH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAA0D,UAAA,mBAAAJ,SAAA,CAAAK,QAAA,CAA0C;IAC1D3D,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAiD,SAAA,CAAAvC,IAAA,CAAgB;IAGEf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAiD,SAAA,CAAAM,OAAA,CAAmB;IACd5D,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAiD,SAAA,CAAArC,WAAA,CAAuB;IAOjCjB,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAS,UAAA,YAAA6C,SAAA,CAAAO,QAAA,CAAiB;IAGL7D,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAS,UAAA,SAAA6C,SAAA,CAAAC,UAAA,CAAsB;;;AA40B/E,OAAM,MAAOO,wBAAwB;EA1lCrCC,YAAA;IA2lCE,KAAAC,aAAa,GAAG;;;;;;;;;;;;EAYhB;IAEA,KAAAC,eAAe,GAAuB,CACpC;MACEjD,KAAK,EAAE,6BAA6B;MACpCC,WAAW,EAAE,oHAAoH;MACjIF,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,yEAAyE;MAChFH,QAAQ,EAAE,CACR,mCAAmC,EACnC,uBAAuB,EACvB,2BAA2B,EAC3B,6BAA6B;KAEhC,EACD;MACEK,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,wGAAwG;MACrHF,IAAI,EAAE,YAAY;MAClBD,KAAK,EAAE,yEAAyE;MAChFH,QAAQ,EAAE,CACR,uBAAuB,EACvB,4BAA4B,EAC5B,6BAA6B,EAC7B,0BAA0B;KAE7B,EACD;MACEK,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE,iGAAiG;MAC9GF,IAAI,EAAE,YAAY;MAClBD,KAAK,EAAE,qEAAqE;MAC5EH,QAAQ,EAAE,CACR,4BAA4B,EAC5B,6BAA6B,EAC7B,4BAA4B,EAC5B,oCAAoC;KAEvC,EACD;MACEK,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,oGAAoG;MACjHF,IAAI,EAAE,MAAM;MACZD,KAAK,EAAE,yEAAyE;MAChFH,QAAQ,EAAE,CACR,6BAA6B,EAC7B,4BAA4B,EAC5B,uBAAuB,EACvB,6BAA6B;KAEhC,EACD;MACEK,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,wGAAwG;MACrHF,IAAI,EAAE,MAAM;MACZD,KAAK,EAAE,mEAAmE;MAC1EH,QAAQ,EAAE,CACR,gCAAgC,EAChC,2BAA2B,EAC3B,4BAA4B,EAC5B,wBAAwB;KAE3B,EACD;MACEK,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,wFAAwF;MACrGF,IAAI,EAAE,MAAM;MACZD,KAAK,EAAE,6EAA6E;MACpFH,QAAQ,EAAE,CACR,mCAAmC,EACnC,+BAA+B,EAC/B,2BAA2B,EAC3B,yBAAyB;KAE5B,CACF;IAED,KAAAuD,iBAAiB,GAAG,CAClB;MACElD,KAAK,EAAE,kCAAkC;MACzCkB,QAAQ,EAAE,oBAAoB;MAC9BjB,WAAW,EAAE,8EAA8E;MAC3FY,OAAO,EAAE,uCAAuC;MAChDC,KAAK,EAAE;KACR,EACD;MACEd,KAAK,EAAE,8BAA8B;MACrCkB,QAAQ,EAAE,uBAAuB;MACjCjB,WAAW,EAAE,gEAAgE;MAC7EY,OAAO,EAAE,6CAA6C;MACtDC,KAAK,EAAE;KACR,EACD;MACEd,KAAK,EAAE,qBAAqB;MAC5BkB,QAAQ,EAAE,qBAAqB;MAC/BjB,WAAW,EAAE,yEAAyE;MACtFa,KAAK,EAAE;KACR,CACF;IAED,KAAAqC,cAAc,GAAoB,CAChC;MAAE/B,GAAG,EAAE,aAAa;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAsC,CAAE,EAC7G;MAAEmB,GAAG,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,uBAAuB;MAAErB,WAAW,EAAE;IAAwB,CAAE,EACjH;MAAEmB,GAAG,EAAE,YAAY;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,IAAI;MAAErB,WAAW,EAAE;IAA4B,CAAE,EAC/F;MAAEmB,GAAG,EAAE,cAAc;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAkC,CAAE,EAC1G;MAAEmB,GAAG,EAAE,gBAAgB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,OAAO;MAAErB,WAAW,EAAE;IAAsC,CAAE,EACjH;MAAEmB,GAAG,EAAE,YAAY;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,oCAAoC;MAAErB,WAAW,EAAE;IAA8B,CAAE,EAChI;MAAEmB,GAAG,EAAE,cAAc;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,UAAU;MAAErB,WAAW,EAAE;IAAgC,CAAE,EAC3G;MAAEmB,GAAG,EAAE,2BAA2B;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAkC,CAAE,EACvH;MAAEmB,GAAG,EAAE,kBAAkB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAwC,CAAE,EACpH;MAAEmB,GAAG,EAAE,oBAAoB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAkC,CAAE,EAChH;MAAEmB,GAAG,EAAE,iBAAiB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAErB,WAAW,EAAE;IAAsC,CAAE,CAClH;IAED,KAAAmD,aAAa,GAAG,CACd;MACEpD,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE,uCAAuC;MACpDF,IAAI,EAAE,SAAS;MACfiC,QAAQ,EAAE,IAAI;MACdI,OAAO,EAAE,yGAAyG;MAClHV,KAAK,EAAE,CACL,sCAAsC,EACtC,2CAA2C,EAC3C,gDAAgD,EAChD,sDAAsD,CACvD;MACDG,IAAI,EAAE,CACJ,qDAAqD,EACrD,mDAAmD,EACnD,wCAAwC;KAE3C,EACD;MACE7B,KAAK,EAAE,+BAA+B;MACtCC,WAAW,EAAE,gDAAgD;MAC7DF,IAAI,EAAE,MAAM;MACZqC,OAAO,EAAE,mHAAmH;MAC5HV,KAAK,EAAE,CACL,kDAAkD,EAClD,wDAAwD,EACxD,sCAAsC,EACtC,kCAAkC,CACnC;MACDG,IAAI,EAAE,CACJ,8CAA8C,EAC9C,yCAAyC,EACzC,0CAA0C;KAE7C,EACD;MACE7B,KAAK,EAAE,2BAA2B;MAClCC,WAAW,EAAE,8CAA8C;MAC3DF,IAAI,EAAE,MAAM;MACZqC,OAAO,EAAE,mFAAmF;MAC5FV,KAAK,EAAE,CACL,gCAAgC,EAChC,6CAA6C,EAC7C,4CAA4C,EAC5C,qCAAqC,CACtC;MACDG,IAAI,EAAE,CACJ,2DAA2D,EAC3D,qDAAqD,EACrD,gDAAgD;KAEnD,CACF;IAED,KAAAwB,qBAAqB,GAAG,CACtB;MACET,OAAO,EAAE,oCAAoC;MAC7C3C,WAAW,EAAE,oDAAoD;MACjEF,IAAI,EAAE,WAAW;MACjB4C,QAAQ,EAAE,MAAM;MAChBE,QAAQ,EAAE,CACR,yDAAyD,EACzD,oDAAoD,EACpD,gDAAgD,EAChD,2CAA2C,CAC5C;MACDN,UAAU,EAAE;KACb,EACD;MACEK,OAAO,EAAE,6BAA6B;MACtC3C,WAAW,EAAE,0DAA0D;MACvEF,IAAI,EAAE,YAAY;MAClB4C,QAAQ,EAAE,QAAQ;MAClBE,QAAQ,EAAE,CACR,kEAAkE,EAClE,mDAAmD,EACnD,0CAA0C,EAC1C,wDAAwD,CACzD;MACDN,UAAU,EAAE;KACb,EACD;MACEK,OAAO,EAAE,oBAAoB;MAC7B3C,WAAW,EAAE,2CAA2C;MACxDF,IAAI,EAAE,OAAO;MACb4C,QAAQ,EAAE,QAAQ;MAClBE,QAAQ,EAAE,CACR,2CAA2C,EAC3C,mCAAmC,EACnC,kDAAkD,EAClD,gDAAgD,CACjD;MACDN,UAAU,EAAE;KACb,CACF;;EAED3B,eAAeA,CAAC0C,IAAY;IAC1BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;MAC5C;MACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAG;MACbH,OAAO,CAACI,KAAK,CAAC,kBAAkB,EAAED,GAAG,CAAC;IACxC,CAAC,CAAC;EACJ;;;uCAvOWhB,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvkCzBtF,EALR,CAAAC,cAAA,aAAkC,gBAEC,aACD,aACD,eACf;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EAC1B;UAEJH,EADF,CAAAC,cAAA,aAA2B,YACE;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjDH,EAAA,CAAAC,cAAA,WAA6B;UAC3BD,EAAA,CAAAE,MAAA,kIACF;UAEJF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAA8B,iBACsC,gBACtD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzBF,EADyB,CAAAG,YAAA,EAAO,EACvB;UAEPH,EADF,CAAAC,cAAA,iBAAqD,gBACzC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAGvBF,EAHuB,CAAAG,YAAA,EAAO,EACjB,EACL,EACC;UAKLH,EAFJ,CAAAC,cAAA,mBAAuC,eACL,cACA;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAC,cAAA,aAAgC;UAC9BD,EAAA,CAAAE,MAAA,6GACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAENH,EAAA,CAAAC,cAAA,eAA+B;UAC7BD,EAAA,CAAAO,UAAA,KAAAiF,wCAAA,mBAA0F;UAkB9FxF,EADE,CAAAG,YAAA,EAAM,EACE;UASAH,EANV,CAAAC,cAAA,mBAAqC,yBAC4B,mBAE/B,eACC,eACC,cACA;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjDH,EAAA,CAAAC,cAAA,aAA4B;UAAAD,EAAA,CAAAE,MAAA,qFAA6E;UAC3GF,EAD2G,CAAAG,YAAA,EAAI,EACzG;UAENH,EAAA,CAAAC,cAAA,eAAoC;UAClCD,EAAA,CAAAO,UAAA,KAAAkF,wCAAA,mBAAiF;UA4BvFzF,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAMJH,EAHN,CAAAC,cAAA,mBAA+B,eACA,eACC,cACA;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtDH,EAAA,CAAAC,cAAA,aAA4B;UAAAD,EAAA,CAAAE,MAAA,qFAA6E;UAC3GF,EAD2G,CAAAG,YAAA,EAAI,EACzG;UAKAH,EAHN,CAAAC,cAAA,eAAiC,eACF,eACI,oBACK;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EADF,CAAAC,cAAA,eAA6B,cACE;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAE/DF,EAF+D,CAAAG,YAAA,EAAI,EAC3D,EACF;UAEJH,EADF,CAAAC,cAAA,eAAgC,SAC3B;UAAAD,EAAA,CAAAE,MAAA,8DAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGzDH,EAFJ,CAAAC,cAAA,eAA4B,eACG,gBACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;UACwBH,EAA9B,CAAAC,cAAA,eAA8B,YAAM;UAAAD,EAAA,CAAAE,MAAA,iEAAkD;UAG5FF,EAH4F,CAAAG,YAAA,EAAO,EAAM,EAC/F,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA6B,eACI,oBACK;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE/CH,EADF,CAAAC,cAAA,eAA6B,cACE;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAEhDF,EAFgD,CAAAG,YAAA,EAAI,EAC5C,EACF;UAIAH,EAHN,CAAAC,cAAA,eAAgC,eACF,eACG,gBACjB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/BH,EAAA,CAAAC,cAAA,kBAAsF;UAAzCD,EAAA,CAAAmB,UAAA,mBAAAuE,2DAAA;YAAA,OAASH,GAAA,CAAA3D,eAAA,CAAA2D,GAAA,CAAAvB,aAAA,CAA8B;UAAA,EAAC;UACnFhE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACL;UACwBH,EAA9B,CAAAC,cAAA,eAA8B,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAG7DF,EAH6D,CAAAG,YAAA,EAAO,EAAM,EAChE,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA6B,eACI,oBACK;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE/CH,EADF,CAAAC,cAAA,eAA6B,cACE;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAE9DF,EAF8D,CAAAG,YAAA,EAAI,EAC1D,EACF;UAIAH,EAHN,CAAAC,cAAA,gBAAgC,gBACE,gBACA,gBACA;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzCH,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACtCH,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzCH,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzCF,EADyC,CAAAG,YAAA,EAAM,EACzC;UACNH,EAAA,CAAAO,UAAA,MAAAoF,yCAAA,mBAAkE;UAmB9E3F,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACE;UAMJH,EAHN,CAAAC,cAAA,oBAAuB,gBACQ,gBACC,eACA;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,gGAAuF;UACrHF,EADqH,CAAAG,YAAA,EAAI,EACnH;UAENH,EAAA,CAAAC,cAAA,gBAAgC;UAC9BD,EAAA,CAAAO,UAAA,MAAAqF,yCAAA,oBAAgE;UAiCtE5F,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAMJH,EAHN,CAAAC,cAAA,oBAAiC,gBACF,gBACC,eACA;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9CH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,mEAA0D;UACxFF,EADwF,CAAAG,YAAA,EAAI,EACtF;UAENH,EAAA,CAAAC,cAAA,gBAAsC;UACpCD,EAAA,CAAAO,UAAA,MAAAsF,yCAAA,mBAAwE;UA4BpF7F,EALU,CAAAG,YAAA,EAAM,EACF,EACE,EACI,EACR,EACN;;;UAvOsEH,EAAA,CAAAI,SAAA,IAAkB;UAAlBJ,EAAA,CAAAS,UAAA,YAAA8E,GAAA,CAAAtB,eAAA,CAAkB;UAgCtCjE,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAS,UAAA,YAAA8E,GAAA,CAAArB,iBAAA,CAAsB;UA4ExBlE,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAK,iBAAA,CAAAkF,GAAA,CAAAvB,aAAA,CAAmB;UAqBRhE,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAS,UAAA,YAAA8E,GAAA,CAAApB,cAAA,CAAiB;UA8BxBnE,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAS,UAAA,YAAA8E,GAAA,CAAAnB,aAAA,CAAgB;UA4ChBpE,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAS,UAAA,YAAA8E,GAAA,CAAAlB,qBAAA,CAAwB;;;qBAzPlF7E,YAAY,EAAAsG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZvG,aAAa,EAAAwG,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACbzG,aAAa,EACbC,aAAa,EAAAyG,EAAA,CAAAC,OAAA,EACbzG,eAAe,EAAA0G,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf3G,cAAc,EACdC,kBAAkB,EAClBC,cAAc;MAAA0G,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}