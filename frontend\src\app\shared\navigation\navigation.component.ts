import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ThemeService } from '../../services/theme.service';
import { AuthService } from '../../services/auth.service';
import { Observable } from 'rxjs';

interface NavigationItem {
  label: string;
  route: string;
  icon: string;
  badge?: number;
  tooltip?: string;
}

@Component({
  selector: 'app-navigation',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatBadgeModule,
    MatTooltipModule,
    MatSlideToggleModule
  ],
  template: `
    <mat-toolbar class="spt-navbar">
      <!-- Brand Section -->
      <div class="navbar-brand">
        <button mat-icon-button routerLink="/dashboard" class="brand-button">
          <mat-icon class="brand-icon">security</mat-icon>
        </button>
        <span class="brand-text" routerLink="/dashboard">SPT</span>
        <span class="brand-subtitle">Security Platform</span>
      </div>

      <!-- Navigation Links -->
      <nav class="navbar-nav">
        <a 
          *ngFor="let item of navigationItems" 
          mat-button 
          [routerLink]="item.route"
          routerLinkActive="active"
          class="nav-link"
          [matTooltip]="item.tooltip || ''"
          matTooltipPosition="below">
          <mat-icon>{{ item.icon }}</mat-icon>
          <span>{{ item.label }}</span>
          <span *ngIf="item.badge" 
                matBadge="{{ item.badge }}" 
                matBadgeColor="warn" 
                matBadgeSize="small"
                class="nav-badge">
          </span>
        </a>
      </nav>

      <!-- Spacer -->
      <div class="navbar-spacer"></div>

      <!-- Actions Section -->
      <div class="navbar-actions">
        <!-- Theme Toggle -->
        <button 
          mat-icon-button 
          (click)="toggleTheme()"
          [matTooltip]="(isDark$ | async) ? 'Switch to light mode' : 'Switch to dark mode'"
          class="theme-toggle">
          <mat-icon>{{ (isDark$ | async) ? 'light_mode' : 'dark_mode' }}</mat-icon>
        </button>

        <!-- Notifications -->
        <button 
          mat-icon-button 
          [matMenuTriggerFor]="notificationsMenu"
          matTooltip="Notifications"
          class="notifications-button">
          <mat-icon matBadge="3" matBadgeColor="warn" matBadgeSize="small">notifications</mat-icon>
        </button>

        <!-- User Menu -->
        <button 
          mat-button 
          [matMenuTriggerFor]="userMenu"
          class="user-menu-button">
          <div class="user-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
          <span class="user-name">{{ currentUser?.username || 'Admin' }}</span>
          <mat-icon class="dropdown-icon">expand_more</mat-icon>
        </button>
      </div>
    </mat-toolbar>

    <!-- Notifications Menu -->
    <mat-menu #notificationsMenu="matMenu" class="notifications-menu">
      <div class="menu-header">
        <h3>Notifications</h3>
        <button mat-icon-button>
          <mat-icon>settings</mat-icon>
        </button>
      </div>
      <div class="notification-item" *ngFor="let notification of notifications">
        <mat-icon [class]="'notification-icon ' + notification.type">{{ notification.icon }}</mat-icon>
        <div class="notification-content">
          <p class="notification-title">{{ notification.title }}</p>
          <p class="notification-message">{{ notification.message }}</p>
          <span class="notification-time">{{ notification.time }}</span>
        </div>
      </div>
      <div class="menu-footer">
        <button mat-button color="primary">View All</button>
      </div>
    </mat-menu>

    <!-- User Menu -->
    <mat-menu #userMenu="matMenu" class="user-menu">
      <div class="user-menu-header">
        <div class="user-avatar-large">
          <mat-icon>account_circle</mat-icon>
        </div>
        <div class="user-info">
          <p class="user-name">{{ currentUser?.username || 'Admin' }}</p>
          <p class="user-email">{{ currentUser?.email || '<EMAIL>' }}</p>
        </div>
      </div>
      <mat-divider></mat-divider>
      <button mat-menu-item routerLink="/settings">
        <mat-icon>settings</mat-icon>
        <span>Settings</span>
      </button>
      <button mat-menu-item routerLink="/profile">
        <mat-icon>person</mat-icon>
        <span>Profile</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  `,
  styles: [`
    .spt-navbar {
      background: var(--spt-surface);
      border-bottom: 1px solid var(--spt-border);
      box-shadow: var(--spt-shadow-sm);
      height: 64px;
      padding: 0 var(--spt-space-6);
      position: sticky;
      top: 0;
      z-index: var(--spt-z-sticky);
      transition: all 0.2s ease;
    }

    .navbar-brand {
      display: flex;
      align-items: center;
      gap: var(--spt-space-3);
      margin-right: var(--spt-space-8);
    }

    .brand-button {
      width: 40px;
      height: 40px;
      background: var(--spt-primary-600);
      color: white;
      border-radius: var(--spt-radius-lg);
    }

    .brand-button:hover {
      background: var(--spt-primary-700);
      transform: scale(1.05);
    }

    .brand-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .brand-text {
      font-size: var(--spt-text-xl);
      font-weight: var(--spt-font-bold);
      color: var(--spt-text-primary);
      cursor: pointer;
      text-decoration: none;
    }

    .brand-subtitle {
      font-size: var(--spt-text-sm);
      color: var(--spt-text-secondary);
      font-weight: var(--spt-font-medium);
    }

    .navbar-nav {
      display: flex;
      gap: var(--spt-space-2);
    }

    .nav-link {
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
      padding: var(--spt-space-2) var(--spt-space-4);
      border-radius: var(--spt-radius-lg);
      color: var(--spt-text-secondary);
      text-decoration: none;
      transition: all 0.2s ease;
      font-weight: var(--spt-font-medium);
      position: relative;
    }

    .nav-link:hover {
      background: var(--spt-primary-50);
      color: var(--spt-primary-700);
      transform: translateY(-1px);
    }

    .nav-link.active {
      background: var(--spt-primary-100);
      color: var(--spt-primary-700);
      font-weight: var(--spt-font-semibold);
    }

    .navbar-spacer {
      flex: 1;
    }

    .navbar-actions {
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
    }

    .theme-toggle,
    .notifications-button {
      width: 40px;
      height: 40px;
      border-radius: var(--spt-radius-lg);
      color: var(--spt-text-secondary);
      transition: all 0.2s ease;
    }

    .theme-toggle:hover,
    .notifications-button:hover {
      background: var(--spt-gray-100);
      color: var(--spt-text-primary);
      transform: scale(1.05);
    }

    .user-menu-button {
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
      padding: var(--spt-space-2) var(--spt-space-3);
      border-radius: var(--spt-radius-lg);
      color: var(--spt-text-primary);
      transition: all 0.2s ease;
    }

    .user-menu-button:hover {
      background: var(--spt-gray-100);
    }

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: var(--spt-radius-full);
      background: var(--spt-primary-100);
      color: var(--spt-primary-600);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-name {
      font-weight: var(--spt-font-medium);
      font-size: var(--spt-text-sm);
    }

    .dropdown-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: var(--spt-text-secondary);
    }

    /* Menu Styles */
    .notifications-menu,
    .user-menu {
      border-radius: var(--spt-radius-xl);
      box-shadow: var(--spt-shadow-xl);
      border: 1px solid var(--spt-border);
      overflow: hidden;
    }

    .menu-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spt-space-4);
      border-bottom: 1px solid var(--spt-border);
      background: var(--spt-bg-secondary);
    }

    .menu-header h3 {
      margin: 0;
      font-size: var(--spt-text-base);
      font-weight: var(--spt-font-semibold);
    }

    .notification-item {
      display: flex;
      gap: var(--spt-space-3);
      padding: var(--spt-space-3) var(--spt-space-4);
      border-bottom: 1px solid var(--spt-border-light);
      transition: background 0.2s ease;
    }

    .notification-item:hover {
      background: var(--spt-bg-secondary);
    }

    .notification-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      margin-top: var(--spt-space-1);
    }

    .notification-icon.success { color: var(--spt-success-600); }
    .notification-icon.warning { color: var(--spt-warning-600); }
    .notification-icon.error { color: var(--spt-error-600); }

    .notification-content {
      flex: 1;
    }

    .notification-title {
      margin: 0 0 var(--spt-space-1) 0;
      font-weight: var(--spt-font-medium);
      font-size: var(--spt-text-sm);
    }

    .notification-message {
      margin: 0 0 var(--spt-space-1) 0;
      font-size: var(--spt-text-xs);
      color: var(--spt-text-secondary);
    }

    .notification-time {
      font-size: var(--spt-text-xs);
      color: var(--spt-text-tertiary);
    }

    .user-menu-header {
      display: flex;
      gap: var(--spt-space-3);
      padding: var(--spt-space-4);
      background: var(--spt-bg-secondary);
    }

    .user-avatar-large {
      width: 48px;
      height: 48px;
      border-radius: var(--spt-radius-full);
      background: var(--spt-primary-100);
      color: var(--spt-primary-600);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-avatar-large mat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .user-info .user-name {
      margin: 0 0 var(--spt-space-1) 0;
      font-weight: var(--spt-font-semibold);
      font-size: var(--spt-text-base);
    }

    .user-info .user-email {
      margin: 0;
      font-size: var(--spt-text-sm);
      color: var(--spt-text-secondary);
    }

    .menu-footer {
      padding: var(--spt-space-3) var(--spt-space-4);
      border-top: 1px solid var(--spt-border);
      background: var(--spt-bg-secondary);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .navbar-nav {
        display: none;
      }
      
      .brand-subtitle {
        display: none;
      }
      
      .user-name {
        display: none;
      }
    }
  `]
})
export class NavigationComponent implements OnInit {
  isDark$: Observable<boolean>;
  currentUser: any = null;

  navigationItems: NavigationItem[] = [
    { label: 'Dashboard', route: '/dashboard', icon: 'dashboard', tooltip: 'Security Overview' },
    { label: 'Scan', route: '/scan', icon: 'security', tooltip: 'Start New Scan' },
    { label: 'Reports', route: '/reports', icon: 'assessment', tooltip: 'Security Reports' },
    { label: 'Checklist', route: '/checklist', icon: 'checklist', tooltip: 'Security Checklist' },
    { label: 'Projects', route: '/projects', icon: 'folder', tooltip: 'Manage Projects' }
  ];

  notifications = [
    {
      type: 'success',
      icon: 'check_circle',
      title: 'Scan Completed',
      message: 'Project scan finished successfully',
      time: '2 minutes ago'
    },
    {
      type: 'warning',
      icon: 'warning',
      title: 'High Risk Detected',
      message: '3 critical vulnerabilities found',
      time: '5 minutes ago'
    },
    {
      type: 'error',
      icon: 'error',
      title: 'Scan Failed',
      message: 'Unable to access project files',
      time: '10 minutes ago'
    }
  ];

  constructor(
    private themeService: ThemeService,
    private authService: AuthService,
    private router: Router
  ) {
    this.isDark$ = this.themeService.isDark$;
  }

  ngOnInit(): void {
    // Get current user info
    this.currentUser = this.authService.getCurrentUser();
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
