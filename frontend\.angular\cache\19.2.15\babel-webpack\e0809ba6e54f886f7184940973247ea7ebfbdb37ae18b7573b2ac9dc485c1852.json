{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { SEVERITY_COLORS } from '../../models/security.models';\nimport { SecurityMetricsChartComponent } from '../../shared/charts/security-metrics-chart/security-metrics-chart.component';\nimport { ScanProgressChartComponent } from '../../shared/charts/scan-progress-chart/scan-progress-chart.component';\nimport { SecurityTrendsDashboardComponent } from '../../shared/charts/security-trends-dashboard/security-trends-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/table\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/tooltip\";\nimport * as i9 from \"@angular/router\";\nconst _c0 = a0 => [\"/scan\", a0];\nfunction DashboardComponent_div_137_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Scan ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", scan_r1.id.substring(0, 8), \"...\");\n  }\n}\nfunction DashboardComponent_div_137_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 77)(2, \"mat-icon\", 78);\n    i0.ɵɵtext(3, \"folder\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getProjectName(scan_r2.project_path));\n  }\n}\nfunction DashboardComponent_div_137_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Chains\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_10_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chain_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", chain_r4, \" \");\n  }\n}\nfunction DashboardComponent_div_137_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 79);\n    i0.ɵɵtemplate(2, DashboardComponent_div_137_td_10_mat_chip_2_Template, 2, 1, \"mat-chip\", 80);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", scan_r5.chains);\n  }\n}\nfunction DashboardComponent_div_137_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"mat-chip\")(2, \"mat-icon\", 82);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scan_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-chip status-\" + scan_r6.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusIcon(scan_r6.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, scan_r6.status), \" \");\n  }\n}\nfunction DashboardComponent_div_137_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Issues\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 83)(2, \"span\", 84);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 85);\n    i0.ɵɵtext(5, \"issues\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((scan_r7.issues == null ? null : scan_r7.issues.length) || 0);\n  }\n}\nfunction DashboardComponent_div_137_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 88);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const scan_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(scan_r8.created_at));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatTime(scan_r8.created_at));\n  }\n}\nfunction DashboardComponent_div_137_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 74);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_137_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75)(1, \"div\", 89)(2, \"button\", 90)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 91)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"download\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const scan_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, scan_r9.id));\n  }\n}\nfunction DashboardComponent_div_137_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 92);\n  }\n}\nfunction DashboardComponent_div_137_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 93);\n  }\n}\nfunction DashboardComponent_div_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"table\", 62);\n    i0.ɵɵelementContainerStart(2, 63);\n    i0.ɵɵtemplate(3, DashboardComponent_div_137_th_3_Template, 2, 0, \"th\", 64)(4, DashboardComponent_div_137_td_4_Template, 3, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 66);\n    i0.ɵɵtemplate(6, DashboardComponent_div_137_th_6_Template, 2, 0, \"th\", 64)(7, DashboardComponent_div_137_td_7_Template, 6, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 67);\n    i0.ɵɵtemplate(9, DashboardComponent_div_137_th_9_Template, 2, 0, \"th\", 64)(10, DashboardComponent_div_137_td_10_Template, 3, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 68);\n    i0.ɵɵtemplate(12, DashboardComponent_div_137_th_12_Template, 2, 0, \"th\", 64)(13, DashboardComponent_div_137_td_13_Template, 6, 6, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 69);\n    i0.ɵɵtemplate(15, DashboardComponent_div_137_th_15_Template, 2, 0, \"th\", 64)(16, DashboardComponent_div_137_td_16_Template, 6, 1, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 70);\n    i0.ɵɵtemplate(18, DashboardComponent_div_137_th_18_Template, 2, 0, \"th\", 64)(19, DashboardComponent_div_137_td_19_Template, 6, 2, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(20, 71);\n    i0.ɵɵtemplate(21, DashboardComponent_div_137_th_21_Template, 2, 0, \"th\", 64)(22, DashboardComponent_div_137_td_22_Template, 8, 3, \"td\", 65);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(23, DashboardComponent_div_137_tr_23_Template, 1, 0, \"tr\", 72)(24, DashboardComponent_div_137_tr_24_Template, 1, 0, \"tr\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.recentScans);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n  }\n}\nfunction DashboardComponent_ng_template_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"mat-icon\", 95);\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No scans yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start your first security scan to see results here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 57)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Start First Scan \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97)(2, \"mat-chip\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 98)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 99)(12, \"mat-chip\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const issue_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getSeverityColor(issue_r10.severity));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", issue_r10.severity, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r10.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", issue_r10.file, \":\", issue_r10.line, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(issue_r10.chain);\n  }\n}\nfunction DashboardComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"app-scan-progress-chart\", 100);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scanProgress\", ctx_r2.getActiveScan())(\"showRealTimeUpdates\", true)(\"updateInterval\", 2000);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    // Additional properties for enhanced header\n    get totalScans() {\n      return this.stats.totalScans;\n    }\n    get activeScans() {\n      return this.recentScans.filter(scan => scan.status === 'running').length;\n    }\n    get criticalIssues() {\n      return this.stats.criticalIssues;\n    }\n    constructor(apiService) {\n      this.apiService = apiService;\n      this.stats = {\n        totalScans: 0,\n        criticalIssues: 0,\n        highIssues: 0,\n        mediumIssues: 0\n      };\n      this.recentScans = [];\n      this.topIssues = [];\n      this.displayedColumns = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];\n    }\n    ngOnInit() {\n      this.loadDashboardData();\n    }\n    loadDashboardData() {\n      // Load recent scans\n      this.apiService.getScanHistory().subscribe({\n        next: response => {\n          this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent\n          this.calculateStats(response.scans);\n        },\n        error: error => {\n          console.error('Error loading scan history:', error);\n          // Use mock data for development\n          this.loadMockData();\n        }\n      });\n    }\n    loadMockData() {\n      // Generate mock data for development\n      const mockScan = this.apiService.generateMockScanResult();\n      this.recentScans = [mockScan];\n      this.topIssues = mockScan.issues;\n      this.stats = {\n        totalScans: 1,\n        criticalIssues: mockScan.severity_counts['critical'] || 0,\n        highIssues: mockScan.severity_counts['high'] || 0,\n        mediumIssues: mockScan.severity_counts['medium'] || 0\n      };\n    }\n    calculateStats(scans) {\n      this.stats.totalScans = scans.length;\n      let criticalTotal = 0;\n      let highTotal = 0;\n      let mediumTotal = 0;\n      let allIssues = [];\n      scans.forEach(scan => {\n        criticalTotal += scan.severity_counts?.['critical'] || 0;\n        highTotal += scan.severity_counts?.['high'] || 0;\n        mediumTotal += scan.severity_counts?.['medium'] || 0;\n        allIssues = allIssues.concat(scan.issues || []);\n      });\n      this.stats.criticalIssues = criticalTotal;\n      this.stats.highIssues = highTotal;\n      this.stats.mediumIssues = mediumTotal;\n      // Get top 5 most severe issues\n      this.topIssues = allIssues.sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity)).slice(0, 5);\n    }\n    getSeverityWeight(severity) {\n      const weights = {\n        critical: 4,\n        high: 3,\n        medium: 2,\n        low: 1,\n        info: 0\n      };\n      return weights[severity] || 0;\n    }\n    getSeverityColor(severity) {\n      return SEVERITY_COLORS[severity] || '#666';\n    }\n    getProjectName(path) {\n      return path.split('/').pop() || path;\n    }\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString();\n    }\n    formatTime(dateString) {\n      return new Date(dateString).toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getStatusIcon(status) {\n      const icons = {\n        completed: 'check_circle',\n        running: 'hourglass_empty',\n        failed: 'error',\n        pending: 'schedule'\n      };\n      return icons[status] || 'help';\n    }\n    // Chart data methods\n    getSecurityMetricsData() {\n      return [{\n        label: 'Critical',\n        value: this.stats.criticalIssues,\n        severity: 'critical',\n        trend: 15 // Mock trend data\n      }, {\n        label: 'High',\n        value: this.stats.highIssues,\n        severity: 'high',\n        trend: -5\n      }, {\n        label: 'Medium',\n        value: this.stats.mediumIssues,\n        severity: 'medium',\n        trend: 8\n      }, {\n        label: 'Low',\n        value: Math.max(0, this.stats.totalScans * 2 - this.stats.criticalIssues - this.stats.highIssues - this.stats.mediumIssues),\n        severity: 'low',\n        trend: -12\n      }];\n    }\n    getActiveScan() {\n      const activeScan = this.recentScans.find(scan => scan.status === 'running');\n      if (!activeScan) return null;\n      return {\n        scanId: activeScan.id,\n        projectName: this.getProjectName(activeScan.project_path),\n        status: 'running',\n        progress: 65,\n        // Mock progress\n        currentStep: 'Analyzing smart contracts',\n        totalSteps: 6,\n        completedSteps: 3,\n        startTime: new Date(activeScan.created_at),\n        estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000),\n        // 5 minutes from now\n        filesScanned: 45,\n        totalFiles: 78,\n        issuesFound: activeScan.issues?.length || 0\n      };\n    }\n    getSecurityTrendsData() {\n      // Generate mock trend data\n      const now = new Date();\n      const data = [];\n      for (let i = 29; i >= 0; i--) {\n        const date = new Date(now);\n        date.setDate(date.getDate() - i);\n        data.push({\n          date,\n          critical: Math.floor(Math.random() * 5) + (i < 10 ? 2 : 0),\n          high: Math.floor(Math.random() * 8) + 3,\n          medium: Math.floor(Math.random() * 12) + 5,\n          low: Math.floor(Math.random() * 15) + 8,\n          totalScans: Math.floor(Math.random() * 3) + 1,\n          averageScore: Math.floor(Math.random() * 30) + 60\n        });\n      }\n      return {\n        period: '30d',\n        data,\n        summary: {\n          totalIssues: this.stats.criticalIssues + this.stats.highIssues + this.stats.mediumIssues,\n          criticalTrend: 15,\n          // Mock trend\n          averageScore: 72,\n          scanFrequency: 1.2\n        }\n      };\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        decls: 174,\n        vars: 18,\n        consts: [[\"noScans\", \"\"], [1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-text\"], [1, \"dashboard-title\"], [1, \"title-icon\"], [1, \"dashboard-subtitle\"], [1, \"header-stats\"], [1, \"quick-stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\", 1, \"action-button\", \"primary\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/reports\", 1, \"action-button\", \"secondary\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-scans\", \"elevated\"], [1, \"stat-card-header\"], [1, \"stat-icon-wrapper\", \"success\"], [1, \"stat-icon\"], [1, \"stat-trend\"], [1, \"trend-icon\", \"positive\"], [1, \"trend-value\"], [1, \"stat-card-content\"], [1, \"stat-number\"], [1, \"stat-description\"], [1, \"stat-card-footer\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [1, \"stat-card\", \"critical-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"critical\"], [1, \"trend-icon\", \"negative\"], [1, \"stat-number\", \"critical\"], [\"mat-button\", \"\", \"color\", \"warn\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"high-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"warning\"], [1, \"trend-icon\", \"neutral\"], [1, \"stat-number\", \"warning\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/reports\"], [1, \"stat-card\", \"medium-issues\", \"elevated\"], [1, \"stat-icon-wrapper\", \"info\"], [1, \"stat-number\", \"info\"], [\"mat-button\", \"\", \"routerLink\", \"/checklist\"], [1, \"content-section\"], [1, \"recent-scans-card\"], [1, \"section-header\"], [1, \"section-title\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/scan\", 1, \"secondary-button\"], [\"class\", \"table-container\", 4, \"ngIf\", \"ngIfElse\"], [1, \"top-issues-card\"], [1, \"issues-list\"], [\"class\", \"issue-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-section\"], [\"title\", \"Security Issues Distribution\", 3, \"data\", \"chartType\", \"showTrends\", \"animated\"], [\"class\", \"chart-section\", 4, \"ngIf\"], [3, \"trendsData\", \"showComparison\", \"autoRefresh\"], [1, \"quick-actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/scan\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/checklist\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/reports\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/settings\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"scans-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"project\"], [\"matColumnDef\", \"chains\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"issues\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"scan-row\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"scan-id\"], [1, \"project-info\"], [1, \"project-icon\"], [1, \"chains-container\"], [\"class\", \"chain-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"chain-chip\"], [1, \"status-icon\"], [1, \"issues-count\"], [1, \"count\"], [1, \"label\"], [1, \"date-info\"], [1, \"date\"], [1, \"time\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View Details\", 3, \"routerLink\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Download Report\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"scan-row\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"issue-item\"], [1, \"issue-severity\"], [1, \"issue-details\"], [1, \"issue-chain\"], [3, \"scanProgress\", \"showRealTimeUpdates\", \"updateInterval\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\", 5)(5, \"mat-icon\", 6);\n            i0.ɵɵtext(6, \"dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(7, \" Security Dashboard \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 7);\n            i0.ɵɵtext(9, \"Monitor your blockchain security posture in real-time\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"span\", 10);\n            i0.ɵɵtext(13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"span\", 11);\n            i0.ɵɵtext(15, \"Total Scans\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 9)(17, \"span\", 10);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"span\", 11);\n            i0.ɵɵtext(20, \"Active\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 9)(22, \"span\", 10);\n            i0.ɵɵtext(23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"span\", 11);\n            i0.ɵɵtext(25, \"Critical\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(26, \"div\", 12)(27, \"button\", 13)(28, \"mat-icon\");\n            i0.ɵɵtext(29, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30, \" Start New Scan \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"button\", 14)(32, \"mat-icon\");\n            i0.ɵɵtext(33, \"assessment\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(34, \" View Reports \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 15)(36, \"mat-card\", 16)(37, \"div\", 17)(38, \"div\", 18)(39, \"mat-icon\", 19);\n            i0.ɵɵtext(40, \"security\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 20)(42, \"mat-icon\", 21);\n            i0.ɵɵtext(43, \"trending_up\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"span\", 22);\n            i0.ɵɵtext(45, \"+12%\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(46, \"div\", 23)(47, \"div\", 24);\n            i0.ɵɵtext(48);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 11);\n            i0.ɵɵtext(50, \"Total Scans\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 25);\n            i0.ɵɵtext(52, \"Completed this month\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"div\", 26)(54, \"button\", 27)(55, \"mat-icon\");\n            i0.ɵɵtext(56, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(57, \" New Scan \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(58, \"mat-card\", 28)(59, \"div\", 17)(60, \"div\", 29)(61, \"mat-icon\", 19);\n            i0.ɵɵtext(62, \"error\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 20)(64, \"mat-icon\", 30);\n            i0.ɵɵtext(65, \"trending_up\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"span\", 22);\n            i0.ɵɵtext(67, \"+3\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(68, \"div\", 23)(69, \"div\", 31);\n            i0.ɵɵtext(70);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"div\", 11);\n            i0.ɵɵtext(72, \"Critical Issues\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"div\", 25);\n            i0.ɵɵtext(74, \"Require immediate attention\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(75, \"div\", 26)(76, \"button\", 32)(77, \"mat-icon\");\n            i0.ɵɵtext(78, \"priority_high\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(79, \" View Details \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(80, \"mat-card\", 33)(81, \"div\", 17)(82, \"div\", 34)(83, \"mat-icon\", 19);\n            i0.ɵɵtext(84, \"warning\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(85, \"div\", 20)(86, \"mat-icon\", 35);\n            i0.ɵɵtext(87, \"trending_flat\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"span\", 22);\n            i0.ɵɵtext(89, \"0\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(90, \"div\", 23)(91, \"div\", 36);\n            i0.ɵɵtext(92);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"div\", 11);\n            i0.ɵɵtext(94, \"High Priority\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"div\", 25);\n            i0.ɵɵtext(96, \"Should be addressed soon\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(97, \"div\", 26)(98, \"button\", 37)(99, \"mat-icon\");\n            i0.ɵɵtext(100, \"visibility\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(101, \" Review \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(102, \"mat-card\", 38)(103, \"div\", 17)(104, \"div\", 39)(105, \"mat-icon\", 19);\n            i0.ɵɵtext(106, \"info\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(107, \"div\", 20)(108, \"mat-icon\", 21);\n            i0.ɵɵtext(109, \"trending_down\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"span\", 22);\n            i0.ɵɵtext(111, \"-5\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(112, \"div\", 23)(113, \"div\", 40);\n            i0.ɵɵtext(114);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"div\", 11);\n            i0.ɵɵtext(116, \"Medium Priority\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(117, \"div\", 25);\n            i0.ɵɵtext(118, \"Monitor and plan fixes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(119, \"div\", 26)(120, \"button\", 41)(121, \"mat-icon\");\n            i0.ɵɵtext(122, \"checklist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(123, \" Checklist \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(124, \"div\", 42)(125, \"mat-card\", 43)(126, \"mat-card-header\", 44)(127, \"div\", 45)(128, \"mat-icon\");\n            i0.ɵɵtext(129, \"history\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(130, \"mat-card-title\");\n            i0.ɵɵtext(131, \"Recent Scans\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(132, \"button\", 46)(133, \"mat-icon\");\n            i0.ɵɵtext(134, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(135, \" New Scan \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(136, \"mat-card-content\");\n            i0.ɵɵtemplate(137, DashboardComponent_div_137_Template, 25, 3, \"div\", 47)(138, DashboardComponent_ng_template_138_Template, 11, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(140, \"mat-card\", 48)(141, \"mat-card-header\")(142, \"mat-card-title\");\n            i0.ɵɵtext(143, \"Top Security Issues\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(144, \"mat-card-content\")(145, \"div\", 49);\n            i0.ɵɵtemplate(146, DashboardComponent_div_146_Template, 14, 8, \"div\", 50);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(147, \"div\", 51);\n            i0.ɵɵelement(148, \"app-security-metrics-chart\", 52);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(149, DashboardComponent_div_149_Template, 2, 3, \"div\", 53);\n            i0.ɵɵelementStart(150, \"div\", 51);\n            i0.ɵɵelement(151, \"app-security-trends-dashboard\", 54);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(152, \"mat-card\", 55)(153, \"mat-card-header\")(154, \"mat-card-title\");\n            i0.ɵɵtext(155, \"Quick Actions\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(156, \"mat-card-content\")(157, \"div\", 56)(158, \"button\", 57)(159, \"mat-icon\");\n            i0.ɵɵtext(160, \"security\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(161, \" Start New Scan \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(162, \"button\", 58)(163, \"mat-icon\");\n            i0.ɵɵtext(164, \"checklist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(165, \" Security Checklist \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(166, \"button\", 59)(167, \"mat-icon\");\n            i0.ɵɵtext(168, \"assessment\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(169, \" Generate Report \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(170, \"button\", 60)(171, \"mat-icon\");\n            i0.ɵɵtext(172, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(173, \" Settings \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            const noScans_r11 = i0.ɵɵreference(139);\n            i0.ɵɵadvance(13);\n            i0.ɵɵtextInterpolate(ctx.totalScans);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.activeScans);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.criticalIssues);\n            i0.ɵɵadvance(25);\n            i0.ɵɵtextInterpolate(ctx.stats.totalScans);\n            i0.ɵɵadvance(22);\n            i0.ɵɵtextInterpolate(ctx.stats.criticalIssues);\n            i0.ɵɵadvance(22);\n            i0.ɵɵtextInterpolate(ctx.stats.highIssues);\n            i0.ɵɵadvance(22);\n            i0.ɵɵtextInterpolate(ctx.stats.mediumIssues);\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"ngIf\", ctx.recentScans.length > 0)(\"ngIfElse\", noScans_r11);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.topIssues);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"data\", ctx.getSecurityMetricsData())(\"chartType\", \"doughnut\")(\"showTrends\", true)(\"animated\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getActiveScan());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"trendsData\", ctx.getSecurityTrendsData())(\"showComparison\", true)(\"autoRefresh\", false);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, i5.MatIconButton, MatTableModule, i6.MatTable, i6.MatHeaderCellDef, i6.MatHeaderRowDef, i6.MatColumnDef, i6.MatCellDef, i6.MatRowDef, i6.MatHeaderCell, i6.MatCell, i6.MatHeaderRow, i6.MatRow, MatChipsModule, i7.MatChip, MatProgressBarModule, MatTooltipModule, i8.MatTooltip, RouterModule, i9.RouterLink, SecurityMetricsChartComponent, ScanProgressChartComponent, SecurityTrendsDashboardComponent],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{padding:0;max-width:100%;margin:0;min-height:100%}.dashboard-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-primary-600) 0%,var(--spt-secondary-600) 100%);color:#fff;padding:var(--spt-space-12) var(--spt-space-8);display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:var(--spt-space-8);border-radius:0 0 var(--spt-radius-3xl) var(--spt-radius-3xl);position:relative;overflow:hidden}.dashboard-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');opacity:.3}.header-content[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-12);align-items:flex-start;position:relative;z-index:1}.header-text[_ngcontent-%COMP%]{flex:1}.dashboard-title[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-2) 0;font-size:var(--spt-text-4xl);font-weight:var(--spt-font-bold);display:flex;align-items:center;gap:var(--spt-space-3);line-height:var(--spt-leading-tight)}.title-icon[_ngcontent-%COMP%]{font-size:40px;width:40px;height:40px;background:#fff3;border-radius:var(--spt-radius-xl);display:flex;align-items:center;justify-content:center}.dashboard-subtitle[_ngcontent-%COMP%]{margin:0;opacity:.9;font-size:var(--spt-text-lg);font-weight:var(--spt-font-normal);line-height:var(--spt-leading-relaxed)}.header-stats[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-6);margin-top:var(--spt-space-2)}.quick-stat[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:var(--spt-space-3) var(--spt-space-4);background:#ffffff26;border-radius:var(--spt-radius-xl);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.stat-value[_ngcontent-%COMP%]{font-size:var(--spt-text-2xl);font-weight:var(--spt-font-bold);line-height:1}.stat-label[_ngcontent-%COMP%]{font-size:var(--spt-text-xs);opacity:.8;text-transform:uppercase;letter-spacing:.05em;margin-top:var(--spt-space-1)}.header-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-3);position:relative;z-index:1}.action-button[_ngcontent-%COMP%]{height:48px;padding:0 var(--spt-space-6);border-radius:var(--spt-radius-xl);font-weight:var(--spt-font-semibold);transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.3)}.action-button.primary[_ngcontent-%COMP%]{background:#fff3;color:#fff}.action-button.primary[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:var(--spt-shadow-lg)}.action-button.secondary[_ngcontent-%COMP%]{background:#ffffff1a;color:#fff}.action-button.secondary[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-1px)}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(320px,1fr));gap:var(--spt-space-6);margin:0 var(--spt-space-8) var(--spt-space-12) var(--spt-space-8)}.stat-card[_ngcontent-%COMP%]{border-radius:var(--spt-radius-2xl);border:1px solid var(--spt-border);background:var(--spt-surface);box-shadow:var(--spt-shadow-sm);transition:all .3s cubic-bezier(.4,0,.2,1);overflow:hidden;position:relative}.stat-card.elevated[_ngcontent-%COMP%]{box-shadow:var(--spt-shadow-lg)}.stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:var(--spt-shadow-xl);border-color:var(--spt-primary-200)}.stat-card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:var(--spt-space-5) var(--spt-space-6) var(--spt-space-3) var(--spt-space-6)}.stat-card-content[_ngcontent-%COMP%]{padding:0 var(--spt-space-6) var(--spt-space-4) var(--spt-space-6)}.stat-card-footer[_ngcontent-%COMP%]{padding:var(--spt-space-3) var(--spt-space-6) var(--spt-space-5) var(--spt-space-6);border-top:1px solid var(--spt-border-light);background:var(--spt-bg-secondary)}.stat-icon-wrapper[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:var(--spt-radius-xl);display:flex;align-items:center;justify-content:center;position:relative;transition:all .3s ease}.stat-icon-wrapper.success[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-success-100) 0%,var(--spt-success-200) 100%);border:1px solid var(--spt-success-300)}.stat-icon-wrapper.critical[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-error-100) 0%,var(--spt-error-200) 100%);border:1px solid var(--spt-error-300)}.stat-icon-wrapper.warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-warning-100) 0%,var(--spt-warning-200) 100%);border:1px solid var(--spt-warning-300)}.stat-icon-wrapper.info[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--spt-info-100) 0%,var(--spt-info-200) 100%);border:1px solid var(--spt-info-300)}.stat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;transition:transform .3s ease}.stat-card[_ngcontent-%COMP%]:hover   .stat-icon[_ngcontent-%COMP%]{transform:scale(1.1)}.stat-icon-wrapper.success[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-success-700)}.stat-icon-wrapper.critical[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-error-700)}.stat-icon-wrapper.warning[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-warning-700)}.stat-icon-wrapper.info[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{color:var(--spt-info-700)}.stat-trend[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-1);padding:var(--spt-space-1) var(--spt-space-2);border-radius:var(--spt-radius-lg);font-size:var(--spt-text-xs);font-weight:var(--spt-font-semibold)}.trend-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.trend-icon.positive[_ngcontent-%COMP%]{color:var(--spt-success-600)}.trend-icon.negative[_ngcontent-%COMP%]{color:var(--spt-error-600)}.trend-icon.neutral[_ngcontent-%COMP%]{color:var(--spt-text-tertiary)}.trend-value[_ngcontent-%COMP%]{font-weight:var(--spt-font-bold)}.stat-number[_ngcontent-%COMP%]{font-size:var(--spt-text-4xl);font-weight:var(--spt-font-bold);color:var(--spt-text-primary);margin-bottom:var(--spt-space-1);line-height:var(--spt-leading-none);transition:color .3s ease}.stat-number.critical[_ngcontent-%COMP%]{color:var(--spt-error-600)}.stat-number.warning[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.stat-number.info[_ngcontent-%COMP%]{color:var(--spt-info-600)}.stat-label[_ngcontent-%COMP%]{font-size:var(--spt-text-base);color:var(--spt-text-primary);font-weight:var(--spt-font-semibold);margin-bottom:var(--spt-space-1);line-height:var(--spt-leading-tight)}.stat-description[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-text-secondary);font-weight:var(--spt-font-normal);line-height:var(--spt-leading-relaxed)}.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:flex-start;gap:var(--spt-space-2);padding:var(--spt-space-2) var(--spt-space-3);border-radius:var(--spt-radius-lg);font-weight:var(--spt-font-medium);transition:all .2s ease}.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translate(4px)}.stat-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.content-section[_ngcontent-%COMP%]{margin:0 32px 40px}.recent-scans-card[_ngcontent-%COMP%], .top-issues-card[_ngcontent-%COMP%], .quick-actions-card[_ngcontent-%COMP%]{border-radius:16px;border:none;box-shadow:0 4px 6px #0000000d,0 10px 15px #0000001a}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:24px 24px 16px;border-bottom:1px solid #e2e8f0}.section-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#667eea;font-size:24px;width:24px;height:24px}.section-title[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{margin:0;font-size:20px;font-weight:600;color:#1e293b}.secondary-button[_ngcontent-%COMP%]{border:2px solid #667eea;color:#667eea;border-radius:8px;font-weight:500;transition:all .3s ease}.secondary-button[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin:0 -24px}.scans-table[_ngcontent-%COMP%]{width:100%;background:transparent}.scans-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:#f8fafc;color:#64748b;font-weight:600;font-size:12px;text-transform:uppercase;letter-spacing:.5px;padding:16px 24px;border-bottom:1px solid #e2e8f0}.scans-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:16px 24px;border-bottom:1px solid #f1f5f9}.scan-row[_ngcontent-%COMP%]{transition:all .2s ease}.scan-row[_ngcontent-%COMP%]:hover{background:#f8fafc}.scan-id[_ngcontent-%COMP%]{font-family:Courier New,monospace;background:#f1f5f9;padding:4px 8px;border-radius:6px;font-size:12px;color:#475569}.project-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.project-icon[_ngcontent-%COMP%]{color:#64748b;font-size:18px;width:18px;height:18px}.chains-container[_ngcontent-%COMP%]{display:flex;gap:6px;flex-wrap:wrap}.chain-chip[_ngcontent-%COMP%]{background:#e0e7ff;color:#3730a3;font-size:11px;font-weight:500;height:24px;border-radius:6px}.status-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:12px;font-weight:500;height:28px;border-radius:8px;padding:0 12px}.status-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.status-completed[_ngcontent-%COMP%]{background:#dcfce7;color:#166534}.status-running[_ngcontent-%COMP%]{background:#dbeafe;color:#1d4ed8}.status-failed[_ngcontent-%COMP%]{background:#fee2e2;color:#dc2626}.status-pending[_ngcontent-%COMP%]{background:#fef3c7;color:#d97706}.issues-count[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.issues-count[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#1e293b}.issues-count[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-size:11px;color:#64748b;text-transform:uppercase;letter-spacing:.5px}.date-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.date-info[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-size:14px;color:#1e293b;font-weight:500}.date-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:12px;color:#64748b}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:4px}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:8px;color:#64748b;transition:all .2s ease}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#f1f5f9;color:#667eea}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;color:#64748b}.empty-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:#cbd5e1;margin-bottom:16px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:#475569;font-size:20px;font-weight:600}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 24px;font-size:14px}.issues-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.issue-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px;padding:15px;border:1px solid #e0e0e0;border-radius:8px}.issue-details[_ngcontent-%COMP%]{flex:1}.issue-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px;color:#333}.issue-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;color:#666;font-size:14px}.issue-details[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#999;font-size:12px}.actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px}.actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{height:60px;display:flex;flex-direction:column;gap:5px}.actions-grid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.chart-section[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-8)}.chart-section[_ngcontent-%COMP%]:last-of-type{margin-bottom:var(--spt-space-6)}@media (max-width: 768px){.chart-section[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-6)}}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}