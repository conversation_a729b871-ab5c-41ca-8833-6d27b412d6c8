{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/expansion\";\nimport * as i6 from \"@angular/material/chips\";\nimport * as i7 from \"@angular/material/table\";\nfunction CliGuideComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", feature_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r1.description);\n  }\n}\nfunction CliGuideComponent_mat_card_28_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_card_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 35)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 29)(10, \"div\", 30)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Commands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"pre\")(16, \"code\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, CliGuideComponent_mat_card_28_div_18_Template, 5, 1, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const method_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(method_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r2.description);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(method_r2.commands);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", method_r2.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Flag\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 56)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r3.flag);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 56)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r4.type);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r5.description);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Default\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"code\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 56);\n    i0.ɵɵtemplate(1, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template, 2, 1, \"code\", 57)(2, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template, 2, 0, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", option_r6.default);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !option_r6.default);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 58);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 59);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"h4\");\n    i0.ɵɵtext(2, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"table\", 46);\n    i0.ɵɵelementContainerStart(4, 47);\n    i0.ɵɵtemplate(5, CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template, 2, 0, \"th\", 48)(6, CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template, 3, 1, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 50);\n    i0.ɵɵtemplate(8, CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template, 2, 0, \"th\", 48)(9, CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template, 3, 1, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 51);\n    i0.ɵɵtemplate(11, CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template, 2, 0, \"th\", 48)(12, CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template, 2, 1, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 52);\n    i0.ɵɵtemplate(14, CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template, 2, 0, \"th\", 48)(15, CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template, 3, 2, \"td\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(16, CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template, 1, 0, \"tr\", 53)(17, CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template, 1, 0, \"tr\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dataSource\", command_r7.options);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r7.optionColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r7.optionColumns);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 30)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Output\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const example_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"p\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29)(4, \"div\", 30)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Command\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"pre\")(10, \"code\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template, 9, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const example_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(example_r9.description);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(example_r9.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", example_r9.output);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"h4\");\n    i0.ɵɵtext(2, \"Examples\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template, 13, 3, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", command_r7.examples);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"h4\");\n    i0.ɵɵtext(2, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const command_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 38)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"code\", 39);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-panel-description\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 41)(9, \"h4\");\n    i0.ɵɵtext(10, \"Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 29)(12, \"pre\")(13, \"code\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, CliGuideComponent_mat_expansion_panel_36_div_15_Template, 18, 3, \"div\", 42)(16, CliGuideComponent_mat_expansion_panel_36_div_16_Template, 4, 1, \"div\", 43)(17, CliGuideComponent_mat_expansion_panel_36_div_17_Template, 8, 1, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const command_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"spt \", command_r7.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", command_r7.description, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(command_r7.usage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.options.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.examples.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", command_r7.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"mat-icon\", 80);\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"strong\");\n    i0.ɵɵtext(5, \"Implementation Notes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const integration_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(integration_r11.notes);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_28_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"mat-icon\", 84);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r12 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r12);\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"h5\");\n    i0.ɵɵtext(2, \"Quick Tips:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 82);\n    i0.ɵɵtemplate(4, CliGuideComponent_mat_expansion_panel_45_div_28_li_4_Template, 5, 1, \"li\", 83);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.getPlatformTips(integration_r11.platform));\n  }\n}\nfunction CliGuideComponent_mat_expansion_panel_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 68)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"mat-icon\", 69);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 70)(9, \"div\", 71)(10, \"h4\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function CliGuideComponent_mat_expansion_panel_45_Template_button_click_12_listener() {\n      const integration_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.copyToClipboard(integration_r11.config));\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 73)(16, \"div\", 74)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function CliGuideComponent_mat_expansion_panel_45_Template_button_click_21_listener() {\n      const integration_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.copyToClipboard(integration_r11.config));\n    });\n    i0.ɵɵelementStart(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"content_copy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"pre\", 76)(25, \"code\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(27, CliGuideComponent_mat_expansion_panel_45_div_27_Template, 8, 1, \"div\", 77)(28, CliGuideComponent_mat_expansion_panel_45_div_28_Template, 5, 1, \"div\", 78);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const integration_r11 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"expanded\", i_r13 === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(integration_r11.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", integration_r11.platform, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", integration_r11.description, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Configuration File: \", integration_r11.filename, \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(integration_r11.filename);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(integration_r11.config);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", integration_r11.notes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.getPlatformTips(integration_r11.platform).length > 0);\n  }\n}\nfunction CliGuideComponent_mat_card_50_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tip_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tip_r14);\n  }\n}\nfunction CliGuideComponent_mat_card_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 85)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 86);\n    i0.ɵɵtemplate(10, CliGuideComponent_mat_card_50_li_10_Template, 2, 1, \"li\", 83);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const practice_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", practice_r15.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(practice_r15.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(practice_r15.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(practice_r15.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", practice_r15.tips);\n  }\n}\nfunction CliGuideComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"h4\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 73)(4, \"div\", 74)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"pre\", 76)(10, \"code\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"p\", 88);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const env_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(env_r16.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(env_r16.type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(env_r16.config);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(env_r16.description);\n  }\n}\nfunction CliGuideComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r17.key);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r17.description);\n  }\n}\nexport class CliGuideComponent {\n  constructor() {\n    this.optionColumns = ['flag', 'type', 'description', 'default'];\n    this.cliFeatures = [{\n      title: 'Security Scanning',\n      description: 'Comprehensive security analysis for blockchain applications',\n      icon: 'security',\n      color: '#1976d2'\n    }, {\n      title: 'Multiple Formats',\n      description: 'Output results in JSON, YAML, CSV, or human-readable formats',\n      icon: 'description',\n      color: '#4caf50'\n    }, {\n      title: 'CI/CD Integration',\n      description: 'Easy integration with continuous integration pipelines',\n      icon: 'integration_instructions',\n      color: '#ff9800'\n    }, {\n      title: 'Configurable',\n      description: 'Flexible configuration options for different environments',\n      icon: 'tune',\n      color: '#9c27b0'\n    }];\n    this.installationMethods = [{\n      title: 'From Source',\n      description: 'Build from source code',\n      icon: 'code',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\ngo build -o spt cmd/main.go`,\n      notes: 'Requires Go 1.21+ to be installed'\n    }, {\n      title: 'Using Make',\n      description: 'Build using Makefile',\n      icon: 'build',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\nmake cli`,\n      notes: 'Binary will be created in build/ directory'\n    }, {\n      title: 'Go Install',\n      description: 'Install directly with Go',\n      icon: 'download',\n      commands: `go install github.com/blockchain-spt/cmd/spt@latest`,\n      notes: 'Installs to $GOPATH/bin'\n    }];\n    this.cliCommands = [{\n      name: 'scan',\n      description: 'Perform security scan on files or directories',\n      usage: 'spt scan [flags] [path]',\n      options: [{\n        flag: '--chain',\n        type: 'string',\n        description: 'Blockchain chain to analyze',\n        default: 'all'\n      }, {\n        flag: '--format',\n        type: 'string',\n        description: 'Output format (json, yaml, csv, table)',\n        default: 'table'\n      }, {\n        flag: '--output',\n        type: 'string',\n        description: 'Output file path'\n      }, {\n        flag: '--severity',\n        type: 'string',\n        description: 'Minimum severity level',\n        default: 'medium'\n      }, {\n        flag: '--recursive',\n        type: 'boolean',\n        description: 'Scan directories recursively',\n        default: 'true'\n      }],\n      examples: [{\n        command: 'spt scan ./contracts',\n        description: 'Scan all files in contracts directory',\n        output: `Scanning ./contracts...\nFound 3 issues:\n  HIGH: Potential reentrancy in contract.sol:42\n  MEDIUM: Unchecked return value in token.sol:15\n  MEDIUM: Gas optimization opportunity in utils.sol:8`\n      }, {\n        command: 'spt scan --chain ethereum --format json ./src',\n        description: 'Scan for Ethereum-specific issues and output as JSON'\n      }],\n      notes: 'Use --help flag with any command to see detailed usage information'\n    }, {\n      name: 'audit',\n      description: 'Perform comprehensive security audit',\n      usage: 'spt audit [flags] [path]',\n      options: [{\n        flag: '--generate-report',\n        type: 'boolean',\n        description: 'Generate detailed report',\n        default: 'false'\n      }, {\n        flag: '--report-path',\n        type: 'string',\n        description: 'Report output path',\n        default: './audit-report.html'\n      }, {\n        flag: '--template',\n        type: 'string',\n        description: 'Report template',\n        default: 'standard'\n      }],\n      examples: [{\n        command: 'spt audit --generate-report ./project',\n        description: 'Perform audit and generate HTML report'\n      }]\n    }, {\n      name: 'check',\n      description: 'Run specific security checks',\n      usage: 'spt check [subcommand] [flags] [path]',\n      options: [{\n        flag: '--fix',\n        type: 'boolean',\n        description: 'Attempt to fix issues automatically',\n        default: 'false'\n      }],\n      examples: [{\n        command: 'spt check deps --fix',\n        description: 'Check dependencies and fix known vulnerabilities'\n      }, {\n        command: 'spt check env',\n        description: 'Check environment configuration for security issues'\n      }]\n    }];\n    this.integrationExamples = [{\n      platform: 'GitHub Actions',\n      description: 'Integrate SPT into GitHub Actions workflow',\n      icon: 'integration_instructions',\n      filename: '.github/workflows/security.yml',\n      config: `name: Security Scan\non: [push, pull_request]\n\njobs:\n  security:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - uses: actions/setup-go@v3\n        with:\n          go-version: '1.21'\n      - name: Install SPT\n        run: go install github.com/blockchain-spt/cmd/spt@latest\n      - name: Run Security Scan\n        run: spt scan --format json --output security-report.json ./\n      - name: Upload Results\n        uses: actions/upload-artifact@v3\n        with:\n          name: security-report\n          path: security-report.json`,\n      notes: 'Add GITHUB_TOKEN to secrets for private repositories'\n    }, {\n      platform: 'GitLab CI',\n      description: 'Integrate SPT into GitLab CI/CD pipeline',\n      icon: 'integration_instructions',\n      filename: '.gitlab-ci.yml',\n      config: `security_scan:\n  stage: test\n  image: golang:1.21\n  script:\n    - go install github.com/blockchain-spt/cmd/spt@latest\n    - spt scan --format json --output security-report.json ./\n    - spt audit --generate-report --report-path audit-report.html ./\n  artifacts:\n    reports:\n      junit: security-report.json\n    paths:\n      - audit-report.html\n    expire_in: 1 week\n  only:\n    - merge_requests\n    - main`,\n      notes: 'Configure artifact storage for report persistence'\n    }, {\n      platform: 'Azure DevOps',\n      description: 'Integrate SPT into Azure DevOps Pipeline',\n      icon: 'cloud',\n      filename: 'azure-pipelines.yml',\n      config: `trigger:\n  branches:\n    include:\n      - main\n      - develop\n  paths:\n    include:\n      - contracts/*\n      - src/*\n\npool:\n  vmImage: 'ubuntu-latest'\n\nvariables:\n  GO_VERSION: '1.21'\n  SPT_VERSION: 'latest'\n\nstages:\n- stage: SecurityScan\n  displayName: 'Security Analysis'\n  jobs:\n  - job: SPTScan\n    displayName: 'SPT Security Scan'\n    steps:\n    - task: GoTool@0\n      displayName: 'Install Go'\n      inputs:\n        version: '\\$(GO_VERSION)'\n\n    - script: |\n        go install github.com/blockchain-spt/cmd/spt@\\$(SPT_VERSION)\n        echo \"SPT installed successfully\"\n      displayName: 'Install SPT CLI'\n\n    - script: |\n        spt scan --format json --output \\$(Agent.TempDirectory)/security-report.json ./\n        spt audit --generate-report --report-path \\$(Agent.TempDirectory)/audit-report.html ./\n      displayName: 'Run Security Scan'\n      continueOnError: true\n\n    - task: PublishTestResults@2\n      displayName: 'Publish Security Results'\n      inputs:\n        testResultsFormat: 'JUnit'\n        testResultsFiles: '\\$(Agent.TempDirectory)/security-report.json'\n        testRunTitle: 'SPT Security Scan Results'\n      condition: always()\n\n    - task: PublishBuildArtifacts@1\n      displayName: 'Publish Security Reports'\n      inputs:\n        pathToPublish: '\\$(Agent.TempDirectory)'\n        artifactName: 'security-reports'\n        publishLocation: 'Container'\n      condition: always()\n\n    - script: |\n        if [ -f \"\\$(Agent.TempDirectory)/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' \\$(Agent.TempDirectory)/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' \\$(Agent.TempDirectory)/security-report.json)\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ] || [ \"\\$HIGH_COUNT\" -gt 5 ]; then\n            echo \"##vso[task.logissue type=error]Critical security issues found: \\$CRITICAL_COUNT critical, \\$HIGH_COUNT high\"\n            exit 1\n          fi\n        fi\n      displayName: 'Evaluate Security Results'\n      condition: always()`,\n      notes: 'Configure service connections for private repositories and adjust thresholds as needed'\n    }, {\n      platform: 'AWS CodeBuild',\n      description: 'Integrate SPT into AWS CodeBuild pipeline',\n      icon: 'cloud_queue',\n      filename: 'buildspec.yml',\n      config: `version: 0.2\n\nenv:\n  variables:\n    GO_VERSION: \"1.21\"\n    SPT_VERSION: \"latest\"\n  parameter-store:\n    GITHUB_TOKEN: \"/spt/github-token\"  # Optional for private repos\n\nphases:\n  install:\n    runtime-versions:\n      golang: \\$GO_VERSION\n    commands:\n      - echo \"Installing SPT CLI...\"\n      - go install github.com/blockchain-spt/cmd/spt@\\$SPT_VERSION\n      - spt version\n\n  pre_build:\n    commands:\n      - echo \"Preparing security scan...\"\n      - mkdir -p reports\n      - echo \"Current directory contents:\"\n      - ls -la\n\n  build:\n    commands:\n      - echo \"Running SPT security scan...\"\n      - spt scan --format json --output reports/security-report.json ./\n      - spt audit --generate-report --report-path reports/audit-report.html ./\n      - echo \"Security scan completed\"\n\n  post_build:\n    commands:\n      - echo \"Processing security results...\"\n      - |\n        if [ -f \"reports/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' reports/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' reports/security-report.json)\n          MEDIUM_COUNT=\\$(jq '.summary.medium // 0' reports/security-report.json)\n\n          echo \"Security Summary:\"\n          echo \"  Critical: \\$CRITICAL_COUNT\"\n          echo \"  High: \\$HIGH_COUNT\"\n          echo \"  Medium: \\$MEDIUM_COUNT\"\n\n          # Fail build if critical issues found\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ]; then\n            echo \"Build failed due to critical security issues\"\n            exit 1\n          fi\n\n          # Warning for high issues\n          if [ \"\\$HIGH_COUNT\" -gt 10 ]; then\n            echo \"Warning: High number of high-severity issues (\\$HIGH_COUNT)\"\n          fi\n        else\n          echo \"Security report not found\"\n          exit 1\n        fi\n\nartifacts:\n  files:\n    - 'reports/**/*'\n  name: spt-security-reports\n\nreports:\n  spt-security:\n    files:\n      - 'reports/security-report.json'\n    file-format: 'JUNITXML'`,\n      notes: 'Store sensitive tokens in AWS Parameter Store or Secrets Manager'\n    }, {\n      platform: 'AWS CodePipeline',\n      description: 'Complete AWS CodePipeline with SPT integration',\n      icon: 'account_tree',\n      filename: 'cloudformation-pipeline.yml',\n      config: `AWSTemplateFormatVersion: '2010-09-09'\nDescription: 'SPT Security Pipeline with CodePipeline'\n\nParameters:\n  GitHubRepo:\n    Type: String\n    Description: GitHub repository name\n  GitHubOwner:\n    Type: String\n    Description: GitHub repository owner\n  GitHubToken:\n    Type: String\n    NoEcho: true\n    Description: GitHub personal access token\n\nResources:\n  # S3 Bucket for artifacts\n  ArtifactsBucket:\n    Type: AWS::S3::Bucket\n    Properties:\n      BucketName: !Sub '\\${AWS::StackName}-spt-artifacts'\n      VersioningConfiguration:\n        Status: Enabled\n      PublicAccessBlockConfiguration:\n        BlockPublicAcls: true\n        BlockPublicPolicy: true\n        IgnorePublicAcls: true\n        RestrictPublicBuckets: true\n\n  # CodeBuild Project for SPT Security Scan\n  SPTSecurityProject:\n    Type: AWS::CodeBuild::Project\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-security'\n      ServiceRole: !GetAtt CodeBuildRole.Arn\n      Artifacts:\n        Type: CODEPIPELINE\n      Environment:\n        Type: LINUX_CONTAINER\n        ComputeType: BUILD_GENERAL1_MEDIUM\n        Image: aws/codebuild/amazonlinux2-x86_64-standard:3.0\n        EnvironmentVariables:\n          - Name: GITHUB_TOKEN\n            Value: !Ref GitHubToken\n            Type: PARAMETER_STORE\n      Source:\n        Type: CODEPIPELINE\n        BuildSpec: |\n          version: 0.2\n          phases:\n            install:\n              runtime-versions:\n                golang: 1.21\n              commands:\n                - go install github.com/blockchain-spt/cmd/spt@latest\n            build:\n              commands:\n                - mkdir -p reports\n                - spt scan --format json --output reports/security-report.json ./\n                - spt audit --generate-report --report-path reports/audit-report.html ./\n          artifacts:\n            files:\n              - 'reports/**/*'\n\n  # CodePipeline\n  SPTPipeline:\n    Type: AWS::CodePipeline::Pipeline\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-pipeline'\n      RoleArn: !GetAtt CodePipelineRole.Arn\n      ArtifactStore:\n        Type: S3\n        Location: !Ref ArtifactsBucket\n      Stages:\n        - Name: Source\n          Actions:\n            - Name: SourceAction\n              ActionTypeId:\n                Category: Source\n                Owner: ThirdParty\n                Provider: GitHub\n                Version: '1'\n              Configuration:\n                Owner: !Ref GitHubOwner\n                Repo: !Ref GitHubRepo\n                Branch: main\n                OAuthToken: !Ref GitHubToken\n              OutputArtifacts:\n                - Name: SourceOutput\n\n        - Name: SecurityScan\n          Actions:\n            - Name: SPTScan\n              ActionTypeId:\n                Category: Build\n                Owner: AWS\n                Provider: CodeBuild\n                Version: '1'\n              Configuration:\n                ProjectName: !Ref SPTSecurityProject\n              InputArtifacts:\n                - Name: SourceOutput\n              OutputArtifacts:\n                - Name: SecurityOutput\n\n  # IAM Roles (simplified - add specific permissions as needed)\n  CodeBuildRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codebuild.amazonaws.com\n            Action: sts:AssumeRole\n      ManagedPolicyArns:\n        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess\n      Policies:\n        - PolicyName: S3Access\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                Resource: !Sub '\\${ArtifactsBucket}/*'\n\n  CodePipelineRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codepipeline.amazonaws.com\n            Action: sts:AssumeRole\n      Policies:\n        - PolicyName: PipelinePolicy\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                  - s3:GetBucketVersioning\n                Resource:\n                  - !Sub '\\${ArtifactsBucket}'\n                  - !Sub '\\${ArtifactsBucket}/*'\n              - Effect: Allow\n                Action:\n                  - codebuild:BatchGetBuilds\n                  - codebuild:StartBuild\n                Resource: !GetAtt SPTSecurityProject.Arn`,\n      notes: 'Deploy using AWS CloudFormation. Customize IAM permissions based on your security requirements.'\n    }, {\n      platform: 'Docker',\n      description: 'Containerized SPT for consistent CI/CD environments',\n      icon: 'developer_board',\n      filename: 'Dockerfile',\n      config: `# Multi-stage Dockerfile for SPT CLI\nFROM golang:1.21-alpine AS builder\n\n# Install dependencies\nRUN apk add --no-cache git ca-certificates\n\n# Set working directory\nWORKDIR /app\n\n# Install SPT CLI\nRUN go install github.com/blockchain-spt/cmd/spt@latest\n\n# Create final image\nFROM alpine:latest\n\n# Install runtime dependencies\nRUN apk add --no-cache ca-certificates jq curl\n\n# Copy SPT binary from builder\nCOPY --from=builder /go/bin/spt /usr/local/bin/spt\n\n# Create non-root user\nRUN addgroup -g 1001 spt && \\\\\n    adduser -D -u 1001 -G spt spt\n\n# Set working directory\nWORKDIR /workspace\n\n# Change ownership\nRUN chown -R spt:spt /workspace\n\n# Switch to non-root user\nUSER spt\n\n# Set entrypoint\nENTRYPOINT [\"spt\"]\nCMD [\"--help\"]\n\n# Usage examples:\n# docker build -t spt-cli .\n# docker run --rm -v \\$(pwd):/workspace spt-cli scan ./\n# docker run --rm -v \\$(pwd):/workspace spt-cli audit --generate-report ./`,\n      notes: 'Use this Docker image in any CI/CD system that supports containers'\n    }, {\n      platform: 'Jenkins',\n      description: 'Jenkins Pipeline with SPT integration',\n      icon: 'build',\n      filename: 'Jenkinsfile',\n      config: `pipeline {\n    agent any\n\n    environment {\n        GO_VERSION = '1.21'\n        SPT_VERSION = 'latest'\n        REPORTS_DIR = 'reports'\n    }\n\n    tools {\n        go 'go-1.21'  // Configure in Jenkins Global Tools\n    }\n\n    stages {\n        stage('Checkout') {\n            steps {\n                checkout scm\n                script {\n                    env.GIT_COMMIT_SHORT = sh(\n                        script: 'git rev-parse --short HEAD',\n                        returnStdout: true\n                    ).trim()\n                }\n            }\n        }\n\n        stage('Install SPT') {\n            steps {\n                sh '''\n                    echo \"Installing SPT CLI...\"\n                    go install github.com/blockchain-spt/cmd/spt@\\${SPT_VERSION}\n                    spt version\n                '''\n            }\n        }\n\n        stage('Security Scan') {\n            steps {\n                sh '''\n                    echo \"Creating reports directory...\"\n                    mkdir -p \\${REPORTS_DIR}\n\n                    echo \"Running SPT security scan...\"\n                    spt scan --format json --output \\${REPORTS_DIR}/security-report.json ./\n\n                    echo \"Generating audit report...\"\n                    spt audit --generate-report --report-path \\${REPORTS_DIR}/audit-report.html ./\n\n                    echo \"Security scan completed\"\n                '''\n            }\n            post {\n                always {\n                    // Archive artifacts\n                    archiveArtifacts artifacts: '\\${REPORTS_DIR}/**/*', fingerprint: true\n\n                    // Publish HTML reports\n                    publishHTML([\n                        allowMissing: false,\n                        alwaysLinkToLastBuild: true,\n                        keepAll: true,\n                        reportDir: '\\${REPORTS_DIR}',\n                        reportFiles: 'audit-report.html',\n                        reportName: 'SPT Security Report'\n                    ])\n                }\n            }\n        }\n\n        stage('Evaluate Results') {\n            steps {\n                script {\n                    if (fileExists(\"\\${REPORTS_DIR}/security-report.json\")) {\n                        def report = readJSON file: \"\\${REPORTS_DIR}/security-report.json\"\n                        def critical = report.summary?.critical ?: 0\n                        def high = report.summary?.high ?: 0\n                        def medium = report.summary?.medium ?: 0\n\n                        echo \"Security Summary:\"\n                        echo \"  Critical: \\${critical}\"\n                        echo \"  High: \\${high}\"\n                        echo \"  Medium: \\${medium}\"\n\n                        // Set build status based on results\n                        if (critical > 0) {\n                            currentBuild.result = 'FAILURE'\n                            error(\"Build failed due to \\${critical} critical security issues\")\n                        } else if (high > 10) {\n                            currentBuild.result = 'UNSTABLE'\n                            echo \"Build marked unstable due to \\${high} high-severity issues\"\n                        }\n\n                        // Add build description\n                        currentBuild.description = \"Critical: \\${critical}, High: \\${high}, Medium: \\${medium}\"\n                    } else {\n                        currentBuild.result = 'FAILURE'\n                        error(\"Security report not found\")\n                    }\n                }\n            }\n        }\n    }\n\n    post {\n        always {\n            // Clean workspace\n            cleanWs()\n        }\n        failure {\n            // Send notifications on failure\n            emailext (\n                subject: \"SPT Security Scan Failed: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan failed for commit \\${env.GIT_COMMIT_SHORT}. Check the build logs for details.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n        unstable {\n            // Send notifications on unstable builds\n            emailext (\n                subject: \"SPT Security Scan Unstable: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan completed with warnings for commit \\${env.GIT_COMMIT_SHORT}. Review the security report.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n    }\n}`,\n      notes: 'Configure Go tools and email notifications in Jenkins. Install required plugins: Pipeline, HTML Publisher, Email Extension.'\n    }];\n    this.cicdBestPractices = [{\n      title: 'Fail Fast Strategy',\n      description: 'Configure your pipeline to fail immediately on critical security issues to prevent vulnerable code from progressing.',\n      icon: 'error',\n      color: '#f44336',\n      tips: ['Set critical severity threshold to 0', 'Use exit codes to stop pipeline execution', 'Implement security gates at multiple stages', 'Configure notifications for security failures']\n    }, {\n      title: 'Artifact Management',\n      description: 'Properly store and manage security reports and artifacts for compliance and tracking.',\n      icon: 'archive',\n      color: '#2196f3',\n      tips: ['Archive security reports for audit trails', 'Use versioned artifact storage', 'Implement retention policies', 'Enable artifact encryption for sensitive data']\n    }, {\n      title: 'Parallel Execution',\n      description: 'Optimize scan performance by running security checks in parallel with other tests.',\n      icon: 'speed',\n      color: '#4caf50',\n      tips: ['Run security scans parallel to unit tests', 'Use matrix builds for multiple environments', 'Implement caching for faster scans', 'Configure resource limits appropriately']\n    }, {\n      title: 'Security Thresholds',\n      description: 'Define appropriate security thresholds based on your project maturity and risk tolerance.',\n      icon: 'tune',\n      color: '#ff9800',\n      tips: ['Start with strict thresholds for new projects', 'Gradually improve legacy project thresholds', 'Use different thresholds for different branches', 'Document threshold decisions and rationale']\n    }, {\n      title: 'Integration Testing',\n      description: 'Test your CI/CD integration thoroughly before deploying to production pipelines.',\n      icon: 'integration_instructions',\n      color: '#9c27b0',\n      tips: ['Test with sample vulnerable code', 'Verify artifact generation and storage', 'Test notification mechanisms', 'Validate security gate functionality']\n    }, {\n      title: 'Monitoring & Alerting',\n      description: 'Implement comprehensive monitoring and alerting for your security pipeline.',\n      icon: 'monitoring',\n      color: '#607d8b',\n      tips: ['Monitor pipeline execution times', 'Set up alerts for scan failures', 'Track security metrics over time', 'Implement dashboard for security trends']\n    }];\n    this.environmentConfigs = [{\n      name: 'Development Environment',\n      type: 'Environment Variables',\n      config: `# Development - More verbose, all severities\nexport SPT_SEVERITY_THRESHOLD=low\nexport SPT_OUTPUT_FORMAT=table\nexport SPT_COLORS=true\nexport SPT_VERBOSE=true\nexport SPT_PARALLEL_SCANS=2\nexport SPT_TIMEOUT=10m\nexport SPT_CACHE_ENABLED=true\nexport SPT_CACHE_DIR=~/.spt/cache`,\n      description: 'Development environment with verbose output and lower thresholds for learning and debugging.'\n    }, {\n      name: 'Staging Environment',\n      type: 'Environment Variables',\n      config: `# Staging - Production-like with medium threshold\nexport SPT_SEVERITY_THRESHOLD=medium\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=4\nexport SPT_TIMEOUT=15m\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_GENERATE_REPORTS=true`,\n      description: 'Staging environment that mirrors production settings with moderate security requirements.'\n    }, {\n      name: 'Production Environment',\n      type: 'Environment Variables',\n      config: `# Production - Strict security requirements\nexport SPT_SEVERITY_THRESHOLD=high\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=8\nexport SPT_TIMEOUT=30m\nexport SPT_FAIL_ON_CRITICAL=true\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_AUDIT_ENABLED=true\nexport SPT_COMPLIANCE_MODE=true`,\n      description: 'Production environment with strict security requirements and comprehensive auditing.'\n    }, {\n      name: 'Docker Configuration',\n      type: 'Docker Environment',\n      config: `# Docker container environment variables\nENV SPT_SEVERITY_THRESHOLD=medium\nENV SPT_OUTPUT_FORMAT=json\nENV SPT_PARALLEL_SCANS=4\nENV SPT_TIMEOUT=20m\nENV SPT_CACHE_ENABLED=false\nENV SPT_WORKSPACE=/workspace\nENV SPT_REPORTS_DIR=/reports\n\n# Volume mounts\n# docker run -v \\$(pwd):/workspace -v \\$(pwd)/reports:/reports spt-cli`,\n      description: 'Containerized environment configuration for consistent cross-platform execution.'\n    }, {\n      name: 'Cloud-Native Configuration',\n      type: 'Kubernetes ConfigMap',\n      config: `apiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: spt-config\n  namespace: security\ndata:\n  SPT_SEVERITY_THRESHOLD: \"medium\"\n  SPT_OUTPUT_FORMAT: \"json\"\n  SPT_PARALLEL_SCANS: \"6\"\n  SPT_TIMEOUT: \"25m\"\n  SPT_CLOUD_STORAGE: \"true\"\n  SPT_METRICS_ENABLED: \"true\"\n  SPT_DISTRIBUTED_SCAN: \"true\"\n---\napiVersion: v1\nkind: Secret\nmetadata:\n  name: spt-secrets\n  namespace: security\ntype: Opaque\nstringData:\n  github-token: \"your-github-token\"\n  api-key: \"your-api-key\"`,\n      description: 'Kubernetes-native configuration using ConfigMaps and Secrets for cloud deployments.'\n    }];\n    this.configExample = `{\n  \"scanning\": {\n    \"chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n    \"severity_threshold\": \"medium\",\n    \"max_file_size\": \"10MB\",\n    \"timeout\": \"5m\",\n    \"parallel_scans\": 4\n  },\n  \"output\": {\n    \"format\": \"table\",\n    \"colors\": true,\n    \"verbose\": false\n  },\n  \"rules\": {\n    \"ethereum\": {\n      \"check_reentrancy\": true,\n      \"check_overflow\": true,\n      \"check_access_control\": true\n    },\n    \"bitcoin\": {\n      \"check_key_management\": true,\n      \"check_transaction_validation\": true\n    }\n  },\n  \"integrations\": {\n    \"vscode\": {\n      \"enabled\": true,\n      \"server_url\": \"http://localhost:8080\"\n    }\n  }\n}`;\n    this.configOptions = [{\n      key: 'scanning.chains',\n      description: 'Array of blockchain chains to analyze'\n    }, {\n      key: 'scanning.severity_threshold',\n      description: 'Minimum severity level to report'\n    }, {\n      key: 'output.format',\n      description: 'Default output format for scan results'\n    }, {\n      key: 'rules.ethereum',\n      description: 'Ethereum-specific security rules configuration'\n    }, {\n      key: 'rules.bitcoin',\n      description: 'Bitcoin-specific security rules configuration'\n    }, {\n      key: 'integrations',\n      description: 'Configuration for IDE and tool integrations'\n    }];\n    this.platformTips = {\n      'GitHub Actions': ['Use GitHub Secrets for sensitive tokens', 'Enable branch protection rules with status checks', 'Configure matrix builds for multiple Go versions', 'Use actions/cache for faster builds'],\n      'GitLab CI': ['Use GitLab CI/CD variables for configuration', 'Configure merge request pipelines', 'Use GitLab Container Registry for custom images', 'Enable pipeline schedules for regular scans'],\n      'Azure DevOps': ['Store secrets in Azure Key Vault', 'Use variable groups for environment-specific configs', 'Configure branch policies with build validation', 'Enable Azure Artifacts for report storage'],\n      'AWS CodeBuild': ['Use Parameter Store for secure configuration', 'Configure VPC settings for private repositories', 'Use CloudWatch for monitoring and alerting', 'Enable S3 artifact encryption'],\n      'AWS CodePipeline': ['Use CloudFormation for infrastructure as code', 'Configure cross-region artifact replication', 'Implement approval gates for production', 'Use EventBridge for pipeline notifications'],\n      'Docker': ['Use multi-stage builds for smaller images', 'Run containers as non-root user', 'Mount volumes for persistent reports', 'Use health checks for container monitoring'],\n      'Jenkins': ['Use Jenkins Credentials for secure storage', 'Configure build triggers with webhooks', 'Use Pipeline as Code with Jenkinsfile', 'Enable Blue Ocean for better UI']\n    };\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      // Could add a snackbar notification here\n      console.log('Configuration copied to clipboard');\n    }).catch(err => {\n      console.error('Failed to copy: ', err);\n    });\n  }\n  getPlatformTips(platform) {\n    return this.platformTips[platform] || [];\n  }\n  static {\n    this.ɵfac = function CliGuideComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CliGuideComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CliGuideComponent,\n      selectors: [[\"app-cli-guide\"]],\n      decls: 94,\n      vars: 8,\n      consts: [[1, \"cli-guide-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [1, \"cli-overview\"], [1, \"overview-card\"], [\"mat-card-avatar\", \"\"], [1, \"cli-features\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [\"animationDuration\", \"300ms\", 1, \"cli-tabs\"], [\"label\", \"Installation\"], [1, \"tab-content\"], [1, \"installation-methods\"], [\"class\", \"method-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Commands\"], [1, \"commands-list\"], [\"class\", \"command-panel\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"CI/CD Integration\"], [1, \"integration-examples\"], [1, \"integration-accordion\"], [\"class\", \"integration-panel\", 3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"best-practices-section\"], [1, \"practices-grid\"], [\"class\", \"practice-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"environment-configs\"], [1, \"env-config-card\"], [1, \"env-examples\"], [\"class\", \"env-example\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-card\"], [1, \"code-block\"], [1, \"code-header\"], [1, \"config-description\"], [1, \"config-options\"], [\"class\", \"config-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature\"], [1, \"method-card\"], [\"class\", \"method-notes\", 4, \"ngIf\"], [1, \"method-notes\"], [1, \"command-panel\"], [1, \"command-name\"], [1, \"command-details\"], [1, \"usage-section\"], [\"class\", \"options-section\", 4, \"ngIf\"], [\"class\", \"examples-section\", 4, \"ngIf\"], [\"class\", \"notes-section\", 4, \"ngIf\"], [1, \"options-section\"], [\"mat-table\", \"\", 1, \"options-table\", 3, \"dataSource\"], [\"matColumnDef\", \"flag\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"default\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [4, \"ngIf\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"examples-section\"], [\"class\", \"example\", 4, \"ngFor\", \"ngForOf\"], [1, \"example\"], [1, \"example-description\"], [\"class\", \"output-block\", 4, \"ngIf\"], [1, \"output-block\"], [1, \"notes-section\"], [1, \"notes-content\"], [1, \"integration-panel\", 3, \"expanded\"], [1, \"platform-icon\"], [1, \"integration-content\"], [1, \"integration-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy configuration\", 1, \"copy-config-btn\", 3, \"click\"], [1, \"spt-code-block\"], [1, \"spt-code-header\"], [\"mat-icon-button\", \"\", 1, \"spt-copy-btn\", 3, \"click\"], [1, \"spt-code-content\"], [\"class\", \"integration-notes\", 4, \"ngIf\"], [\"class\", \"platform-tips\", 4, \"ngIf\"], [1, \"integration-notes\"], [1, \"notes-icon\"], [1, \"platform-tips\"], [1, \"tips-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"tip-icon\"], [1, \"practice-card\"], [1, \"practice-tips\"], [1, \"env-example\"], [1, \"env-description\"], [1, \"config-option\"]],\n      template: function CliGuideComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"terminal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" CLI Guide \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 2);\n          i0.ɵɵtext(7, \" Complete guide to the SPT command-line interface \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-header\")(11, \"mat-icon\", 5);\n          i0.ɵɵtext(12, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-card-title\");\n          i0.ɵɵtext(14, \"SPT CLI Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"mat-card-subtitle\");\n          i0.ɵɵtext(16, \"Powerful command-line security scanning tool\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"mat-card-content\")(18, \"p\");\n          i0.ɵɵtext(19, \"The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 6);\n          i0.ɵɵtemplate(21, CliGuideComponent_div_21_Template, 8, 5, \"div\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"mat-tab-group\", 8)(23, \"mat-tab\", 9)(24, \"div\", 10)(25, \"h2\");\n          i0.ɵɵtext(26, \"Installation Methods\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 11);\n          i0.ɵɵtemplate(28, CliGuideComponent_mat_card_28_Template, 19, 5, \"mat-card\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"mat-tab\", 13)(30, \"div\", 10)(31, \"h2\");\n          i0.ɵɵtext(32, \"Available Commands\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\");\n          i0.ɵɵtext(34, \"Complete reference for all SPT CLI commands and their options.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 14);\n          i0.ɵɵtemplate(36, CliGuideComponent_mat_expansion_panel_36_Template, 18, 6, \"mat-expansion-panel\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"mat-tab\", 16)(38, \"div\", 10)(39, \"h2\");\n          i0.ɵɵtext(40, \"CI/CD Integration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"p\");\n          i0.ɵɵtext(42, \"Integrate SPT CLI into your continuous integration and deployment pipelines.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 17)(44, \"mat-accordion\", 18);\n          i0.ɵɵtemplate(45, CliGuideComponent_mat_expansion_panel_45_Template, 29, 9, \"mat-expansion-panel\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 20)(47, \"h3\");\n          i0.ɵɵtext(48, \"CI/CD Best Practices\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 21);\n          i0.ɵɵtemplate(50, CliGuideComponent_mat_card_50_Template, 11, 6, \"mat-card\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 23)(52, \"h3\");\n          i0.ɵɵtext(53, \"Environment-Specific Configurations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-card\", 24)(55, \"mat-card-header\")(56, \"mat-icon\", 5);\n          i0.ɵɵtext(57, \"settings_applications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"mat-card-title\");\n          i0.ɵɵtext(59, \"Environment Variables\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"mat-card-subtitle\");\n          i0.ɵɵtext(61, \"Configure SPT for different environments\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"mat-card-content\")(63, \"div\", 25);\n          i0.ɵɵtemplate(64, CliGuideComponent_div_64_Template, 14, 4, \"div\", 26);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(65, \"mat-tab\", 27)(66, \"div\", 10)(67, \"h2\");\n          i0.ɵɵtext(68, \"Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\");\n          i0.ɵɵtext(70, \"Configure SPT CLI for your development environment and preferences.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"mat-card\", 28)(72, \"mat-card-header\")(73, \"mat-icon\", 5);\n          i0.ɵɵtext(74, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"mat-card-title\");\n          i0.ɵɵtext(76, \"Configuration File\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"mat-card-subtitle\");\n          i0.ɵɵtext(78, \"spt.config.json\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"mat-card-content\")(80, \"div\", 29)(81, \"div\", 30)(82, \"mat-icon\");\n          i0.ɵɵtext(83, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"span\");\n          i0.ɵɵtext(85, \"JSON Configuration\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"pre\")(87, \"code\");\n          i0.ɵɵtext(88);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(89, \"div\", 31)(90, \"h4\");\n          i0.ɵɵtext(91, \"Configuration Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"div\", 32);\n          i0.ɵɵtemplate(93, CliGuideComponent_div_93_Template, 5, 2, \"div\", 33);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cliFeatures);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.installationMethods);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cliCommands);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.integrationExamples);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cicdBestPractices);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngForOf\", ctx.environmentConfigs);\n          i0.ɵɵadvance(24);\n          i0.ɵɵtextInterpolate(ctx.configExample);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.configOptions);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatExpansionModule, i5.MatAccordion, i5.MatExpansionPanel, i5.MatExpansionPanelHeader, i5.MatExpansionPanelTitle, i5.MatExpansionPanelDescription, MatChipsModule, i6.MatChip, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow],\n      styles: [\".cli-guide-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  color: #1976d2;\\n  margin: 0 0 8px 0;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1em;\\n  margin: 0;\\n}\\n\\n.cli-overview[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.overview-card[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.cli-features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-top: 16px;\\n}\\n\\n.feature[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n}\\n\\n.feature[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n}\\n\\n.feature[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n\\n.feature[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n.cli-tabs[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 24px 0;\\n}\\n\\n.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  margin-bottom: 8px;\\n}\\n\\n.installation-methods[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 24px;\\n  margin-top: 24px;\\n}\\n\\n.method-card[_ngcontent-%COMP%] {\\n  height: fit-content;\\n}\\n\\n.method-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 12px;\\n  padding: 8px 12px;\\n  background: #e3f2fd;\\n  border-radius: 4px;\\n  color: #1976d2;\\n}\\n\\n.commands-list[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.command-panel[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.command-name[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: 500;\\n}\\n\\n.command-details[_ngcontent-%COMP%] {\\n  padding: 16px 0;\\n}\\n\\n.usage-section[_ngcontent-%COMP%], \\n.options-section[_ngcontent-%COMP%], \\n.examples-section[_ngcontent-%COMP%], \\n.notes-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.usage-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.options-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.examples-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.notes-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #1976d2;\\n}\\n\\n.code-block[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-bottom: 16px;\\n}\\n\\n.code-header[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 8px 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px;\\n  background: #fafafa;\\n  overflow-x: auto;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.9em;\\n}\\n\\n.output-block[_ngcontent-%COMP%] {\\n  border: 1px solid #4caf50;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-top: 8px;\\n}\\n\\n.output-block[_ngcontent-%COMP%]   .code-header[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  border-bottom-color: #4caf50;\\n}\\n\\n.output-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n}\\n\\n.options-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.options-table[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 0.9em;\\n}\\n\\n.example[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  padding: 16px;\\n  background: #f9f9f9;\\n  border-radius: 8px;\\n}\\n\\n.example-description[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-weight: 500;\\n  color: #1976d2;\\n}\\n\\n.notes-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  padding: 12px;\\n  background: #e3f2fd;\\n  border-radius: 8px;\\n  color: #1976d2;\\n}\\n\\n.notes-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.integration-examples[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\\n  gap: 24px;\\n  margin-top: 24px;\\n}\\n\\n.integration-card[_ngcontent-%COMP%] {\\n  height: fit-content;\\n}\\n\\n.integration-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 12px;\\n  padding: 8px 12px;\\n  background: #fff3e0;\\n  border-radius: 4px;\\n  color: #f57c00;\\n}\\n\\n.config-card[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.config-description[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.config-description[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #1976d2;\\n}\\n\\n.config-options[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n}\\n\\n.config-option[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n}\\n\\n.config-option[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n  color: #1976d2;\\n}\\n\\n.config-option[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n@media (max-width: 768px) {\\n  .cli-features[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .installation-methods[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .integration-examples[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .config-options[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatTabsModule", "MatCardModule", "MatIconModule", "MatExpansionModule", "MatChipsModule", "MatTableModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "feature_r1", "color", "ɵɵtextInterpolate", "icon", "title", "description", "method_r2", "notes", "ɵɵtemplate", "CliGuideComponent_mat_card_28_div_18_Template", "commands", "ɵɵproperty", "option_r3", "flag", "option_r4", "type", "option_r5", "option_r6", "default", "CliGuideComponent_mat_expansion_panel_36_div_15_td_15_code_1_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_15_span_2_Template", "ɵɵelement", "ɵɵelementContainerStart", "CliGuideComponent_mat_expansion_panel_36_div_15_th_5_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_6_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_th_8_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_9_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_th_11_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_12_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_th_14_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_td_15_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_tr_16_Template", "CliGuideComponent_mat_expansion_panel_36_div_15_tr_17_Template", "command_r7", "options", "ctx_r7", "optionColumns", "example_r9", "output", "CliGuideComponent_mat_expansion_panel_36_div_16_div_3_div_12_Template", "command", "CliGuideComponent_mat_expansion_panel_36_div_16_div_3_Template", "examples", "CliGuideComponent_mat_expansion_panel_36_div_15_Template", "CliGuideComponent_mat_expansion_panel_36_div_16_Template", "CliGuideComponent_mat_expansion_panel_36_div_17_Template", "ɵɵtextInterpolate1", "name", "usage", "length", "integration_r11", "tip_r12", "CliGuideComponent_mat_expansion_panel_45_div_28_li_4_Template", "getPlatformTips", "platform", "ɵɵlistener", "CliGuideComponent_mat_expansion_panel_45_Template_button_click_12_listener", "ɵɵrestoreView", "_r10", "$implicit", "ɵɵnextContext", "ɵɵresetView", "copyToClipboard", "config", "CliGuideComponent_mat_expansion_panel_45_Template_button_click_21_listener", "CliGuideComponent_mat_expansion_panel_45_div_27_Template", "CliGuideComponent_mat_expansion_panel_45_div_28_Template", "i_r13", "filename", "tip_r14", "CliGuideComponent_mat_card_50_li_10_Template", "practice_r15", "tips", "env_r16", "option_r17", "key", "CliGuideComponent", "constructor", "cliFeatures", "installationMethods", "cliCommands", "integrationExamples", "cicdBestPractices", "environmentConfigs", "config<PERSON><PERSON><PERSON>", "configOptions", "platformTips", "text", "navigator", "clipboard", "writeText", "then", "console", "log", "catch", "err", "error", "selectors", "decls", "vars", "consts", "template", "CliGuideComponent_Template", "rf", "ctx", "CliGuideComponent_div_21_Template", "CliGuideComponent_mat_card_28_Template", "CliGuideComponent_mat_expansion_panel_36_Template", "CliGuideComponent_mat_expansion_panel_45_Template", "CliGuideComponent_mat_card_50_Template", "CliGuideComponent_div_64_Template", "CliGuideComponent_div_93_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "Mat<PERSON><PERSON>", "MatTabGroup", "i3", "MatCard", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i4", "MatIcon", "i5", "Mat<PERSON><PERSON>rdi<PERSON>", "MatExpansionPanel", "MatExpansionPanelHeader", "MatExpansionPanelTitle", "MatExpansionPanelDescription", "i6", "MatChip", "i7", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\cli-guide\\cli-guide.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTableModule } from '@angular/material/table';\n\ninterface CliCommand {\n  name: string;\n  description: string;\n  usage: string;\n  options: CliOption[];\n  examples: CliExample[];\n  notes?: string;\n}\n\ninterface CliOption {\n  flag: string;\n  description: string;\n  type: string;\n  default?: string;\n  required?: boolean;\n}\n\ninterface CliExample {\n  command: string;\n  description: string;\n  output?: string;\n}\n\n@Component({\n  selector: 'app-cli-guide',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatTabsModule,\n    MatCardModule,\n    MatIconModule,\n    MatExpansionModule,\n    MatChipsModule,\n    MatTableModule\n  ],\n  template: `\n    <div class=\"cli-guide-container\">\n      <div class=\"page-header\">\n        <h1>\n          <mat-icon>terminal</mat-icon>\n          CLI Guide\n        </h1>\n        <p class=\"page-subtitle\">\n          Complete guide to the SPT command-line interface\n        </p>\n      </div>\n\n      <div class=\"cli-overview\">\n        <mat-card class=\"overview-card\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>info</mat-icon>\n            <mat-card-title>SPT CLI Overview</mat-card-title>\n            <mat-card-subtitle>Powerful command-line security scanning tool</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <p>The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.</p>\n            <div class=\"cli-features\">\n              <div class=\"feature\" *ngFor=\"let feature of cliFeatures\">\n                <mat-icon [style.color]=\"feature.color\">{{ feature.icon }}</mat-icon>\n                <div>\n                  <strong>{{ feature.title }}</strong>\n                  <p>{{ feature.description }}</p>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <mat-tab-group class=\"cli-tabs\" animationDuration=\"300ms\">\n        <!-- Installation Tab -->\n        <mat-tab label=\"Installation\">\n          <div class=\"tab-content\">\n            <h2>Installation Methods</h2>\n            \n            <div class=\"installation-methods\">\n              <mat-card class=\"method-card\" *ngFor=\"let method of installationMethods\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>{{ method.icon }}</mat-icon>\n                  <mat-card-title>{{ method.title }}</mat-card-title>\n                  <mat-card-subtitle>{{ method.description }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>terminal</mat-icon>\n                      <span>Commands</span>\n                    </div>\n                    <pre><code>{{ method.commands }}</code></pre>\n                  </div>\n                  <div class=\"method-notes\" *ngIf=\"method.notes\">\n                    <mat-icon>info</mat-icon>\n                    <span>{{ method.notes }}</span>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Commands Tab -->\n        <mat-tab label=\"Commands\">\n          <div class=\"tab-content\">\n            <h2>Available Commands</h2>\n            <p>Complete reference for all SPT CLI commands and their options.</p>\n            \n            <div class=\"commands-list\">\n              <mat-expansion-panel \n                *ngFor=\"let command of cliCommands\" \n                class=\"command-panel\">\n                <mat-expansion-panel-header>\n                  <mat-panel-title>\n                    <code class=\"command-name\">spt {{ command.name }}</code>\n                  </mat-panel-title>\n                  <mat-panel-description>\n                    {{ command.description }}\n                  </mat-panel-description>\n                </mat-expansion-panel-header>\n\n                <div class=\"command-details\">\n                  <div class=\"usage-section\">\n                    <h4>Usage</h4>\n                    <div class=\"code-block\">\n                      <pre><code>{{ command.usage }}</code></pre>\n                    </div>\n                  </div>\n\n                  <div class=\"options-section\" *ngIf=\"command.options.length > 0\">\n                    <h4>Options</h4>\n                    <table mat-table [dataSource]=\"command.options\" class=\"options-table\">\n                      <ng-container matColumnDef=\"flag\">\n                        <th mat-header-cell *matHeaderCellDef>Flag</th>\n                        <td mat-cell *matCellDef=\"let option\">\n                          <code>{{ option.flag }}</code>\n                        </td>\n                      </ng-container>\n                      <ng-container matColumnDef=\"type\">\n                        <th mat-header-cell *matHeaderCellDef>Type</th>\n                        <td mat-cell *matCellDef=\"let option\">\n                          <mat-chip>{{ option.type }}</mat-chip>\n                        </td>\n                      </ng-container>\n                      <ng-container matColumnDef=\"description\">\n                        <th mat-header-cell *matHeaderCellDef>Description</th>\n                        <td mat-cell *matCellDef=\"let option\">{{ option.description }}</td>\n                      </ng-container>\n                      <ng-container matColumnDef=\"default\">\n                        <th mat-header-cell *matHeaderCellDef>Default</th>\n                        <td mat-cell *matCellDef=\"let option\">\n                          <code *ngIf=\"option.default\">{{ option.default }}</code>\n                          <span *ngIf=\"!option.default\">-</span>\n                        </td>\n                      </ng-container>\n                      <tr mat-header-row *matHeaderRowDef=\"optionColumns\"></tr>\n                      <tr mat-row *matRowDef=\"let row; columns: optionColumns;\"></tr>\n                    </table>\n                  </div>\n\n                  <div class=\"examples-section\" *ngIf=\"command.examples.length > 0\">\n                    <h4>Examples</h4>\n                    <div class=\"example\" *ngFor=\"let example of command.examples\">\n                      <p class=\"example-description\">{{ example.description }}</p>\n                      <div class=\"code-block\">\n                        <div class=\"code-header\">\n                          <mat-icon>terminal</mat-icon>\n                          <span>Command</span>\n                        </div>\n                        <pre><code>{{ example.command }}</code></pre>\n                      </div>\n                      <div class=\"output-block\" *ngIf=\"example.output\">\n                        <div class=\"code-header\">\n                          <mat-icon>output</mat-icon>\n                          <span>Output</span>\n                        </div>\n                        <pre><code>{{ example.output }}</code></pre>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div class=\"notes-section\" *ngIf=\"command.notes\">\n                    <h4>Notes</h4>\n                    <div class=\"notes-content\">\n                      <mat-icon>info</mat-icon>\n                      <p>{{ command.notes }}</p>\n                    </div>\n                  </div>\n                </div>\n              </mat-expansion-panel>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Integration Tab -->\n        <mat-tab label=\"CI/CD Integration\">\n          <div class=\"tab-content\">\n            <h2>CI/CD Integration</h2>\n            <p>Integrate SPT CLI into your continuous integration and deployment pipelines.</p>\n            \n            <div class=\"integration-examples\">\n              <mat-accordion class=\"integration-accordion\">\n                <mat-expansion-panel\n                  *ngFor=\"let integration of integrationExamples; let i = index\"\n                  class=\"integration-panel\"\n                  [expanded]=\"i === 0\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <mat-icon class=\"platform-icon\">{{ integration.icon }}</mat-icon>\n                      {{ integration.platform }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      {{ integration.description }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n\n                  <div class=\"integration-content\">\n                    <div class=\"integration-header\">\n                      <h4>Configuration File: {{ integration.filename }}</h4>\n                      <button mat-icon-button class=\"copy-config-btn\"\n                              (click)=\"copyToClipboard(integration.config)\"\n                              matTooltip=\"Copy configuration\">\n                        <mat-icon>content_copy</mat-icon>\n                      </button>\n                    </div>\n\n                    <div class=\"spt-code-block\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>code</mat-icon>\n                        <span>{{ integration.filename }}</span>\n                        <button mat-icon-button class=\"spt-copy-btn\"\n                                (click)=\"copyToClipboard(integration.config)\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>{{ integration.config }}</code></pre>\n                    </div>\n\n                    <div class=\"integration-notes\" *ngIf=\"integration.notes\">\n                      <mat-icon class=\"notes-icon\">lightbulb</mat-icon>\n                      <div class=\"notes-content\">\n                        <strong>Implementation Notes:</strong>\n                        <p>{{ integration.notes }}</p>\n                      </div>\n                    </div>\n\n                    <!-- Platform-specific quick tips -->\n                    <div class=\"platform-tips\" *ngIf=\"getPlatformTips(integration.platform).length > 0\">\n                      <h5>Quick Tips:</h5>\n                      <ul class=\"tips-list\">\n                        <li *ngFor=\"let tip of getPlatformTips(integration.platform)\">\n                          <mat-icon class=\"tip-icon\">check_circle</mat-icon>\n                          <span>{{ tip }}</span>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n\n            <!-- CI/CD Best Practices Section -->\n            <div class=\"best-practices-section\">\n              <h3>CI/CD Best Practices</h3>\n              <div class=\"practices-grid\">\n                <mat-card class=\"practice-card\" *ngFor=\"let practice of cicdBestPractices\">\n                  <mat-card-header>\n                    <mat-icon mat-card-avatar [style.background-color]=\"practice.color\">{{ practice.icon }}</mat-icon>\n                    <mat-card-title>{{ practice.title }}</mat-card-title>\n                  </mat-card-header>\n                  <mat-card-content>\n                    <p>{{ practice.description }}</p>\n                    <ul class=\"practice-tips\">\n                      <li *ngFor=\"let tip of practice.tips\">{{ tip }}</li>\n                    </ul>\n                  </mat-card-content>\n                </mat-card>\n              </div>\n            </div>\n\n            <!-- Environment-Specific Configurations -->\n            <div class=\"environment-configs\">\n              <h3>Environment-Specific Configurations</h3>\n              <mat-card class=\"env-config-card\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>settings_applications</mat-icon>\n                  <mat-card-title>Environment Variables</mat-card-title>\n                  <mat-card-subtitle>Configure SPT for different environments</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"env-examples\">\n                    <div class=\"env-example\" *ngFor=\"let env of environmentConfigs\">\n                      <h4>{{ env.name }}</h4>\n                      <div class=\"spt-code-block\">\n                        <div class=\"spt-code-header\">\n                          <mat-icon>terminal</mat-icon>\n                          <span>{{ env.type }}</span>\n                        </div>\n                        <pre class=\"spt-code-content\"><code>{{ env.config }}</code></pre>\n                      </div>\n                      <p class=\"env-description\">{{ env.description }}</p>\n                    </div>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Configuration Tab -->\n        <mat-tab label=\"Configuration\">\n          <div class=\"tab-content\">\n            <h2>Configuration</h2>\n            <p>Configure SPT CLI for your development environment and preferences.</p>\n            \n            <mat-card class=\"config-card\">\n              <mat-card-header>\n                <mat-icon mat-card-avatar>settings</mat-icon>\n                <mat-card-title>Configuration File</mat-card-title>\n                <mat-card-subtitle>spt.config.json</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"code-block\">\n                  <div class=\"code-header\">\n                    <mat-icon>code</mat-icon>\n                    <span>JSON Configuration</span>\n                  </div>\n                  <pre><code>{{ configExample }}</code></pre>\n                </div>\n                <div class=\"config-description\">\n                  <h4>Configuration Options</h4>\n                  <div class=\"config-options\">\n                    <div class=\"config-option\" *ngFor=\"let option of configOptions\">\n                      <strong>{{ option.key }}</strong>\n                      <p>{{ option.description }}</p>\n                    </div>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styles: [`\n    .cli-guide-container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .page-header {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .page-header h1 {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      color: #1976d2;\n      margin: 0 0 8px 0;\n    }\n\n    .page-subtitle {\n      color: #666;\n      font-size: 1.1em;\n      margin: 0;\n    }\n\n    .cli-overview {\n      margin-bottom: 32px;\n    }\n\n    .overview-card {\n      border: 1px solid #e0e0e0;\n    }\n\n    .cli-features {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 16px;\n      margin-top: 16px;\n    }\n\n    .feature {\n      display: flex;\n      align-items: flex-start;\n      gap: 12px;\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .feature mat-icon {\n      margin-top: 2px;\n    }\n\n    .feature strong {\n      display: block;\n      margin-bottom: 4px;\n    }\n\n    .feature p {\n      margin: 0;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    .cli-tabs {\n      margin-bottom: 32px;\n    }\n\n    .tab-content {\n      padding: 24px 0;\n    }\n\n    .tab-content h2 {\n      color: #1976d2;\n      margin-bottom: 8px;\n    }\n\n    .installation-methods {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .method-card {\n      height: fit-content;\n    }\n\n    .method-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #e3f2fd;\n      border-radius: 4px;\n      color: #1976d2;\n    }\n\n    .commands-list {\n      margin-top: 24px;\n    }\n\n    .command-panel {\n      margin-bottom: 8px;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .command-name {\n      background: #f5f5f5;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-family: 'Courier New', monospace;\n      font-weight: 500;\n    }\n\n    .command-details {\n      padding: 16px 0;\n    }\n\n    .usage-section,\n    .options-section,\n    .examples-section,\n    .notes-section {\n      margin-bottom: 24px;\n    }\n\n    .usage-section h4,\n    .options-section h4,\n    .examples-section h4,\n    .notes-section h4 {\n      margin: 0 0 12px 0;\n      color: #1976d2;\n    }\n\n    .code-block {\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      overflow: hidden;\n      margin-bottom: 16px;\n    }\n\n    .code-header {\n      background: #f5f5f5;\n      padding: 8px 16px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-weight: 500;\n      border-bottom: 1px solid #e0e0e0;\n    }\n\n    .code-block pre {\n      margin: 0;\n      padding: 16px;\n      background: #fafafa;\n      overflow-x: auto;\n    }\n\n    .code-block code {\n      font-family: 'Courier New', monospace;\n      font-size: 0.9em;\n    }\n\n    .output-block {\n      border: 1px solid #4caf50;\n      border-radius: 8px;\n      overflow: hidden;\n      margin-top: 8px;\n    }\n\n    .output-block .code-header {\n      background: #e8f5e8;\n      border-bottom-color: #4caf50;\n    }\n\n    .output-block pre {\n      background: #f1f8e9;\n    }\n\n    .options-table {\n      width: 100%;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .options-table code {\n      background: #f5f5f5;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-size: 0.9em;\n    }\n\n    .example {\n      margin-bottom: 24px;\n      padding: 16px;\n      background: #f9f9f9;\n      border-radius: 8px;\n    }\n\n    .example-description {\n      margin: 0 0 12px 0;\n      font-weight: 500;\n      color: #1976d2;\n    }\n\n    .notes-content {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n      padding: 12px;\n      background: #e3f2fd;\n      border-radius: 8px;\n      color: #1976d2;\n    }\n\n    .notes-content p {\n      margin: 0;\n    }\n\n    .integration-examples {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .integration-card {\n      height: fit-content;\n    }\n\n    .integration-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #fff3e0;\n      border-radius: 4px;\n      color: #f57c00;\n    }\n\n    .config-card {\n      margin-top: 24px;\n    }\n\n    .config-description {\n      margin-top: 24px;\n    }\n\n    .config-description h4 {\n      margin: 0 0 16px 0;\n      color: #1976d2;\n    }\n\n    .config-options {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 16px;\n    }\n\n    .config-option {\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .config-option strong {\n      display: block;\n      margin-bottom: 4px;\n      color: #1976d2;\n    }\n\n    .config-option p {\n      margin: 0;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    @media (max-width: 768px) {\n      .cli-features {\n        grid-template-columns: 1fr;\n      }\n      \n      .installation-methods {\n        grid-template-columns: 1fr;\n      }\n      \n      .integration-examples {\n        grid-template-columns: 1fr;\n      }\n      \n      .config-options {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class CliGuideComponent {\n  optionColumns: string[] = ['flag', 'type', 'description', 'default'];\n\n  cliFeatures = [\n    {\n      title: 'Security Scanning',\n      description: 'Comprehensive security analysis for blockchain applications',\n      icon: 'security',\n      color: '#1976d2'\n    },\n    {\n      title: 'Multiple Formats',\n      description: 'Output results in JSON, YAML, CSV, or human-readable formats',\n      icon: 'description',\n      color: '#4caf50'\n    },\n    {\n      title: 'CI/CD Integration',\n      description: 'Easy integration with continuous integration pipelines',\n      icon: 'integration_instructions',\n      color: '#ff9800'\n    },\n    {\n      title: 'Configurable',\n      description: 'Flexible configuration options for different environments',\n      icon: 'tune',\n      color: '#9c27b0'\n    }\n  ];\n\n  installationMethods = [\n    {\n      title: 'From Source',\n      description: 'Build from source code',\n      icon: 'code',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\ngo build -o spt cmd/main.go`,\n      notes: 'Requires Go 1.21+ to be installed'\n    },\n    {\n      title: 'Using Make',\n      description: 'Build using Makefile',\n      icon: 'build',\n      commands: `git clone https://github.com/blockchain-spt/spt.git\ncd spt\nmake cli`,\n      notes: 'Binary will be created in build/ directory'\n    },\n    {\n      title: 'Go Install',\n      description: 'Install directly with Go',\n      icon: 'download',\n      commands: `go install github.com/blockchain-spt/cmd/spt@latest`,\n      notes: 'Installs to $GOPATH/bin'\n    }\n  ];\n\n  cliCommands: CliCommand[] = [\n    {\n      name: 'scan',\n      description: 'Perform security scan on files or directories',\n      usage: 'spt scan [flags] [path]',\n      options: [\n        { flag: '--chain', type: 'string', description: 'Blockchain chain to analyze', default: 'all' },\n        { flag: '--format', type: 'string', description: 'Output format (json, yaml, csv, table)', default: 'table' },\n        { flag: '--output', type: 'string', description: 'Output file path' },\n        { flag: '--severity', type: 'string', description: 'Minimum severity level', default: 'medium' },\n        { flag: '--recursive', type: 'boolean', description: 'Scan directories recursively', default: 'true' }\n      ],\n      examples: [\n        {\n          command: 'spt scan ./contracts',\n          description: 'Scan all files in contracts directory',\n          output: `Scanning ./contracts...\nFound 3 issues:\n  HIGH: Potential reentrancy in contract.sol:42\n  MEDIUM: Unchecked return value in token.sol:15\n  MEDIUM: Gas optimization opportunity in utils.sol:8`\n        },\n        {\n          command: 'spt scan --chain ethereum --format json ./src',\n          description: 'Scan for Ethereum-specific issues and output as JSON'\n        }\n      ],\n      notes: 'Use --help flag with any command to see detailed usage information'\n    },\n    {\n      name: 'audit',\n      description: 'Perform comprehensive security audit',\n      usage: 'spt audit [flags] [path]',\n      options: [\n        { flag: '--generate-report', type: 'boolean', description: 'Generate detailed report', default: 'false' },\n        { flag: '--report-path', type: 'string', description: 'Report output path', default: './audit-report.html' },\n        { flag: '--template', type: 'string', description: 'Report template', default: 'standard' }\n      ],\n      examples: [\n        {\n          command: 'spt audit --generate-report ./project',\n          description: 'Perform audit and generate HTML report'\n        }\n      ]\n    },\n    {\n      name: 'check',\n      description: 'Run specific security checks',\n      usage: 'spt check [subcommand] [flags] [path]',\n      options: [\n        { flag: '--fix', type: 'boolean', description: 'Attempt to fix issues automatically', default: 'false' }\n      ],\n      examples: [\n        {\n          command: 'spt check deps --fix',\n          description: 'Check dependencies and fix known vulnerabilities'\n        },\n        {\n          command: 'spt check env',\n          description: 'Check environment configuration for security issues'\n        }\n      ]\n    }\n  ];\n\n  integrationExamples = [\n    {\n      platform: 'GitHub Actions',\n      description: 'Integrate SPT into GitHub Actions workflow',\n      icon: 'integration_instructions',\n      filename: '.github/workflows/security.yml',\n      config: `name: Security Scan\non: [push, pull_request]\n\njobs:\n  security:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - uses: actions/setup-go@v3\n        with:\n          go-version: '1.21'\n      - name: Install SPT\n        run: go install github.com/blockchain-spt/cmd/spt@latest\n      - name: Run Security Scan\n        run: spt scan --format json --output security-report.json ./\n      - name: Upload Results\n        uses: actions/upload-artifact@v3\n        with:\n          name: security-report\n          path: security-report.json`,\n      notes: 'Add GITHUB_TOKEN to secrets for private repositories'\n    },\n    {\n      platform: 'GitLab CI',\n      description: 'Integrate SPT into GitLab CI/CD pipeline',\n      icon: 'integration_instructions',\n      filename: '.gitlab-ci.yml',\n      config: `security_scan:\n  stage: test\n  image: golang:1.21\n  script:\n    - go install github.com/blockchain-spt/cmd/spt@latest\n    - spt scan --format json --output security-report.json ./\n    - spt audit --generate-report --report-path audit-report.html ./\n  artifacts:\n    reports:\n      junit: security-report.json\n    paths:\n      - audit-report.html\n    expire_in: 1 week\n  only:\n    - merge_requests\n    - main`,\n      notes: 'Configure artifact storage for report persistence'\n    },\n    {\n      platform: 'Azure DevOps',\n      description: 'Integrate SPT into Azure DevOps Pipeline',\n      icon: 'cloud',\n      filename: 'azure-pipelines.yml',\n      config: `trigger:\n  branches:\n    include:\n      - main\n      - develop\n  paths:\n    include:\n      - contracts/*\n      - src/*\n\npool:\n  vmImage: 'ubuntu-latest'\n\nvariables:\n  GO_VERSION: '1.21'\n  SPT_VERSION: 'latest'\n\nstages:\n- stage: SecurityScan\n  displayName: 'Security Analysis'\n  jobs:\n  - job: SPTScan\n    displayName: 'SPT Security Scan'\n    steps:\n    - task: GoTool@0\n      displayName: 'Install Go'\n      inputs:\n        version: '\\$(GO_VERSION)'\n\n    - script: |\n        go install github.com/blockchain-spt/cmd/spt@\\$(SPT_VERSION)\n        echo \"SPT installed successfully\"\n      displayName: 'Install SPT CLI'\n\n    - script: |\n        spt scan --format json --output \\$(Agent.TempDirectory)/security-report.json ./\n        spt audit --generate-report --report-path \\$(Agent.TempDirectory)/audit-report.html ./\n      displayName: 'Run Security Scan'\n      continueOnError: true\n\n    - task: PublishTestResults@2\n      displayName: 'Publish Security Results'\n      inputs:\n        testResultsFormat: 'JUnit'\n        testResultsFiles: '\\$(Agent.TempDirectory)/security-report.json'\n        testRunTitle: 'SPT Security Scan Results'\n      condition: always()\n\n    - task: PublishBuildArtifacts@1\n      displayName: 'Publish Security Reports'\n      inputs:\n        pathToPublish: '\\$(Agent.TempDirectory)'\n        artifactName: 'security-reports'\n        publishLocation: 'Container'\n      condition: always()\n\n    - script: |\n        if [ -f \"\\$(Agent.TempDirectory)/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' \\$(Agent.TempDirectory)/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' \\$(Agent.TempDirectory)/security-report.json)\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ] || [ \"\\$HIGH_COUNT\" -gt 5 ]; then\n            echo \"##vso[task.logissue type=error]Critical security issues found: \\$CRITICAL_COUNT critical, \\$HIGH_COUNT high\"\n            exit 1\n          fi\n        fi\n      displayName: 'Evaluate Security Results'\n      condition: always()`,\n      notes: 'Configure service connections for private repositories and adjust thresholds as needed'\n    },\n    {\n      platform: 'AWS CodeBuild',\n      description: 'Integrate SPT into AWS CodeBuild pipeline',\n      icon: 'cloud_queue',\n      filename: 'buildspec.yml',\n      config: `version: 0.2\n\nenv:\n  variables:\n    GO_VERSION: \"1.21\"\n    SPT_VERSION: \"latest\"\n  parameter-store:\n    GITHUB_TOKEN: \"/spt/github-token\"  # Optional for private repos\n\nphases:\n  install:\n    runtime-versions:\n      golang: \\$GO_VERSION\n    commands:\n      - echo \"Installing SPT CLI...\"\n      - go install github.com/blockchain-spt/cmd/spt@\\$SPT_VERSION\n      - spt version\n\n  pre_build:\n    commands:\n      - echo \"Preparing security scan...\"\n      - mkdir -p reports\n      - echo \"Current directory contents:\"\n      - ls -la\n\n  build:\n    commands:\n      - echo \"Running SPT security scan...\"\n      - spt scan --format json --output reports/security-report.json ./\n      - spt audit --generate-report --report-path reports/audit-report.html ./\n      - echo \"Security scan completed\"\n\n  post_build:\n    commands:\n      - echo \"Processing security results...\"\n      - |\n        if [ -f \"reports/security-report.json\" ]; then\n          CRITICAL_COUNT=\\$(jq '.summary.critical // 0' reports/security-report.json)\n          HIGH_COUNT=\\$(jq '.summary.high // 0' reports/security-report.json)\n          MEDIUM_COUNT=\\$(jq '.summary.medium // 0' reports/security-report.json)\n\n          echo \"Security Summary:\"\n          echo \"  Critical: \\$CRITICAL_COUNT\"\n          echo \"  High: \\$HIGH_COUNT\"\n          echo \"  Medium: \\$MEDIUM_COUNT\"\n\n          # Fail build if critical issues found\n          if [ \"\\$CRITICAL_COUNT\" -gt 0 ]; then\n            echo \"Build failed due to critical security issues\"\n            exit 1\n          fi\n\n          # Warning for high issues\n          if [ \"\\$HIGH_COUNT\" -gt 10 ]; then\n            echo \"Warning: High number of high-severity issues (\\$HIGH_COUNT)\"\n          fi\n        else\n          echo \"Security report not found\"\n          exit 1\n        fi\n\nartifacts:\n  files:\n    - 'reports/**/*'\n  name: spt-security-reports\n\nreports:\n  spt-security:\n    files:\n      - 'reports/security-report.json'\n    file-format: 'JUNITXML'`,\n      notes: 'Store sensitive tokens in AWS Parameter Store or Secrets Manager'\n    },\n    {\n      platform: 'AWS CodePipeline',\n      description: 'Complete AWS CodePipeline with SPT integration',\n      icon: 'account_tree',\n      filename: 'cloudformation-pipeline.yml',\n      config: `AWSTemplateFormatVersion: '2010-09-09'\nDescription: 'SPT Security Pipeline with CodePipeline'\n\nParameters:\n  GitHubRepo:\n    Type: String\n    Description: GitHub repository name\n  GitHubOwner:\n    Type: String\n    Description: GitHub repository owner\n  GitHubToken:\n    Type: String\n    NoEcho: true\n    Description: GitHub personal access token\n\nResources:\n  # S3 Bucket for artifacts\n  ArtifactsBucket:\n    Type: AWS::S3::Bucket\n    Properties:\n      BucketName: !Sub '\\${AWS::StackName}-spt-artifacts'\n      VersioningConfiguration:\n        Status: Enabled\n      PublicAccessBlockConfiguration:\n        BlockPublicAcls: true\n        BlockPublicPolicy: true\n        IgnorePublicAcls: true\n        RestrictPublicBuckets: true\n\n  # CodeBuild Project for SPT Security Scan\n  SPTSecurityProject:\n    Type: AWS::CodeBuild::Project\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-security'\n      ServiceRole: !GetAtt CodeBuildRole.Arn\n      Artifacts:\n        Type: CODEPIPELINE\n      Environment:\n        Type: LINUX_CONTAINER\n        ComputeType: BUILD_GENERAL1_MEDIUM\n        Image: aws/codebuild/amazonlinux2-x86_64-standard:3.0\n        EnvironmentVariables:\n          - Name: GITHUB_TOKEN\n            Value: !Ref GitHubToken\n            Type: PARAMETER_STORE\n      Source:\n        Type: CODEPIPELINE\n        BuildSpec: |\n          version: 0.2\n          phases:\n            install:\n              runtime-versions:\n                golang: 1.21\n              commands:\n                - go install github.com/blockchain-spt/cmd/spt@latest\n            build:\n              commands:\n                - mkdir -p reports\n                - spt scan --format json --output reports/security-report.json ./\n                - spt audit --generate-report --report-path reports/audit-report.html ./\n          artifacts:\n            files:\n              - 'reports/**/*'\n\n  # CodePipeline\n  SPTPipeline:\n    Type: AWS::CodePipeline::Pipeline\n    Properties:\n      Name: !Sub '\\${AWS::StackName}-spt-pipeline'\n      RoleArn: !GetAtt CodePipelineRole.Arn\n      ArtifactStore:\n        Type: S3\n        Location: !Ref ArtifactsBucket\n      Stages:\n        - Name: Source\n          Actions:\n            - Name: SourceAction\n              ActionTypeId:\n                Category: Source\n                Owner: ThirdParty\n                Provider: GitHub\n                Version: '1'\n              Configuration:\n                Owner: !Ref GitHubOwner\n                Repo: !Ref GitHubRepo\n                Branch: main\n                OAuthToken: !Ref GitHubToken\n              OutputArtifacts:\n                - Name: SourceOutput\n\n        - Name: SecurityScan\n          Actions:\n            - Name: SPTScan\n              ActionTypeId:\n                Category: Build\n                Owner: AWS\n                Provider: CodeBuild\n                Version: '1'\n              Configuration:\n                ProjectName: !Ref SPTSecurityProject\n              InputArtifacts:\n                - Name: SourceOutput\n              OutputArtifacts:\n                - Name: SecurityOutput\n\n  # IAM Roles (simplified - add specific permissions as needed)\n  CodeBuildRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codebuild.amazonaws.com\n            Action: sts:AssumeRole\n      ManagedPolicyArns:\n        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess\n      Policies:\n        - PolicyName: S3Access\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                Resource: !Sub '\\${ArtifactsBucket}/*'\n\n  CodePipelineRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: codepipeline.amazonaws.com\n            Action: sts:AssumeRole\n      Policies:\n        - PolicyName: PipelinePolicy\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - s3:GetObject\n                  - s3:PutObject\n                  - s3:GetBucketVersioning\n                Resource:\n                  - !Sub '\\${ArtifactsBucket}'\n                  - !Sub '\\${ArtifactsBucket}/*'\n              - Effect: Allow\n                Action:\n                  - codebuild:BatchGetBuilds\n                  - codebuild:StartBuild\n                Resource: !GetAtt SPTSecurityProject.Arn`,\n      notes: 'Deploy using AWS CloudFormation. Customize IAM permissions based on your security requirements.'\n    },\n    {\n      platform: 'Docker',\n      description: 'Containerized SPT for consistent CI/CD environments',\n      icon: 'developer_board',\n      filename: 'Dockerfile',\n      config: `# Multi-stage Dockerfile for SPT CLI\nFROM golang:1.21-alpine AS builder\n\n# Install dependencies\nRUN apk add --no-cache git ca-certificates\n\n# Set working directory\nWORKDIR /app\n\n# Install SPT CLI\nRUN go install github.com/blockchain-spt/cmd/spt@latest\n\n# Create final image\nFROM alpine:latest\n\n# Install runtime dependencies\nRUN apk add --no-cache ca-certificates jq curl\n\n# Copy SPT binary from builder\nCOPY --from=builder /go/bin/spt /usr/local/bin/spt\n\n# Create non-root user\nRUN addgroup -g 1001 spt && \\\\\n    adduser -D -u 1001 -G spt spt\n\n# Set working directory\nWORKDIR /workspace\n\n# Change ownership\nRUN chown -R spt:spt /workspace\n\n# Switch to non-root user\nUSER spt\n\n# Set entrypoint\nENTRYPOINT [\"spt\"]\nCMD [\"--help\"]\n\n# Usage examples:\n# docker build -t spt-cli .\n# docker run --rm -v \\$(pwd):/workspace spt-cli scan ./\n# docker run --rm -v \\$(pwd):/workspace spt-cli audit --generate-report ./`,\n      notes: 'Use this Docker image in any CI/CD system that supports containers'\n    },\n    {\n      platform: 'Jenkins',\n      description: 'Jenkins Pipeline with SPT integration',\n      icon: 'build',\n      filename: 'Jenkinsfile',\n      config: `pipeline {\n    agent any\n\n    environment {\n        GO_VERSION = '1.21'\n        SPT_VERSION = 'latest'\n        REPORTS_DIR = 'reports'\n    }\n\n    tools {\n        go 'go-1.21'  // Configure in Jenkins Global Tools\n    }\n\n    stages {\n        stage('Checkout') {\n            steps {\n                checkout scm\n                script {\n                    env.GIT_COMMIT_SHORT = sh(\n                        script: 'git rev-parse --short HEAD',\n                        returnStdout: true\n                    ).trim()\n                }\n            }\n        }\n\n        stage('Install SPT') {\n            steps {\n                sh '''\n                    echo \"Installing SPT CLI...\"\n                    go install github.com/blockchain-spt/cmd/spt@\\${SPT_VERSION}\n                    spt version\n                '''\n            }\n        }\n\n        stage('Security Scan') {\n            steps {\n                sh '''\n                    echo \"Creating reports directory...\"\n                    mkdir -p \\${REPORTS_DIR}\n\n                    echo \"Running SPT security scan...\"\n                    spt scan --format json --output \\${REPORTS_DIR}/security-report.json ./\n\n                    echo \"Generating audit report...\"\n                    spt audit --generate-report --report-path \\${REPORTS_DIR}/audit-report.html ./\n\n                    echo \"Security scan completed\"\n                '''\n            }\n            post {\n                always {\n                    // Archive artifacts\n                    archiveArtifacts artifacts: '\\${REPORTS_DIR}/**/*', fingerprint: true\n\n                    // Publish HTML reports\n                    publishHTML([\n                        allowMissing: false,\n                        alwaysLinkToLastBuild: true,\n                        keepAll: true,\n                        reportDir: '\\${REPORTS_DIR}',\n                        reportFiles: 'audit-report.html',\n                        reportName: 'SPT Security Report'\n                    ])\n                }\n            }\n        }\n\n        stage('Evaluate Results') {\n            steps {\n                script {\n                    if (fileExists(\"\\${REPORTS_DIR}/security-report.json\")) {\n                        def report = readJSON file: \"\\${REPORTS_DIR}/security-report.json\"\n                        def critical = report.summary?.critical ?: 0\n                        def high = report.summary?.high ?: 0\n                        def medium = report.summary?.medium ?: 0\n\n                        echo \"Security Summary:\"\n                        echo \"  Critical: \\${critical}\"\n                        echo \"  High: \\${high}\"\n                        echo \"  Medium: \\${medium}\"\n\n                        // Set build status based on results\n                        if (critical > 0) {\n                            currentBuild.result = 'FAILURE'\n                            error(\"Build failed due to \\${critical} critical security issues\")\n                        } else if (high > 10) {\n                            currentBuild.result = 'UNSTABLE'\n                            echo \"Build marked unstable due to \\${high} high-severity issues\"\n                        }\n\n                        // Add build description\n                        currentBuild.description = \"Critical: \\${critical}, High: \\${high}, Medium: \\${medium}\"\n                    } else {\n                        currentBuild.result = 'FAILURE'\n                        error(\"Security report not found\")\n                    }\n                }\n            }\n        }\n    }\n\n    post {\n        always {\n            // Clean workspace\n            cleanWs()\n        }\n        failure {\n            // Send notifications on failure\n            emailext (\n                subject: \"SPT Security Scan Failed: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan failed for commit \\${env.GIT_COMMIT_SHORT}. Check the build logs for details.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n        unstable {\n            // Send notifications on unstable builds\n            emailext (\n                subject: \"SPT Security Scan Unstable: \\${env.JOB_NAME} - \\${env.BUILD_NUMBER}\",\n                body: \"Security scan completed with warnings for commit \\${env.GIT_COMMIT_SHORT}. Review the security report.\",\n                to: \"\\${env.CHANGE_AUTHOR_EMAIL}\"\n            )\n        }\n    }\n}`,\n      notes: 'Configure Go tools and email notifications in Jenkins. Install required plugins: Pipeline, HTML Publisher, Email Extension.'\n    }\n  ];\n\n  cicdBestPractices = [\n    {\n      title: 'Fail Fast Strategy',\n      description: 'Configure your pipeline to fail immediately on critical security issues to prevent vulnerable code from progressing.',\n      icon: 'error',\n      color: '#f44336',\n      tips: [\n        'Set critical severity threshold to 0',\n        'Use exit codes to stop pipeline execution',\n        'Implement security gates at multiple stages',\n        'Configure notifications for security failures'\n      ]\n    },\n    {\n      title: 'Artifact Management',\n      description: 'Properly store and manage security reports and artifacts for compliance and tracking.',\n      icon: 'archive',\n      color: '#2196f3',\n      tips: [\n        'Archive security reports for audit trails',\n        'Use versioned artifact storage',\n        'Implement retention policies',\n        'Enable artifact encryption for sensitive data'\n      ]\n    },\n    {\n      title: 'Parallel Execution',\n      description: 'Optimize scan performance by running security checks in parallel with other tests.',\n      icon: 'speed',\n      color: '#4caf50',\n      tips: [\n        'Run security scans parallel to unit tests',\n        'Use matrix builds for multiple environments',\n        'Implement caching for faster scans',\n        'Configure resource limits appropriately'\n      ]\n    },\n    {\n      title: 'Security Thresholds',\n      description: 'Define appropriate security thresholds based on your project maturity and risk tolerance.',\n      icon: 'tune',\n      color: '#ff9800',\n      tips: [\n        'Start with strict thresholds for new projects',\n        'Gradually improve legacy project thresholds',\n        'Use different thresholds for different branches',\n        'Document threshold decisions and rationale'\n      ]\n    },\n    {\n      title: 'Integration Testing',\n      description: 'Test your CI/CD integration thoroughly before deploying to production pipelines.',\n      icon: 'integration_instructions',\n      color: '#9c27b0',\n      tips: [\n        'Test with sample vulnerable code',\n        'Verify artifact generation and storage',\n        'Test notification mechanisms',\n        'Validate security gate functionality'\n      ]\n    },\n    {\n      title: 'Monitoring & Alerting',\n      description: 'Implement comprehensive monitoring and alerting for your security pipeline.',\n      icon: 'monitoring',\n      color: '#607d8b',\n      tips: [\n        'Monitor pipeline execution times',\n        'Set up alerts for scan failures',\n        'Track security metrics over time',\n        'Implement dashboard for security trends'\n      ]\n    }\n  ];\n\n  environmentConfigs = [\n    {\n      name: 'Development Environment',\n      type: 'Environment Variables',\n      config: `# Development - More verbose, all severities\nexport SPT_SEVERITY_THRESHOLD=low\nexport SPT_OUTPUT_FORMAT=table\nexport SPT_COLORS=true\nexport SPT_VERBOSE=true\nexport SPT_PARALLEL_SCANS=2\nexport SPT_TIMEOUT=10m\nexport SPT_CACHE_ENABLED=true\nexport SPT_CACHE_DIR=~/.spt/cache`,\n      description: 'Development environment with verbose output and lower thresholds for learning and debugging.'\n    },\n    {\n      name: 'Staging Environment',\n      type: 'Environment Variables',\n      config: `# Staging - Production-like with medium threshold\nexport SPT_SEVERITY_THRESHOLD=medium\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=4\nexport SPT_TIMEOUT=15m\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_GENERATE_REPORTS=true`,\n      description: 'Staging environment that mirrors production settings with moderate security requirements.'\n    },\n    {\n      name: 'Production Environment',\n      type: 'Environment Variables',\n      config: `# Production - Strict security requirements\nexport SPT_SEVERITY_THRESHOLD=high\nexport SPT_OUTPUT_FORMAT=json\nexport SPT_COLORS=false\nexport SPT_VERBOSE=false\nexport SPT_PARALLEL_SCANS=8\nexport SPT_TIMEOUT=30m\nexport SPT_FAIL_ON_CRITICAL=true\nexport SPT_FAIL_ON_HIGH=true\nexport SPT_AUDIT_ENABLED=true\nexport SPT_COMPLIANCE_MODE=true`,\n      description: 'Production environment with strict security requirements and comprehensive auditing.'\n    },\n    {\n      name: 'Docker Configuration',\n      type: 'Docker Environment',\n      config: `# Docker container environment variables\nENV SPT_SEVERITY_THRESHOLD=medium\nENV SPT_OUTPUT_FORMAT=json\nENV SPT_PARALLEL_SCANS=4\nENV SPT_TIMEOUT=20m\nENV SPT_CACHE_ENABLED=false\nENV SPT_WORKSPACE=/workspace\nENV SPT_REPORTS_DIR=/reports\n\n# Volume mounts\n# docker run -v \\$(pwd):/workspace -v \\$(pwd)/reports:/reports spt-cli`,\n      description: 'Containerized environment configuration for consistent cross-platform execution.'\n    },\n    {\n      name: 'Cloud-Native Configuration',\n      type: 'Kubernetes ConfigMap',\n      config: `apiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: spt-config\n  namespace: security\ndata:\n  SPT_SEVERITY_THRESHOLD: \"medium\"\n  SPT_OUTPUT_FORMAT: \"json\"\n  SPT_PARALLEL_SCANS: \"6\"\n  SPT_TIMEOUT: \"25m\"\n  SPT_CLOUD_STORAGE: \"true\"\n  SPT_METRICS_ENABLED: \"true\"\n  SPT_DISTRIBUTED_SCAN: \"true\"\n---\napiVersion: v1\nkind: Secret\nmetadata:\n  name: spt-secrets\n  namespace: security\ntype: Opaque\nstringData:\n  github-token: \"your-github-token\"\n  api-key: \"your-api-key\"`,\n      description: 'Kubernetes-native configuration using ConfigMaps and Secrets for cloud deployments.'\n    }\n  ];\n\n  configExample = `{\n  \"scanning\": {\n    \"chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n    \"severity_threshold\": \"medium\",\n    \"max_file_size\": \"10MB\",\n    \"timeout\": \"5m\",\n    \"parallel_scans\": 4\n  },\n  \"output\": {\n    \"format\": \"table\",\n    \"colors\": true,\n    \"verbose\": false\n  },\n  \"rules\": {\n    \"ethereum\": {\n      \"check_reentrancy\": true,\n      \"check_overflow\": true,\n      \"check_access_control\": true\n    },\n    \"bitcoin\": {\n      \"check_key_management\": true,\n      \"check_transaction_validation\": true\n    }\n  },\n  \"integrations\": {\n    \"vscode\": {\n      \"enabled\": true,\n      \"server_url\": \"http://localhost:8080\"\n    }\n  }\n}`;\n\n  configOptions = [\n    {\n      key: 'scanning.chains',\n      description: 'Array of blockchain chains to analyze'\n    },\n    {\n      key: 'scanning.severity_threshold',\n      description: 'Minimum severity level to report'\n    },\n    {\n      key: 'output.format',\n      description: 'Default output format for scan results'\n    },\n    {\n      key: 'rules.ethereum',\n      description: 'Ethereum-specific security rules configuration'\n    },\n    {\n      key: 'rules.bitcoin',\n      description: 'Bitcoin-specific security rules configuration'\n    },\n    {\n      key: 'integrations',\n      description: 'Configuration for IDE and tool integrations'\n    }\n  ];\n\n  platformTips: { [key: string]: string[] } = {\n    'GitHub Actions': [\n      'Use GitHub Secrets for sensitive tokens',\n      'Enable branch protection rules with status checks',\n      'Configure matrix builds for multiple Go versions',\n      'Use actions/cache for faster builds'\n    ],\n    'GitLab CI': [\n      'Use GitLab CI/CD variables for configuration',\n      'Configure merge request pipelines',\n      'Use GitLab Container Registry for custom images',\n      'Enable pipeline schedules for regular scans'\n    ],\n    'Azure DevOps': [\n      'Store secrets in Azure Key Vault',\n      'Use variable groups for environment-specific configs',\n      'Configure branch policies with build validation',\n      'Enable Azure Artifacts for report storage'\n    ],\n    'AWS CodeBuild': [\n      'Use Parameter Store for secure configuration',\n      'Configure VPC settings for private repositories',\n      'Use CloudWatch for monitoring and alerting',\n      'Enable S3 artifact encryption'\n    ],\n    'AWS CodePipeline': [\n      'Use CloudFormation for infrastructure as code',\n      'Configure cross-region artifact replication',\n      'Implement approval gates for production',\n      'Use EventBridge for pipeline notifications'\n    ],\n    'Docker': [\n      'Use multi-stage builds for smaller images',\n      'Run containers as non-root user',\n      'Mount volumes for persistent reports',\n      'Use health checks for container monitoring'\n    ],\n    'Jenkins': [\n      'Use Jenkins Credentials for secure storage',\n      'Configure build triggers with webhooks',\n      'Use Pipeline as Code with Jenkinsfile',\n      'Enable Blue Ocean for better UI'\n    ]\n  };\n\n  copyToClipboard(text: string): void {\n    navigator.clipboard.writeText(text).then(() => {\n      // Could add a snackbar notification here\n      console.log('Configuration copied to clipboard');\n    }).catch(err => {\n      console.error('Failed to copy: ', err);\n    });\n  }\n\n  getPlatformTips(platform: string): string[] {\n    return this.platformTips[platform] || [];\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;IA6DxCC,EADF,CAAAC,cAAA,cAAyD,eACf;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnEH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAEhCF,EAFgC,CAAAG,YAAA,EAAI,EAC5B,EACF;;;;IALMH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,WAAA,UAAAC,UAAA,CAAAC,KAAA,CAA6B;IAACP,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAQ,iBAAA,CAAAF,UAAA,CAAAG,IAAA,CAAkB;IAEhDT,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAAF,UAAA,CAAAI,KAAA,CAAmB;IACxBV,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAQ,iBAAA,CAAAF,UAAA,CAAAK,WAAA,CAAyB;;;;;IA8B1BX,EADF,CAAAC,cAAA,cAA+C,eACnC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;;;;IADEH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAC,KAAA,CAAkB;;;;;IAd1Bb,EAFJ,CAAAC,cAAA,mBAAyE,sBACtD,kBACW;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACnDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAC7CF,EAD6C,CAAAG,YAAA,EAAoB,EAC/C;IAIZH,EAHN,CAAAC,cAAA,uBAAkB,cACQ,eACG,gBACb;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAChBF,EADgB,CAAAG,YAAA,EAAO,EACjB;IACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAClCF,EADkC,CAAAG,YAAA,EAAO,EAAM,EACzC;IACNH,EAAA,CAAAc,UAAA,KAAAC,6CAAA,kBAA+C;IAKnDf,EADE,CAAAG,YAAA,EAAmB,EACV;;;;IAjBmBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAH,IAAA,CAAiB;IAC3BT,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAF,KAAA,CAAkB;IACfV,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAD,WAAA,CAAwB;IAQ9BX,EAAA,CAAAI,SAAA,IAAqB;IAArBJ,EAAA,CAAAQ,iBAAA,CAAAI,SAAA,CAAAI,QAAA,CAAqB;IAEPhB,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAiB,UAAA,SAAAL,SAAA,CAAAC,KAAA,CAAkB;;;;;IAyCvCb,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAsC,WAC9B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC3B;;;;IADGH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAQ,iBAAA,CAAAU,SAAA,CAAAC,IAAA,CAAiB;;;;;IAIzBnB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAsC,eAC1B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC7BF,EAD6B,CAAAG,YAAA,EAAW,EACnC;;;;IADOH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAQ,iBAAA,CAAAY,SAAA,CAAAC,IAAA,CAAiB;;;;;IAI7BrB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACtDH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAc,SAAA,CAAAX,WAAA,CAAwB;;;;;IAG9DX,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EAAA,CAAAC,cAAA,WAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA3BH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAQ,iBAAA,CAAAe,SAAA,CAAAC,OAAA,CAAoB;;;;;IACjDxB,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFxCH,EAAA,CAAAC,cAAA,aAAsC;IAEpCD,EADA,CAAAc,UAAA,IAAAW,qEAAA,mBAA6B,IAAAC,qEAAA,mBACC;IAChC1B,EAAA,CAAAG,YAAA,EAAK;;;;IAFIH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAiB,UAAA,SAAAM,SAAA,CAAAC,OAAA,CAAoB;IACpBxB,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAiB,UAAA,UAAAM,SAAA,CAAAC,OAAA,CAAqB;;;;;IAGhCxB,EAAA,CAAA2B,SAAA,aAAyD;;;;;IACzD3B,EAAA,CAAA2B,SAAA,aAA+D;;;;;IA1BjE3B,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAA4B,uBAAA,OAAkC;IAEhC5B,EADA,CAAAc,UAAA,IAAAe,6DAAA,iBAAsC,IAAAC,6DAAA,iBACA;;IAIxC9B,EAAA,CAAA4B,uBAAA,OAAkC;IAEhC5B,EADA,CAAAc,UAAA,IAAAiB,6DAAA,iBAAsC,IAAAC,6DAAA,iBACA;;IAIxChC,EAAA,CAAA4B,uBAAA,QAAyC;IAEvC5B,EADA,CAAAc,UAAA,KAAAmB,8DAAA,iBAAsC,KAAAC,8DAAA,iBACA;;IAExClC,EAAA,CAAA4B,uBAAA,QAAqC;IAEnC5B,EADA,CAAAc,UAAA,KAAAqB,8DAAA,iBAAsC,KAAAC,8DAAA,iBACA;;IAMxCpC,EADA,CAAAc,UAAA,KAAAuB,8DAAA,iBAAoD,KAAAC,8DAAA,iBACM;IAE9DtC,EADE,CAAAG,YAAA,EAAQ,EACJ;;;;;IA3BaH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAiB,UAAA,eAAAsB,UAAA,CAAAC,OAAA,CAA8B;IAwBzBxC,EAAA,CAAAI,SAAA,IAA8B;IAA9BJ,EAAA,CAAAiB,UAAA,oBAAAwB,MAAA,CAAAC,aAAA,CAA8B;IACjB1C,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAiB,UAAA,qBAAAwB,MAAA,CAAAC,aAAA,CAAuB;;;;;IAiBpD1C,EAFJ,CAAAC,cAAA,cAAiD,cACtB,eACb;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IACdF,EADc,CAAAG,YAAA,EAAO,EACf;IACDH,EAAL,CAAAC,cAAA,UAAK,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAAM,EACxC;;;;IADOH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,iBAAA,CAAAmC,UAAA,CAAAC,MAAA,CAAoB;;;;;IAbjC5C,EADF,CAAAC,cAAA,cAA8D,YAC7B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGxDH,EAFJ,CAAAC,cAAA,cAAwB,cACG,eACb;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,cAAO;IACfF,EADe,CAAAG,YAAA,EAAO,EAChB;IACDH,EAAL,CAAAC,cAAA,UAAK,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAClCF,EADkC,CAAAG,YAAA,EAAO,EAAM,EACzC;IACNH,EAAA,CAAAc,UAAA,KAAA+B,qEAAA,kBAAiD;IAOnD7C,EAAA,CAAAG,YAAA,EAAM;;;;IAf2BH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAQ,iBAAA,CAAAmC,UAAA,CAAAhC,WAAA,CAAyB;IAM3CX,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAQ,iBAAA,CAAAmC,UAAA,CAAAG,OAAA,CAAqB;IAEP9C,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAiB,UAAA,SAAA0B,UAAA,CAAAC,MAAA,CAAoB;;;;;IAVjD5C,EADF,CAAAC,cAAA,cAAkE,SAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAc,UAAA,IAAAiC,8DAAA,mBAA8D;IAiBhE/C,EAAA,CAAAG,YAAA,EAAM;;;;IAjBqCH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAiB,UAAA,YAAAsB,UAAA,CAAAS,QAAA,CAAmB;;;;;IAoB5DhD,EADF,CAAAC,cAAA,cAAiD,SAC3C;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEZH,EADF,CAAAC,cAAA,cAA2B,eACf;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAE1BF,EAF0B,CAAAG,YAAA,EAAI,EACtB,EACF;;;;IAFCH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAA+B,UAAA,CAAA1B,KAAA,CAAmB;;;;;IAvExBb,EALN,CAAAC,cAAA,8BAEwB,iCACM,sBACT,eACY;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACxC;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAIzBH,EAFJ,CAAAC,cAAA,cAA6B,cACA,SACrB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EADP,CAAAC,cAAA,eAAwB,WACjB,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAElCF,EAFkC,CAAAG,YAAA,EAAO,EAAM,EACvC,EACF;IAsDNH,EApDA,CAAAc,UAAA,KAAAmC,wDAAA,mBAAgE,KAAAC,wDAAA,kBA+BE,KAAAC,wDAAA,kBAqBjB;IAQrDnD,EADE,CAAAG,YAAA,EAAM,EACc;;;;IA3EWH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAoD,kBAAA,SAAAb,UAAA,CAAAc,IAAA,KAAsB;IAGjDrD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAAb,UAAA,CAAA5B,WAAA,MACF;IAOeX,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAA+B,UAAA,CAAAe,KAAA,CAAmB;IAIJtD,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAiB,UAAA,SAAAsB,UAAA,CAAAC,OAAA,CAAAe,MAAA,KAAgC;IA+B/BvD,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,SAAAsB,UAAA,CAAAS,QAAA,CAAAO,MAAA,KAAiC;IAqBpCvD,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAiB,UAAA,SAAAsB,UAAA,CAAA1B,KAAA,CAAmB;;;;;IA0D3Cb,EADF,CAAAC,cAAA,cAAyD,mBAC1B;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/CH,EADF,CAAAC,cAAA,cAA2B,aACjB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAE9BF,EAF8B,CAAAG,YAAA,EAAI,EAC1B,EACF;;;;IAFCH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAQ,iBAAA,CAAAgD,eAAA,CAAA3C,KAAA,CAAuB;;;;;IASxBb,EADF,CAAAC,cAAA,SAA8D,mBACjC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EACnB;;;;IADGH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAQ,iBAAA,CAAAiD,OAAA,CAAS;;;;;IAJnBzD,EADF,CAAAC,cAAA,cAAoF,SAC9E;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAc,UAAA,IAAA4C,6DAAA,iBAA8D;IAKlE1D,EADE,CAAAG,YAAA,EAAK,EACD;;;;;IALkBH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAiB,UAAA,YAAAwB,MAAA,CAAAkB,eAAA,CAAAH,eAAA,CAAAI,QAAA,EAAwC;;;;;;IA1C9D5D,EANN,CAAAC,cAAA,8BAGuB,iCACO,sBACT,mBACiB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjEH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAIzBH,EAFJ,CAAAC,cAAA,cAAiC,cACC,UAC1B;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,kBAEwC;IADhCD,EAAA,CAAA6D,UAAA,mBAAAC,2EAAA;MAAA,MAAAN,eAAA,GAAAxD,EAAA,CAAA+D,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAzC,EAAA,CAAAkE,aAAA;MAAA,OAAAlE,EAAA,CAAAmE,WAAA,CAAS1B,MAAA,CAAA2B,eAAA,CAAAZ,eAAA,CAAAa,MAAA,CAAmC;IAAA,EAAC;IAEnDrE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACL;IAIFH,EAFJ,CAAAC,cAAA,eAA4B,eACG,gBACjB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,kBACsD;IAA9CD,EAAA,CAAA6D,UAAA,mBAAAS,2EAAA;MAAA,MAAAd,eAAA,GAAAxD,EAAA,CAAA+D,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAzC,EAAA,CAAAkE,aAAA;MAAA,OAAAlE,EAAA,CAAAmE,WAAA,CAAS1B,MAAA,CAAA2B,eAAA,CAAAZ,eAAA,CAAAa,MAAA,CAAmC;IAAA,EAAC;IACnDrE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACL;IACwBH,EAA9B,CAAAC,cAAA,eAA8B,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAAM,EACrE;IAWNH,EATA,CAAAc,UAAA,KAAAyD,wDAAA,kBAAyD,KAAAC,wDAAA,kBAS2B;IAUxFxE,EADE,CAAAG,YAAA,EAAM,EACc;;;;;;IApDpBH,EAAA,CAAAiB,UAAA,aAAAwD,KAAA,OAAoB;IAGgBzE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAQ,iBAAA,CAAAgD,eAAA,CAAA/C,IAAA,CAAsB;IACtDT,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAAI,eAAA,CAAAI,QAAA,MACF;IAEE5D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAAI,eAAA,CAAA7C,WAAA,MACF;IAKMX,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoD,kBAAA,yBAAAI,eAAA,CAAAkB,QAAA,KAA8C;IAW1C1E,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAgD,eAAA,CAAAkB,QAAA,CAA0B;IAME1E,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAgD,eAAA,CAAAa,MAAA,CAAwB;IAG9BrE,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAiB,UAAA,SAAAuC,eAAA,CAAA3C,KAAA,CAAuB;IAS3Bb,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAiB,UAAA,SAAAwB,MAAA,CAAAkB,eAAA,CAAAH,eAAA,CAAAI,QAAA,EAAAL,MAAA,KAAsD;;;;;IA0BhFvD,EAAA,CAAAC,cAAA,SAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAdH,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAAQ,iBAAA,CAAAmE,OAAA,CAAS;;;;;IANjD3E,EAFJ,CAAAC,cAAA,mBAA2E,sBACxD,kBACqD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClGH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IACtCF,EADsC,CAAAG,YAAA,EAAiB,EACrC;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjCH,EAAA,CAAAC,cAAA,aAA0B;IACxBD,EAAA,CAAAc,UAAA,KAAA8D,4CAAA,iBAAsC;IAG5C5E,EAFI,CAAAG,YAAA,EAAK,EACY,EACV;;;;IATmBH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,WAAA,qBAAAwE,YAAA,CAAAtE,KAAA,CAAyC;IAACP,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAQ,iBAAA,CAAAqE,YAAA,CAAApE,IAAA,CAAmB;IACvET,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,iBAAA,CAAAqE,YAAA,CAAAnE,KAAA,CAAoB;IAGjCV,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAqE,YAAA,CAAAlE,WAAA,CAA0B;IAEPX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAiB,UAAA,YAAA4D,YAAA,CAAAC,IAAA,CAAgB;;;;;IAmBpC9E,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGnBH,EAFJ,CAAAC,cAAA,cAA4B,cACG,eACjB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAc;IACtBF,EADsB,CAAAG,YAAA,EAAO,EACvB;IACwBH,EAA9B,CAAAC,cAAA,cAA8B,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IACtDF,EADsD,CAAAG,YAAA,EAAO,EAAM,EAC7D;IACNH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAClDF,EADkD,CAAAG,YAAA,EAAI,EAChD;;;;IATAH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAQ,iBAAA,CAAAuE,OAAA,CAAA1B,IAAA,CAAc;IAIRrD,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAQ,iBAAA,CAAAuE,OAAA,CAAA1D,IAAA,CAAc;IAEcrB,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAQ,iBAAA,CAAAuE,OAAA,CAAAV,MAAA,CAAgB;IAE3BrE,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAQ,iBAAA,CAAAuE,OAAA,CAAApE,WAAA,CAAqB;;;;;IAiChDX,EADF,CAAAC,cAAA,cAAgE,aACtD;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAC7BF,EAD6B,CAAAG,YAAA,EAAI,EAC3B;;;;IAFIH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAQ,iBAAA,CAAAwE,UAAA,CAAAC,GAAA,CAAgB;IACrBjF,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAwE,UAAA,CAAArE,WAAA,CAAwB;;;AAuTjD,OAAM,MAAOuE,iBAAiB;EA7mB9BC,YAAA;IA8mBE,KAAAzC,aAAa,GAAa,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,CAAC;IAEpE,KAAA0C,WAAW,GAAG,CACZ;MACE1E,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,6DAA6D;MAC1EF,IAAI,EAAE,UAAU;MAChBF,KAAK,EAAE;KACR,EACD;MACEG,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,8DAA8D;MAC3EF,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,EACD;MACEG,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,wDAAwD;MACrEF,IAAI,EAAE,0BAA0B;MAChCF,KAAK,EAAE;KACR,EACD;MACEG,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,2DAA2D;MACxEF,IAAI,EAAE,MAAM;MACZF,KAAK,EAAE;KACR,CACF;IAED,KAAA8E,mBAAmB,GAAG,CACpB;MACE3E,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE,wBAAwB;MACrCF,IAAI,EAAE,MAAM;MACZO,QAAQ,EAAE;;4BAEY;MACtBH,KAAK,EAAE;KACR,EACD;MACEH,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,sBAAsB;MACnCF,IAAI,EAAE,OAAO;MACbO,QAAQ,EAAE;;SAEP;MACHH,KAAK,EAAE;KACR,EACD;MACEH,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,0BAA0B;MACvCF,IAAI,EAAE,UAAU;MAChBO,QAAQ,EAAE,qDAAqD;MAC/DH,KAAK,EAAE;KACR,CACF;IAED,KAAAyE,WAAW,GAAiB,CAC1B;MACEjC,IAAI,EAAE,MAAM;MACZ1C,WAAW,EAAE,+CAA+C;MAC5D2C,KAAK,EAAE,yBAAyB;MAChCd,OAAO,EAAE,CACP;QAAErB,IAAI,EAAE,SAAS;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,6BAA6B;QAAEa,OAAO,EAAE;MAAK,CAAE,EAC/F;QAAEL,IAAI,EAAE,UAAU;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,wCAAwC;QAAEa,OAAO,EAAE;MAAO,CAAE,EAC7G;QAAEL,IAAI,EAAE,UAAU;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE;MAAkB,CAAE,EACrE;QAAEQ,IAAI,EAAE,YAAY;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,wBAAwB;QAAEa,OAAO,EAAE;MAAQ,CAAE,EAChG;QAAEL,IAAI,EAAE,aAAa;QAAEE,IAAI,EAAE,SAAS;QAAEV,WAAW,EAAE,8BAA8B;QAAEa,OAAO,EAAE;MAAM,CAAE,CACvG;MACDwB,QAAQ,EAAE,CACR;QACEF,OAAO,EAAE,sBAAsB;QAC/BnC,WAAW,EAAE,uCAAuC;QACpDiC,MAAM,EAAE;;;;;OAKT,EACD;QACEE,OAAO,EAAE,+CAA+C;QACxDnC,WAAW,EAAE;OACd,CACF;MACDE,KAAK,EAAE;KACR,EACD;MACEwC,IAAI,EAAE,OAAO;MACb1C,WAAW,EAAE,sCAAsC;MACnD2C,KAAK,EAAE,0BAA0B;MACjCd,OAAO,EAAE,CACP;QAAErB,IAAI,EAAE,mBAAmB;QAAEE,IAAI,EAAE,SAAS;QAAEV,WAAW,EAAE,0BAA0B;QAAEa,OAAO,EAAE;MAAO,CAAE,EACzG;QAAEL,IAAI,EAAE,eAAe;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,oBAAoB;QAAEa,OAAO,EAAE;MAAqB,CAAE,EAC5G;QAAEL,IAAI,EAAE,YAAY;QAAEE,IAAI,EAAE,QAAQ;QAAEV,WAAW,EAAE,iBAAiB;QAAEa,OAAO,EAAE;MAAU,CAAE,CAC5F;MACDwB,QAAQ,EAAE,CACR;QACEF,OAAO,EAAE,uCAAuC;QAChDnC,WAAW,EAAE;OACd;KAEJ,EACD;MACE0C,IAAI,EAAE,OAAO;MACb1C,WAAW,EAAE,8BAA8B;MAC3C2C,KAAK,EAAE,uCAAuC;MAC9Cd,OAAO,EAAE,CACP;QAAErB,IAAI,EAAE,OAAO;QAAEE,IAAI,EAAE,SAAS;QAAEV,WAAW,EAAE,qCAAqC;QAAEa,OAAO,EAAE;MAAO,CAAE,CACzG;MACDwB,QAAQ,EAAE,CACR;QACEF,OAAO,EAAE,sBAAsB;QAC/BnC,WAAW,EAAE;OACd,EACD;QACEmC,OAAO,EAAE,eAAe;QACxBnC,WAAW,EAAE;OACd;KAEJ,CACF;IAED,KAAA4E,mBAAmB,GAAG,CACpB;MACE3B,QAAQ,EAAE,gBAAgB;MAC1BjD,WAAW,EAAE,4CAA4C;MACzDF,IAAI,EAAE,0BAA0B;MAChCiE,QAAQ,EAAE,gCAAgC;MAC1CL,MAAM,EAAE;;;;;;;;;;;;;;;;;;;qCAmBuB;MAC/BxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,WAAW;MACrBjD,WAAW,EAAE,0CAA0C;MACvDF,IAAI,EAAE,0BAA0B;MAChCiE,QAAQ,EAAE,gBAAgB;MAC1BL,MAAM,EAAE;;;;;;;;;;;;;;;WAeH;MACLxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,cAAc;MACxBjD,WAAW,EAAE,0CAA0C;MACvDF,IAAI,EAAE,OAAO;MACbiE,QAAQ,EAAE,qBAAqB;MAC/BL,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAkEY;MACpBxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,eAAe;MACzBjD,WAAW,EAAE,2CAA2C;MACxDF,IAAI,EAAE,aAAa;MACnBiE,QAAQ,EAAE,eAAe;MACzBL,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAsEc;MACtBxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,kBAAkB;MAC5BjD,WAAW,EAAE,gDAAgD;MAC7DF,IAAI,EAAE,cAAc;MACpBiE,QAAQ,EAAE,6BAA6B;MACvCL,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDA4J2C;MACnDxD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,QAAQ;MAClBjD,WAAW,EAAE,qDAAqD;MAClEF,IAAI,EAAE,iBAAiB;MACvBiE,QAAQ,EAAE,YAAY;MACtBL,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2EAyC6D;MACrExD,KAAK,EAAE;KACR,EACD;MACE+C,QAAQ,EAAE,SAAS;MACnBjD,WAAW,EAAE,uCAAuC;MACpDF,IAAI,EAAE,OAAO;MACbiE,QAAQ,EAAE,aAAa;MACvBL,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6HZ;MACIxD,KAAK,EAAE;KACR,CACF;IAED,KAAA2E,iBAAiB,GAAG,CAClB;MACE9E,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,sHAAsH;MACnIF,IAAI,EAAE,OAAO;MACbF,KAAK,EAAE,SAAS;MAChBuE,IAAI,EAAE,CACJ,sCAAsC,EACtC,2CAA2C,EAC3C,6CAA6C,EAC7C,+CAA+C;KAElD,EACD;MACEpE,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,uFAAuF;MACpGF,IAAI,EAAE,SAAS;MACfF,KAAK,EAAE,SAAS;MAChBuE,IAAI,EAAE,CACJ,2CAA2C,EAC3C,gCAAgC,EAChC,8BAA8B,EAC9B,+CAA+C;KAElD,EACD;MACEpE,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,oFAAoF;MACjGF,IAAI,EAAE,OAAO;MACbF,KAAK,EAAE,SAAS;MAChBuE,IAAI,EAAE,CACJ,2CAA2C,EAC3C,6CAA6C,EAC7C,oCAAoC,EACpC,yCAAyC;KAE5C,EACD;MACEpE,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,2FAA2F;MACxGF,IAAI,EAAE,MAAM;MACZF,KAAK,EAAE,SAAS;MAChBuE,IAAI,EAAE,CACJ,+CAA+C,EAC/C,6CAA6C,EAC7C,iDAAiD,EACjD,4CAA4C;KAE/C,EACD;MACEpE,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,kFAAkF;MAC/FF,IAAI,EAAE,0BAA0B;MAChCF,KAAK,EAAE,SAAS;MAChBuE,IAAI,EAAE,CACJ,kCAAkC,EAClC,wCAAwC,EACxC,8BAA8B,EAC9B,sCAAsC;KAEzC,EACD;MACEpE,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,6EAA6E;MAC1FF,IAAI,EAAE,YAAY;MAClBF,KAAK,EAAE,SAAS;MAChBuE,IAAI,EAAE,CACJ,kCAAkC,EAClC,iCAAiC,EACjC,kCAAkC,EAClC,yCAAyC;KAE5C,CACF;IAED,KAAAW,kBAAkB,GAAG,CACnB;MACEpC,IAAI,EAAE,yBAAyB;MAC/BhC,IAAI,EAAE,uBAAuB;MAC7BgD,MAAM,EAAE;;;;;;;;kCAQoB;MAC5B1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,qBAAqB;MAC3BhC,IAAI,EAAE,uBAAuB;MAC7BgD,MAAM,EAAE;;;;;;;;iCAQmB;MAC3B1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,wBAAwB;MAC9BhC,IAAI,EAAE,uBAAuB;MAC7BgD,MAAM,EAAE;;;;;;;;;;gCAUkB;MAC1B1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,sBAAsB;MAC5BhC,IAAI,EAAE,oBAAoB;MAC1BgD,MAAM,EAAE;;;;;;;;;;uEAUyD;MACjE1D,WAAW,EAAE;KACd,EACD;MACE0C,IAAI,EAAE,4BAA4B;MAClChC,IAAI,EAAE,sBAAsB;MAC5BgD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAsBY;MACpB1D,WAAW,EAAE;KACd,CACF;IAED,KAAA+E,aAAa,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BhB;IAEA,KAAAC,aAAa,GAAG,CACd;MACEV,GAAG,EAAE,iBAAiB;MACtBtE,WAAW,EAAE;KACd,EACD;MACEsE,GAAG,EAAE,6BAA6B;MAClCtE,WAAW,EAAE;KACd,EACD;MACEsE,GAAG,EAAE,eAAe;MACpBtE,WAAW,EAAE;KACd,EACD;MACEsE,GAAG,EAAE,gBAAgB;MACrBtE,WAAW,EAAE;KACd,EACD;MACEsE,GAAG,EAAE,eAAe;MACpBtE,WAAW,EAAE;KACd,EACD;MACEsE,GAAG,EAAE,cAAc;MACnBtE,WAAW,EAAE;KACd,CACF;IAED,KAAAiF,YAAY,GAAgC;MAC1C,gBAAgB,EAAE,CAChB,yCAAyC,EACzC,mDAAmD,EACnD,kDAAkD,EAClD,qCAAqC,CACtC;MACD,WAAW,EAAE,CACX,8CAA8C,EAC9C,mCAAmC,EACnC,iDAAiD,EACjD,6CAA6C,CAC9C;MACD,cAAc,EAAE,CACd,kCAAkC,EAClC,sDAAsD,EACtD,iDAAiD,EACjD,2CAA2C,CAC5C;MACD,eAAe,EAAE,CACf,8CAA8C,EAC9C,iDAAiD,EACjD,4CAA4C,EAC5C,+BAA+B,CAChC;MACD,kBAAkB,EAAE,CAClB,+CAA+C,EAC/C,6CAA6C,EAC7C,yCAAyC,EACzC,4CAA4C,CAC7C;MACD,QAAQ,EAAE,CACR,2CAA2C,EAC3C,iCAAiC,EACjC,sCAAsC,EACtC,4CAA4C,CAC7C;MACD,SAAS,EAAE,CACT,4CAA4C,EAC5C,wCAAwC,EACxC,uCAAuC,EACvC,iCAAiC;KAEpC;;EAEDxB,eAAeA,CAACyB,IAAY;IAC1BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;MAC5C;MACAC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAClD,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAG;MACbH,OAAO,CAACI,KAAK,CAAC,kBAAkB,EAAED,GAAG,CAAC;IACxC,CAAC,CAAC;EACJ;EAEA1C,eAAeA,CAACC,QAAgB;IAC9B,OAAO,IAAI,CAACgC,YAAY,CAAChC,QAAQ,CAAC,IAAI,EAAE;EAC1C;;;uCA37BWsB,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5lBpB7G,EAHN,CAAAC,cAAA,aAAiC,aACN,SACnB,eACQ;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAyB;UACvBD,EAAA,CAAAE,MAAA,yDACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAKAH,EAHN,CAAAC,cAAA,aAA0B,kBACQ,uBACb,mBACW;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzCH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,oDAA4C;UACjEF,EADiE,CAAAG,YAAA,EAAoB,EACnE;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAE,MAAA,qIAA6H;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpIH,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAAc,UAAA,KAAAiG,iCAAA,iBAAyD;UAUjE/G,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;UAMAH,EAJN,CAAAC,cAAA,wBAA0D,kBAE1B,eACH,UACnB;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7BH,EAAA,CAAAC,cAAA,eAAkC;UAChCD,EAAA,CAAAc,UAAA,KAAAkG,sCAAA,wBAAyE;UAsB/EhH,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA0B,eACC,UACnB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sEAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAErEH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAc,UAAA,KAAAmG,iDAAA,mCAEwB;UAiF9BjH,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAAmC,eACR,UACnB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oFAA4E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGjFH,EADF,CAAAC,cAAA,eAAkC,yBACa;UAC3CD,EAAA,CAAAc,UAAA,KAAAoG,iDAAA,mCAGuB;UAsD3BlH,EADE,CAAAG,YAAA,EAAgB,EACZ;UAIJH,EADF,CAAAC,cAAA,eAAoC,UAC9B;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAc,UAAA,KAAAqG,sCAAA,wBAA2E;UAa/EnH,EADE,CAAAG,YAAA,EAAM,EACF;UAIJH,EADF,CAAAC,cAAA,eAAiC,UAC3B;UAAAD,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGxCH,EAFJ,CAAAC,cAAA,oBAAkC,uBACf,mBACW;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1DH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACtDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,gDAAwC;UAC7DF,EAD6D,CAAAG,YAAA,EAAoB,EAC/D;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACU;UACxBD,EAAA,CAAAc,UAAA,KAAAsG,iCAAA,mBAAgE;UAgB5EpH,EALU,CAAAG,YAAA,EAAM,EACW,EACV,EACP,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA+B,eACJ,UACnB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2EAAmE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAItEH,EAFJ,CAAAC,cAAA,oBAA8B,uBACX,mBACW;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACnDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACpCF,EADoC,CAAAG,YAAA,EAAoB,EACtC;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACQ,eACG,gBACb;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;UACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAChCF,EADgC,CAAAG,YAAA,EAAO,EAAM,EACvC;UAEJH,EADF,CAAAC,cAAA,eAAgC,UAC1B;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAc,UAAA,KAAAuG,iCAAA,kBAAgE;UAWhFrH,EAPc,CAAAG,YAAA,EAAM,EACF,EACW,EACV,EACP,EACE,EACI,EACZ;;;UA5R6CH,EAAA,CAAAI,SAAA,IAAc;UAAdJ,EAAA,CAAAiB,UAAA,YAAA6F,GAAA,CAAA1B,WAAA,CAAc;UAmBNpF,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,YAAA6F,GAAA,CAAAzB,mBAAA,CAAsB;UAgCjDrF,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAiB,UAAA,YAAA6F,GAAA,CAAAxB,WAAA,CAAc;UA6FRtF,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAiB,UAAA,YAAA6F,GAAA,CAAAvB,mBAAA,CAAwB;UA8DGvF,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAiB,UAAA,YAAA6F,GAAA,CAAAtB,iBAAA,CAAoB;UA0B5BxF,EAAA,CAAAI,SAAA,IAAqB;UAArBJ,EAAA,CAAAiB,UAAA,YAAA6F,GAAA,CAAArB,kBAAA,CAAqB;UAoCrDzF,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAQ,iBAAA,CAAAsG,GAAA,CAAApB,aAAA,CAAmB;UAKkB1F,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,YAAA6F,GAAA,CAAAnB,aAAA,CAAgB;;;qBAhT9EnG,YAAY,EAAA8H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/H,YAAY,EACZC,aAAa,EAAA+H,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACbhI,aAAa,EAAAiI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACbtI,aAAa,EAAAuI,EAAA,CAAAC,OAAA,EACbvI,kBAAkB,EAAAwI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,sBAAA,EAAAJ,EAAA,CAAAK,4BAAA,EAClB5I,cAAc,EAAA6I,EAAA,CAAAC,OAAA,EACd7I,cAAc,EAAA8I,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}