{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/checkbox\";\nfunction RegisterComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username must be at least 3 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_spinner_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 42);\n  }\n}\nfunction RegisterComponent_span_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control) {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return {\n      'passwordMismatch': true\n    };\n  }\n  return null;\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.isLoading = false;\n    this.registerForm = this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: passwordMatchValidator\n    });\n  }\n  onSubmit() {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      const userData = {\n        username: this.registerForm.value.username,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword\n      };\n      this.authService.register(userData).subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate(['/dashboard']);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Registration failed', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 139,\n      vars: 20,\n      consts: [[1, \"register-container\"], [1, \"background-pattern\"], [1, \"app-header\"], [1, \"header-content\"], [1, \"brand\"], [1, \"brand-icon\"], [1, \"brand-text\"], [1, \"header-actions\"], [\"mat-button\", \"\", \"routerLink\", \"/login\", 1, \"header-link\"], [1, \"register-content\"], [1, \"register-card-wrapper\"], [1, \"register-card\"], [1, \"register-header\"], [1, \"logo\"], [1, \"logo-icon-wrapper\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"subtitle\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"form-options\"], [\"formControlName\", \"agreeToTerms\"], [\"href\", \"#\", 1, \"terms-link\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"register-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"card-actions\"], [\"routerLink\", \"/login\", 1, \"login-link\"], [1, \"features-section\"], [1, \"features-header\"], [1, \"features-icon\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"feature-content\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"header\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"shield\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"SPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵtext(11, \" Sign In \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"mat-card\", 11)(15, \"mat-card-header\", 12)(16, \"div\", 13)(17, \"div\", 14)(18, \"mat-icon\", 15);\n          i0.ɵɵtext(19, \"security\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"h1\");\n          i0.ɵɵtext(22, \"Create Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 17);\n          i0.ɵɵtext(24, \"Join SPT Security Platform\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"mat-card-content\")(26, \"form\", 18);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_26_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(27, \"div\", 19)(28, \"mat-form-field\", 20)(29, \"mat-label\");\n          i0.ɵɵtext(30, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"input\", 21);\n          i0.ɵɵelementStart(32, \"mat-icon\", 22);\n          i0.ɵɵtext(33, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, RegisterComponent_mat_error_34_Template, 2, 0, \"mat-error\", 23)(35, RegisterComponent_mat_error_35_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"mat-form-field\", 20)(37, \"mat-label\");\n          i0.ɵɵtext(38, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"input\", 24);\n          i0.ɵɵelementStart(40, \"mat-icon\", 22);\n          i0.ɵɵtext(41, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, RegisterComponent_mat_error_42_Template, 2, 0, \"mat-error\", 23)(43, RegisterComponent_mat_error_43_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"mat-form-field\", 20)(45, \"mat-label\");\n          i0.ɵɵtext(46, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(47, \"input\", 25);\n          i0.ɵɵelementStart(48, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_48_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(49, \"mat-icon\");\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(51, RegisterComponent_mat_error_51_Template, 2, 0, \"mat-error\", 23)(52, RegisterComponent_mat_error_52_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"mat-form-field\", 20)(54, \"mat-label\");\n          i0.ɵɵtext(55, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"input\", 27);\n          i0.ɵɵelementStart(57, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_57_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(58, \"mat-icon\");\n          i0.ɵɵtext(59);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(60, RegisterComponent_mat_error_60_Template, 2, 0, \"mat-error\", 23)(61, RegisterComponent_mat_error_61_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 28)(63, \"mat-checkbox\", 29);\n          i0.ɵɵtext(64, \" I agree to the \");\n          i0.ɵɵelementStart(65, \"a\", 30);\n          i0.ɵɵtext(66, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \" and \");\n          i0.ɵɵelementStart(68, \"a\", 30);\n          i0.ɵɵtext(69, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(70, \"div\", 31)(71, \"button\", 32);\n          i0.ɵɵtemplate(72, RegisterComponent_mat_spinner_72_Template, 1, 0, \"mat-spinner\", 33)(73, RegisterComponent_span_73_Template, 2, 0, \"span\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"mat-card-actions\", 34)(75, \"p\");\n          i0.ɵɵtext(76, \"Already have an account? \");\n          i0.ɵɵelementStart(77, \"a\", 35);\n          i0.ɵɵtext(78, \"Sign in\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(79, \"div\", 36)(80, \"div\", 37)(81, \"mat-icon\", 38);\n          i0.ɵɵtext(82, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"h2\");\n          i0.ɵɵtext(84, \"Security Features\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 39)(86, \"div\", 40)(87, \"mat-icon\");\n          i0.ɵɵtext(88, \"analytics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 41)(90, \"h3\");\n          i0.ɵɵtext(91, \"Smart Contract Analysis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"p\");\n          i0.ɵɵtext(93, \"Advanced static analysis for solidity contracts\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"div\", 39)(95, \"div\", 40)(96, \"mat-icon\");\n          i0.ɵɵtext(97, \"visibility\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 41)(99, \"h3\");\n          i0.ɵɵtext(100, \"Real-time Monitoring\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"p\");\n          i0.ɵɵtext(102, \"Continuous security monitoring\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(103, \"div\", 39)(104, \"div\", 40)(105, \"mat-icon\");\n          i0.ɵɵtext(106, \"assessment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\", 41)(108, \"h3\");\n          i0.ɵɵtext(109, \"Comprehensive Analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"p\");\n          i0.ɵɵtext(111, \"Detailed security reports and insights\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(112, \"div\", 39)(113, \"div\", 40)(114, \"mat-icon\");\n          i0.ɵɵtext(115, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(116, \"div\", 41)(117, \"h3\");\n          i0.ɵɵtext(118, \"Enterprise Security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"p\");\n          i0.ɵɵtext(120, \"Professional-grade protection\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(121, \"div\", 39)(122, \"div\", 40)(123, \"mat-icon\");\n          i0.ɵɵtext(124, \"integration_instructions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(125, \"div\", 41)(126, \"h3\");\n          i0.ɵɵtext(127, \"Easy Integration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"p\");\n          i0.ɵɵtext(129, \"Seamless API and SDK integration\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(130, \"div\", 39)(131, \"div\", 40)(132, \"mat-icon\");\n          i0.ɵɵtext(133, \"devices\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(134, \"div\", 41)(135, \"h3\");\n          i0.ɵɵtext(136, \"Multi-platform Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"p\");\n          i0.ɵɵtext(138, \"Works across all major platforms\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_9_0;\n          let tmp_10_0;\n          let tmp_15_0;\n          let tmp_16_0;\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_2_0.hasError(\"email\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_3_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_4_0.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_9_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_10_0.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hideConfirmPassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_15_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_15_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.registerForm.hasError(\"passwordMismatch\") && ((tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, MatInputModule, i8.MatInput, MatButtonModule, i9.MatButton, i9.MatIconButton, MatIconModule, i10.MatIcon, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule, MatCheckboxModule, i12.MatCheckbox],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  position: relative;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  overflow-x: hidden;\\n}\\n\\n.background-pattern[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px), radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);\\n  background-size: 50px 50px;\\n  background-position: 0 0, 25px 25px;\\n  pointer-events: none;\\n}\\n\\n.app-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 1rem 2rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 1.5rem;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n\\n.header-link[_ngcontent-%COMP%] {\\n  color: white !important;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 8px;\\n  padding: 0.5rem 1rem;\\n  transition: all 0.3s ease;\\n}\\n.header-link[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.register-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 2rem;\\n  gap: 3rem;\\n  align-items: flex-start;\\n}\\n\\n.register-card-wrapper[_ngcontent-%COMP%] {\\n  flex: 0 0 400px;\\n  max-width: 400px;\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 32px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 20px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.register-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.logo-icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 60px;\\n  height: 60px;\\n  background: rgba(102, 126, 234, 0.1);\\n  border-radius: 12px;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: #667eea;\\n}\\n\\n.logo-text[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n\\n.logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.logo-text[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  margin: 4px 0 0 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-options[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.terms-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n}\\n\\n.terms-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  margin-top: 25px;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n.register-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.card-actions[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 14px;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 600;\\n}\\n\\n.login-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.features-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n  padding: 20px;\\n}\\n\\n.features-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 30px;\\n}\\n.features-header[_ngcontent-%COMP%]   .features-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.features-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 20px;\\n  margin-bottom: 16px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateY(-2px);\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 48px;\\n  height: 48px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 10px;\\n  flex-shrink: 0;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  color: white;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: white;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  color: rgba(255, 255, 255, 0.8);\\n  line-height: 1.4;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 30px;\\n    padding: 1rem;\\n  }\\n  .register-card-wrapper[_ngcontent-%COMP%] {\\n    flex: none;\\n    max-width: 100%;\\n  }\\n  .register-card[_ngcontent-%COMP%] {\\n    padding: 30px 20px;\\n  }\\n  .features-section[_ngcontent-%COMP%] {\\n    order: -1;\\n    text-align: center;\\n  }\\n  .logo[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .logo-text[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .brand[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n}\\nmat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\nmat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\nmat-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "passwordMatchValidator", "control", "password", "get", "confirmPassword", "value", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "snackBar", "hidePassword", "hideConfirmPassword", "isLoading", "registerForm", "group", "username", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "agreeToTerms", "requiredTrue", "validators", "onSubmit", "valid", "userData", "register", "subscribe", "next", "open", "duration", "navigate", "error", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_26_listener", "ɵɵtemplate", "RegisterComponent_mat_error_34_Template", "RegisterComponent_mat_error_35_Template", "RegisterComponent_mat_error_42_Template", "RegisterComponent_mat_error_43_Template", "RegisterComponent_Template_button_click_48_listener", "RegisterComponent_mat_error_51_Template", "RegisterComponent_mat_error_52_Template", "RegisterComponent_Template_button_click_57_listener", "RegisterComponent_mat_error_60_Template", "RegisterComponent_mat_error_61_Template", "RegisterComponent_mat_spinner_72_Template", "RegisterComponent_span_73_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "tmp_3_0", "tmp_4_0", "ɵɵtextInterpolate", "tmp_9_0", "tmp_10_0", "tmp_15_0", "tmp_16_0", "touched", "invalid", "i5", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatButton", "MatIconButton", "i10", "MatIcon", "i11", "MatProgressSpinner", "i12", "MatCheckbox", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\register.component.ts", "D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\components\\auth\\register.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { AuthService, RegisterRequest } from '../../services/auth.service';\n\n// Custom validator for password confirmation\nfunction passwordMatchValidator(control: AbstractControl): {[key: string]: any} | null {\n  const password = control.get('password');\n  const confirmPassword = control.get('confirmPassword');\n  \n  if (password && confirmPassword && password.value !== confirmPassword.value) {\n    return { 'passwordMismatch': true };\n  }\n  return null;\n}\n\n@Component({\n  selector: 'app-register',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.scss']\n})\nexport class RegisterComponent {\n  registerForm: FormGroup;\n  hidePassword = true;\n  hideConfirmPassword = true;\n  isLoading = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, { validators: passwordMatchValidator });\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n\n      const userData: RegisterRequest = {\n        username: this.registerForm.value.username,\n        email: this.registerForm.value.email,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword\n      };\n\n      this.authService.register(userData).subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', { duration: 3000 });\n          this.router.navigate(['/dashboard']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.snackBar.open(error.message || 'Registration failed', 'Close', { duration: 5000 });\n        }\n      });\n    }\n  }\n}\n", "<div class=\"register-container\">\n  <!-- Background Pattern -->\n  <div class=\"background-pattern\"></div>\n\n  <!-- Header -->\n  <header class=\"app-header\">\n    <div class=\"header-content\">\n      <div class=\"brand\">\n        <mat-icon class=\"brand-icon\">shield</mat-icon>\n        <span class=\"brand-text\">SPT</span>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-button routerLink=\"/login\" class=\"header-link\">\n          Sign In\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <div class=\"register-content\">\n    <div class=\"register-card-wrapper\">\n      <mat-card class=\"register-card\">\n        <mat-card-header class=\"register-header\">\n          <div class=\"logo\">\n            <div class=\"logo-icon-wrapper\">\n              <mat-icon class=\"logo-icon\">security</mat-icon>\n            </div>\n            <div class=\"logo-text\">\n              <h1>Create Account</h1>\n              <p class=\"subtitle\">Join SPT Security Platform</p>\n            </div>\n          </div>\n        </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n          <div class=\"form-fields\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Email</mat-label>\n              <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n              <mat-icon matSuffix>email</mat-icon>\n              <mat-error *ngIf=\"registerForm.get('email')?.hasError('required')\">\n                Email is required\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.get('email')?.hasError('email')\">\n                Please enter a valid email\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Username</mat-label>\n              <input matInput formControlName=\"username\" autocomplete=\"username\">\n              <mat-icon matSuffix>account_circle</mat-icon>\n              <mat-error *ngIf=\"registerForm.get('username')?.hasError('required')\">\n                Username is required\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.get('username')?.hasError('minlength')\">\n                Username must be at least 3 characters\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Password</mat-label>\n              <input matInput \n                     [type]=\"hidePassword ? 'password' : 'text'\" \n                     formControlName=\"password\"\n                     autocomplete=\"new-password\">\n              <button mat-icon-button matSuffix \n                      (click)=\"hidePassword = !hidePassword\" \n                      [attr.aria-label]=\"'Hide password'\" \n                      [attr.aria-pressed]=\"hidePassword\"\n                      type=\"button\">\n                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n              </button>\n              <mat-error *ngIf=\"registerForm.get('password')?.hasError('required')\">\n                Password is required\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.get('password')?.hasError('minlength')\">\n                Password must be at least 8 characters\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Confirm Password</mat-label>\n              <input matInput \n                     [type]=\"hideConfirmPassword ? 'password' : 'text'\" \n                     formControlName=\"confirmPassword\"\n                     autocomplete=\"new-password\">\n              <button mat-icon-button matSuffix \n                      (click)=\"hideConfirmPassword = !hideConfirmPassword\" \n                      [attr.aria-label]=\"'Hide password'\" \n                      [attr.aria-pressed]=\"hideConfirmPassword\"\n                      type=\"button\">\n                <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n              </button>\n              <mat-error *ngIf=\"registerForm.get('confirmPassword')?.hasError('required')\">\n                Please confirm your password\n              </mat-error>\n              <mat-error *ngIf=\"registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched\">\n                Passwords do not match\n              </mat-error>\n            </mat-form-field>\n\n            <div class=\"form-options\">\n              <mat-checkbox formControlName=\"agreeToTerms\">\n                I agree to the <a href=\"#\" class=\"terms-link\">Terms of Service</a> and <a href=\"#\" class=\"terms-link\">Privacy Policy</a>\n              </mat-checkbox>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button mat-raised-button \n                    color=\"primary\" \n                    type=\"submit\" \n                    class=\"register-button\"\n                    [disabled]=\"registerForm.invalid || isLoading\">\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n              <span *ngIf=\"!isLoading\">Create Account</span>\n            </button>\n          </div>\n        </form>\n      </mat-card-content>\n\n      <mat-card-actions class=\"card-actions\">\n        <p>Already have an account? \n          <a routerLink=\"/login\" class=\"login-link\">Sign in</a>\n        </p>\n      </mat-card-actions>\n    </mat-card>\n\n    <!-- Features Section -->\n    <div class=\"features-section\">\n      <div class=\"features-header\">\n        <mat-icon class=\"features-icon\">security</mat-icon>\n        <h2>Security Features</h2>\n      </div>\n\n      <div class=\"feature-card\">\n        <div class=\"feature-icon\">\n          <mat-icon>analytics</mat-icon>\n        </div>\n        <div class=\"feature-content\">\n          <h3>Smart Contract Analysis</h3>\n          <p>Advanced static analysis for solidity contracts</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\">\n        <div class=\"feature-icon\">\n          <mat-icon>visibility</mat-icon>\n        </div>\n        <div class=\"feature-content\">\n          <h3>Real-time Monitoring</h3>\n          <p>Continuous security monitoring</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\">\n        <div class=\"feature-icon\">\n          <mat-icon>assessment</mat-icon>\n        </div>\n        <div class=\"feature-content\">\n          <h3>Comprehensive Analytics</h3>\n          <p>Detailed security reports and insights</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\">\n        <div class=\"feature-icon\">\n          <mat-icon>business</mat-icon>\n        </div>\n        <div class=\"feature-content\">\n          <h3>Enterprise Security</h3>\n          <p>Professional-grade protection</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\">\n        <div class=\"feature-icon\">\n          <mat-icon>integration_instructions</mat-icon>\n        </div>\n        <div class=\"feature-content\">\n          <h3>Easy Integration</h3>\n          <p>Seamless API and SDK integration</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card\">\n        <div class=\"feature-icon\">\n          <mat-icon>devices</mat-icon>\n        </div>\n        <div class=\"feature-content\">\n          <h3>Multi-platform Support</h3>\n          <p>Works across all major platforms</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAyB,gBAAgB;AAEzG,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;IC8BhDC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA6G;IAC3GD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADvG5D;AACA,SAASE,sBAAsBA,CAACC,OAAwB;EACtD,MAAMC,QAAQ,GAAGD,OAAO,CAACE,GAAG,CAAC,UAAU,CAAC;EACxC,MAAMC,eAAe,GAAGH,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC;EAEtD,IAAID,QAAQ,IAAIE,eAAe,IAAIF,QAAQ,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;IAC3E,OAAO;MAAE,kBAAkB,EAAE;IAAI,CAAE;EACrC;EACA,OAAO,IAAI;AACb;AAoBA,OAAM,MAAOC,iBAAiB;EAM5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,SAAS,GAAG,KAAK;IAQf,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACzCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACkC,KAAK,CAAC,CAAC;MACpDlB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9Df,eAAe,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACgC,QAAQ,CAAC,CAAC;MAC5CG,YAAY,EAAE,CAAC,KAAK,EAAE,CAACnC,UAAU,CAACoC,YAAY,CAAC;KAChD,EAAE;MAAEC,UAAU,EAAEvB;IAAsB,CAAE,CAAC;EAC5C;EAEAwB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,YAAY,CAACU,KAAK,EAAE;MAC3B,IAAI,CAACX,SAAS,GAAG,IAAI;MAErB,MAAMY,QAAQ,GAAoB;QAChCT,QAAQ,EAAE,IAAI,CAACF,YAAY,CAACV,KAAK,CAACY,QAAQ;QAC1CG,KAAK,EAAE,IAAI,CAACL,YAAY,CAACV,KAAK,CAACe,KAAK;QACpClB,QAAQ,EAAE,IAAI,CAACa,YAAY,CAACV,KAAK,CAACH,QAAQ;QAC1CE,eAAe,EAAE,IAAI,CAACW,YAAY,CAACV,KAAK,CAACD;OAC1C;MAED,IAAI,CAACK,WAAW,CAACkB,QAAQ,CAACD,QAAQ,CAAC,CAACE,SAAS,CAAC;QAC5CC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACf,SAAS,GAAG,KAAK;UACtB,IAAI,CAACH,QAAQ,CAACmB,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAC3F,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACH,QAAQ,CAACmB,IAAI,CAACG,KAAK,CAACC,OAAO,IAAI,qBAAqB,EAAE,OAAO,EAAE;YAAEH,QAAQ,EAAE;UAAI,CAAE,CAAC;QACzF;OACD,CAAC;IACJ;EACF;;;uCA5CWzB,iBAAiB,EAAAX,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAwC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAAwC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBrC,iBAAiB;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3C9BvD,EAAA,CAAAC,cAAA,aAAgC;UAE9BD,EAAA,CAAAI,SAAA,aAAsC;UAMhCJ,EAHN,CAAAC,cAAA,gBAA2B,aACG,aACP,kBACY;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,UAAG;UAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;UAEJH,EADF,CAAAC,cAAA,aAA4B,iBACiC;UACzDD,EAAA,CAAAE,MAAA,iBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACC;UAQGH,EANZ,CAAAC,cAAA,cAA8B,eACO,oBACD,2BACW,eACrB,eACe,oBACD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACtCF,EADsC,CAAAG,YAAA,EAAW,EAC3C;UAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,aAAoB;UAAAD,EAAA,CAAAE,MAAA,kCAA0B;UAGpDF,EAHoD,CAAAG,YAAA,EAAI,EAC9C,EACF,EACU;UAGlBH,EADF,CAAAC,cAAA,wBAAkB,gBACyC;UAAxBD,EAAA,CAAAyD,UAAA,sBAAAC,qDAAA;YAAA,OAAYF,GAAA,CAAA3B,QAAA,EAAU;UAAA,EAAC;UAGlD7B,EAFJ,CAAAC,cAAA,eAAyB,0BACiC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA0E;UAC1EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAIpCH,EAHA,CAAA2D,UAAA,KAAAC,uCAAA,wBAAmE,KAAAC,uCAAA,wBAGH;UAGlE7D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAAmE;UACnEJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAI7CH,EAHA,CAAA2D,UAAA,KAAAG,uCAAA,wBAAsE,KAAAC,uCAAA,wBAGC;UAGzE/D,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAGmC;UACnCJ,EAAA,CAAAC,cAAA,kBAIsB;UAHdD,EAAA,CAAAyD,UAAA,mBAAAO,oDAAA;YAAA,OAAAR,GAAA,CAAAvC,YAAA,IAAAuC,GAAA,CAAAvC,YAAA;UAAA,EAAsC;UAI5CjB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UAITH,EAHA,CAAA2D,UAAA,KAAAM,uCAAA,wBAAsE,KAAAC,uCAAA,wBAGC;UAGzElE,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAI,SAAA,iBAGmC;UACnCJ,EAAA,CAAAC,cAAA,kBAIsB;UAHdD,EAAA,CAAAyD,UAAA,mBAAAU,oDAAA;YAAA,OAAAX,GAAA,CAAAtC,mBAAA,IAAAsC,GAAA,CAAAtC,mBAAA;UAAA,EAAoD;UAI1DlB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAyD;UACrEF,EADqE,CAAAG,YAAA,EAAW,EACvE;UAITH,EAHA,CAAA2D,UAAA,KAAAS,uCAAA,wBAA6E,KAAAC,uCAAA,wBAGgC;UAG/GrE,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,eAA0B,wBACqB;UAC3CD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAG1HF,EAH0H,CAAAG,YAAA,EAAI,EAC3G,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,eAA0B,kBAK+B;UAErDD,EADA,CAAA2D,UAAA,KAAAW,yCAAA,0BAA6C,KAAAC,kCAAA,mBACpB;UAIjCvE,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACU;UAGjBH,EADF,CAAAC,cAAA,4BAAuC,SAClC;UAAAD,EAAA,CAAAE,MAAA,iCACD;UAAAF,EAAA,CAAAC,cAAA,aAA0C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAGvDF,EAHuD,CAAAG,YAAA,EAAI,EACnD,EACa,EACV;UAKPH,EAFJ,CAAAC,cAAA,eAA8B,eACC,oBACK;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACvBF,EADuB,CAAAG,YAAA,EAAK,EACtB;UAIFH,EAFJ,CAAAC,cAAA,eAA0B,eACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EAC1B;UAEJH,EADF,CAAAC,cAAA,eAA6B,UACvB;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uDAA+C;UAEtDF,EAFsD,CAAAG,YAAA,EAAI,EAClD,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA0B,eACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EAC3B;UAEJH,EADF,CAAAC,cAAA,eAA6B,UACvB;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,uCAA8B;UAErCF,EAFqC,CAAAG,YAAA,EAAI,EACjC,EACF;UAIFH,EAFJ,CAAAC,cAAA,gBAA0B,gBACE,iBACd;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EAC3B;UAEJH,EADF,CAAAC,cAAA,gBAA6B,WACvB;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,+CAAsC;UAE7CF,EAF6C,CAAAG,YAAA,EAAI,EACzC,EACF;UAIFH,EAFJ,CAAAC,cAAA,gBAA0B,gBACE,iBACd;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;UAEJH,EADF,CAAAC,cAAA,gBAA6B,WACvB;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAEpCF,EAFoC,CAAAG,YAAA,EAAI,EAChC,EACF;UAIFH,EAFJ,CAAAC,cAAA,gBAA0B,gBACE,iBACd;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UACpCF,EADoC,CAAAG,YAAA,EAAW,EACzC;UAEJH,EADF,CAAAC,cAAA,gBAA6B,WACvB;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,yCAAgC;UAEvCF,EAFuC,CAAAG,YAAA,EAAI,EACnC,EACF;UAIFH,EAFJ,CAAAC,cAAA,gBAA0B,gBACE,iBACd;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UACnBF,EADmB,CAAAG,YAAA,EAAW,EACxB;UAEJH,EADF,CAAAC,cAAA,gBAA6B,WACvB;UAAAD,EAAA,CAAAE,MAAA,+BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,yCAAgC;UAK7CF,EAL6C,CAAAG,YAAA,EAAI,EACnC,EACF,EACF,EACF,EACF,EAtM0B;;;;;;;;;;;UAmClBH,EAAA,CAAAwE,SAAA,IAA0B;UAA1BxE,EAAA,CAAAyE,UAAA,cAAAjB,GAAA,CAAApC,YAAA,CAA0B;UAMdpB,EAAA,CAAAwE,SAAA,GAAqD;UAArDxE,EAAA,CAAAyE,UAAA,UAAAC,OAAA,GAAAlB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,4BAAAkE,OAAA,CAAAC,QAAA,aAAqD;UAGrD3E,EAAA,CAAAwE,SAAA,EAAkD;UAAlDxE,EAAA,CAAAyE,UAAA,UAAAG,OAAA,GAAApB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,4BAAAoE,OAAA,CAAAD,QAAA,UAAkD;UASlD3E,EAAA,CAAAwE,SAAA,GAAwD;UAAxDxE,EAAA,CAAAyE,UAAA,UAAAI,OAAA,GAAArB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAqE,OAAA,CAAAF,QAAA,aAAwD;UAGxD3E,EAAA,CAAAwE,SAAA,EAAyD;UAAzDxE,EAAA,CAAAyE,UAAA,UAAAK,OAAA,GAAAtB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAsE,OAAA,CAAAH,QAAA,cAAyD;UAQ9D3E,EAAA,CAAAwE,SAAA,GAA2C;UAA3CxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAAvC,YAAA,uBAA2C;UAK1CjB,EAAA,CAAAwE,SAAA,EAAmC;;UAG/BxE,EAAA,CAAAwE,SAAA,GAAkD;UAAlDxE,EAAA,CAAA+E,iBAAA,CAAAvB,GAAA,CAAAvC,YAAA,mCAAkD;UAElDjB,EAAA,CAAAwE,SAAA,EAAwD;UAAxDxE,EAAA,CAAAyE,UAAA,UAAAO,OAAA,GAAAxB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAwE,OAAA,CAAAL,QAAA,aAAwD;UAGxD3E,EAAA,CAAAwE,SAAA,EAAyD;UAAzDxE,EAAA,CAAAyE,UAAA,UAAAQ,QAAA,GAAAzB,GAAA,CAAApC,YAAA,CAAAZ,GAAA,+BAAAyE,QAAA,CAAAN,QAAA,cAAyD;UAQ9D3E,EAAA,CAAAwE,SAAA,GAAkD;UAAlDxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAAtC,mBAAA,uBAAkD;UAKjDlB,EAAA,CAAAwE,SAAA,EAAmC;;UAG/BxE,EAAA,CAAAwE,SAAA,GAAyD;UAAzDxE,EAAA,CAAA+E,iBAAA,CAAAvB,GAAA,CAAAtC,mBAAA,mCAAyD;UAEzDlB,EAAA,CAAAwE,SAAA,EAA+D;UAA/DxE,EAAA,CAAAyE,UAAA,UAAAS,QAAA,GAAA1B,GAAA,CAAApC,YAAA,CAAAZ,GAAA,sCAAA0E,QAAA,CAAAP,QAAA,aAA+D;UAG/D3E,EAAA,CAAAwE,SAAA,EAA+F;UAA/FxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAApC,YAAA,CAAAuD,QAAA,0BAAAQ,QAAA,GAAA3B,GAAA,CAAApC,YAAA,CAAAZ,GAAA,sCAAA2E,QAAA,CAAAC,OAAA,EAA+F;UAiBrGpF,EAAA,CAAAwE,SAAA,IAA8C;UAA9CxE,EAAA,CAAAyE,UAAA,aAAAjB,GAAA,CAAApC,YAAA,CAAAiE,OAAA,IAAA7B,GAAA,CAAArC,SAAA,CAA8C;UACtCnB,EAAA,CAAAwE,SAAA,EAAe;UAAfxE,EAAA,CAAAyE,UAAA,SAAAjB,GAAA,CAAArC,SAAA,CAAe;UACtBnB,EAAA,CAAAwE,SAAA,EAAgB;UAAhBxE,EAAA,CAAAyE,UAAA,UAAAjB,GAAA,CAAArC,SAAA,CAAgB;;;qBDxFjC9B,YAAY,EAAAiG,EAAA,CAAAC,IAAA,EACZjG,mBAAmB,EAAAmD,EAAA,CAAA+C,aAAA,EAAA/C,EAAA,CAAAgD,oBAAA,EAAAhD,EAAA,CAAAiD,eAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAlD,EAAA,CAAAmD,kBAAA,EAAAnD,EAAA,CAAAoD,eAAA,EACnBrG,aAAa,EAAAsG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EACbzG,kBAAkB,EAAA0G,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClB7G,cAAc,EAAA8G,EAAA,CAAAC,QAAA,EACd9G,eAAe,EAAA+G,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfhH,aAAa,EAAAiH,GAAA,CAAAC,OAAA,EACbjH,wBAAwB,EAAAkH,GAAA,CAAAC,kBAAA,EACxBlH,iBAAiB,EACjBC,iBAAiB,EAAAkH,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}