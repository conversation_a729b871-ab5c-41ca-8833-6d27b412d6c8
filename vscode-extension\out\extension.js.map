{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,kDAAuD;AACvD,yCAA4C;AAC5C,uEAAoE;AACpE,mDAA2D;AAC3D,mEAAgE;AAChE,6DAA0D;AAC1D,uEAAoE;AACpE,+CAAqD;AACrD,8CAAwD;AACxD,4CAAuD;AACvD,kDAAmD;AAEnD,IAAI,gBAAkC,CAAC;AACvC,IAAI,SAAuB,CAAC;AAC5B,IAAI,oBAA0C,CAAC;AAC/C,IAAI,kBAAsC,CAAC;AAC3C,IAAI,eAAgC,CAAC;AACrC,IAAI,aAAmC,CAAC;AACxC,IAAI,WAAkC,CAAC;AACvC,IAAI,cAA8B,CAAC;AAEnC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IAErE,mCAAmC;IACnC,aAAa,GAAG,IAAI,8BAAoB,EAAE,CAAC;IAE3C,oCAAoC;IACpC,WAAW,GAAG,IAAI,+BAAqB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IAEhE,wBAAwB;IACxB,SAAS,GAAG,IAAI,qBAAY,CAAC,aAAa,CAAC,CAAC;IAC5C,SAAS,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAEtC,sDAAsD;IACtD,cAAc,GAAG,IAAI,2BAAc,CAAC,WAAW,CAAC,CAAC;IAEjD,oDAAoD;IACpD,eAAe,GAAG,IAAI,wBAAe,CAAC,aAAa,CAAC,CAAC;IAErD,+BAA+B;IAC/B,gBAAgB,GAAG,IAAI,2BAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IAElE,iCAAiC;IACjC,kBAAkB,GAAG,IAAI,4BAAkB,EAAE,CAAC;IAE9C,gCAAgC;IAChC,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,OAAO,CAAC,CAAC;IAEzD,qBAAqB;IACrB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE;QAC7D,gBAAgB,EAAE,oBAAoB;QACtC,eAAe,EAAE,IAAI;KACxB,CAAC,CAAC;IAEH,oBAAoB;IACpB,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1B,qBAAqB;IACrB,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAE3B,uBAAuB;IACvB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;IAE7E,yBAAyB;IACzB,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAE7B,wBAAwB;IACxB,cAAc,CAAC,OAAO,CAAC,CAAC;IAExB,mDAAmD;IACnD,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,EAAE;QAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,oEAAoE,EACpE,SAAS,CACZ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,IAAI,SAAS,KAAK,SAAS,EAAE;gBACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;aACtD;QACL,CAAC,CAAC,CAAC;KACN;SAAM;QACH,oDAAoD;QACpD,IAAI,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YACrC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;SACrD;KACJ;IAED,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,QAAQ,EACR,kBAAkB,EAClB,eAAe,EACf,gBAAgB,EAChB,WAAW,CACd,CAAC;AACN,CAAC;AAzED,4BAyEC;AAED,SAAS,gBAAgB,CAAC,OAAgC;IACtD,yBAAyB;IACzB,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QACvF,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,YAAY,EAAE,CAAC;YACjD,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qCAAqC,CAAC,CAAC;aAC/E;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,wCAAwC,CAAC,CAAC;aAC9E;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;SACrE;IACL,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;QAC7E,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QACrF,6BAA6B;QAC7B,IAAI,CAAC,MAAM,SAAS,CAAC,mBAAmB,EAAE,EAAE;YACxC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,0CAA0C,CAAC,CAAC;YAC7E,OAAO;SACV;QACD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACpC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;YAC3D,OAAO;SACV;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,gBAAgB,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QAC/E,6BAA6B;QAC7B,IAAI,CAAC,MAAM,SAAS,CAAC,mBAAmB,EAAE,EAAE;YACxC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,uCAAuC,CAAC,CAAC;YAC1E,OAAO;SACV;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,MAAM,gBAAgB,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,+BAA+B;IAC/B,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QAC3F,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,mBAAmB,EACnB,iBAAiB,EACjB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,0BAA0B,EAAE,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,yBAAyB;IACzB,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACnF,MAAM,YAAY,GAAG,GAAG,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC;QACzE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,6BAA6B;IAC7B,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACnF,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACnF,oBAAoB,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,KAAK,EAAE,KAAU,EAAE,EAAE;QACzF,MAAM,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,KAAK,EAAE,KAAU,EAAE,EAAE;QAC/F,MAAM,gBAAgB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAC7F,eAAe,CAAC,SAAS,EAAE,CAAC;QAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,MAAM,0BAA0B,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QACrG,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEvF,MAAM,MAAM,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;QACtD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,yBAAyB,EACzB,0BAA0B,CAC7B,CAAC;AACN,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAgC;IACvD,6BAA6B;IAC7B,MAAM,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,gBAAgB,CAAC,CAAC;IAChE,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAChE;QACI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE;QACtC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;QAClC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE;KACvC,EACD,gBAAgB,CACnB,CAAC;IAEF,0BAA0B;IAC1B,MAAM,aAAa,GAAG,IAAI,6BAAa,CAAC,gBAAgB,CAAC,CAAC;IAC1D,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAC1D;QACI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;KAC7C,EACD,aAAa,CAChB,CAAC;IAEF,+BAA+B;IAC/B,MAAM,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;IACpD,MAAM,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,8BAA8B,CACxE;QACI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;KAC7C,EACD,kBAAkB,EAClB,GAAG,EACH,GAAG,CACN,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,kBAAkB,EAClB,eAAe,EACf,oBAAoB,CACvB,CAAC;AACN,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAgC;IACzD,8BAA8B;IAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;QAC/E,IAAI,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YACrC,MAAM,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SACxD;IACL,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;QAChF,IAAI,MAAM,IAAI,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;YACjD,MAAM,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAC/D;IACL,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,KAAK,EAAE,EAAE;QACjF,IAAI,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;YACnC,aAAa,CAAC,OAAO,EAAE,CAAC;YACxB,4CAA4C;YAC5C,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAAE;gBAC7C,eAAe,CAAC,SAAS,EAAE,CAAC;aAC/B;SACJ;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,gBAAgB,EAChB,gBAAgB,EAChB,wBAAwB,CAC3B,CAAC;AACN,CAAC;AAED,SAAS,cAAc,CAAC,OAAgC;IACpD,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CACnD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,GAAG,CACN,CAAC;IAEF,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC;IACrC,aAAa,CAAC,OAAO,GAAG,sDAAsD,CAAC;IAC/E,aAAa,CAAC,OAAO,GAAG,iBAAiB,CAAC;IAC1C,aAAa,CAAC,IAAI,EAAE,CAAC;IAErB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC9C,CAAC;AAED,KAAK,UAAU,0BAA0B;IACrC,kDAAkD;IAClD,gCAAgC;IAChC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;KA0BN,CAAC;AACN,CAAC;AAED,SAAgB,UAAU;IACtB,IAAI,eAAe,EAAE;QACjB,eAAe,CAAC,UAAU,EAAE,CAAC;KAChC;IACD,IAAI,kBAAkB,EAAE;QACpB,kBAAkB,CAAC,OAAO,EAAE,CAAC;KAChC;AACL,CAAC;AAPD,gCAOC"}