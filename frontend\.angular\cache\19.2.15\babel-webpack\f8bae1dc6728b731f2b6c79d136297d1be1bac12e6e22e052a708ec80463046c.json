{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { Chart, registerables } from 'chart.js';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/tabs\";\nconst _c0 = [\"trendsCanvas\"];\nconst _c1 = [\"scoreCanvas\"];\nconst _c2 = [\"volumeCanvas\"];\nfunction SecurityTrendsDashboardComponent_mat_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const period_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", period_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", period_r1.label, \" \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"bug_report\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 31);\n    i0.ɵɵtext(6, \"Total Issues\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"div\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 34)(11, \"mat-icon\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 35)(16, \"div\", 30)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 31);\n    i0.ɵɵtext(20, \"Avg Security Score\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 32)(22, \"div\", 33);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 36);\n    i0.ɵɵelement(25, \"div\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 38)(27, \"div\", 30)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 31);\n    i0.ɵɵtext(31, \"Scan Frequency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 32)(33, \"div\", 33);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 39);\n    i0.ɵɵtext(36, \"scans per day\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 40)(38, \"div\", 30)(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"span\", 31);\n    i0.ɵɵtext(42, \"Overall Trend\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 32)(44, \"div\", 41)(45, \"mat-icon\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.trendsData.summary.totalIssues);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getTrendColor(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend), false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTrendIcon(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.Math.abs(ctx_r1.trendsData.summary.criticalTrend), \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.trendsData.summary.averageScore, \"/100\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.trendsData.summary.averageScore, \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.trendsData.summary.scanFrequency);\n    i0.ɵɵadvance(10);\n    i0.ɵɵclassMap(\"trend-\" + ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTrendIcon(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend) === \"up\" ? \"Increasing\" : ctx_r1.getTrendDirection(ctx_r1.trendsData.summary.criticalTrend) === \"down\" ? \"Improving\" : \"Stable\");\n  }\n}\nfunction SecurityTrendsDashboardComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Security Trends\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Loading trends data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_canvas_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 44, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Security trends chart for \" + ctx_r1.selectedPeriod);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Trends Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No security trends data available for the selected period.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Security Scores\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Loading score data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_canvas_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 44, 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Security scores chart for \" + ctx_r1.selectedPeriod);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Score Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No security score data available for the selected period.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Scan Volume\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Loading volume data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_canvas_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 44, 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Scan volume chart for \" + ctx_r1.selectedPeriod);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Volume Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No scan volume data available for the selected period.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 51);\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Critical issues are increasing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" by \", ctx_r1.trendsData.summary.criticalTrend, \"%. Consider reviewing security practices and increasing scan frequency. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Security is improving!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Critical issues decreased by \", ctx_r1.Math.abs(ctx_r1.trendsData.summary.criticalTrend), \"%. Keep up the good security practices. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 54);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Low security score detected.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Average score is \", ctx_r1.trendsData.summary.averageScore, \"/100. Focus on addressing high and critical severity issues. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\", 55);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"strong\");\n    i0.ɵɵtext(5, \"Low scan frequency.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Consider increasing scan frequency to \", ctx_r1.trendsData.summary.scanFrequency, \" per day for better security monitoring. \");\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Key Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtemplate(7, SecurityTrendsDashboardComponent_div_61_div_7_Template, 7, 1, \"div\", 49)(8, SecurityTrendsDashboardComponent_div_61_div_8_Template, 7, 1, \"div\", 49)(9, SecurityTrendsDashboardComponent_div_61_div_9_Template, 7, 1, \"div\", 49)(10, SecurityTrendsDashboardComponent_div_61_div_10_Template, 7, 1, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.criticalTrend > 10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.criticalTrend < -10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.averageScore < 60);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendsData.summary.scanFrequency < 1);\n  }\n}\nfunction SecurityTrendsDashboardComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"mat-icon\", 57);\n    i0.ɵɵtext(2, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Trends Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start running security scans to see trends and analytics here.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 58)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Start First Scan \");\n    i0.ɵɵelementEnd()();\n  }\n}\n// Register Chart.js components\nChart.register(...registerables);\nexport let SecurityTrendsDashboardComponent = /*#__PURE__*/(() => {\n  class SecurityTrendsDashboardComponent {\n    constructor() {\n      this.trendsData = null;\n      this.showComparison = true;\n      this.autoRefresh = false;\n      this.destroy$ = new Subject();\n      this.trendsChart = null;\n      this.scoreChart = null;\n      this.volumeChart = null;\n      this.selectedPeriod = '30d';\n      this.selectedView = 'trends';\n      this.selectedTabIndex = 0;\n      this.isLoading = false;\n      this.periods = [{\n        value: '7d',\n        label: 'Last 7 Days'\n      }, {\n        value: '30d',\n        label: 'Last 30 Days'\n      }, {\n        value: '90d',\n        label: 'Last 90 Days'\n      }, {\n        value: '1y',\n        label: 'Last Year'\n      }];\n      this.chartViews = [{\n        value: 'trends',\n        label: 'Security Trends',\n        icon: 'trending_up'\n      }, {\n        value: 'scores',\n        label: 'Security Scores',\n        icon: 'grade'\n      }, {\n        value: 'volume',\n        label: 'Scan Volume',\n        icon: 'bar_chart'\n      }];\n    }\n    ngOnInit() {\n      // Component initialization\n    }\n    ngAfterViewInit() {\n      this.createCharts();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.destroyCharts();\n    }\n    destroyCharts() {\n      if (this.trendsChart) {\n        this.trendsChart.destroy();\n        this.trendsChart = null;\n      }\n      if (this.scoreChart) {\n        this.scoreChart.destroy();\n        this.scoreChart = null;\n      }\n      if (this.volumeChart) {\n        this.volumeChart.destroy();\n        this.volumeChart = null;\n      }\n    }\n    createCharts() {\n      this.createTrendsChart();\n      this.createScoreChart();\n      this.createVolumeChart();\n    }\n    createTrendsChart() {\n      if (!this.trendsCanvas?.nativeElement || !this.trendsData) {\n        return;\n      }\n      const ctx = this.trendsCanvas.nativeElement.getContext('2d');\n      if (!ctx) return;\n      if (this.trendsChart) {\n        this.trendsChart.destroy();\n      }\n      const labels = this.trendsData.data.map(point => point.date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      }));\n      const config = {\n        type: 'line',\n        data: {\n          labels,\n          datasets: [{\n            label: 'Critical',\n            data: this.trendsData.data.map(point => point.critical),\n            borderColor: '#dc2626',\n            backgroundColor: 'rgba(220, 38, 38, 0.1)',\n            borderWidth: 3,\n            fill: false,\n            tension: 0.4\n          }, {\n            label: 'High',\n            data: this.trendsData.data.map(point => point.high),\n            borderColor: '#ea580c',\n            backgroundColor: 'rgba(234, 88, 12, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4\n          }, {\n            label: 'Medium',\n            data: this.trendsData.data.map(point => point.medium),\n            borderColor: '#d97706',\n            backgroundColor: 'rgba(217, 119, 6, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4\n          }, {\n            label: 'Low',\n            data: this.trendsData.data.map(point => point.low),\n            borderColor: '#65a30d',\n            backgroundColor: 'rgba(101, 163, 13, 0.1)',\n            borderWidth: 2,\n            fill: false,\n            tension: 0.4\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          interaction: {\n            mode: 'index',\n            intersect: false\n          },\n          plugins: {\n            legend: {\n              display: true,\n              position: 'top',\n              labels: {\n                usePointStyle: true,\n                padding: 20,\n                font: {\n                  family: 'Inter, sans-serif',\n                  size: 12,\n                  weight: 500\n                }\n              }\n            },\n            tooltip: {\n              backgroundColor: 'rgba(0, 0, 0, 0.8)',\n              titleColor: '#ffffff',\n              bodyColor: '#ffffff',\n              borderColor: '#374151',\n              borderWidth: 1,\n              cornerRadius: 8\n            }\n          },\n          scales: {\n            x: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Date',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              grid: {\n                color: '#f3f4f6'\n              }\n            },\n            y: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Number of Issues',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              beginAtZero: true,\n              grid: {\n                color: '#f3f4f6'\n              }\n            }\n          },\n          animation: {\n            duration: 1000,\n            easing: 'easeInOutQuart'\n          }\n        }\n      };\n      this.trendsChart = new Chart(ctx, config);\n    }\n    createScoreChart() {\n      if (!this.scoreCanvas?.nativeElement || !this.trendsData) {\n        return;\n      }\n      const ctx = this.scoreCanvas.nativeElement.getContext('2d');\n      if (!ctx) return;\n      if (this.scoreChart) {\n        this.scoreChart.destroy();\n      }\n      const labels = this.trendsData.data.map(point => point.date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      }));\n      const config = {\n        type: 'bar',\n        data: {\n          labels,\n          datasets: [{\n            label: 'Security Score',\n            data: this.trendsData.data.map(point => point.averageScore),\n            backgroundColor: this.trendsData.data.map(point => point.averageScore >= 80 ? '#10b981' : point.averageScore >= 60 ? '#f59e0b' : point.averageScore >= 40 ? '#ef4444' : '#dc2626'),\n            borderColor: this.trendsData.data.map(point => point.averageScore >= 80 ? '#059669' : point.averageScore >= 60 ? '#d97706' : point.averageScore >= 40 ? '#dc2626' : '#b91c1c'),\n            borderWidth: 1,\n            borderRadius: 4\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          plugins: {\n            legend: {\n              display: false\n            },\n            tooltip: {\n              backgroundColor: 'rgba(0, 0, 0, 0.8)',\n              titleColor: '#ffffff',\n              bodyColor: '#ffffff',\n              borderColor: '#374151',\n              borderWidth: 1,\n              cornerRadius: 8,\n              callbacks: {\n                label: context => {\n                  return `Security Score: ${context.parsed.y}/100`;\n                }\n              }\n            }\n          },\n          scales: {\n            x: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Date',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              grid: {\n                display: false\n              }\n            },\n            y: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Security Score',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              min: 0,\n              max: 100,\n              grid: {\n                color: '#f3f4f6'\n              }\n            }\n          },\n          animation: {\n            duration: 1000,\n            easing: 'easeInOutQuart'\n          }\n        }\n      };\n      this.scoreChart = new Chart(ctx, config);\n    }\n    createVolumeChart() {\n      if (!this.volumeCanvas?.nativeElement || !this.trendsData) {\n        return;\n      }\n      const ctx = this.volumeCanvas.nativeElement.getContext('2d');\n      if (!ctx) return;\n      if (this.volumeChart) {\n        this.volumeChart.destroy();\n      }\n      const labels = this.trendsData.data.map(point => point.date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      }));\n      const config = {\n        type: 'bar',\n        data: {\n          labels,\n          datasets: [{\n            label: 'Scans Performed',\n            data: this.trendsData.data.map(point => point.totalScans),\n            backgroundColor: '#3b82f6',\n            borderColor: '#2563eb',\n            borderWidth: 1,\n            borderRadius: 4\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          plugins: {\n            legend: {\n              display: false\n            },\n            tooltip: {\n              backgroundColor: 'rgba(0, 0, 0, 0.8)',\n              titleColor: '#ffffff',\n              bodyColor: '#ffffff',\n              borderColor: '#374151',\n              borderWidth: 1,\n              cornerRadius: 8\n            }\n          },\n          scales: {\n            x: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Date',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              grid: {\n                display: false\n              }\n            },\n            y: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Number of Scans',\n                font: {\n                  family: 'Inter, sans-serif',\n                  weight: 500\n                }\n              },\n              beginAtZero: true,\n              grid: {\n                color: '#f3f4f6'\n              }\n            }\n          },\n          animation: {\n            duration: 1000,\n            easing: 'easeInOutQuart'\n          }\n        }\n      };\n      this.volumeChart = new Chart(ctx, config);\n    }\n    onPeriodChange(period) {\n      this.selectedPeriod = period;\n      this.refreshData();\n    }\n    onViewChange(view) {\n      this.selectedView = view;\n    }\n    refreshData() {\n      this.isLoading = true;\n      // Simulate data refresh\n      setTimeout(() => {\n        this.createCharts();\n        this.isLoading = false;\n      }, 500);\n    }\n    exportChart() {\n      let chart = null;\n      let filename = '';\n      switch (this.selectedView) {\n        case 'trends':\n          chart = this.trendsChart;\n          filename = 'security-trends';\n          break;\n        case 'scores':\n          chart = this.scoreChart;\n          filename = 'security-scores';\n          break;\n        case 'volume':\n          chart = this.volumeChart;\n          filename = 'scan-volume';\n          break;\n      }\n      if (chart) {\n        const url = chart.toBase64Image();\n        const link = document.createElement('a');\n        link.download = `${filename}-${this.selectedPeriod}.png`;\n        link.href = url;\n        link.click();\n      }\n    }\n    getTrendDirection(value) {\n      if (value > 5) return 'up';\n      if (value < -5) return 'down';\n      return 'flat';\n    }\n    getTrendIcon(direction) {\n      switch (direction) {\n        case 'up':\n          return 'trending_up';\n        case 'down':\n          return 'trending_down';\n        case 'flat':\n          return 'trending_flat';\n      }\n    }\n    getTrendColor(direction, isGoodTrend = false) {\n      if (direction === 'flat') return 'var(--spt-text-secondary)';\n      if (isGoodTrend) {\n        return direction === 'up' ? 'var(--spt-success-600)' : 'var(--spt-error-600)';\n      } else {\n        return direction === 'up' ? 'var(--spt-error-600)' : 'var(--spt-success-600)';\n      }\n    }\n    static {\n      this.ɵfac = function SecurityTrendsDashboardComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SecurityTrendsDashboardComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SecurityTrendsDashboardComponent,\n        selectors: [[\"app-security-trends-dashboard\"]],\n        viewQuery: function SecurityTrendsDashboardComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 7);\n            i0.ɵɵviewQuery(_c1, 7);\n            i0.ɵɵviewQuery(_c2, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trendsCanvas = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scoreCanvas = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.volumeCanvas = _t.first);\n          }\n        },\n        inputs: {\n          trendsData: \"trendsData\",\n          showComparison: \"showComparison\",\n          autoRefresh: \"autoRefresh\"\n        },\n        decls: 63,\n        vars: 24,\n        consts: [[\"trendsCanvas\", \"\"], [\"scoreCanvas\", \"\"], [\"volumeCanvas\", \"\"], [1, \"trends-dashboard\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"dashboard-title\"], [1, \"dashboard-subtitle\"], [1, \"header-controls\"], [\"appearance\", \"outline\", 1, \"period-select\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh Data\", 1, \"action-btn\", 3, \"click\", \"disabled\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export Chart\", 1, \"action-btn\", 3, \"click\"], [\"class\", \"summary-cards\", 4, \"ngIf\"], [1, \"chart-section\"], [1, \"chart-tabs\", 3, \"selectedIndexChange\", \"selectedTabChange\", \"selectedIndex\"], [\"mat-tab-label\", \"\"], [1, \"chart-container\"], [1, \"chart-header\"], [1, \"chart-content\"], [\"class\", \"chart-loading\", 4, \"ngIf\"], [\"class\", \"chart-canvas\", 4, \"ngIf\"], [\"class\", \"chart-empty\", 4, \"ngIf\"], [\"class\", \"insights-panel\", 4, \"ngIf\"], [\"class\", \"empty-dashboard\", 4, \"ngIf\"], [3, \"value\"], [1, \"summary-cards\"], [1, \"summary-card\", \"total-issues\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-content\"], [1, \"metric-value\"], [1, \"metric-trend\"], [1, \"summary-card\", \"security-score\"], [1, \"score-bar\"], [1, \"score-fill\"], [1, \"summary-card\", \"scan-frequency\"], [1, \"metric-label\"], [1, \"summary-card\", \"trend-indicator\"], [1, \"trend-status\"], [1, \"chart-loading\"], [1, \"spinning\"], [1, \"chart-canvas\"], [1, \"chart-empty\"], [1, \"insights-panel\"], [1, \"insights-header\"], [1, \"insights-content\"], [\"class\", \"insight-item\", 4, \"ngIf\"], [1, \"insight-item\"], [1, \"insight-icon\", \"warning\"], [1, \"insight-text\"], [1, \"insight-icon\", \"success\"], [1, \"insight-icon\", \"error\"], [1, \"insight-icon\", \"info\"], [1, \"empty-dashboard\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"]],\n        template: function SecurityTrendsDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h2\", 6)(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"trending_up\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6, \" Security Trends Analysis \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p\", 7);\n            i0.ɵɵtext(8, \"Monitor security metrics and trends over time\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 8)(10, \"mat-form-field\", 9)(11, \"mat-label\");\n            i0.ɵɵtext(12, \"Time Period\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-select\", 10);\n            i0.ɵɵtwoWayListener(\"valueChange\", function SecurityTrendsDashboardComponent_Template_mat_select_valueChange_13_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedPeriod, $event) || (ctx.selectedPeriod = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function SecurityTrendsDashboardComponent_Template_mat_select_selectionChange_13_listener($event) {\n              return ctx.onPeriodChange($event.value);\n            });\n            i0.ɵɵtemplate(14, SecurityTrendsDashboardComponent_mat_option_14_Template, 2, 2, \"mat-option\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function SecurityTrendsDashboardComponent_Template_button_click_16_listener() {\n              return ctx.refreshData();\n            });\n            i0.ɵɵelementStart(17, \"mat-icon\");\n            i0.ɵɵtext(18, \"refresh\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function SecurityTrendsDashboardComponent_Template_button_click_19_listener() {\n              return ctx.exportChart();\n            });\n            i0.ɵɵelementStart(20, \"mat-icon\");\n            i0.ɵɵtext(21, \"download\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(22, SecurityTrendsDashboardComponent_div_22_Template, 49, 13, \"div\", 15);\n            i0.ɵɵelementStart(23, \"div\", 16)(24, \"mat-tab-group\", 17);\n            i0.ɵɵtwoWayListener(\"selectedIndexChange\", function SecurityTrendsDashboardComponent_Template_mat_tab_group_selectedIndexChange_24_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedTabIndex, $event) || (ctx.selectedTabIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectedTabChange\", function SecurityTrendsDashboardComponent_Template_mat_tab_group_selectedTabChange_24_listener($event) {\n              return ctx.onViewChange(ctx.chartViews[$event.index].value);\n            });\n            i0.ɵɵelementStart(25, \"mat-tab\");\n            i0.ɵɵtemplate(26, SecurityTrendsDashboardComponent_ng_template_26_Template, 4, 0, \"ng-template\", 18);\n            i0.ɵɵelementStart(27, \"div\", 19)(28, \"div\", 20)(29, \"h3\");\n            i0.ɵɵtext(30, \"Security Issues Over Time\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"p\");\n            i0.ɵɵtext(32, \"Track the evolution of security issues by severity level\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"div\", 21);\n            i0.ɵɵtemplate(34, SecurityTrendsDashboardComponent_div_34_Template, 5, 0, \"div\", 22)(35, SecurityTrendsDashboardComponent_canvas_35_Template, 2, 1, \"canvas\", 23)(36, SecurityTrendsDashboardComponent_div_36_Template, 7, 0, \"div\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"mat-tab\");\n            i0.ɵɵtemplate(38, SecurityTrendsDashboardComponent_ng_template_38_Template, 4, 0, \"ng-template\", 18);\n            i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 20)(41, \"h3\");\n            i0.ɵɵtext(42, \"Security Score History\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"p\");\n            i0.ɵɵtext(44, \"Monitor overall security score improvements over time\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 21);\n            i0.ɵɵtemplate(46, SecurityTrendsDashboardComponent_div_46_Template, 5, 0, \"div\", 22)(47, SecurityTrendsDashboardComponent_canvas_47_Template, 2, 1, \"canvas\", 23)(48, SecurityTrendsDashboardComponent_div_48_Template, 7, 0, \"div\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(49, \"mat-tab\");\n            i0.ɵɵtemplate(50, SecurityTrendsDashboardComponent_ng_template_50_Template, 4, 0, \"ng-template\", 18);\n            i0.ɵɵelementStart(51, \"div\", 19)(52, \"div\", 20)(53, \"h3\");\n            i0.ɵɵtext(54, \"Scan Activity Volume\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"p\");\n            i0.ɵɵtext(56, \"Track scanning frequency and activity patterns\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"div\", 21);\n            i0.ɵɵtemplate(58, SecurityTrendsDashboardComponent_div_58_Template, 5, 0, \"div\", 22)(59, SecurityTrendsDashboardComponent_canvas_59_Template, 2, 1, \"canvas\", 23)(60, SecurityTrendsDashboardComponent_div_60_Template, 7, 0, \"div\", 24);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(61, SecurityTrendsDashboardComponent_div_61_Template, 11, 4, \"div\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(62, SecurityTrendsDashboardComponent_div_62_Template, 11, 0, \"div\", 26);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(13);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedPeriod);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.periods);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"spinning\", ctx.isLoading);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.trendsData);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"selectedIndex\", ctx.selectedTabIndex);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.trendsData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.trendsData);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.trendsData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.trendsData);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.trendsData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.trendsData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.trendsData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.trendsData);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatSelectModule, i4.MatFormField, i4.MatLabel, i5.MatSelect, i5.MatOption, MatFormFieldModule, MatTabsModule, i6.MatTabLabel, i6.MatTab, i6.MatTabGroup],\n        styles: [\".trends-dashboard[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-6)}.dashboard-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:var(--spt-space-6);background:var(--spt-surface);border-radius:var(--spt-radius-xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm)}.header-content[_ngcontent-%COMP%]{flex:1}.dashboard-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);margin:0 0 var(--spt-space-2) 0;font-size:var(--spt-text-2xl);font-weight:var(--spt-font-bold);color:var(--spt-text-primary)}.dashboard-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px;color:var(--spt-primary-600)}.dashboard-subtitle[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-base);color:var(--spt-text-secondary);line-height:var(--spt-leading-relaxed)}.header-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-4)}.period-select[_ngcontent-%COMP%]{min-width:160px}.period-select[_ngcontent-%COMP%]     .mat-mdc-form-field-wrapper{padding-bottom:0}.header-actions[_ngcontent-%COMP%]{display:flex;gap:var(--spt-space-2)}.action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:var(--spt-radius-lg);color:var(--spt-text-secondary);transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{background:var(--spt-primary-50);color:var(--spt-primary-600);transform:scale(1.05)}.summary-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:var(--spt-space-4)}.summary-card[_ngcontent-%COMP%]{background:var(--spt-surface);border-radius:var(--spt-radius-xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm);padding:var(--spt-space-6);transition:all .3s ease}.summary-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:var(--spt-shadow-md)}.card-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);margin-bottom:var(--spt-space-4)}.card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.summary-card.total-issues[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-error-600)}.summary-card.security-score[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.summary-card.scan-frequency[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-info-600)}.summary-card.trend-indicator[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--spt-success-600)}.card-title[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary);text-transform:uppercase;letter-spacing:.05em}.card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-2)}.metric-value[_ngcontent-%COMP%]{font-size:var(--spt-text-3xl);font-weight:var(--spt-font-bold);color:var(--spt-text-primary);line-height:var(--spt-leading-none)}.metric-trend[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-1);font-size:var(--spt-text-sm);font-weight:var(--spt-font-semibold)}.metric-trend[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.metric-label[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-text-secondary)}.score-bar[_ngcontent-%COMP%]{width:100%;height:8px;background:var(--spt-gray-200);border-radius:var(--spt-radius-full);overflow:hidden}.score-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,var(--spt-error-500) 0%,var(--spt-warning-500) 50%,var(--spt-success-500) 100%);border-radius:var(--spt-radius-full);transition:width .5s ease}.trend-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-2);font-size:var(--spt-text-base);font-weight:var(--spt-font-semibold)}.trend-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}.trend-status.trend-up[_ngcontent-%COMP%]{color:var(--spt-error-600)}.trend-status.trend-down[_ngcontent-%COMP%]{color:var(--spt-success-600)}.trend-status.trend-flat[_ngcontent-%COMP%]{color:var(--spt-text-secondary)}.chart-section[_ngcontent-%COMP%]{background:var(--spt-surface);border-radius:var(--spt-radius-xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm);overflow:hidden}.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab-group{--mdc-tab-indicator-active-indicator-color: var(--spt-primary-600)}.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab{min-width:120px}.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab .mdc-tab__content{display:flex;align-items:center;gap:var(--spt-space-2)}.chart-tabs[_ngcontent-%COMP%]     .mat-mdc-tab-header{border-bottom:1px solid var(--spt-border-light)}.chart-container[_ngcontent-%COMP%]{padding:var(--spt-space-6)}.chart-header[_ngcontent-%COMP%]{margin-bottom:var(--spt-space-6)}.chart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-2) 0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.chart-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-sm);color:var(--spt-text-secondary);line-height:var(--spt-leading-relaxed)}.chart-content[_ngcontent-%COMP%]{position:relative;height:400px;display:flex;align-items:center;justify-content:center}.chart-content.loading[_ngcontent-%COMP%]{background:var(--spt-bg-secondary);border-radius:var(--spt-radius-lg)}.chart-canvas[_ngcontent-%COMP%]{max-width:100%;max-height:100%}.chart-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:var(--spt-space-3);color:var(--spt-text-secondary)}.chart-loading[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:var(--spt-primary-500)}.chart-empty[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;color:var(--spt-text-secondary)}.chart-empty[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:var(--spt-text-tertiary);margin-bottom:var(--spt-space-4)}.chart-empty[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-2) 0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.chart-empty[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-sm);max-width:300px;line-height:var(--spt-leading-relaxed)}.insights-panel[_ngcontent-%COMP%]{background:var(--spt-surface);border-radius:var(--spt-radius-xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm);padding:var(--spt-space-6)}.insights-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spt-space-3);margin-bottom:var(--spt-space-4)}.insights-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;color:var(--spt-warning-600)}.insights-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:var(--spt-text-lg);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.insights-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spt-space-4)}.insight-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:var(--spt-space-3);padding:var(--spt-space-4);background:var(--spt-bg-secondary);border-radius:var(--spt-radius-lg);border-left:4px solid transparent}.insight-item[_ngcontent-%COMP%]   .insight-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px;margin-top:var(--spt-space-1);flex-shrink:0}.insight-item[_ngcontent-%COMP%]   .insight-icon.success[_ngcontent-%COMP%]{color:var(--spt-success-600)}.insight-item[_ngcontent-%COMP%]   .insight-icon.warning[_ngcontent-%COMP%]{color:var(--spt-warning-600)}.insight-item[_ngcontent-%COMP%]   .insight-icon.error[_ngcontent-%COMP%]{color:var(--spt-error-600)}.insight-item[_ngcontent-%COMP%]   .insight-icon.info[_ngcontent-%COMP%]{color:var(--spt-info-600)}.insight-item[_ngcontent-%COMP%]:has(.insight-icon.success){border-left-color:var(--spt-success-600);background:var(--spt-success-50)}.insight-item[_ngcontent-%COMP%]:has(.insight-icon.warning){border-left-color:var(--spt-warning-600);background:var(--spt-warning-50)}.insight-item[_ngcontent-%COMP%]:has(.insight-icon.error){border-left-color:var(--spt-error-600);background:var(--spt-error-50)}.insight-item[_ngcontent-%COMP%]:has(.insight-icon.info){border-left-color:var(--spt-info-600);background:var(--spt-info-50)}.insight-text[_ngcontent-%COMP%]{font-size:var(--spt-text-sm);color:var(--spt-text-primary);line-height:var(--spt-leading-relaxed)}.insight-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-weight:var(--spt-font-semibold)}.empty-dashboard[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--spt-space-16);text-align:center;background:var(--spt-surface);border-radius:var(--spt-radius-xl);border:1px solid var(--spt-border);box-shadow:var(--spt-shadow-sm)}.empty-icon[_ngcontent-%COMP%]{font-size:80px;width:80px;height:80px;color:var(--spt-text-tertiary);margin-bottom:var(--spt-space-6)}.empty-dashboard[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-3) 0;font-size:var(--spt-text-xl);font-weight:var(--spt-font-semibold);color:var(--spt-text-primary)}.empty-dashboard[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 var(--spt-space-8) 0;font-size:var(--spt-text-base);color:var(--spt-text-secondary);max-width:400px;line-height:var(--spt-leading-relaxed)}.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.dashboard-header[_ngcontent-%COMP%]{flex-direction:column;gap:var(--spt-space-4);align-items:stretch}.header-controls[_ngcontent-%COMP%]{justify-content:space-between}.summary-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}.chart-content[_ngcontent-%COMP%]{height:300px}.insights-content[_ngcontent-%COMP%]{gap:var(--spt-space-3)}.insight-item[_ngcontent-%COMP%]{flex-direction:column;gap:var(--spt-space-2)}}\"]\n      });\n    }\n  }\n  return SecurityTrendsDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}