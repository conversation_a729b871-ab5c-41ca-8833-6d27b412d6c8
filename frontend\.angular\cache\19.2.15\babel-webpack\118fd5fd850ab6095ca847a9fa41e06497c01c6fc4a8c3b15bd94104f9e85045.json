{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let ThemeService = /*#__PURE__*/(() => {\n  class ThemeService {\n    constructor() {\n      this.THEME_KEY = 'spt-theme';\n      this.themeSubject = new BehaviorSubject('light');\n      this.isDarkSubject = new BehaviorSubject(false);\n      this.theme$ = this.themeSubject.asObservable();\n      this.isDark$ = this.isDarkSubject.asObservable();\n      this.initializeTheme();\n      this.setupMediaQueryListener();\n    }\n    initializeTheme() {\n      const savedTheme = localStorage.getItem(this.THEME_KEY);\n      const theme = savedTheme || 'light';\n      this.setTheme(theme);\n    }\n    setupMediaQueryListener() {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n      mediaQuery.addEventListener('change', () => {\n        if (this.themeSubject.value === 'auto') {\n          this.updateDarkMode();\n        }\n      });\n    }\n    setTheme(theme) {\n      this.themeSubject.next(theme);\n      localStorage.setItem(this.THEME_KEY, theme);\n      this.updateDarkMode();\n    }\n    toggleTheme() {\n      const currentTheme = this.themeSubject.value;\n      const newTheme = currentTheme === 'light' ? 'dark' : 'light';\n      this.setTheme(newTheme);\n    }\n    updateDarkMode() {\n      const theme = this.themeSubject.value;\n      let isDark = false;\n      if (theme === 'dark') {\n        isDark = true;\n      } else if (theme === 'auto') {\n        isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n      this.isDarkSubject.next(isDark);\n      this.applyTheme(isDark);\n    }\n    applyTheme(isDark) {\n      const root = document.documentElement;\n      if (isDark) {\n        root.setAttribute('data-theme', 'dark');\n        root.classList.add('dark');\n      } else {\n        root.setAttribute('data-theme', 'light');\n        root.classList.remove('dark');\n      }\n    }\n    getCurrentTheme() {\n      return this.themeSubject.value;\n    }\n    isDarkMode() {\n      return this.isDarkSubject.value;\n    }\n    static {\n      this.ɵfac = function ThemeService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ThemeService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ThemeService,\n        factory: ThemeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ThemeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}