{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { d as ALT, C as CONTROL, M as MAC_META, e as META, f as SHIFT } from './keycodes-CpHkExLC.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-esHZRgIN.mjs';\nimport { a as coerceElement } from './element-x4z00URv.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = /*#__PURE__*/new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nlet InputModalityDetector = /*#__PURE__*/(() => {\n  class InputModalityDetector {\n    _platform = inject(Platform);\n    _listenerCleanups;\n    /** Emits whenever an input modality is detected. */\n    modalityDetected;\n    /** Emits when the input modality changes. */\n    modalityChanged;\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n      return this._modality.value;\n    }\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n    _mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n    _modality = new BehaviorSubject(null);\n    /** Options for this InputModalityDetector. */\n    _options;\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n    _lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n    _onKeydown = event => {\n      // If this is one of the keys we should ignore, then ignore it and don't update the input\n      // modality to keyboard.\n      if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n        return;\n      }\n      this._modality.next('keyboard');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _onMousedown = event => {\n      // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n      // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n      // after the previous touch event.\n      if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n        return;\n      }\n      // Fake mousedown events are fired by some screen readers when controls are activated by the\n      // screen reader. Attribute them to keyboard input modality.\n      this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _onTouchstart = event => {\n      // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n      // events are fired. Again, attribute to keyboard input modality.\n      if (isFakeTouchstartFromScreenReader(event)) {\n        this._modality.next('keyboard');\n        return;\n      }\n      // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n      // triggered via mouse vs touch.\n      this._lastTouchMs = Date.now();\n      this._modality.next('touch');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    constructor() {\n      const ngZone = inject(NgZone);\n      const document = inject(DOCUMENT);\n      const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {\n        optional: true\n      });\n      this._options = {\n        ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n        ...options\n      };\n      // Skip the first emission as it's null.\n      this.modalityDetected = this._modality.pipe(skip(1));\n      this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n      // If we're not in a browser, this service should do nothing, as there's no relevant input\n      // modality to detect.\n      if (this._platform.isBrowser) {\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        this._listenerCleanups = ngZone.runOutsideAngular(() => {\n          return [_bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions)];\n        });\n      }\n    }\n    ngOnDestroy() {\n      this._modality.complete();\n      this._listenerCleanups?.forEach(cleanup => cleanup());\n    }\n    static ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InputModalityDetector)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InputModalityDetector,\n      factory: InputModalityDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return InputModalityDetector;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode = /*#__PURE__*/function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n  return FocusMonitorDetectionMode;\n}(FocusMonitorDetectionMode || {});\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nlet FocusMonitor = /*#__PURE__*/(() => {\n  class FocusMonitor {\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _inputModalityDetector = inject(InputModalityDetector);\n    /** The focus origin that the next focus event is a result of. */\n    _origin = null;\n    /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n    _lastFocusOrigin;\n    /** Whether the window has just been focused. */\n    _windowFocused = false;\n    /** The timeout id of the window focus timeout. */\n    _windowFocusTimeoutId;\n    /** The timeout id of the origin clearing timeout. */\n    _originTimeoutId;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n    _originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n    _elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n    _monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n    _rootNodeFocusListenerCount = new Map();\n    /**\n     * The specified detection mode, used for attributing the origin of a focus\n     * event.\n     */\n    _detectionMode;\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _windowFocusListener = () => {\n      // Make a note of when the window regains focus, so we can\n      // restore the origin info for the focused element.\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false);\n    };\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, {\n      optional: true\n    });\n    /** Subject for stopping our InputModalityDetector subscription. */\n    _stopInputModalityDetector = new Subject();\n    constructor() {\n      const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n    }\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _rootNodeFocusAndBlurListener = event => {\n      const target = _getEventTarget(event);\n      // We need to walk up the ancestor chain in order to support `checkChildren`.\n      for (let element = target; element; element = element.parentElement) {\n        if (event.type === 'focus') {\n          this._onFocus(event, element);\n        } else {\n          this._onBlur(event, element);\n        }\n      }\n    };\n    monitor(element, checkChildren = false) {\n      const nativeElement = coerceElement(element);\n      // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n      if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n        // Note: we don't want the observable to emit at all so we don't pass any parameters.\n        return of();\n      }\n      // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n      // the shadow root, rather than the `document`, because the browser won't emit focus events\n      // to the `document`, if focus is moving within the same shadow root.\n      const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n      const cachedInfo = this._elementInfo.get(nativeElement);\n      // Check if we're already monitoring this element.\n      if (cachedInfo) {\n        if (checkChildren) {\n          // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n          // observers into ones that behave as if `checkChildren` was turned on. We need a more\n          // robust solution.\n          cachedInfo.checkChildren = true;\n        }\n        return cachedInfo.subject;\n      }\n      // Create monitored element info.\n      const info = {\n        checkChildren: checkChildren,\n        subject: new Subject(),\n        rootNode\n      };\n      this._elementInfo.set(nativeElement, info);\n      this._registerGlobalListeners(info);\n      return info.subject;\n    }\n    stopMonitoring(element) {\n      const nativeElement = coerceElement(element);\n      const elementInfo = this._elementInfo.get(nativeElement);\n      if (elementInfo) {\n        elementInfo.subject.complete();\n        this._setClasses(nativeElement);\n        this._elementInfo.delete(nativeElement);\n        this._removeGlobalListeners(elementInfo);\n      }\n    }\n    focusVia(element, origin, options) {\n      const nativeElement = coerceElement(element);\n      const focusedElement = this._getDocument().activeElement;\n      // If the element is focused already, calling `focus` again won't trigger the event listener\n      // which means that the focus classes won't be updated. If that's the case, update the classes\n      // directly without waiting for an event.\n      if (nativeElement === focusedElement) {\n        this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n      } else {\n        this._setOrigin(origin);\n        // `focus` isn't available on the server\n        if (typeof nativeElement.focus === 'function') {\n          nativeElement.focus(options);\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n      return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n      const doc = this._getDocument();\n      return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n      if (this._origin) {\n        // If the origin was realized via a touch interaction, we need to perform additional checks\n        // to determine whether the focus origin should be attributed to touch or program.\n        if (this._originFromTouchInteraction) {\n          return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n        } else {\n          return this._origin;\n        }\n      }\n      // If the window has just regained focus, we can restore the most recent origin from before the\n      // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n      // focus. This typically means one of two things happened:\n      //\n      // 1) The element was programmatically focused, or\n      // 2) The element was focused via screen reader navigation (which generally doesn't fire\n      //    events).\n      //\n      // Because we can't distinguish between these two cases, we default to setting `program`.\n      if (this._windowFocused && this._lastFocusOrigin) {\n        return this._lastFocusOrigin;\n      }\n      // If the interaction is coming from an input label, we consider it a mouse interactions.\n      // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n      // our detection, because all our assumptions are for `mousedown`. We need to handle this\n      // special case, because it's very common for checkboxes and radio buttons.\n      if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n        return 'mouse';\n      }\n      return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n      // Please note that this check is not perfect. Consider the following edge case:\n      //\n      // <div #parent tabindex=\"0\">\n      //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n      // </div>\n      //\n      // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n      // #child, #parent is programmatically focused. This code will attribute the focus to touch\n      // instead of program. This is a relatively minor edge-case that can be worked around by using\n      // focusVia(parent, 'program') to focus #parent.\n      return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n      element.classList.toggle('cdk-focused', !!origin);\n      element.classList.toggle('cdk-touch-focused', origin === 'touch');\n      element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n      element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n      element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n      this._ngZone.runOutsideAngular(() => {\n        this._origin = origin;\n        this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n        // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n        // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n        // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n        // a touch event because when a touch event is fired, the associated focus event isn't yet in\n        // the event queue. Before doing so, clear any pending timeouts.\n        if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n          clearTimeout(this._originTimeoutId);\n          const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n          this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n        }\n      });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n      // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n      // focus event affecting the monitored element. If we want to use the origin of the first event\n      // instead we should check for the cdk-focused class here and return if the element already has\n      // it. (This only matters for elements that have includesChildren = true).\n      // If we are not counting child-element-focus as focused, make sure that the event target is the\n      // monitored element itself.\n      const elementInfo = this._elementInfo.get(element);\n      const focusEventTarget = _getEventTarget(event);\n      if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n        return;\n      }\n      this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n      // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n      // order to focus another child of the monitored element.\n      const elementInfo = this._elementInfo.get(element);\n      if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n        return;\n      }\n      this._setClasses(element);\n      this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n      if (info.subject.observers.length) {\n        this._ngZone.run(() => info.subject.next(origin));\n      }\n    }\n    _registerGlobalListeners(elementInfo) {\n      if (!this._platform.isBrowser) {\n        return;\n      }\n      const rootNode = elementInfo.rootNode;\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n      if (!rootNodeFocusListeners) {\n        this._ngZone.runOutsideAngular(() => {\n          rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n          rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        });\n      }\n      this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n      // Register global listeners when first element is monitored.\n      if (++this._monitoredElementCount === 1) {\n        // Note: we listen to events in the capture phase so we\n        // can detect them even if the user stops propagation.\n        this._ngZone.runOutsideAngular(() => {\n          const window = this._getWindow();\n          window.addEventListener('focus', this._windowFocusListener);\n        });\n        // The InputModalityDetector is also just a collection of global listeners.\n        this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n          this._setOrigin(modality, true /* isFromInteraction */);\n        });\n      }\n    }\n    _removeGlobalListeners(elementInfo) {\n      const rootNode = elementInfo.rootNode;\n      if (this._rootNodeFocusListenerCount.has(rootNode)) {\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n        if (rootNodeFocusListeners > 1) {\n          this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n        } else {\n          rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n          rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n          this._rootNodeFocusListenerCount.delete(rootNode);\n        }\n      }\n      // Unregister global listeners when last element is unmonitored.\n      if (! --this._monitoredElementCount) {\n        const window = this._getWindow();\n        window.removeEventListener('focus', this._windowFocusListener);\n        // Equivalently, stop our InputModalityDetector subscription.\n        this._stopInputModalityDetector.next();\n        // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n        clearTimeout(this._windowFocusTimeoutId);\n        clearTimeout(this._originTimeoutId);\n      }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n      this._setClasses(element, origin);\n      this._emitOrigin(elementInfo, origin);\n      this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n      const results = [];\n      this._elementInfo.forEach((info, currentElement) => {\n        if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n          results.push([currentElement, info]);\n        }\n      });\n      return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n      const {\n        _mostRecentTarget: mostRecentTarget,\n        mostRecentModality\n      } = this._inputModalityDetector;\n      // If the last interaction used the mouse on an element contained by one of the labels\n      // of an `input`/`textarea` that is currently focused, it is very likely that the\n      // user redirected focus using the label.\n      if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n        return false;\n      }\n      const labels = focusEventTarget.labels;\n      if (labels) {\n        for (let i = 0; i < labels.length; i++) {\n          if (labels[i].contains(mostRecentTarget)) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n    static ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusMonitor)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusMonitor,\n      factory: FocusMonitor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return FocusMonitor;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nlet CdkMonitorFocus = /*#__PURE__*/(() => {\n  class CdkMonitorFocus {\n    _elementRef = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _monitorSubscription;\n    _focusOrigin = null;\n    cdkFocusChange = new EventEmitter();\n    constructor() {}\n    get focusOrigin() {\n      return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n      const element = this._elementRef.nativeElement;\n      this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n        this._focusOrigin = origin;\n        this.cdkFocusChange.emit(origin);\n      });\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      if (this._monitorSubscription) {\n        this._monitorSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMonitorFocus)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMonitorFocus,\n      selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n      outputs: {\n        cdkFocusChange: \"cdkFocusChange\"\n      },\n      exportAs: [\"cdkMonitorFocus\"]\n    });\n  }\n  return CdkMonitorFocus;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkMonitorFocus as C, FocusMonitor as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FocusMonitorDetectionMode as c, FOCUS_MONITOR_DEFAULT_OPTIONS as d };\n//# sourceMappingURL=focus-monitor-e2l_RpN3.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}